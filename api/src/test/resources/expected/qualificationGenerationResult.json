[{"success": true, "nbTry": 1, "title": "2 - description", "errorMessage": null, "model": "gpt-4.1-mini"}, {"success": true, "nbTry": 1, "title": "3 - description des comportements", "errorMessage": null, "model": "gpt-4.1-mini"}, {"success": true, "nbTry": 1, "title": "4 - codes rome", "errorMessage": null, "model": "gpt-4.1"}, {"success": true, "nbTry": 1, "title": "5 - classifications", "errorMessage": null, "model": "gpt-4.1-mini"}, {"success": true, "nbTry": 1, "title": "6 - nive<PERSON> de maitrise", "errorMessage": null, "model": "gpt-4.1-mini"}, {"success": true, "nbTry": 1, "title": "7 - comportements", "errorMessage": null, "model": "gpt-4.1-mini"}, {"success": true, "nbTry": 1, "title": "8 - activités", "errorMessage": null, "model": "gpt-4.1"}]