{"objectType": "userPage", "pageIndex": 0, "numberOfElementsInPage": 8, "pageSize": 8, "totalNumberOfElements": 8, "totalPages": 1, "content": [{"userType": "foUserSummary", "id": "45f3ce6c-f1c9-4356-9d07-0bca1b241400", "firstName": "noProfileJean", "lastName": "noProfileDupont", "enabled": null, "email": "noProfile@localhost", "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": "Pizzaiolo", "jobTitle": "Pizzaiolo", "city": null}, "capacitiesCount": 0, "experiencesCount": 1, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 1.0}, "transactionalBlacklisted": null, "createdAt": "2020-02-01T23:00:00Z"}, {"userType": "foUserSummary", "id": "45f3ce6c-f1c9-4356-9d07-0bca1b241401", "firstName": "noLocationJean", "lastName": "noLocationDupont", "enabled": null, "email": "noLocation@localhost", "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "jobTitle": null, "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "transactionalBlacklisted": null, "createdAt": "2020-02-01T23:00:00Z"}, {"userType": "foUserSummary", "id": "45f3ce6c-f1c9-4356-9d07-0bca1b241402", "firstName": "locationJean", "lastName": "locationDupont", "enabled": null, "email": "location@localhost", "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "jobTitle": null, "city": "59000"}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "transactionalBlacklisted": null, "createdAt": "2020-02-01T23:00:00Z"}, {"userType": "foUserSummary", "id": "45f3ce6c-f1c9-4356-9d07-0bca1b241403", "firstName": "oneChannelJean", "lastName": "oneChannelDupont", "enabled": null, "email": "oneChannel@localhost", "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "jobTitle": null, "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "transactionalBlacklisted": null, "createdAt": "2020-02-01T23:00:00Z"}, {"userType": "foUserSummary", "id": "45f3ce6c-f1c9-4356-9d07-0bca1b241404", "firstName": "twoChannels<PERSON>ean", "lastName": "twoChannelsDupont", "enabled": null, "email": "twoChannels@localhost", "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "jobTitle": null, "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "transactionalBlacklisted": null, "createdAt": "2020-02-01T23:00:00Z"}, {"userType": "foUserSummary", "id": "45f3ce6c-f1c9-4356-9d07-0bca1b241405", "firstName": "noInformationJean", "lastName": "noInformationDupont", "enabled": null, "email": "noInformation@localhost", "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "jobTitle": null, "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "transactionalBlacklisted": null, "createdAt": "2020-02-01T23:00:00Z"}, {"userType": "foUserSummary", "id": "45f3ce6c-f1c9-4356-9d07-0bca1b241406", "firstName": "oneOTJean", "lastName": "oneOTDupont", "enabled": null, "email": "oneOT@localhost", "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "jobTitle": null, "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "transactionalBlacklisted": null, "createdAt": "2020-02-01T23:00:00Z"}, {"userType": "foUserSummary", "id": "45f3ce6c-f1c9-4356-9d07-0bca1b241407", "firstName": "twoOTJean", "lastName": "twoOTDupont", "enabled": null, "email": "twoOT@localhost", "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": null, "salary": null, "selectedActivity": null, "selectedActivityId": null, "selectedOccupation": null, "jobTitle": null, "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "transactionalBlacklisted": null, "createdAt": "2020-02-01T23:00:00Z"}]}