{"pageIndex": 0, "numberOfElementsInPage": 10, "pageSize": 10, "totalNumberOfElements": 10, "totalPages": 1, "content": [{"id": "e2e1ab74-9f3e-4d03-b997-887b40f27700", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CREATED_ACCOUNT", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}, {"id": "e2e1ab74-9f3e-4d03-b997-887b40f27701", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CONFIRMED_SITUATION", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}, {"id": "e2e1ab74-9f3e-4d03-b997-887b40f27702", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CONFIRMED_PROFESSION_DIRECT", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}, {"id": "e2e1ab74-9f3e-4d03-b997-887b40f27703", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CONFIRMED_CITY", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}, {"id": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CONFIRMED_SALARY", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}, {"id": "e2e1ab74-9f3e-4d03-b997-887b40f27705", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "CONFIRMED_CONTACT_DETAILS", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}, {"id": "e2e1ab74-9f3e-4d03-b997-887b40f27706", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "LEGACY_ACCOUNT", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}, {"id": "e2e1ab74-9f3e-4d03-b997-887b40f27707", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "NOT_AFFECTED", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}, {"id": "e2e1ab74-9f3e-4d03-b997-887b40f27708", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "BO_INITIALIZED", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}, {"id": "e2e1ab74-9f3e-4d03-b997-887b40f27709", "firstName": null, "lastName": null, "enabled": null, "userProfileProgress": {"userRegistrationState": {"userRegistrationStateStep": "BO_CONFIRMED", "situation": "EMPLOYEE", "salary": 2500, "selectedOccupation": "Pompière", "selectedOccupationId": "e2e1ab74-9f3e-4d03-b997-887b40f27704", "jobTitle": "Un métier bien connu", "city": null}, "capacitiesCount": 0, "experiencesCount": 0, "hasBehaviors": false, "candidaturesCount": 0, "masteryLevel": 0.0}, "email": "<EMAIL>", "transactionalBlacklisted": null}]}