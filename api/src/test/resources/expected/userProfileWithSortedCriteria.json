{"contactTime": "AFTERNOON", "birthDate": null, "salary": null, "situation": null, "location": null, "isSourceInitialized": false, "lastCandidatures": [], "lastExperiences": [], "behaviors": [], "criteria": [{"code": "CRV-1-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for CRV-1-1", "titleForBO": null, "icon": "smile", "valueIndex": 0, "selected": true}, {"code": "CRV-1-2", "titleForQuestion": "La nuit", "titleStandalone": "Title for CRV-1-2", "titleForBO": null, "icon": "smile", "valueIndex": 1, "selected": false}, {"code": "CRV-1-3", "titleForQuestion": "La nuit", "titleStandalone": "Title for CRV-1-3", "titleForBO": null, "icon": "smile", "valueIndex": 2, "selected": true}, {"code": "C3-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for C3-1", "titleForBO": null, "icon": "smile", "valueIndex": 0, "selected": false}, {"code": "C3-2", "titleForQuestion": "La nuit", "titleStandalone": "Title for C3-2", "titleForBO": null, "icon": "smile", "valueIndex": 1, "selected": false}, {"code": "C4-1", "titleForQuestion": "La nuit", "titleStandalone": "Title for C4-1", "titleForBO": null, "icon": "smile", "valueIndex": 0, "selected": true}, {"code": "C4-2", "titleForQuestion": "La nuit", "titleStandalone": "Title for C4-2", "titleForBO": null, "icon": "smile", "valueIndex": 1, "selected": false}], "includesMenus": ["jobs_matching"], "isExportable": false}