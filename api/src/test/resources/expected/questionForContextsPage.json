{"pageIndex": 0, "numberOfElementsInPage": 5, "pageSize": 5, "totalNumberOfElements": 5, "totalPages": 1, "content": [{"title": "Context question 1", "suggestedAnswers": {"none": "None  - Context question 1", "low": "Low  - Context question 1", "medium": "Medium  - Context question 1", "high": "High  - Context question 1"}, "contexts": [{"lastModifiedBy": null, "code": "CT-22", "title": "<PERSON><PERSON><PERSON> de sécurité (ne pas fumer)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"lastModifiedBy": null, "code": "CT-19", "title": "Règles de sécurité (règles de circulation)", "description": "Ceci est une règle de sécurité", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}]}, {"title": "Context question 2", "suggestedAnswers": {"none": "None  - Context question 2", "low": "Low  - Context question 2", "medium": "Medium  - Context question 2", "high": "High  - Context question 2"}, "contexts": [{"lastModifiedBy": null, "code": "CT-4", "title": "Différentes lignes / différents services", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-01", "title": "Connaissances", "description": "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches."}}, "origin": null}, {"lastModifiedBy": null, "code": "CT-20", "title": "Règles de tri des déchets", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"lastModifiedBy": null, "code": "CT-8", "title": "Tâches répétitives", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-02", "title": "Gestes pros et matériel associé", "description": "Le geste professionnel est un savoir-faire spécifique attaché à un corps de métier. Il peut être lié à l’utilisation de matériels dédiés."}}, "origin": null}]}, {"title": "Context question 3", "suggestedAnswers": {"none": "None  - Context question 3", "low": "Low  - Context question 3", "medium": "Medium  - Context question 3", "high": "High  - Context question 3"}, "contexts": [{"lastModifiedBy": null, "code": "CT-22", "title": "<PERSON><PERSON><PERSON> de sécurité (ne pas fumer)", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"lastModifiedBy": null, "code": "CT-20", "title": "Règles de tri des déchets", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"lastModifiedBy": null, "code": "CT-19", "title": "Règles de sécurité (règles de circulation)", "description": "Ceci est une règle de sécurité", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}, {"lastModifiedBy": null, "code": "CT-16", "title": "Présentiel", "description": "", "categoryLevel": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>, horaires de jour, amplitude inférieure à 8 heures.", "score": 0, "category": {"code": "CCT-03", "title": "R<PERSON>hme et conditions de travail", "description": "Permet d’identifier l’intensité et les variations de cadence de l’activité décrite. La répétition des tâches exécutées est également concernée. On peut parler de pénibilité."}}, "origin": null}]}, {"title": "Title for Titre custom de contexte ?", "suggestedAnswers": {"none": "None  - Title for Titre custom de contexte ?", "low": "Low  - Title for Titre custom de contexte ?", "medium": "Medium  - Title for Titre custom de contexte ?", "high": "High  - Title for Titre custom de contexte ?"}, "contexts": [{"lastModifiedBy": null, "code": "CT-20", "title": "Règles de tri des déchets", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}]}, {"title": "Title for Titre custom de contexte ?", "suggestedAnswers": {"none": "None  - Title for Titre custom de contexte ?", "low": "Low  - Title for Titre custom de contexte ?", "medium": "Medium  - Title for Titre custom de contexte ?", "high": "High  - Title for Titre custom de contexte ?"}, "contexts": [{"lastModifiedBy": null, "code": "CT-20", "title": "Règles de tri des déchets", "description": "", "categoryLevel": {"title": "Non significatif", "description": "", "score": 0, "category": {"code": "CCT-05", "title": "Procédures et modes opératoires", "description": "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues."}}, "origin": null}]}]}