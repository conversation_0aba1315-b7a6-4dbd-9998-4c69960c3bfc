{"objectType": "sourcingCandidaturePage", "pageIndex": 0, "numberOfElementsInPage": 1, "pageSize": 42, "totalNumberOfElements": 1, "totalPages": 1, "content": [{"sourcingCandidatureType": "SourcingCandidatureItemDTO", "generated": null, "candidatureId": 1, "anonymousCode": "c", "color": "a", "situation": null, "firstName": null, "lastName": null, "phone": null, "email": null, "lastActionDate": "2020-02-02T00:00:00+01:00", "submissionDate": "2020-10-03T07:05:05+02:00", "answer": null, "state": "NEW", "lastNote": "", "userId": "3c2a6fbf-e563-4b0b-9ae0-af62cfa12f8d", "numberOfOfferCandidatures": 0, "hasSoftSkillPdf": true, "location": {"city": null, "citycode": null, "postcode": null, "departmentCode": "55", "regionName": null, "longitude": null, "latitude": null, "radiusInKm": null}, "sectors": [{"id": "f992a0f7-c5a8-11ee-8ff0-0242ac110003", "code": "C-06", "label": "Relation clients, SAV, support", "abbreviation": "SAV"}, {"id": "f992a69a-c5a8-11ee-8ff0-0242ac110003", "code": "C-07", "label": "IT", "abbreviation": "IT"}, {"id": null, "code": "C-XX", "label": "B, C, D", "abbreviation": "Autres"}]}]}