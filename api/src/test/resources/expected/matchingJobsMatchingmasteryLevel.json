{"pageIndex": 0, "numberOfElementsInPage": 2, "pageSize": 20, "totalNumberOfElements": 2, "totalPages": 1, "content": [{"title": "Job with code J1 and organization Recruiter(super=AbstractOrganization(siren=, siret=, address=, latitude=5.0, longitude=55.0), organizationType=ENTERPRISE)", "service": "s1", "recruiterCode": "E-042", "recruiterTitle": "Entreprise du 42", "employerCode": null, "employerTitle": null, "state": "PUBLISHED", "modifiable": false, "recruitmentProfiles": [], "candidatureId": null, "matchingRateInPercent": 100}, {"title": "Job with code J2 and organization Recruiter(super=AbstractOrganization(siren=, siret=, address=, latitude=5.0, longitude=55.0), organizationType=ENTERPRISE)", "service": "s1", "recruiterCode": "E-042", "recruiterTitle": "Entreprise du 42", "employerCode": null, "employerTitle": null, "state": "PUBLISHED", "modifiable": false, "recruitmentProfiles": [], "candidatureId": null, "matchingRateInPercent": 100}]}