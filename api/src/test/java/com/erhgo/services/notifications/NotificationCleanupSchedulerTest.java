package com.erhgo.services.notifications;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.UserNotificationMotherObject;
import com.erhgo.domain.userprofile.notification.AbstractNotification;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.security.Role;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import java.time.OffsetDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@TestPropertySource(properties = {"application.notificationCleanup.retentionInMonths=3"})
class NotificationCleanupSchedulerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    NotificationCleanupScheduler notificationCleanupScheduler;

    @Autowired
    NotificationRepository notificationRepository;

    @Test
    @ResetDataAfter
    @com.erhgo.security.WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    void cleanupOldNotifications_shouldDeleteNotificationsOlderThanThreeMonths() {
        var oldNotification = applicationContext.getBean(UserNotificationMotherObject.class)
                .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                .withCreatedDate(OffsetDateTime.now().minusMonths(4))
                .withSubjectAndContent("Old notification", "This should be deleted")
                .buildAndPersist();

        var recentNotification = applicationContext.getBean(UserNotificationMotherObject.class)
                .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                .withCreatedDate(OffsetDateTime.now().minusMonths(2))
                .withSubjectAndContent("Recent notification", "This should be kept")
                .buildAndPersist();

        var veryOldNotification = applicationContext.getBean(UserNotificationMotherObject.class)
                .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                .withCreatedDate(OffsetDateTime.now().minusMonths(6))
                .withSubjectAndContent("Very old notification", "This should also be deleted")
                .buildAndPersist();

        notificationCleanupScheduler.cleanupOldNotifications();

        txHelper.doInTransaction(() -> {
            var remainingNotifications = notificationRepository.findAll();
            var remainingIds = remainingNotifications.stream()
                    .map(AbstractNotification::getId)
                    .toList();

            assertThat(remainingIds).contains(recentNotification.getId());
            assertThat(remainingIds).doesNotContain(oldNotification.getId());
            assertThat(remainingIds).doesNotContain(veryOldNotification.getId());
        });
    }

    @Test
    @ResetDataAfter
    @com.erhgo.security.WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    void cleanupOldNotifications_shouldHandleEmptyCase() {
        var recentNotification = applicationContext.getBean(UserNotificationMotherObject.class)
                .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                .withCreatedDate(OffsetDateTime.now().minusMonths(1))
                .withSubjectAndContent("Recent notification", "This should be kept")
                .buildAndPersist();

        var initialCount = notificationRepository.count();

        notificationCleanupScheduler.cleanupOldNotifications();

        txHelper.doInTransaction(() -> {
            var finalCount = notificationRepository.count();
            assertThat(finalCount).isEqualTo(initialCount);

            var notification = notificationRepository.findById(recentNotification.getId());
            assertThat(notification).isPresent();
        });
    }

    @Test
    @ResetDataAfter
    @com.erhgo.security.WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    void cleanupOldNotifications_shouldDeleteMultipleOldNotifications() {
        var oldNotifications = List.of(
                applicationContext.getBean(UserNotificationMotherObject.class)
                        .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                        .withCreatedDate(OffsetDateTime.now().minusMonths(4))
                        .withSubjectAndContent("Old 1", "Content 1")
                        .buildAndPersist(),
                applicationContext.getBean(UserNotificationMotherObject.class)
                        .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                        .withCreatedDate(OffsetDateTime.now().minusMonths(5))
                        .withSubjectAndContent("Old 2", "Content 2")
                        .buildAndPersist(),
                applicationContext.getBean(UserNotificationMotherObject.class)
                        .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                        .withCreatedDate(OffsetDateTime.now().minusMonths(6))
                        .withSubjectAndContent("Old 3", "Content 3")
                        .buildAndPersist()
        );

        var recentNotifications = List.of(
                applicationContext.getBean(UserNotificationMotherObject.class)
                        .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                        .withCreatedDate(OffsetDateTime.now().minusMonths(1))
                        .withSubjectAndContent("Recent 1", "Content 1")
                        .buildAndPersist(),
                applicationContext.getBean(UserNotificationMotherObject.class)
                        .withRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist())
                        .withCreatedDate(OffsetDateTime.now().minusMonths(2))
                        .withSubjectAndContent("Recent 2", "Content 2")
                        .buildAndPersist()
        );

        notificationCleanupScheduler.cleanupOldNotifications();

        txHelper.doInTransaction(() -> {
            var remainingNotifications = notificationRepository.findAll();
            var remainingIds = remainingNotifications.stream()
                    .map(AbstractNotification::getId)
                    .toList();

            recentNotifications.forEach(notification ->
                    assertThat(remainingIds).contains(notification.getId()));

            oldNotifications.forEach(notification ->
                    assertThat(remainingIds).doesNotContain(notification.getId()));
        });
    }
}
