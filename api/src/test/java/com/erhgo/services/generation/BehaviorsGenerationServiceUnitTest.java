package com.erhgo.services.generation;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.openapi.dto.ErhgoOccupationBehaviorsCategoriesDTO;
import com.erhgo.repositories.BehaviorRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.ActivityDTOBuilder;
import com.erhgo.services.dtobuilder.AuditingDTOBuilder;
import com.erhgo.services.dtobuilder.ErhgoOccupationDataDTOBuilder;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ClassUtils;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;
import static org.springframework.test.util.ReflectionTestUtils.setField;

@ExtendWith(MockitoExtension.class)
class BehaviorsGenerationServiceUnitTest {

    static final double temperature = 42d;

    static final String model = "gpt42";
    static final String occupationTitle = "Title of occupation";
    static final UUID OCCUPATION_UUID = UUID.randomUUID();

    @Mock
    GenerationClient generationClient;
    @Mock
    KeycloakMockService keycloakService;
    @Mock
    SecurityService securityService;
    @Mock
    ErhgoOccupationRepository erhgoOccupationRepository;
    @Mock
    BehaviorRepository behaviorRepository;

    @InjectMocks
    BehaviorGenerationService service;

    @Mock
    private ModelMapper modelMapper;

    static final String OPENAI_RESPONSE = "{\"result\": [\"CONFIDENTIALITY\", \"TENACITY\", \"PERSEVERANCE\"]}";

    private ChatCompletionResponse chatCompletionResponse(String choice) {
        return new ChatCompletionResponse(choice, new OpenAIResponse<>());
    }

    @BeforeEach
    void initialize() {
        setField(service, "erhgoOccupationDataDTOBuilder", new ErhgoOccupationDataDTOBuilder(modelMapper, keycloakService, null, new ActivityDTOBuilder(new AuditingDTOBuilder(keycloakService)),
                null,
                null,
                null));
        setField(service, "promptConfig", new PromptConfig().setModel(model).setTemperature(temperature).setMaxRetry(3).setMessageFilename("BehaviorsQualificationMessages.yaml"));
        setField(service, "yamlPromptReader", new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }));

        lenient().when(behaviorRepository.findByBehaviorCategory(any(BehaviorCategory.class))).thenAnswer(invocation -> {
            BehaviorCategory category = invocation.getArgument(0);
            Behavior behavior = new Behavior();
            behavior.setBehaviorCategory(category);
            return List.of(behavior);
        });

        lenient().when(modelMapper.map(any(ErhgoOccupation.class), eq(ErhgoOccupationBehaviorsCategoriesDTO.class)))
                .thenReturn(new ErhgoOccupationBehaviorsCategoriesDTO());
        mockOccupationRepository();

        lenient().when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(OPENAI_RESPONSE));
    }

    private void mockOccupationRepository() {
        var occupation = new ErhgoOccupationMotherObject().withTitle(occupationTitle).withId(OCCUPATION_UUID).withCapacities(CapacityGenerator.buildCapacity()).build();
        lenient().when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.of(occupation));
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateBehaviors() {
        mockOccupationRepository();
        var expectedBehaviors = List.of(BehaviorCategory.CONFIDENTIALITY, BehaviorCategory.TENACITY, BehaviorCategory.PERSEVERANCE);
        var generated = service.generate(occupationTitle).getResult();
        Assertions.assertEquals(expectedBehaviors, generated);
    }

    @Test
    @SneakyThrows
    void generateAndValidateBehaviors_invalidesBehaviors() {
        var openAIResponse = "['XYZ', 'ABC']";
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(openAIResponse));

        Assertions.assertThrows(FatalGenerationException.class, () -> {
            service.generate(occupationTitle);
        });

    }

    @Test
    @SneakyThrows
    void generateAndValidateBehaviors_invalidTooManyBehaviors() {
        var openAIResponse = "['CONFIDENTIALITY', 'TENACITY', 'PERSEVERANCE', 'VIGILANCE']";
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(openAIResponse));

        Assertions.assertThrows(FatalGenerationException.class, () -> {
            service.generate(occupationTitle);
        });
    }


    @Test
    @SneakyThrows
    void associateBehaviorsToOccupationClearPreviousBehaviors() {
        when(erhgoOccupationRepository.save(any(ErhgoOccupation.class))).thenAnswer(invocation -> {
            var res = invocation.getArgument(0);
            ReflectionTestUtils.setField(res, "id", OCCUPATION_UUID);
            return res;
        });
        mockOccupationRepository();
        var updatedOccupation = service.associateBehaviorsToOccupation(OCCUPATION_UUID).getResult();
        Assertions.assertNotNull(updatedOccupation);
        Assertions.assertEquals(true, updatedOccupation.getIsBehaviorCategory2Overloaded());

    }


    @Test
    void associateBehaviorsToOccupation_OccupationNotFound() {
        when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.empty());

        Assertions.assertThrows(EntityNotFoundException.class, () -> {
            service.associateBehaviorsToOccupation(OCCUPATION_UUID);
        });
    }


}
