package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.openapi.dto.ErhgoOccupationBehaviorsCategoriesDTO;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dtobuilder.ErhgoClassificationDTOBuilder;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ClassUtils;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.util.ReflectionTestUtils.setField;

@ExtendWith(MockitoExtension.class)
class ClassificationGenerationServiceUnitTest {

    static final double temperature = 42d;

    static final String model = "gpt42";
    static final String occupationTitle = "Title of occupation";
    static final UUID OCCUPATION_UUID = UUID.randomUUID();

    @Mock
    GenerationClient generationClient;
    @Mock
    ErhgoOccupationRepository erhgoOccupationRepository;
    @Mock
    ErhgoClassificationRepository erhgoClassificationRepository;
    @Mock
    SecurityService securityService;

    @InjectMocks
    ClassificationGenerationService service;

    @Mock
    private ModelMapper modelMapper;

    static final String OPENAI_RESPONSE_CLASSIFICATION = "{\"result\": [\"SO-07\",\"SO-06\",\"SO-09\",\"SO-05\"]}";

    @BeforeEach
    void initialize() {
        setField(service, "erhgoClassificationDTOBuilder", new ErhgoClassificationDTOBuilder(modelMapper));
        setField(service, "promptConfig", new PromptConfig().setMessageFilename("ClassificationMessages.yaml").setTemperature(temperature).setModel(model).setMaxRetry(3).setMaxTokens(1800));
        setField(service, "yamlPromptReader", new YamlPromptReader(new ResourceLoader() {
            @Override
            public Resource getResource(String location) {
                return new ClassPathResource(location.replace("classpath:", ""));
            }

            @Override
            public ClassLoader getClassLoader() {
                return ClassUtils.getDefaultClassLoader();
            }
        }));

        lenient().when(erhgoClassificationRepository.findErhgoClassificationByCodeIn(anyCollection())).thenAnswer(invocation -> {
            List<String> codes = invocation.getArgument(0);
            return codes.stream().map(code -> {
                ErhgoClassification mockClassification = mock(ErhgoClassification.class);
                lenient().when(mockClassification.getCode()).thenReturn(code);
                return mockClassification;
            }).toList();
        });
        lenient().when(modelMapper.map(any(ErhgoOccupation.class), eq(ErhgoOccupationBehaviorsCategoriesDTO.class)))
                .thenReturn(new ErhgoOccupationBehaviorsCategoriesDTO());
        setField(ClassificationGenerationService.class, "MIN_CLASSIFICATION", 1);
        setField(ClassificationGenerationService.class, "MAX_CLASSIFICATION", 6);

    }

    private void mockOpenAIResponse(String response) {
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(new ChatCompletionResponse(response, null));
    }

    private void mockOccupationRepository() {
        var occupation = new ErhgoOccupationMotherObject().withTitle(occupationTitle).withId(OCCUPATION_UUID).withCapacities(CapacityGenerator.buildCapacity()).build();
        when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.of(occupation));
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateClassifications() {
        mockOpenAIResponse(OPENAI_RESPONSE_CLASSIFICATION);
        var generatedClassifications = service.generate(occupationTitle);
        assertThat(generatedClassifications.getResult()).extracting(ErhgoClassification::getCode).containsExactlyInAnyOrder("SO-07", "SO-06", "SO-09", "SO-05");
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateClassifications_invalidCodes() {
        var openAIResponse = "{\"result\": [\"ABC-071\",\"ABC-06\",\"ABC-09\",\"ABC-05\"]}";
        mockOpenAIResponse(openAIResponse);
        when(erhgoClassificationRepository.findErhgoClassificationByCodeIn(anyList())).thenReturn(List.of());

        Assertions.assertThrows(FatalGenerationException.class, () -> {
            service.generate(occupationTitle);
        });
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateClassifications_invalidTooFewClassifications() {
        setField(ClassificationGenerationService.class, "MIN_CLASSIFICATION", 42);
        mockOpenAIResponse(OPENAI_RESPONSE_CLASSIFICATION);
        assertThrows(FatalGenerationException.class, () -> service.generate(occupationTitle));
    }

    @Test
    @SneakyThrows
    void callOpenAIAndValidateClassifications_invalidTooManyClassifications() {
        setField(ClassificationGenerationService.class, "MAX_CLASSIFICATION", 1);
        mockOpenAIResponse(OPENAI_RESPONSE_CLASSIFICATION);
        assertThrows(FatalGenerationException.class, () -> service.generate(occupationTitle));
    }

    @Test
    @SneakyThrows
    void associateClassificationsToOccupationClearPreviousClassification() {
        when(erhgoOccupationRepository.save(any(ErhgoOccupation.class))).thenAnswer(invocation -> {
            var res = invocation.getArgument(0);
            ReflectionTestUtils.setField(res, "id", OCCUPATION_UUID);
            return res;
        });
        mockOccupationRepository();
        mockOpenAIResponse(OPENAI_RESPONSE_CLASSIFICATION);
        var generatedClassifications = service.generateAndAssociateClassificationsAssociations(OCCUPATION_UUID);
        Assertions.assertNotNull(generatedClassifications);
        Assertions.assertEquals(4, generatedClassifications.getResult().size());

    }


    @Test
    @SneakyThrows
    void associateClassificationsToOccupation_OccupationNotFound() {
        when(erhgoOccupationRepository.findById(OCCUPATION_UUID)).thenReturn(Optional.empty());
        assertThrows(EntityNotFoundException.class, () -> service.generateAndAssociateClassificationsAssociations(OCCUPATION_UUID));
    }

}
