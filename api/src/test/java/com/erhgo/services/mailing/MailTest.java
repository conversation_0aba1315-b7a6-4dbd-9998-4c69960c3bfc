package com.erhgo.services.mailing;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.GmailScopes;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.common.base.Joiner;
import jakarta.mail.Address;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import sendinblue.ApiClient;
import sendinblue.ApiException;
import sendinblue.auth.ApiKeyAuth;
import sibApi.TransactionalEmailsApi;
import sibModel.SendSmtpEmail;
import sibModel.SendSmtpEmailMessageVersions;
import sibModel.SendSmtpEmailTo;
import sibModel.SendSmtpEmailTo1;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
class MailTest {

    final com.fasterxml.jackson.databind.ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    record DailyRecapOffer(
            String TITRE,
            String CONTRAT,
            String VILLE,
            String CODE_POSTAL,
            String LIEN,
            String SALAIRE,
            String OCCUPATION_ID
    ) {
    }

    record DailyRecapPerRecruiter(String RECRUTEUR, List<DailyRecapOffer> OFFRES) {
    }

    record DailyRecap(String PRENOM, int NB_OFFRES, String SOUHAITS,
                      List<DailyRecapPerRecruiter> OFFRES_PAR_RECRUTEUR) {

    }

    @SneakyThrows
    @Test
    @Disabled
    void mail_template_recap_offres() {
        var apiClient = new ApiClient();
        var apiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("api-key");
        apiKeyAuth.setApiKey("xxx");
        var txClient = new TransactionalEmailsApi(apiClient);
        var parameters = new DailyRecap("Michel", 4, "'La formation, la pédagogie, l’apprentissage' ; 'Le soin, l’attention aux autres, la santé'", List.of(
                new DailyRecapPerRecruiter("McDonalds", List.of(
                        new DailyRecapOffer("Technicien de maintenance", "Alternance", "Lyon", "69004", "https://app.erhgo.fr", "entre 15 000 € et 25 000 €", "O-1"),
                        new DailyRecapOffer("Exploitant agricole", "CDI", "Villeurbanne", "69004", "https://app.erhgo.fr", "45 000 €", "O-2"),
                        new DailyRecapOffer("Premier ministre", "CDD", "Pouilly-en-Auxois", "08800", "https://app.erhgo.fr", null, "O-3")
                )),
                new DailyRecapPerRecruiter("APEC", List.of(
                        new DailyRecapOffer("Conseiller clientèle", "CDD", "Paris", "75005", "https://app.erhgo.fr", "", "O-4")
                ))
        ));
        if (false) txClient.sendTransacEmail(
                new SendSmtpEmail()
                        .templateId(4L)
                        .to(List.of(new SendSmtpEmailTo().email("*******")))
                        .params(parameters)
        );
        var re = txClient.sendTransacEmail(
                new SendSmtpEmail()
                        .templateId(3L)
                        .messageVersions(List.of(
                                new SendSmtpEmailMessageVersions().to(List.of(new SendSmtpEmailTo1().email("*******"))).params(objectMapper.convertValue(parameters, new TypeReference<Map<String, Object>>() {
                                })).subject("2 - Test 1"),
                                new SendSmtpEmailMessageVersions().to(List.of(new SendSmtpEmailTo1().email("%*******"), new SendSmtpEmailTo1().email("*******"))).params(objectMapper.convertValue(parameters, new TypeReference<Map<String, Object>>() {
                                })).subject("2 - Recipient KO")
                        )));
        log.error("Alors ? {} {}", re, Joiner.on(", ").join(re.getMessageIds()));
    }

    @SneakyThrows
    @Test
    @Disabled("Only used to test on internal services when using the method 'SendSmtpEmailTo' ")
    void mail_template_composer() {
        var apiClient = new ApiClient();
        var apiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("api-key");
        apiKeyAuth.setApiKey("xxx");
        var txClient = new TransactionalEmailsApi(apiClient);
        var parameters = Map.of(
                "WRONG_SIRET", true,
                "FIRSTNAME", "Lea",
                "SIRET", "1234",
                "COMPANY_NAME", "Nestlé",
                "SOURCING_URL", "https://testing-jerecrute.jenesuispasuncv.fr");
        txClient.sendTransacEmail(
                new SendSmtpEmail()
                        .templateId(5L)
                        .to(List.of(new SendSmtpEmailTo().email("*******")))
                        .params(parameters) // => params.WRONG_SIRET dans le template
        );
    }


    @SneakyThrows
    @Test
    @Disabled("Only used to test on internal services when using the method 'SendSmtpEmailTo' ")
    void mail_template_composer_proposal() {
        var apiClient = new ApiClient();
        var apiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("api-key");
        apiKeyAuth.setApiKey("xxx");
        var txClient = new TransactionalEmailsApi(apiClient);
        var classifications = List.of("");
        var cl = String.join(", ", classifications);
        var expectedParams = Map.of(
                "recruitment_code", "R-1",
                "organization", "Entreprise du S-42",
                "job_title", "A sourcing Job",
                "contract", "CDI",
                "time", "5h par semaine",
                "salary", "Non précisé",
                "location", "42000 Lyon",
                "erhgoClassifications", cl.isEmpty() ? "empty" : cl
        );
        txClient.sendTransacEmail(
                new SendSmtpEmail()
                        .templateId(13L)
                        .to(List.of(new SendSmtpEmailTo().email("*******")))
                        .params(expectedParams)
        );
    }

    @SneakyThrows
    @Test
    @Disabled
    void mail_template_composer_sourcing() {
        var apiClient = new ApiClient();
        var apiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("api-key");
        apiKeyAuth.setApiKey("xxx");
        var txClient = new TransactionalEmailsApi(apiClient);
        var parameters = Map.of(
                "FULLNAME", "un compte keycloak du recruteur",
                "ORGANIZATION_NAME", "Entreprise du recruteur",
                "SPONTANEOUS_LINK", "https://staging.jenesuispasuncv.fr/#/sourcing",
                "NEW_SPONTANEOUS_CANDIDATURES", "0",
                "TOTAL_SPONTANEOUS_CANDIDATURES", "5",
                "CANDIDATURES", List.of(Map.of(
                                "TITLE", "Titre du recrutement 1",
                                "LOCATION", "Ville du rec 1",
                                "TOTAL_CANDIDATURES", 9,
                                "NEW_CANDIDATURES", 5,
                                "LINK", "https://google.com"
                        ),
                        Map.of(
                                "TITLE", "Titre du recrutement 2",
                                "LOCATION", "Ville du rec 2",
                                "TOTAL_CANDIDATURES", 8,
                                "NEW_CANDIDATURES", 2,
                                "LINK", "https://#/recruitment-detail/" + 56
                        )));
        txClient.sendTransacEmail(
                new SendSmtpEmail()
                        .templateId(22L)
                        .to(List.of(new SendSmtpEmailTo().email("*******")))
                        .params(parameters) // => params.WRONG_SIRET dans le template
        );
    }


    @SneakyThrows
    @Test
    @Disabled
    void sendValidationEmailCandidatureTemplate() {
        var apiClient = new ApiClient();
        var apiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("api-key");
        apiKeyAuth.setApiKey("xxx");
        var txClient = new TransactionalEmailsApi(apiClient);

        var parameters = Map.of("JOB", "operateur",
                "RECRUITER", "Ma super boite",
                "CITY", "69000 - Lyon",
                "NAME", "Emma",
                "CONTRACT", "CDI",
                "TIME", "39",
                "SALARY", "22000 - 30000");

        txClient.sendTransacEmail(
                new SendSmtpEmail()
                        .templateId(14L)
                        .to(List.of(new SendSmtpEmailTo().email("*******")))
                        .params(parameters)
        );
    }


    public static Gmail getGmailService() throws GeneralSecurityException, IOException {
        var credentials = ServiceAccountCredentials.fromStream(
                        new ByteArrayInputStream("""
                                {
                                  "type": "service_account",
                                  "project_id": "erhgo-smtp",
                                  "private_key_id": "xxx",
                                  "private_key": "xxx",
                                  "client_email": "*******",
                                  "client_id": "107053018452210406208",
                                  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                                  "token_uri": "https://oauth2.googleapis.com/token",
                                  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                                  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/erhgo-smtp%40erhgo-smtp.iam.gserviceaccount.com",
                                  "universe_domain": "googleapis.com"
                                }
                                """.getBytes(StandardCharsets.UTF_8))
                )
                .createScoped(Collections.singleton(GmailScopes.GMAIL_SEND))
                .createDelegated(USER_EMAIL);

        return new Gmail.Builder(GoogleNetHttpTransport.newTrustedTransport(), JSON_FACTORY, new HttpCredentialsAdapter(credentials))
                .setApplicationName(APPLICATION_NAME)
                .build();
    }

    private static void addMultiparts(MimeMessage message) throws MessagingException, IOException {
        message.setSubject("[TEST ERIC jenesuisPASunCV] Assistant Commercial et ADV (F/H) - mab202404-3971");
        var content = """
                anonymouscode: M0R3
                email : *******
                nom : Eric
                prenom : Westrelin
                ville : Lyon
                cp : 69003
                portable : +33601234567
                saisielib : 
                origine: jenesuisPASunCV
                datesoumission : 30/05/2024
                """.replaceAll("\n", "\r\n");
        // Create the message part
        var textBodyPart = new MimeBodyPart();
        textBodyPart.setText(content, "utf-8");
        textBodyPart.setHeader("Content-Transfer-Encoding", "quoted-printable");

        // Create multipart
        var multipart = new MimeMultipart();
        multipart.addBodyPart(textBodyPart);
        String[] filePaths = {
                "/home/<USER>/Downloads/Profil_jenesuisPASunCV_M0R3.pdf",
                "/home/<USER>/Downloads/Profil_jenesuisPASunCV_Eric_Westrelin.pdf"
        };

        for (String filePath : filePaths) {
            var attachmentBodyPart = new MimeBodyPart();
            try (InputStream inputStream = new FileInputStream(filePath)) {
                attachmentBodyPart.setFileName(filePath.substring(filePath.lastIndexOf("/") + 1));
                attachmentBodyPart.setContent(inputStream.readAllBytes(), "application/pdf");
            }
            multipart.addBodyPart(attachmentBodyPart);
        }

        // Set the multipart content
        message.setContent(multipart);
    }

    @SneakyThrows
    public MimeMessage createEmail(String bcc, String from) throws MessagingException {
        var email = new MimeMessage(Session.getDefaultInstance(System.getProperties(), null));
        email.setFrom(new InternetAddress(from));
        email.addRecipient(Message.RecipientType.BCC, new InternetAddress(bcc));
        //email.addRecipient(Message.RecipientType.TO, new InternetAddress("*******"));
        email.addRecipient(Message.RecipientType.TO, new InternetAddress("*******"));
        email.setReplyTo(new Address[]{new InternetAddress("*******")});
        addMultiparts(email);
        return email;
    }

    public void sendMessage(Gmail service, String userId, MimeMessage email) throws MessagingException, IOException {
        var buffer = new ByteArrayOutputStream();
        email.writeTo(buffer);
        var rawMessageBytes = buffer.toByteArray();
        var encodedEmail = Base64.getUrlEncoder().encodeToString(rawMessageBytes);
        com.google.api.services.gmail.model.Message message = new com.google.api.services.gmail.model.Message();
        message.setRaw(encodedEmail);
        log.error(service.users().messages().send(userId, message).execute().toPrettyString());
    }

    static String USER_EMAIL = "*******";
    static String APPLICATION_NAME = "erhgo-smtp";
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();

    @SneakyThrows
    @Test
    @Disabled
    void testApiGoogle() {
        var service = getGmailService();
        var email = createEmail("*******", USER_EMAIL);
        sendMessage(service, USER_EMAIL, email);

    }

    @Test
    @Disabled
    void mail_recruitment() throws ApiException {
        var apiClient = new ApiClient();
        var apiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("api-key");
        apiKeyAuth.setApiKey("xxx");
        var txClient = new TransactionalEmailsApi(apiClient);
        var parameters = Map.of(
                "TIME", "39",
                "SALARY", "22000 - 30000",
                "CITY", "",
                "JOB", "Développeur",
                "RECRUITER", "APRIL",
                "NAME", "Amir",
                "CONTRACT", "CDI");
        txClient.sendTransacEmail(
                new SendSmtpEmail()
                        .templateId(1L)
                        .to(List.of(new SendSmtpEmailTo().email("*******")))
                        .params(parameters) // => params.WRONG_SIRET dans le template
        );
    }

    @SneakyThrows
    @Test
    @Disabled("Only used to test on internal services when using the method 'SendSmtpEmailTo' ")
    void mail_template_proposal_candidatures() {
        var apiClient = new ApiClient();
        var apiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("api-key");
        apiKeyAuth.setApiKey("xxx");
        var txClient = new TransactionalEmailsApi(apiClient);
        var classifications = List.of("");
        var cl = String.join(", ", classifications);
        var expectedParams = Map.of(
                "recruitment_code", "R-1",
                "occupation_id", "O-1",
                "organization", "Entreprise du S-42",
                "job_title", "A sourcing Job",
                "contract", "CDI",
                "time", "5h par semaine",
                "salary", "Non précisé",
                "location", "42000 Lyon",
                "erhgoClassifications", cl.isEmpty() ? "empty" : cl
        );
        txClient.sendTransacEmail(
                new SendSmtpEmail()
                        .templateId(2L)
                        .to(List.of(new SendSmtpEmailTo().email("*******")))
                        .params(expectedParams)
        );
    }


    @SneakyThrows
    @Test
    @Disabled
    void mail_template_recap_offres_add_blacklist_occupation() {
        var apiClient = new ApiClient();
        var apiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("api-key");
        apiKeyAuth.setApiKey("xxx");
        var txClient = new TransactionalEmailsApi(apiClient);
        var parameters = new DailyRecap("Michel", 4, "'La formation, la pédagogie, l’apprentissage' ; 'Le soin, l’attention aux autres, la santé'", List.of(
                new DailyRecapPerRecruiter("McDonalds", List.of(
                        new DailyRecapOffer("Technicien de maintenance", "Alternance", "Lyon", "69004", "https://app.erhgo.fr", "entre 15 000 € et 25 000 €", "O-1"),
                        new DailyRecapOffer("Exploitant agricole", "CDI", "Villeurbanne", "69004", "https://app.erhgo.fr", "45 000 €", "O-2"),
                        new DailyRecapOffer("Premier ministre", "CDD", "Pouilly-en-Auxois", "08800", "https://app.erhgo.fr", null, "O-3")
                )),
                new DailyRecapPerRecruiter("APEC", List.of(
                        new DailyRecapOffer("Conseiller clientèle", "CDD", "Paris", "75005", "https://app.erhgo.fr", "", "O-4")
                ))
        ));
        txClient.sendTransacEmail(
                new SendSmtpEmail()
                        .templateId(4L)
                        .to(List.of(new SendSmtpEmailTo().email("*******")))
                        .params(parameters)
        );
    }
}
