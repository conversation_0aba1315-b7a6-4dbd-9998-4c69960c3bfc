package com.erhgo.services.mailing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.enums.NotificationState;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.domain.userprofile.notification.SuspendedRecruitmentNotification;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;

import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;

class SendCandidatureSuspendedNotificationServiceTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    SendCandidatureSuspendedNotificationService service;

    @Autowired
    RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    NotificationRepository notificationRepository;

    @Autowired
    TransactionTestHelper txHelper;

    @MockBean
    MailingListService mailingListService;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    void should_notify_candidatures_on_suspended_recruitments_and_mark_as_notified() {
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withEmail("<EMAIL>").buildAndPersist();

        var suspendedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.UNPUBLISHED)
                .buildAndPersist();

        var candidatureToNotify = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(suspendedRecruitment)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withNotifiedOnClosedRecruitment(false)
                .buildAndPersist();

        Mockito.when(mailingListService.sendMailsToProfilesForTemplate(
                        Mockito.anySet(), Mockito.anyString(), Mockito.anyLong(), Mockito.anyMap(), Mockito.anyMap()))
                .thenReturn(CompletableFuture.completedFuture(java.util.Set.of(userProfile.userId())));

        service.sendSuspendedRecruitmentNotifications();

        txHelper.doInTransaction(() -> {
            var updatedCandidature = recruitmentCandidatureRepository.findById(candidatureToNotify.getId()).orElseThrow();
            assertThat(updatedCandidature.isNotifiedOnClosedRecruitment()).isTrue();

            var notifications = notificationRepository.findAll().stream()
                    .filter(n -> n instanceof SuspendedRecruitmentNotification)
                    .map(n -> (SuspendedRecruitmentNotification) n)
                    .toList();

            assertThat(notifications).hasSize(1);
            var notification = notifications.get(0);
            assertThat(notification.getUserProfile()).isEqualTo(userProfile);
            assertThat(notification.getRecruitment()).isEqualTo(suspendedRecruitment);
            assertThat(notification.getState()).isEqualTo(NotificationState.NEW);
            assertThat(notification.getType()).isEqualTo(NotificationType.EMAIL);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void should_not_notify_candidatures_already_marked_as_notified() {
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();

        var suspendedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.SELECTION)
                .buildAndPersist();

        var alreadyNotifiedCandidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(suspendedRecruitment)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withNotifiedOnClosedRecruitment(true)
                .buildAndPersist();

        service.sendSuspendedRecruitmentNotifications();

        txHelper.doInTransaction(() -> {
            var notifications = notificationRepository.findAll().stream()
                    .filter(n -> n instanceof SuspendedRecruitmentNotification)
                    .toList();

            assertThat(notifications).isEmpty();
        });

        Mockito.verifyNoInteractions(mailingListService);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void should_not_notify_candidatures_on_published_recruitments() {
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();

        var publishedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .buildAndPersist();

        var candidatureOnPublishedRecruitment = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(publishedRecruitment)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withNotifiedOnClosedRecruitment(false)
                .buildAndPersist();

        service.sendSuspendedRecruitmentNotifications();

        txHelper.doInTransaction(() -> {
            var notifications = notificationRepository.findAll().stream()
                    .filter(n -> n instanceof SuspendedRecruitmentNotification)
                    .toList();

            assertThat(notifications).isEmpty();
        });

        Mockito.verifyNoInteractions(mailingListService);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void should_not_notify_refused_candidatures() {
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).buildAndPersist();

        var suspendedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.UNPUBLISHED)
                .buildAndPersist();

        var refusedCandidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(suspendedRecruitment)
                .withState(GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS)
                .withNotifiedOnClosedRecruitment(false)
                .buildAndPersist();

        service.sendSuspendedRecruitmentNotifications();

        txHelper.doInTransaction(() -> {
            var notifications = notificationRepository.findAll().stream()
                    .filter(n -> n instanceof SuspendedRecruitmentNotification)
                    .toList();

            assertThat(notifications).isEmpty();
        });

        Mockito.verifyNoInteractions(mailingListService);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @SneakyThrows
    void should_handle_multiple_candidatures_and_mark_all_as_notified() {
        var userProfile1 = applicationContext.getBean(UserProfileMotherObject.class).withEmail("<EMAIL>").buildAndPersist();
        var userProfile2 = applicationContext.getBean(UserProfileMotherObject.class).withEmail("<EMAIL>").buildAndPersist();

        var suspendedRecruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.UNPUBLISHED)
                .buildAndPersist();

        var candidature1 = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile1)
                .withRecruitment(suspendedRecruitment)
                .withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO)
                .withNotifiedOnClosedRecruitment(false)
                .buildAndPersist();

        var candidature2 = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile2)
                .withRecruitment(suspendedRecruitment)
                .withState(GlobalCandidatureState.STAND_BY)
                .withNotifiedOnClosedRecruitment(false)
                .buildAndPersist();

        Mockito.when(mailingListService.sendMailsToProfilesForTemplate(
                        Mockito.anySet(), Mockito.anyString(), Mockito.anyLong(), Mockito.anyMap(), Mockito.anyMap()))
                .thenReturn(CompletableFuture.completedFuture(java.util.Set.of(userProfile1.userId(), userProfile2.userId())));

        service.sendSuspendedRecruitmentNotifications();

        txHelper.doInTransaction(() -> {
            var updatedCandidature1 = recruitmentCandidatureRepository.findById(candidature1.getId()).orElseThrow();
            var updatedCandidature2 = recruitmentCandidatureRepository.findById(candidature2.getId()).orElseThrow();

            assertThat(updatedCandidature1.isNotifiedOnClosedRecruitment()).isTrue();
            assertThat(updatedCandidature2.isNotifiedOnClosedRecruitment()).isTrue();

            var notifications = notificationRepository.findAll().stream()
                    .filter(n -> n instanceof SuspendedRecruitmentNotification)
                    .map(n -> (SuspendedRecruitmentNotification) n)
                    .toList();

            assertThat(notifications).hasSize(2);
        });
    }
}
