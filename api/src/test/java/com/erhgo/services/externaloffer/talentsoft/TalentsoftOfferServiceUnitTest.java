package com.erhgo.services.externaloffer.talentsoft;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

import static com.erhgo.TestUtils.assertJsonSerializedObjectMatchesContent;

class TalentsoftOfferServiceUnitTest {

    @Language("json")
    String json = """
            {
              "_pagination": {
                "start": 1,
                "count": 50,
                "total": 84,
                "resultsPerPage": 50,
                "hasMore": true,
                "links": [
                  {
                    "href": "https://testgroupama-cand.talent-soft.com/api/v2/offersummaries?apiCriteria.organisation=28&count=50&start=2",
                    "rel": "next"
                  },
                  {
                    "href": "https://testgroupama-cand.talent-soft.com/api/v2/offersummaries?apiCriteria.organisation=28&count=50&start=2",
                    "rel": "last"
                  }
                ],
                "lastPage": 2
              },
              "data": [
                {
                  "reference": "2024-57032",
                  "isTopOffer": false,
                  "title": "Conseiller Commercial - Agence Amplepuis (69) H/F",
                  "location": null,
                  "modificationDate": null,
                  "contractType": {
                    "code": 653,
                    "clientCode": "_TS_CO_Contract_CDI",
                    "label": "CDI",
                    "active": true,
                    "parentCode": 25,
                    "type": "contractType",
                    "parentType": "",
                    "hasChildren": false
                  },
                  "offerFamilyCategory": {
                    "code": 375,
                    "clientCode": "_TS_CDF15F94-76A7-41BD-BB29-9B688336229A",
                    "label": "Commercial-clientèle particuliers",
                    "active": true,
                    "parentCode": 332,
                    "type": "offerProfile",
                    "parentType": "",
                    "hasChildren": false
                  },
                  "organisationName": "Groupama Rhône Alpes Auvergne",
                  "organisationDescription": "Être ancré dans la réalité de nos clients-sociétaires et engagé auprès d'eux, quel que soit son métier, c'côtés !\\r<br /><br />",
                  "organisationLogoUrl": "https://testgroupama-cand.talent-soft.com/Handlers/Image.ashx?imagetype=logo&entityid=28",
                  "contractDuration": null,
                  "contractTypeCountry": {
                    "code": 25,
                    "clientCode": "_TS_CO_Country_France",
                    "label": "France",
                    "active": true,
                    "parentCode": 22,
                    "type": "country",
                    "parentType": "",
                    "hasChildren": false
                  },
                  "description1": "C'est à Amplepuis que nous vous proposons de devenir.",
                  "description2": "Animé(e) par le sens du service, vous êtes reconnu(e!",
                  "description1Formatted": "<p>C'est à Amplepuis que nous vous proposon",
                  "description2Formatted": "<p>Animé(e) par le sens du service, vous êt",
                  "geographicalLocation": [],
                  "country": [
                    {
                      "code": 25,
                      "clientCode": "_TS_CO_Country_France",
                      "label": "France",
                      "active": true,
                      "parentCode": 22,
                      "type": "offerCountry",
                      "parentType": "",
                      "hasChildren": false
                    }
                  ],
                  "region": [
                    {
                      "code": 5112,
                      "clientCode": "_TS_473a440e-1b9b-4d46-a60f-ce765a61316f",
                      "label": "Auvergne-Rhône-Alpes",
                      "active": true,
                      "parentCode": 25,
                      "type": "offerRegion",
                      "parentType": "",
                      "hasChildren": false
                    }
                  ],
                  "department": [
                    {
                      "code": 5177,
                      "clientCode": "_TS_22253ca7-c807-4b2c-a3ee-ca4e3ba560e7",
                      "label": "RHONE (69)",
                      "active": true,
                      "parentCode": 5112,
                      "type": "offerDepartment",
                      "parentType": "",
                      "hasChildren": false
                    }
                  ],
                  "latitude": null,
                  "longitude": null,
                  "professionalCategory": {
                    "code": 2666,
                    "clientCode": "_TS_949CA148-F4BD-4A49-9E96-1B120EDEB066",
                    "label": "Non cadre",
                    "active": true,
                    "parentCode": null,
                    "type": "professionalCategory",
                    "parentType": "",
                    "hasChildren": false
                  },
                  "_links": [
                    {
                      "href": "https://testgroupama-cand.talent-soft.com/api/v2/offers/getoffer?reference=2024-57032",
                      "rel": "detail"
                    }
                  ],
                  "offerUrl": "https://testgroupama-cand.talent-soft.com/Pages/Offre/detailoffre.aspx?idOffre=57032&idOrigine=&LCID=1036&offerReference=2024-57032",
                  "urlRedirectionEmployee": null,
                  "urlRedirectionApplicant": null,
                  "startPublicationDate": "2024-09-20T17:09:29.99",
                  "beginningDate": null
                },
                {
                  "reference": "2024-57021",
                  "isTopOffer": false,
                  "title": "Conseiller en Gestion de Patrimoine - Secteur ISERE (38) H/F",
                  "location": null,
                  "modificationDate": null,
                  "contractType": {
                    "code": 653,
                    "clientCode": "_TS_CO_Contract_CDI",
                    "label": "CDI",
                    "active": true,
                    "parentCode": 25,
                    "type": "contractType",
                    "parentType": "",
                    "hasChildren": false
                  },
                  "offerFamilyCategory": {
                    "code": 380,
                    "clientCode": "_TS_BD142830-20DF-4CEF-A475-E204A4C0BA40",
                    "label": "Conseil en gestion de patrimoine",
                    "active": true,
                    "parentCode": 332,
                    "type": "offerProfile",
                    "parentType": "",
                    "hasChildren": false
                  },
                  "organisationName": "Groupama Rhône Alpes Auvergne",
                  "organisationDescription": "Être ancré dans la réalité de nos clients-sociétaires et engagé auprès d'eux,\\r<br /><br />",
                  "organisationLogoUrl": "https://testgroupama-cand.talent-soft.com/Handlers/Image.ashx?imagetype=logo&entityid=28",
                  "contractDuration": null,
                  "contractTypeCountry": {
                    "code": 25,
                    "clientCode": "_TS_CO_Country_France",
                    "label": "France",
                    "active": true,
                    "parentCode": 22,
                    "type": "country",
                    "parentType": "",
                    "hasChildren": false
                  },
                  "description1": "Nous recherchons un Conseiller en Gestion de Patrimoine intervenant sur le secteur de l'Isère (38) :", 
                  "description2": "Vous justifiez d'une expérience réussie d'au moins 5 ans dans la vente de solutions d'épargne auprès.",
                  "description1Formatted": "<p>Nous recherchons un&nbsp;Conseiller en Gestion de Patrimoine intervenant&nbsp;sur&nbsp;le",
                  "description2Formatted": "<p>Vous justifiez d'une expérience réussie d'au moins 5 ans dans la vente de solutions d'épargne",
                  "geographicalLocation": [],
                  "country": [
                    {
                      "code": 25,
                      "clientCode": "_TS_CO_Country_France",
                      "label": "France",
                      "active": true,
                      "parentCode": 22,
                      "type": "offerCountry",
                      "parentType": "",
                      "hasChildren": false
                    }
                  ],
                  "region": [
                    {
                      "code": 5112,
                      "clientCode": "_TS_473a440e-1b9b-4d46-a60f-ce765a61316f",
                      "label": "Auvergne-Rhône-Alpes",
                      "active": true,
                      "parentCode": 25,
                      "type": "offerRegion",
                      "parentType": "",
                      "hasChildren": false
                    }
                  ],
                  "department": [
                    {
                      "code": 5173,
                      "clientCode": "_TS_0555c5f9-3840-4b12-aff7-88f0c6916ca6",
                      "label": "ISERE (38)",
                      "active": true,
                      "parentCode": 5112,
                      "type": "offerDepartment",
                      "parentType": "",
                      "hasChildren": false
                    }
                  ],
                  "latitude": null,
                  "longitude": null,
                  "professionalCategory": {
                    "code": 2664,
                    "clientCode": "_TS_654FC699-E6BB-42E7-8CDA-1C929E46CE6C",
                    "label": "Cadre",
                    "active": true,
                    "parentCode": null,
                    "type": "professionalCategory",
                    "parentType": "",
                    "hasChildren": false
                  },
                  "_links": [
                    {
                      "href": "https://testgroupama-cand.talent-soft.com/api/v2/offers/getoffer?reference=2024-57021",
                      "rel": "detail"
                    }
                  ],
                  "offerUrl": "https://testgroupama-cand.talent-soft.com/Pages/Offre/detailoffre.aspx?idOffre=57021&idOrigine=&LCID=1036&offerReference=2024-57021",
                  "urlRedirectionEmployee": null,
                  "urlRedirectionApplicant": null,
                  "startPublicationDate": "2024-09-19T16:44:38.953",
                  "beginningDate": null
                }
              ]
            }
            """;

    @SneakyThrows
    @Test
    void OfferDeserialization() {
        var jobJSONParser = new GenericJobJsonParser<>(TalentsoftJob.class, "data");
        var jobs = jobJSONParser.parseJobs(json, new AtsGetOfferConfig()).getContent();
        Assertions.assertThat(jobs).hasSize(2);
        assertJsonSerializedObjectMatchesContent(jobs, "talentsoftExternalOffers.json");
    }

}
