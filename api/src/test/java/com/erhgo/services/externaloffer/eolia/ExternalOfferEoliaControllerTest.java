package com.erhgo.services.externaloffer.eolia;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.externaloffer.RecruitmentCreationState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.openapi.dto.AtsOfferSimulatedDTO;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferRecruitmentService;
import com.erhgo.services.externaloffer.ExternalOfferScheduler;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.notification.RemoteOfferEvent;
import com.erhgo.services.externaloffer.notification.RemoteOfferMessageDTO;
import com.erhgo.services.notifier.Notifier;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
class ExternalOfferEoliaControllerTest extends AbstractIntegrationTest {

    public static final String DEFAULT_MODIFICATION_DATE_AS_STRING = "10/08/2010 14:00:01";
    public static final LocalDateTime DEFAULT_REMOTE_DATE_TIME = LocalDateTime.of(2020, 10, 10, 10, 10);
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    ExternalOfferRepository repository;
    @Autowired
    ExternalOfferScheduler externalOfferScheduler;

    @MockBean
    Notifier notifier;
    @MockBean
    GenericAtsClient atsClient;
    @MockBean
    //Unused but mocked to simplify tests
    ExternalOfferRecruitmentService externalOfferRecruitmentService;

    private static final String DATA_OPENING_TAG = "<data>";
    private static final String DATA_CLOSING_TAG = "</data>";
    private static final String UUID_GENERIC = "00000000-0000-0000-0000-000000000000";


    private static final String XML_HEADER = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";

    @Language("XML")
    private static final String OFFER_TEMPLATE = """
            <job>
                <id>42</id>
                <bu label="Business Unit" type="varchar(50)">MonEntreprise</bu>
                <nomclient label="Client" type="varchar(255)">MonClient</nomclient>
                <ref type="varchar(50)">rEf202105-%s</ref>
                <nomposte type="varchar(255)">offre %s - Adjoint responsable pôle chiffrage (H/F) - Etupes (25)</nomposte>
                <nbrecrut type="varchar(50)">1</nbrecrut>
                <datecreation label="Date de création" type="datetime">07/05/2021 12:31:46</datecreation>
                <datemodification label="Date de modification" type="datetime">%s</datemodification>
                <datedernierepublication label="Date de publication" type="datetime">23/06/2022 14:35:23</datedernierepublication>
                <saisie2 label="Ville (CP)" type="varchar(255)">Etupes (25460)</saisie2>
                <activite label="Type de contrat">CDI / CDD</activite>
            </job>""";


    @BeforeEach
    void prepare() {
        Mockito.when(atsClient.fetch(ArgumentMatchers.argThat(a -> a != null && a.getRemoteUrl().contains("digitalrecruiters")), ArgumentMatchers.eq(Optional.empty()))).thenReturn("""
                {"ads":  []}
                """);
        Mockito.when(atsClient.fetch(ArgumentMatchers.argThat(a -> a != null && a.getRemoteUrl().contains("keolis")), ArgumentMatchers.eq(Optional.empty()))).thenReturn("""
                """);
        Mockito.when(atsClient.fetch(ArgumentMatchers.argThat(a -> a != null && a.getRemoteUrl().contains("talentdetection")), ArgumentMatchers.eq(Optional.empty()))).thenReturn("""
                {"Results": []}""");
        Mockito.when(atsClient.fetch(ArgumentMatchers.argThat(a -> a != null && a.getRemoteUrl().contains("gestmax")), ArgumentMatchers.any())).thenReturn("""
                {"data": []}""");
        Mockito.when(atsClient.fetch(ArgumentMatchers.argThat(a -> a != null && a.getRemoteUrl().contains("inrecruiting")), ArgumentMatchers.any())).thenReturn("""
                []""");
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void simulate_ats_offer_no_modification() throws Exception {
        var id = "12";
        var allXml = XML_HEADER + DATA_OPENING_TAG + OFFER_TEMPLATE.formatted(id, id, DEFAULT_MODIFICATION_DATE_AS_STRING) + DATA_CLOSING_TAG;
        var xml = XML_HEADER + OFFER_TEMPLATE.formatted(id, id, DEFAULT_MODIFICATION_DATE_AS_STRING);
        applicationContext.getBean(ExternalOfferMotherObject.class)
                .withRemoteId("ReF202105-%s".formatted(id))
                .withRemoteModificationDate(DEFAULT_REMOTE_DATE_TIME)
                .withRawContent(xml)
                .withRecruiterCode("S-21663")
                .withLastEventType(ExternalOfferEventType.CREATED)
                .withATSCode("eolia")
                .buildAndPersist();
        var dto = new AtsOfferSimulatedDTO().rawFlow(allXml);

        performPost("/external-offer/simulate-ats-offer", dto)
                .andExpect(status().isNoContent());
        Mockito.verify(notifier).sendMessage(ArgumentMatchers.assertArg(a -> {
            Assertions.assertThat(a.getClass()).isAssignableFrom(RemoteOfferMessageDTO.class);
            Assertions.assertThat(a.getText()).isEqualTo("Aucune modification constatée - ATS eolia--MB");
        }));
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @NullSource
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void initialize_extracted_offer_data_init_data_from_xml(Boolean withRecruitment) throws Exception {
        @Language("XML")
        var completeRawXml = """
                <job>
                    <id>42</id>
                    <ref type="varchar(50)">reF202105-%s</ref>
                    <activite label="Type de contrat">CDI / CDD</activite>
                    <bu label="Business Unit">martinbelaysoud</bu>
                    <categorie label="Statut">Agent de maîtrise</categorie>
                    <nomposte type="varchar(255)">offre %s - Adjoint responsable pôle chiffrage (H/F) - Etupes (25)</nomposte>
                    <consultant label="RH">Alexa GOURDON</consultant>
                    <datecreation label="Date de création">02/02/2024 10:39:30</datecreation>
                    <datemodification label="Date de modification">02/03/2024 10:49:30</datemodification>
                    <datedernierepublication label="Date de publication">02/04/2024 10:39:30</datedernierepublication>
                    <daterecrut1 label="Date de démarrage du poste">02/05/2024 10:39:30</daterecrut1>
                    <direction label="Motif du recrutement">Modification</direction>
                    <liste1 label='Type de contrat'>CDI</liste1>
                    <listerecrut1 label="Métier">Commerce</listerecrut1>
                    <listerecrut2 label="Emploi">Adjoint responsable</listerecrut2>
                    <listerecrut3 label="Package de rémunération brut annuel">Entre 20 et 24k</listerecrut3>
                    <listerecrut4 label="Poste budgété">Donnée</listerecrut4>
                    <listerecrut5 label="Enseigne">Donnée</listerecrut5>
                    <listerecrut6 label="Travail le samedi">Non</listerecrut6>
                    <listerecrut7 label="Région du poste">Lyon</listerecrut7>
                    <memo2 label="Missions">missions</memo2>
                    <memo3 label="Profil">profil</memo3>
                    <memo4 label="Ce que nous proposons">Ce que nous proposons🔍</memo4>
                    <memo5 label="A propos de nous ">A propos de nous</memo5>
                    <nbrecrut>45</nbrecrut>
                    <nomclient label="Client">Martin Belaysoud</nomclient>
                    <pays label="Pays">France</pays>
                    <region label="Région">Auvergne Rhone-Alpes</region>
                    <saisie1 label="Intitulé du poste">Adjoint responsable</saisie1>
                    <saisie2 label="Ville (CP)">Lyon (69003)</saisie2>
                    <saisierecrut1 label="Nom de la personne à remplacer">Jean Dupont</saisierecrut1>
                    <saisierecrut4 label="Ville du poste">Lyon</saisierecrut4>
                    <version>fr-FR</version>
                </job>""";

        var id = "12";
        var xml = completeRawXml.formatted(id, id, DEFAULT_MODIFICATION_DATE_AS_STRING);
        var externalOffer = applicationContext.getBean(ExternalOfferMotherObject.class)
                .withRemoteId(id)
                .withRecruiterCode("S-21663")
                .withRecruitment(BooleanUtils.isNotFalse(withRecruitment) ? applicationContext.getBean(RecruitmentMotherObject.class).withId(Long.parseLong(id)).buildAndPersist() : null)
                .withRawContent(xml)
                .withATSCode("eolia")
                .withPreviousXmls(withRecruitment == null ? new String[]{xml, xml} : new String[]{})
                .buildAndPersist();

        var expectedResult = withRecruitment == null ? "externalOfferInitXmlDataWithRecruitmentAndPreviousXml" : (withRecruitment ? "externalOfferInitXmlDataWithRecruitment" : "externalOfferInitXmlData");

        performGetAndExpect(
                "/external-offer/initialize-extracted-offer-data?externalOfferId=%s".formatted(externalOffer.getUuid()),
                expectedResult,
                false)
                .andExpect(status().isOk());
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void simulate_ats_offer_check_offers_are_correctly_built(boolean viaSimulation) throws Exception {
        var busBefore = EventPublisherUtils.applicationEventPublisher;
        try {
            var busMock = Mockito.mock(ApplicationEventPublisher.class);
            EventPublisherUtils.resetPublisher(busMock);

            var createdRemoteId = "1";
            var modifiedRemoteId = "2";
            var republishedRemoteId = "3";
            var noActionRemoteId = "4";
            var republishedAndModifiedRemoteId = "5";
            var suspendedRemoteId = "6";
            var alreadySuspendedRemoteId = "7";
            var modifiedInexistant = "8";
            var canceledSuspendedRemoteId = "9";
            var canceledModifiedRemoteId = "10";
            var offerWithSameRemoteIdOnOtherConfigCode = "11";
            var suspendedOfferWithSameRemoteIdOnOtherConfigCode = "12";

            var modifiedJobModificationDateAsString = "10/08/2025 14:00:01";
            var defaultXml = XML_HEADER + "<job><activite label=\"Type de contrat\">CDI / CDD</activite><datemodification>%s</datemodification></job>%n".formatted(DEFAULT_MODIFICATION_DATE_AS_STRING);
            var otherXml = XML_HEADER + "<a3></a3>\n";

            var allXmls = Stream
                    .of(createdRemoteId, modifiedRemoteId, republishedRemoteId, noActionRemoteId, republishedAndModifiedRemoteId, modifiedInexistant, canceledModifiedRemoteId, offerWithSameRemoteIdOnOtherConfigCode, suspendedOfferWithSameRemoteIdOnOtherConfigCode)
                    .collect(Collectors.toMap(
                            Function.identity(),
                            id -> OFFER_TEMPLATE.formatted(id, id, (id.equals(modifiedRemoteId) || id.equals(republishedAndModifiedRemoteId) || id.equals(modifiedInexistant) || id.equals(canceledModifiedRemoteId)) ? modifiedJobModificationDateAsString : DEFAULT_MODIFICATION_DATE_AS_STRING))
                    );

            var duplicated = OFFER_TEMPLATE.formatted(createdRemoteId, createdRemoteId, DEFAULT_MODIFICATION_DATE_AS_STRING);
            var allXmlsWithDuplicate = new ArrayList<>(allXmls.values());
            allXmlsWithDuplicate.add(duplicated);
            var emulatedGlobalXml = allXmlsWithDuplicate.stream().collect(Collectors.joining("", XML_HEADER + DATA_OPENING_TAG, DATA_CLOSING_TAG));
            Stream.of(modifiedRemoteId, republishedRemoteId, noActionRemoteId, republishedAndModifiedRemoteId, suspendedRemoteId, alreadySuspendedRemoteId, modifiedInexistant, canceledSuspendedRemoteId, canceledModifiedRemoteId, offerWithSameRemoteIdOnOtherConfigCode, suspendedOfferWithSameRemoteIdOnOtherConfigCode)
                    .forEach(remoteId ->
                            {
                                var wasSuspended = Set.of(alreadySuspendedRemoteId, republishedRemoteId, republishedAndModifiedRemoteId, suspendedOfferWithSameRemoteIdOnOtherConfigCode).contains(remoteId);
                                var recruitment = remoteId.equals(modifiedInexistant) ? null :
                                        applicationContext.getBean(RecruitmentMotherObject.class).withJobTitle("Recrutement sur remoteId %s".formatted(remoteId)).withId(Long.parseLong(remoteId)).withState(wasSuspended ? RecruitmentState.SELECTION : RecruitmentState.PUBLISHED).buildAndPersist();
                                var offersWithSameRemoteIdOnOtherConfigCode = Set.of(suspendedOfferWithSameRemoteIdOnOtherConfigCode, offerWithSameRemoteIdOnOtherConfigCode);
                                applicationContext.getBean(ExternalOfferMotherObject.class)
                                        .withRemoteId("Ref202105-%s".formatted(remoteId))
                                        .withATSCode("eolia")
                                        .withRecruiterCode("S-21663")
                                        .withRemoteModificationDate(DEFAULT_REMOTE_DATE_TIME)
                                        .withPreviousXmls(remoteId.equals(modifiedRemoteId) ? new String[]{otherXml, otherXml, otherXml, otherXml, otherXml} : new String[]{})
                                        .withRawContent(defaultXml)
                                        .withRecruitment(recruitment)
                                        .withOfferTitle(recruitment == null ? "No recruitment" : offersWithSameRemoteIdOnOtherConfigCode.contains(remoteId) ? "DUPLICATED_TO_UPDATE" : recruitment.getJobTitle())
                                        .withRecruitmentCreationState((canceledSuspendedRemoteId.equals(remoteId) || canceledModifiedRemoteId.equals(remoteId)) ? RecruitmentCreationState.IGNORE : RecruitmentCreationState.DONE)
                                        .withLastEventType(wasSuspended ? ExternalOfferEventType.SUSPENDED : ExternalOfferEventType.CREATED)
                                        .withConfigCode(offersWithSameRemoteIdOnOtherConfigCode.contains(remoteId) ? "DUPLICATED_TO_UPDATE" : null)
                                        .buildAndPersist();
                            }
                    );

            if (viaSimulation) {
                var dto = new AtsOfferSimulatedDTO().rawFlow(emulatedGlobalXml);
                performPost("/external-offer/simulate-ats-offer", dto)
                        .andExpect(status().isNoContent());
            } else {
                Mockito.when(atsClient.fetch(ArgumentMatchers.argThat(a -> a.getRemoteUrl().contains("eolia") && a.getRemoteUrl().contains("martin")), ArgumentMatchers.any())).thenReturn(emulatedGlobalXml);
                externalOfferScheduler.analyzeDataForExternalOffersSecondPass();
            }

            txHelper.doInTransaction(() -> {
                var modifiedJobExpectedDate = LocalDateTime.parse(modifiedJobModificationDateAsString, DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"));
                var createdJobExpectedDate = LocalDateTime.parse(DEFAULT_MODIFICATION_DATE_AS_STRING, DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"));

                assertThat(findByRemoteId(createdRemoteId))
                        .matches(r -> matches(r, ExternalOfferEventType.CREATED, allXmls.get(createdRemoteId), createdJobExpectedDate));

                var actual = findByRemoteId(modifiedRemoteId);
                assertThat(actual)
                        .matches(r -> matches(r, ExternalOfferEventType.MODIFIED, allXmls.get(modifiedRemoteId), modifiedJobExpectedDate));

                assertThat(actual.getExternalOfferContentHistory())
                        .hasSize(5)
                        .first().satisfies(a -> {
                            Assertions.assertThat(a.getRawContent()).isEqualTo(defaultXml);
                        });
                assertThat(actual.getExternalOfferContentHistory().stream().toList().subList(1, 5))
                        .allMatch(a -> a.getRawContent().equals(otherXml))
                ;

                assertThat(findByRemoteId(republishedRemoteId))
                        .matches(r -> matches(r, ExternalOfferEventType.REPUBLISHED, defaultXml, DEFAULT_REMOTE_DATE_TIME)
                        );
                assertThat(findByRemoteId(noActionRemoteId))
                        .matches(r -> matches(r, ExternalOfferEventType.CREATED, defaultXml, DEFAULT_REMOTE_DATE_TIME)
                        );

                assertThat(findByRemoteId(republishedAndModifiedRemoteId))
                        .matches(r -> matches(r, ExternalOfferEventType.MODIFIED, allXmls.get(republishedAndModifiedRemoteId), modifiedJobExpectedDate)
                        );

                assertThat(findByRemoteId(suspendedRemoteId))
                        .matches(r -> matches(r, ExternalOfferEventType.SUSPENDED, defaultXml, DEFAULT_REMOTE_DATE_TIME)
                        );

                assertThat(findByRemoteId(canceledModifiedRemoteId))
                        .matches(r -> matches(r, ExternalOfferEventType.CREATED, defaultXml, DEFAULT_REMOTE_DATE_TIME)
                        );

                assertThat(findByRemoteId(canceledSuspendedRemoteId))
                        .matches(r -> matches(r, ExternalOfferEventType.CREATED, defaultXml, DEFAULT_REMOTE_DATE_TIME)
                        );

                txHelper.doInTransaction(() -> {
                    var offerWithSameRemoteIdOnDifferentConfigCodeShouldGetUpdated = findByRemoteId(offerWithSameRemoteIdOnOtherConfigCode);
                    assertThat(offerWithSameRemoteIdOnDifferentConfigCodeShouldGetUpdated.getConfigCode()).isEqualTo("MB");
                    assertThat(offerWithSameRemoteIdOnDifferentConfigCodeShouldGetUpdated.getOfferTitle()).contains("Adjoint responsable pôle chiffrage");
                    assertThat(offerWithSameRemoteIdOnDifferentConfigCodeShouldGetUpdated.getExternalOfferContentHistory()).hasSize(1);
                    var suspendedOfferWithSameRemoteIdOnDifferentConfigCodeShouldGetUpdated = findByRemoteId(suspendedOfferWithSameRemoteIdOnOtherConfigCode);
                    assertThat(suspendedOfferWithSameRemoteIdOnDifferentConfigCodeShouldGetUpdated.getConfigCode()).isEqualTo("MB");
                    assertThat(suspendedOfferWithSameRemoteIdOnDifferentConfigCodeShouldGetUpdated.getOfferTitle()).contains("Adjoint responsable pôle chiffrage");
                    assertThat(suspendedOfferWithSameRemoteIdOnDifferentConfigCodeShouldGetUpdated.getLastEventType()).isEqualTo(ExternalOfferEventType.CREATED);
                });

                var fail = new AtomicReference<RemoteOfferEvent>();
                Mockito.verify(busMock, Mockito.atLeast(1)).publishEvent((Object) Mockito
                        .assertArg(e -> {
                            if (e instanceof RemoteOfferEvent event && event.getAtsConfig().getConfigCode() != null && event.getAtsConfig().getConfigCode().equals("MB")) {
                                Assertions.assertThat(event.getSuspendedOffers()).extracting(a -> a.getRemoteId().split("-")[1]).containsExactlyInAnyOrder("6");
                                Assertions.assertThat(event.getReactivatedOffers()).extracting(a -> a.getRemoteId().split("-")[1]).containsExactlyInAnyOrder("5", "3");
                                Assertions.assertThat(event.getCreatedOffers()).extracting(a -> a.getRemoteId().split("-")[1]).containsExactlyInAnyOrder("1", "11", "12");
                                Assertions.assertThat(event.getModifiedOffers()).extracting(a -> a.getRemoteId().split("-")[1]).containsExactlyInAnyOrder("8", "5", "2");
                                fail.set(event);
                            }
                        }));

                busBefore.publishEvent(fail.get());
                var expectedNotificationTexts = new HashSet<>(Set.of("""
                        
                        ATS eolia--MB : 9 modifications constatées
                        - *3 offres nouvelles* :
                            -> <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| offre 11 - Adjoint responsable pôle chiffrage (H/F) - Etupes (25) à Etupes (25460)> (nom du client: MonClient)
                            -> <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| offre 12 - Adjoint responsable pôle chiffrage (H/F) - Etupes (25) à Etupes (25460)> (nom du client: MonClient)
                            -> <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| offre 1 - Adjoint responsable pôle chiffrage (H/F) - Etupes (25) à Etupes (25460)> (nom du client: MonClient)
                        - *1 offre suspendue* :
                            -> <https://sourcing.erhgo.fr/#/recruitment-detail/5| Recrutement sur remoteId 6 à <?>> (nom du client: <?>)
                        - *2 offres republiées* :
                            -> <https://sourcing.erhgo.fr/#/recruitment-detail/2| Recrutement sur remoteId 3 à <?>> (nom du client: <?>)
                            -> <https://sourcing.erhgo.fr/#/recruitment-detail/4| offre 5 - Adjoint responsable pôle chiffrage (H/F) - Etupes (25) à Etupes (25460)> (nom du client: MonClient)
                        - *3 offres modifiées* :
                            -> <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| offre 2 - Adjoint responsable pôle chiffrage (H/F) - Etupes (25) à Etupes (25460)> (nom du client: MonClient)
                            -> <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| offre 5 - Adjoint responsable pôle chiffrage (H/F) - Etupes (25) à Etupes (25460)> (nom du client: MonClient)
                            -> [:warning: Offre sans recrutement] <https://sourcing.erhgo.fr/#/edit-recruitment?externalOfferId=00000000-0000-0000-0000-000000000000| offre 8 - Adjoint responsable pôle chiffrage (H/F) - Etupes (25) à Etupes (25460)> (nom du client: MonClient)
                        """));
                Mockito.verify(notifier, Mockito.times(1)).sendMessage(ArgumentMatchers.assertArg(a ->
                {
                    assertThat(a).isOfAnyClassIn(RemoteOfferMessageDTO.class);
                    var expected = replaceUuidByGenericOne(a.getText());
                    if (!expectedNotificationTexts.isEmpty()) {
                        if (a.getText().contains("eolia--MB")) {
                            assertThat(expected).isEqualTo(expectedNotificationTexts.iterator().next());
                            expectedNotificationTexts.clear();
                        }
                    }

                }));
                Assertions.assertThat(expectedNotificationTexts).isEmpty();
            });
        } finally {
            EventPublisherUtils.resetPublisher(busBefore);
        }
    }

    @ParameterizedTest
    @ValueSource(ints = {-1, 0, 1})
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void simulate_ats_offer_publish_event(int delay) {
        var busBefore = EventPublisherUtils.applicationEventPublisher;
        try {
            var busMock = Mockito.mock(ApplicationEventPublisher.class);
            EventPublisherUtils.resetPublisher(busMock);
            txHelper.doInTransaction(() -> {
                var sql = """
                        INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES
                                ('ats.notification-delay-in-hours.%s', '%d')
                        ON DUPLICATE KEY UPDATE propertyValue = VALUES(propertyValue)
                        """.formatted(delay == -1 ? "default" : "eolia--MB", delay);

                applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
            });

            var id = "12";
            var xml = OFFER_TEMPLATE.formatted(id, id, DEFAULT_MODIFICATION_DATE_AS_STRING);

            Mockito.when(atsClient.fetch(ArgumentMatchers.any(), ArgumentMatchers.eq(Optional.empty()))).thenReturn(XML_HEADER + DATA_OPENING_TAG + xml + DATA_CLOSING_TAG);
            applicationContext.getBean(ExternalOfferServiceProvider.class).getService(new AtsGetOfferConfig().setAtsCode("eolia").setConfigCode("MB")).fetchAndUpdateOffers();
            Mockito.verify(busMock).publishEvent((Object) ArgumentMatchers.assertArg(a -> {
                Assertions.assertThat(a).isOfAnyClassIn(RemoteOfferEvent.class);
            }));
        } finally {
            EventPublisherUtils.resetPublisher(busBefore);

        }
    }


    private ExternalOffer findByRemoteId(String modifiedRemoteId) {
        return applicationContext.getBean(EntityManager.class).createQuery("SELECT a FROM ExternalOffer a WHERE remoteId = ?1", ExternalOffer.class).setParameter(1, "rEF202105-%s".formatted(modifiedRemoteId)).getSingleResult();
    }

    private String replaceUuidByGenericOne(String message) {
        var regex = "\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b";

        var pattern = Pattern.compile(regex);
        var matcher = pattern.matcher(message);

        return matcher.replaceAll(UUID_GENERIC);
    }

    private boolean matches(ExternalOffer offer, ExternalOfferEventType expectedEvent, String expectedXml, LocalDateTime expectedDate) {
        assertThat(offer.getLastEventType()).isEqualTo(expectedEvent);
        assertThat(offer.getLastRawContent()).endsWith(expectedXml);
        assertThat(offer.getRemoteLastModificationDate()).isEqualTo(expectedDate);
        return true;
    }
}
