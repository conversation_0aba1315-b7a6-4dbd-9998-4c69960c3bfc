package com.erhgo.services.externaloffer.eolia;

import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;

class EoliaJobMBFlavoredUnitTest {
    @Language("XML")

    static final String XML = """
            <job>
                <id>3939</id>
                <bu label="Business Unit" type="varchar(50)">d</bu>
                <nomclient label="Client" type="varchar(255)">z z</nomclient>
                <nomposte type="varchar(255)">ALT - Infographiste (H/F) - Lyon</nomposte>
                <ref type="varchar(50)">xxx-3939</ref>
                <nbrecrut type="varchar(50)">1</nbrecrut>
                <datecreation label="Date de création" type="datetime">10/04/2024 08:44:23</datecreation>
                <datemodification label="Date de modification" type="datetime">%s</datemodification>
                <datedernierepublication label="Date de publication" type="datetime">10/04/2024 08:45:11</datedernierepublication>
                <saisie1 label="Intitulé du poste" type="varchar(255)">Infographiste en alternance (H/F) </saisie1>
                <saisie2 label="Ville (CP)" type="varchar(255)">Lyon </saisie2>
                <memo2 label="Missions" type="longtext">
                <![CDATA[ <p style="text-align: left">yolo</p> ]]>
                </memo2>
                <liste1 label="Type de contrat" type="varchar(255)">ALTERNANCE</liste1>
                <activite label="Type de contrat" type="varchar(255)">ALTERNANCE</activite>
                <direction label="Motif du recrutement" type="varchar(255)">Remplacement</direction>
                <categorie label="Statut" type="varchar(255)">Employé</categorie>
                <pays label="Pays" type="varchar(50)">FRANCE</pays>
                <region label="Région" type="varchar(255)">Auvergne-Rhône-Alpes</region>
                <consultant label="RH" type="varchar(255)">a a</consultant>
                <consultant2 label="RH 2" type="varchar(255)">b b</consultant2>
                <daterecrut1 label="Date de démarrage du poste" type="datetime">02/09/2024 00:00:00</daterecrut1>
                <saisierecrut1 label="Nom de la personne à remplacer" type="varchar(255)">Mp ll mm</saisierecrut1>
                <saisierecrut4 label="Ville du poste" type="varchar(255)">Lyon</saisierecrut4>
                <listerecrut1 label="Métier" type="varchar(255)">Marketing / Communication</listerecrut1>
                <listerecrut2 label="Emploi" type="varchar(255)">Graphiste</listerecrut2>
                <version type="varchar(50)">fr-FR</version>
            </job>
            """;

    @Language("XML")
    static final String OTHER_XML = """
            <job>
                <id>3939</id>
                <bu label="Business Unit" type="varchar(50)">d</bu>
                <nomclient label="Client" type="varchar(255)">z z</nomclient>
                <nomposte type="varchar(255)">ALT - Infographiste (H/F) - Lyon</nomposte>
                <ref type="varchar(50)">xxx-3939</ref>
                <nbrecrut type="varchar(50)">1</nbrecrut>
                <datecreation label="Date de création" type="datetime">10/04/2024 08:44:23</datecreation>
                <datemodification label="Date de modification" type="datetime">%s</datemodification>
                <datedernierepublication label="Date de publication" type="datetime">10/04/2024 08:45:11</datedernierepublication>
                <saisie1 label="Intitulé du poste" type="varchar(255)">Infographiste en alternance (H/F) </saisie1>
                <saisie2 label="Ville (CP)" type="varchar(255)">Lyon </saisie2>
                <memo2 label="Missions" type="longtext">
                <![CDATA[ <p style="text-align: left">yolo</p> ]]>
                </memo2>
                <liste1 label="Type de contrat" type="varchar(255)">ALTERNANCE</liste1>
                <activite label="Type de contrat" type="varchar(255)">ALTERNANCE</activite>
                <direction label="Motif du recrutement" type="varchar(255)">Remplacement</direction>
                <categorie label="Statut" type="varchar(255)">Employé</categorie>
                <pays label="Pays" type="varchar(50)">FRANCE</pays>
                <region label="Région" type="varchar(255)">Auvergne-Rhône-Alpes</region>
                <consultant label="RH" type="varchar(255)">a a</consultant>
                <consultant2 label="RH 2" type="varchar(255)">b b</consultant2>
                <daterecrut1 label="Date de démarrage du poste" type="datetime">02/09/2024 00:00:00</daterecrut1>
                <saisierecrut1 label="Nom de la personne à remplacer" type="varchar(255)">Mp ll mm</saisierecrut1>
                <saisierecrut4 label="Ville du poste" type="varchar(255)">Lyon</saisierecrut4>
                <listerecrut1 label="Métier" type="varchar(255)">Marketing / Communication</listerecrut1>
                <listerecrut2 label="Emploi" type="varchar(255)">Graphiste</listerecrut2>
                <version type="varchar(50)">NOPE</version>
            </job>
            """;
    GenericJobXmlParser<EoliaJob> parser = new GenericJobXmlParser<>(EoliaJob.class, "/data/job", "EOLIA");

    @Test
    void notModified() {
        Assertions.assertThat(parser.parseJob(XML.formatted("10/04/2024 08:44:23")).doesModify(parser.parseJob(XML.formatted("10/04/2024 08:44:23")))).isFalse();
    }

    @Test
    void onlyDateIsModified_after() {
        Assertions.assertThat(parser.parseJob(XML.formatted("10/04/2024 08:44:23")).doesModify(parser.parseJob(XML.formatted("01/04/2014 08:44:23")))).isFalse();
    }

    @Test
    void onlyDateIsNotModified_before() {
        Assertions.assertThat(parser.parseJob(XML.formatted("10/04/2024 08:44:23")).doesModify(parser.parseJob(XML.formatted("01/04/2054 08:44:23")))).isFalse();
    }

    @Test
    void otherModif() {
        Assertions.assertThat(parser.parseJob(XML.formatted("10/04/2024 08:44:23")).doesModify(parser.parseJob(OTHER_XML.formatted("10/04/2014 08:44:23")))).isTrue();
    }

}
