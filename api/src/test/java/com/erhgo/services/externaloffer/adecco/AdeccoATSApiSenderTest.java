package com.erhgo.services.externaloffer.adecco;

import com.erhgo.openapi.client.adecco.ApiClient;
import com.erhgo.openapi.client.adecco.api.CandidateJobUnauthenticatedApi;
import com.erhgo.openapi.client.adecco.api.model.*;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.util.List;


/***
 * Liens utiles :
 * - <a href="https://www.adecco.fr/resultats-offres-emploi/c-cdd/d-rh%C3%B4ne?buname=adecco.fr%7cadeccopme.fr%7c&rss=1&employmenttype=ADCFREMP004">Le Flux Adecco</a>
 * - cf api/src/misc/adecco
 * - https://app.wiremock.cloud/mock-apis/ogyre/stubs/8481152a-cc35-47fd-866d-df28d095a11b
 * - Lien sourcing : <EMAIL>
 * - BO - Batch récupération des offres : com.erhgo.services.externaloffer.ExternalOfferScheduler analyzeDataForExternalOffers
 * - BO - Batch envoi des candidatures : com.erhgo.services.externaloffer.adecco.AdeccoATSApiNotificationScheduler handleNewCandidatures
 * - staging sourcing : <EMAIL>
 * - staging fo : <EMAIL>
 *  <br/>
 * Questions/confirmations :
 * - API_KEY QA/prod ?
 * - Traitement du retour : flag success & created, utile ou le status suffit ?
 * - spec : uniquement un exemple (cf adecco_api_original.yaml ligne 196, schéma objet requestJobApplyJobBoard) => cf. adecco.yaml
 */
@Disabled
class AdeccoATSApiSenderTest {

    static final String API_KEY = "898c74b3590d421697352b4b882ffee4";
    static final String REMOTE_ID = "15623215";
    static final String URL_MOCK = "https://erhgo.wiremockapi.cloud";
    static final String URL_QA = "https://api-uat.adecco.fr/candidate/jobs/v3.0";
    static final String URL_PROD = "https://api.adecco.fr/candidate/jobs/v3.0";

    @SneakyThrows
    @Test
    void test() {

        var apiClient = new ApiClient();
        var command = buildCommand();
        apiClient.updateBaseUri(URL_MOCK);
        var client = new CandidateJobUnauthenticatedApi(apiClient);

        var res = client.postcreateAccountandApplyForJobBoardWithHttpInfo(
                REMOTE_ID,
                command,
                API_KEY,
                "jnspcv_adecco"
        );
        Assertions.assertThat(res.getStatusCode()).isBetween(200, 299);
    }

    private static RequestJobApplyJobBoard buildCommand() throws IOException {
        return new RequestJobApplyJobBoard()
                .user(new User()
                        .email("<EMAIL>")
                        .firstname("EricC")
                        .lastname("DupontC")
                        // /!\ N° de tél FR  obligatoire /!\
                        .phone("**********")
                )
                .userProfile(new UserProfile()
                        .civility(/* hard coded as we don't (want to) know 1=male, 2=female*/ 1)
                        .address(
                                new Address()
                                        .city("Lyon")
                                        .country(1)
                                        .zipCode("69003")
                        )
                        .qualification(List.of(1495))
                        .contractType(List.of(11, 12))
                        .hasHandicap(false)
                        .hasAcceptedByEmail(false)
                        .hasAcceptedByPhone(false)
                        .consent(true)
                )
                .uploadedCV(new UploadedCV().fileName("profile_competence_Eric_Dupont.pdf")._file(Base64.getEncoder().encodeToString(Files.readAllBytes(Path.of("/home/<USER>/Downloads/profile-s.pdf")))))
                .sourceInfo(new SourceInfo().source("184"))
                .creationFlowStatus("");
    }
}
