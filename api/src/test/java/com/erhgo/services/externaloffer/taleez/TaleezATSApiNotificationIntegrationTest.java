package com.erhgo.services.externaloffer.taleez;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.externaloffer.AbstractATSApiNotificationIntegrationTest;
import lombok.SneakyThrows;
import okhttp3.Response;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class TaleezATSApiNotificationIntegrationTest extends AbstractATSApiNotificationIntegrationTest {

    public static final String TALEEZ_RECRUITER_CODE = "S-21690";
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    TaleezATSApiNotificationScheduler scheduler;
    @MockBean
    KeycloakMockService keycloakService;

    @SneakyThrows
    @ResetDataAfter
    @Test
    void handleNewCandidatures() {
        var capturedRequest = doSendCandidature(TALEEZ_RECRUITER_CODE, scheduler, "taleez");
        assertBodyContains(capturedRequest.body(), "taleezSendCandidatureCommand");
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(any(), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verifyNoMoreInteractions(userProfileCompetencesExportService);
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    void refuseCandidature() {
        var response = mock(Response.class);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(410);

        var capturedRequest = doSendCandidature(TALEEZ_RECRUITER_CODE, scheduler, "taleez", response);
        var candidature = applicationContext.getBean(RecruitmentCandidatureRepository.class).findById(getCandidature().getId()).orElseThrow();
        Assertions.assertThat(candidature.isRefused()).isTrue();
    }
}
