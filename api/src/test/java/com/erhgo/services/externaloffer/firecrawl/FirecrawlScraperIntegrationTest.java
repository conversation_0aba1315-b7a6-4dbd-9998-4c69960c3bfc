package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.ExternalOfferRecruitmentService;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class FirecrawlScraperIntegrationTest extends AbstractIntegrationTest {

    private static final String ATS_CODE = "FIRECRAWL";
    private static final String CONFIG_CODE = "STEF";
    private static final String RECRUITER_CODE = "S-21912";
    private static final String TEST_URL = "https://example.com/careers";
    private static final String OFFER_URL_MUST_CONTAIN = "job-invite";

    private static final List<String> JOB_URLS = List.of(
            "https://example.com/careers/job-invite/1",
            "https://example.com/careers/job-invite/2",
            "https://example.com/careers/job-invite/3",
            "https://example.com/careers/job-invite/4"
    );
    @MockBean
    ExternalOfferRecruitmentService externalOfferRecruitmentService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ExternalOfferRepository externalOfferRepository;
    @Autowired
    private ExternalOfferServiceProvider serviceProvider;
    @Autowired
    private GenericJobJsonParser<ScrappedJob> firecrawlJobParser;
    @MockBean
    private FirecrawlScraperService scraperService;
    private AtsGetOfferConfig config;

    @BeforeEach
    void setUp() {
        applicationContext.getBean(RecruiterMotherObject.class)
                .withCode(RECRUITER_CODE)
                .withOrganizationType(AbstractOrganization.OrganizationType.SOURCING)
                .buildAndPersist();

        config = new AtsGetOfferConfig()
                .setAtsCode(ATS_CODE)
                .setConfigCode(CONFIG_CODE)
                .setRecruiterCode(RECRUITER_CODE)
                .setOfferUrlMustContain(OFFER_URL_MUST_CONTAIN)
                .setRemoteUrl(TEST_URL);
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void shouldCreateOffersFromScrapedJobUrls() {
        when(scraperService.scrapeJobsAtUrl(anyString(), anyString())).thenReturn(JOB_URLS);

        initializeServiceAndFetchOffers();

        txHelper.doInTransaction(() -> {
            var offers = externalOfferRepository.findExternalOffersForConfig(ATS_CODE, RECRUITER_CODE, CONFIG_CODE);
            Assertions.assertThat(offers)
                    .hasSize(4)
                    .allMatch(o -> o.getLastEventType() == ExternalOfferEventType.CREATED)
                    .allMatch(o -> o.getAtsCode().equals(ATS_CODE))
                    .allMatch(o -> o.getConfigCode().equals(CONFIG_CODE))
                    .allMatch(o -> o.getComputedRecruiterCode().equals(RECRUITER_CODE))
                    .anyMatch(o -> o.getRemoteId().equals(JOB_URLS.getFirst()))
                    .anyMatch(o -> o.getRemoteId().equals(JOB_URLS.get(1)))
                    .anyMatch(o -> o.getRemoteId().equals(JOB_URLS.get(2)))
                    .anyMatch(o -> o.getRemoteId().equals(JOB_URLS.get(3)));
        });
    }

    @SneakyThrows
    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void shouldHandleEmptyScrapingResultGracefully() {
        when(scraperService.scrapeJobsAtUrl(anyString(), anyString())).thenReturn(List.of());

        initializeServiceAndFetchOffers();

        txHelper.doInTransaction(() -> {
            var offers = externalOfferRepository.findExternalOffersForConfig(ATS_CODE, RECRUITER_CODE, CONFIG_CODE);
            Assertions.assertThat(offers).isEmpty();
        });
    }

    private void initializeServiceAndFetchOffers() {
        serviceProvider.initializeCustomService(FirecrawlSynchronizer.class, config, firecrawlJobParser);
        serviceProvider.getService(config).fetchAndUpdateOffers();
    }
}
