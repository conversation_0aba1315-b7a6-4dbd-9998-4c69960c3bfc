package com.erhgo.services.externaloffer.eolia;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.mailing.MailNotifier;
import com.erhgo.services.mailing.SendEmailValidationCandidatureService;
import com.erhgo.services.sourcing.SourcingMailingService;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import jakarta.persistence.EntityManager;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Answers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;

import java.time.OffsetDateTime;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;

class EoliaATSMailNotificationIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    RecruitmentCandidatureRepository candidatureRepository;
    @MockBean
    SourcingMailingService sourcingMailingService;
    @MockBean
    SendEmailValidationCandidatureService sendEmailValidationCandidatureService;
    @MockBean
    KeycloakMockService keycloakService;
    @MockBean
    MailNotifier mailNotifier;
    @MockBean(answer = Answers.RETURNS_MOCKS)
    UserProfileCompetencesExportService userProfileCompetencesExportService;

    @Value("${ats.eolia.fetch[0].recruiterCode}")
    private String recruiterCode;

    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    void ensure_event_is_not_handled_non_ats(boolean forAts) {
        var stateForId = Stream.of(CandidatureSynchronizationState.values())
                .map(s -> {
                    var userProfileMotherObject = applicationContext.getBean(UserProfileMotherObject.class);
                    // Ignore user without xp (we can't generate profile competences)
                    if (s != CandidatureSynchronizationState.WAITING) {
                        userProfileMotherObject.withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist());
                    }
                    return applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                            .withRecruiterCode(recruiterCode)
                            .withATSCode(forAts ? "eolia" : null)
                            .withState(GlobalCandidatureState.NEW)
                            .withSubmissionDate(OffsetDateTime.now().minusMinutes(10))
                            .withUserProfile(userProfileMotherObject.withEmail("email").buildAndPersist())
                            .withCandidatureSynchronizationState(s)
                            .buildAndPersist();
                })
                .collect(Collectors.toMap(AbstractCandidature::getId, RecruitmentCandidature::getSynchronizationState));


        applicationContext.getBean(EoliaATSMailNotificationScheduler.class).handleNewCandidatures();

        assertThat(candidatureRepository.findAll())
                .allMatch(c -> stateForId.get(c.getId()) == c.getSynchronizationState());

        Mockito.verifyNoInteractions(mailNotifier);
    }

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @Test
    void ensure_service_is_disabled() {
        txHelper.doInTransaction(() -> {
            var sql = """
                    INSERT INTO ConfigurableProperty(propertyKey, propertyValue)
                    VALUES ('ats.disable-candidature-notification.eolia', 'true');
                    """;
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
        var userId = "42";
        String email = "a@a";
        persistCandidature(userId, OffsetDateTime.now().minusMinutes(10), email, true, false, false);
        applicationContext.getBean(EoliaATSMailNotificationScheduler.class).handleNewCandidatures();
        Mockito.verifyNoInteractions(mailNotifier);
    }

    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @Test
    void ensure_event_is_handled() {
        var userId = "42";
        String email = "a@a";
        var candidature = persistCandidature(userId, OffsetDateTime.now().minusMinutes(100), email, true, false, false);
        persistCandidature("noxp", OffsetDateTime.now().minusMinutes(100), email, false, false, false);
        var archived = persistCandidature("arch", OffsetDateTime.now().minusMinutes(100), email, true, true, false);
        var refused = persistCandidature("ref", OffsetDateTime.now().minusMinutes(100), email, true, false, true);
        var erroneousCandidature = persistCandidature("Bof", OffsetDateTime.now().minusMinutes(100), email, true, false, false);
        var erroneousCandidature2 = persistCandidature("Bof2", OffsetDateTime.now().minusMinutes(200), email, true, false, false);
        var tooSoonCandidature = persistCandidature("Nope either", OffsetDateTime.now().plusMinutes(10), "b@b", true, false, false);

        Mockito.doThrow(GenericTechnicalException.class).when(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(erroneousCandidature.getId()), anyString(), eq(ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS));
        Mockito.doThrow(GenericTechnicalException.class).when(mailNotifier).sendMail(anyCollection(), anyString(), anyString(), anyBoolean(), argThat(t -> t.contains(erroneousCandidature2.getJobTitle())), anyString(), any(FilePartProvider.class), any(FilePartProvider.class));

        applicationContext.getBean(EoliaATSMailNotificationScheduler.class).handleNewCandidatures();

        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(candidature.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(candidature.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS));
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(erroneousCandidature2.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verify(userProfileCompetencesExportService).getProfileCompetenceForBatch(eq(erroneousCandidature2.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS));
        verify(userProfileCompetencesExportService, Mockito.never()).getProfileCompetenceForBatch(eq(erroneousCandidature.getId()), Mockito.any(), eq(ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE));
        verify(mailNotifier, Mockito.times(2)).sendMail(eq(Set.of("<EMAIL>")), eq(email), anyString(), anyBoolean(), Mockito.contains("offer for eolia"), Mockito.contains("""
                anonymouscode : AAA a@a
                email : a@a
                nom : l
                prenom : f
                ville : Pau
                cp : 42000
                portable : 0123
                saisielib :\s
                origine : jenesuisPASunCV
                datesoumission :\s""".replace("\n", "\r\n")), Mockito.any(FilePartProvider.class), Mockito.any(FilePartProvider.class));
        var candidatureOK = candidatureRepository.findById(candidature.getId()).orElseThrow();
        assertThat(candidatureOK.getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.DONE);
        assertThat(candidatureOK.getRemoteNotifiedIdentifier()).isEqualTo("<EMAIL>");
        assertThat(candidatureRepository.findById(tooSoonCandidature.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.WAITING);
        assertThat(candidatureRepository.findById(erroneousCandidature.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.ERROR);
        assertThat(candidatureRepository.findById(erroneousCandidature2.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.ERROR);
        assertThat(candidatureRepository.findById(archived.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.IGNORE);
        assertThat(candidatureRepository.findById(refused.getId()).orElseThrow().getSynchronizationState()).isEqualTo(CandidatureSynchronizationState.IGNORE);
    }

    private RecruitmentCandidature persistCandidature(String userId, OffsetDateTime submissionDate, String email, boolean withXP, boolean archived, boolean refused) {
        String ln = "l", fn = "f";
        var userProfileMotherObject = applicationContext.getBean(UserProfileMotherObject.class);
        if (withXP) {
            userProfileMotherObject
                    .withExperienceOnOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist());
        }
        return applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(
                        userProfileMotherObject
                                .withUserId(userId)
                                .withEmail(email)
                                .withFirstname(fn)
                                .withLastname(ln)
                                .withPhoneNumber("0123")
                                .withLocation(Location.builder().city("Pau").postcode("42000").build())
                                .buildAndPersist()
                )
                .withIsArchived(archived)
                .withRefusalData(refused ? CandidatureEmailRefusalState.NONE : null, "me")
                .withRecruiterCode(recruiterCode)
                .withATSCode("eolia")
                .withState(GlobalCandidatureState.NEW)
                .withCandidatureSynchronizationState(CandidatureSynchronizationState.WAITING)
                .withSubmissionDate(submissionDate)
                .withAnonymousCode("AAA %s".formatted(email))
                .buildAndPersist();
    }

}
