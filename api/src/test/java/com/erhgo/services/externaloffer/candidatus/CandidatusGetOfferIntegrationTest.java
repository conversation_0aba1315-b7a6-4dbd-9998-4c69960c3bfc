package com.erhgo.services.externaloffer.candidatus;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.externaloffer.ExternalOfferEventType;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.externaloffer.GenericAtsClient;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import org.assertj.core.api.Assertions;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Optional;


class CandidatusGetOfferIntegrationTest extends AbstractIntegrationTest {
    public static final String ATS_CODE = "CANDIDATUS";
    public static final String CONFIG_CODE = "ARTELOGE";

    @MockBean
    GenericAtsClient atsClient;

    @Language("XML")
    private static final String OFFERS = """
             <CandidatusWebAPIResponse>
              <JobsList>
                <Job>
                    <JobId><![CDATA[12345]]></JobId>
                    <JobDate><![CDATA[20241027]]></JobDate>
                    <JobName><![CDATA[Technicien de Maintenance]]></JobName>
                    <JobItem1Id><![CDATA[1]]></JobItem1Id>
                    <JobItem1Name><![CDATA[ERHGO]]></JobItem1Name>
                    <JobItem2Id><![CDATA[2]]></JobItem2Id>
                    <JobItem2Name><![CDATA[CDI]]></JobItem2Name>
                    <JobItem3Id><![CDATA[3]]></JobItem3Id>
                    <JobItem3Name><![CDATA[Temps Plein]]></JobItem3Name>
                    <JobItem4Id><![CDATA[4]]></JobItem4Id>
                    <JobItem4Name><![CDATA[Confirmé]]></JobItem4Name>
                    <JobDescription><![CDATA[Description du poste de Technicien de Maintenance.]]></JobDescription>
                    <JobURL><![CDATA[https://example.com/offre/12345]]></JobURL>
                    <JobEmail><![CDATA[<EMAIL>]]></JobEmail>
                    <JobReference><![CDATA[REF-TECH-MAINT]]></JobReference>
                    <JobLocationPostalCode><![CDATA[75001]]></JobLocationPostalCode>
                    <JobLocationCity><![CDATA[Paris]]></JobLocationCity>
                    <JobGeneric>
                        <organisme><![CDATA[ARTELOGE]]></organisme>
                        <contrat><![CDATA[CDI]]></contrat>
                        <type_emploi><![CDATA[Temps Plein]]></type_emploi>
                        <duree_cdd><![CDATA[]]></duree_cdd>
                        <experience><![CDATA[5 ans]]></experience>
                        <qualification><![CDATA[BAC+2]]></qualification>
                    </JobGeneric>
                </Job>
                <Job>
                    <JobId><![CDATA[67890]]></JobId>
                    <JobDate><![CDATA[20241026]]></JobDate>
                    <JobName><![CDATA[Consultant Junior]]></JobName>
                    <JobItem1Id><![CDATA[5]]></JobItem1Id>
                    <JobItem1Name><![CDATA[ACME Corp]]></JobItem1Name>
                    <JobItem2Id><![CDATA[6]]></JobItem2Id>
                    <JobItem2Name><![CDATA[CDD]]></JobItem2Name>
                    <JobItem3Id><![CDATA[7]]></JobItem3Id>
                    <JobItem3Name><![CDATA[Temps Plein]]></JobItem3Name>
                    <Job4Id><![CDATA[8]]></Job4Id>
                    <Job4Name><![CDATA[Débutant]]></Job4Name>
                    <JobDescription><![CDATA[Description du poste de Consultant Junior.]]></JobDescription>
                    <JobURL><![CDATA[https://example.com/offre/67890]]></JobURL>
                    <JobEmail><![CDATA[<EMAIL>]]></JobEmail>
                    <JobReference><![CDATA[REF-CONSULT-JR]]></JobReference>
                    <JobLocationPostalCode><![CDATA[13001]]></JobLocationPostalCode>
                    <JobLocationCity><![CDATA[Marseille]]></JobLocationCity>
                    <JobGeneric>
                        <organisme><![CDATA[ARTELOGE]]></organisme>
                        <contrat><![CDATA[CDD]]></contrat>
                        <type_emploi><![CDATA[Temps Plein]]></type_emploi>
                        <duree_cdd><![CDATA[12 mois]]></duree_cdd>
                        <experience><![CDATA[0-2 ans]]></experience>
                        <qualification><![CDATA[BAC+5]]></qualification>
                    </JobGeneric>
                </Job>
              </JobsList>
            </CandidatusWebAPIResponse>
            """;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void created_events_are_fired_for_candidatus_offers() {
        Mockito.when(atsClient.fetch(ArgumentMatchers.any(), ArgumentMatchers.eq(Optional.empty()))).thenReturn(OFFERS);

        applicationContext.getBean(ExternalOfferServiceProvider.class).getService(new AtsGetOfferConfig().setAtsCode(ATS_CODE).setConfigCode(CONFIG_CODE)).fetchAndUpdateOffers();

        txHelper.doInTransaction(() -> {
            var allOffers = applicationContext.getBean(ExternalOfferRepository.class).findAll();
            Assertions.assertThat(allOffers)
                    .hasSize(2)
                    .allMatch(a -> a.getLastEventType() == ExternalOfferEventType.CREATED)
                    .anyMatch(a -> a.getRemoteId().equals("12345"))
                    .anyMatch(a -> a.getRemoteId().equals("67890"))
                    .allMatch(a -> a.getAtsCode().equals(ATS_CODE))
                    .allMatch(a -> a.getConfigCode().equals(CONFIG_CODE));
        });
    }
}
