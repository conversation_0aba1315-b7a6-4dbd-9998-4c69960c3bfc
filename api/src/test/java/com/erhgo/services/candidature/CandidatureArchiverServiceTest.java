package com.erhgo.services.candidature;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import java.time.OffsetDateTime;

import static org.assertj.core.api.Assertions.assertThat;


@EnableRetry
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@TestPropertySource(properties = {"sendinblue.apiKey=42", "sendinblue.retry-delay=50"})
class CandidatureArchiverServiceTest extends AbstractIntegrationTest {

    @Autowired
    CandidatureArchiverService service;
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    TransactionTestHelper txHelper;
    @Autowired
    AbstractCandidatureRepository repository;

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void archive_candidatures_refused_more_than_3_months_ago() {

        var refusedCandidatureLessThanThreeMonth = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withIsArchived(false)
                .withRefusalData(CandidatureEmailRefusalState.NONE, "Alfred")
                .withRefusalDate(1)
                .buildAndPersist();

        var refusedCandidatureMoreThanThreeMonth = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withIsArchived(false)
                .withRefusalData(CandidatureEmailRefusalState.NONE, "Alfred")
                .withRefusalDate(4)
                .buildAndPersist();

        var candidatureAlreadyArchived = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withIsArchived(true)
                .withRefusalData(CandidatureEmailRefusalState.NONE, "Alfred")
                .withRefusalDate(4)
                .buildAndPersist();

        var candidatureNotRefused = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withIsArchived(false)
                .withState(GlobalCandidatureState.INTERNAL_POSITION)
                .withLastActionDate(OffsetDateTime.now().minusMonths(5))
                .buildAndPersist();

        service.archiveCandidatures();

        txHelper.doInTransaction(() -> {
            var refusedCandidatureNotArchived = repository.findById(refusedCandidatureLessThanThreeMonth.getId()).orElseThrow();
            var refusedCandidatureArchived = repository.findById(refusedCandidatureMoreThanThreeMonth.getId()).orElseThrow();
            var candidatureNotArchived = repository.findById(candidatureNotRefused.getId()).orElseThrow();
            assertThat(repository.findById(candidatureAlreadyArchived.getId()).orElseThrow().getUpdatedDate()).isEqualTo(candidatureAlreadyArchived.getUpdatedDate());

            assertThat(refusedCandidatureNotArchived.isArchived()).isFalse();
            assertThat(refusedCandidatureArchived.isArchived()).isTrue();
            assertThat(candidatureNotArchived.isArchived()).isFalse();

        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void archive_candidatures_treated_more_than_12_months_ago() {
        var candidaturesToArchive = GlobalCandidatureState.treated().stream().map(s -> (
                applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                        .withState(s)
                        .withLastActionDate(OffsetDateTime.now().minusMonths(13))
                        .buildAndPersist()
        )).toList();

        var candidaturesNotToArchive = GlobalCandidatureState.treated().stream().map(s -> (
                applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                        .withState(s)
                        .withLastActionDate(OffsetDateTime.now().minusMonths(11))
                        .buildAndPersist()
        )).toList();
        service.archiveCandidatures();

        txHelper.doInTransaction(() -> {
            var expectedRefused = candidaturesToArchive.stream().map(c -> repository.findById(c.getId()).orElseThrow()).toList();
            var expectedNotRefused = candidaturesNotToArchive.stream().map(c -> repository.findById(c.getId()).orElseThrow()).toList();

            assertThat(expectedRefused)
                    .hasSize(candidaturesToArchive.size())
                    .isNotEmpty()
                    .allMatch(AbstractCandidature::isArchived);

            assertThat(expectedNotRefused)
                    .hasSize(candidaturesNotToArchive.size())
                    .isNotEmpty()
                    .noneMatch(AbstractCandidature::isArchived);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void archive_candidatures_untreated_more_than_6_months_ago() {
        var candidaturesToArchive = GlobalCandidatureState.untreated().stream().map(s -> (
                applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                        .withState(s)
                        .withLastActionDate(OffsetDateTime.now().minusMonths(7))
                        .buildAndPersist()
        )).toList();

        var candidaturesNotToArchive = GlobalCandidatureState.untreated().stream().map(s -> (
                applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                        .withState(s)
                        .withLastActionDate(OffsetDateTime.now().minusMonths(5))
                        .buildAndPersist()
        )).toList();
        service.archiveCandidatures();

        txHelper.doInTransaction(() -> {
            var expectedRefused = candidaturesToArchive.stream().map(c -> repository.findById(c.getId()).orElseThrow()).toList();
            var expectedNotRefused = candidaturesNotToArchive.stream().map(c -> repository.findById(c.getId()).orElseThrow()).toList();

            assertThat(expectedRefused)
                    .hasSize(candidaturesToArchive.size())
                    .isNotEmpty()
                    .allMatch(AbstractCandidature::isArchived);

            assertThat(expectedNotRefused)
                    .hasSize(candidaturesNotToArchive.size())
                    .isNotEmpty()
                    .noneMatch(AbstractCandidature::isArchived);
        });
    }
}
