package com.erhgo.services.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.services.mailing.RecruitmentNotificationGenerator;
import com.erhgo.utils.DateTimeUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.when;

class SourcingResendNotificationServiceTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    SourcingJobRecruitmentService sourcingService;

    @Autowired
    RecruitmentRepository recruitmentRepository;
    @Autowired
    SourcingScheduler sourcingScheduler;

    @MockBean
    RecruitmentNotificationGenerator recruitmentNotificationGenerator;

    @Test
    void notificationsAreSent() {
        var publicationInstant = Instant.now().minus(7, ChronoUnit.DAYS);
        var publicationDate = DateTimeUtils.localDateToDate(publicationInstant.atZone(DateTimeUtils.ZONE_ID).toLocalDateTime());
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withPublicationDate(publicationDate)
                .withState(RecruitmentState.PUBLISHED)
                .withCreatedDate(publicationDate)
                .buildAndPersist();

        when(recruitmentNotificationGenerator.generateNotifications(anySet(), anyLong())).thenReturn(CompletableFuture.completedFuture(null));

        sourcingScheduler.sendRecruitmentReminderNotifications();

        Mockito.verify(recruitmentNotificationGenerator).generateNotifications(anySet(), Mockito.eq(recruitment.getId()));
    }

}
