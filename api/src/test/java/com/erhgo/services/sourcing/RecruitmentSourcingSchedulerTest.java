package com.erhgo.services.sourcing;


import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.openapi.dto.InviteToRecruitmentCommandDTO;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.RecruitmentNotificationGenerator;
import com.erhgo.utils.DateTimeUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class RecruitmentSourcingSchedulerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    SourcingScheduler sourcingScheduler;

    @MockBean
    RecruitmentNotificationGenerator sendCandidatureProposalService;

    @MockBean
    SourcingService sourcingService;


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void sendRecruitmentReminderNotificationsTest() {

        var recruitment1 = createRecruitment(7);
        var recruitment2 = createRecruitment(14);
        var recruitment3 = createRecruitment(3);
        var recruitment4 = createRecruitment(0);
        var recruitment5 = createRecruitment(1);
        var recruitment6 = createRecruitment(27);

        when(sendCandidatureProposalService.generateNotifications(anySet(), anyLong())).thenReturn(CompletableFuture.completedFuture(null));
        when(sourcingService.generateRecruitmentNotifications(any(InviteToRecruitmentCommandDTO.class))).thenReturn(CompletableFuture.completedFuture(null));

        sourcingScheduler.sendRecruitmentReminderNotifications();

        verify(sourcingService, times(1)).generateRecruitmentNotifications(argThat(arg -> arg.getRecruitmentId().equals(recruitment1.getId())));
        verify(sourcingService, times(1)).generateRecruitmentNotifications(argThat(arg -> arg.getRecruitmentId().equals(recruitment2.getId())));
        verify(sourcingService, never()).generateRecruitmentNotifications(argThat(arg -> arg.getRecruitmentId().equals(recruitment4.getId())));


        verify(sourcingService, never()).generateRecruitmentNotifications(argThat(arg -> arg.getRecruitmentId().equals(recruitment3.getId())));
        verify(sourcingService, never()).generateRecruitmentNotifications(argThat(arg -> arg.getRecruitmentId().equals(recruitment5.getId())));
        verify(sourcingService, never()).generateRecruitmentNotifications(argThat(arg -> arg.getRecruitmentId().equals(recruitment6.getId())));

    }

    private Recruitment createRecruitment(int daysAgo) {
        var publicationInstant = Instant.now().minus(daysAgo, ChronoUnit.DAYS);
        var publicationDate = DateTimeUtils.localDateToDate(publicationInstant.atZone(DateTimeUtils.ZONE_ID).toLocalDateTime());

        return applicationContext.getBean(RecruitmentMotherObject.class)
                .withPublicationDate(publicationDate)
                .withState(RecruitmentState.PUBLISHED)
                .withCreatedDate(publicationDate)
                .buildAndPersist();
    }

}
