package com.erhgo.services.reminder;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class EmailToNotifyAllowAllFilterTest {

    @Test
    void filterAllowAllShouldReturnTrueForGmail() {
        Assertions.assertThat(new EmailToNotifyAllowAllFilter().emailAccepted("<EMAIL>")).isTrue();
    }

    @Test
    void filterAllowAllShouldReturnTrueForEmpty() {
        Assertions.assertThat(new EmailToNotifyAllowAllFilter().emailAccepted(null)).isTrue();
    }

    @Test
    void filterAllowAllShouldReturnTrueForErhgo() {
        Assertions.assertThat(new EmailToNotifyAllowAllFilter().emailAccepted("<EMAIL>")).isTrue();
    }

    @Test
    void filterAllowAllShouldReturnTrueForLV() {
        Assertions.assertThat(new EmailToNotifyAllowAllFilter().emailAccepted("<EMAIL>")).isTrue();
    }

    @Test
    void filterAllowAllShouldReturnTrueForInvalid() {
        Assertions.assertThat(new EmailToNotifyAllowAllFilter().emailAccepted("jean-paul")).isTrue();
    }


}
