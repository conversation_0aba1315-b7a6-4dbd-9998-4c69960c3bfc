package com.erhgo.repositories.dummy;

import com.erhgo.controller.secured.AbstractController;
import com.erhgo.domain.dummy.DummyEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(DummyController.API_ABSTRACT_DUMMY_TEST)
@RequiredArgsConstructor
@ActiveProfiles("test")
public class DummyController extends AbstractController<DummyEntity, DummyService> {

    public static final String API_ABSTRACT_DUMMY_TEST = "/api/odas/dummy/test";

}
