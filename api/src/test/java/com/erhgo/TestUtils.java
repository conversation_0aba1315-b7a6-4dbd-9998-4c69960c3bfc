package com.erhgo;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.google.common.base.Charsets;
import com.google.common.io.CharStreams;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.text.PDFTextStripper;
import org.assertj.core.api.Assertions;
import org.hamcrest.CustomTypeSafeMatcher;
import org.hamcrest.Matcher;
import org.json.JSONException;
import org.junit.Assert;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtClaimNames;
import org.springframework.security.test.context.TestSecurityContextHolder;
import org.springframework.test.web.servlet.ResultMatcher;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Slf4j
public class TestUtils {

    // Set to true to generate test files without failing - useful when multiple tests files have to be generated at once
    private static final boolean GENERATES_NO_FAIL = false;

    public static Matcher<String> stringThatMatchesDate(OffsetDateTime date, ObjectMapper objectMapper) {
        Matcher<String> matcher = new CustomTypeSafeMatcher<>("Date does not match") {
            @Override
            protected boolean matchesSafely(String item) {
                try {
                    return objectMapper.readValue("\"" + item + "\"", OffsetDateTime.class).toEpochSecond() == date.toEpochSecond();
                } catch (IOException e) {
                    log.info("Error while parsing", e);
                    return false;
                }
            }
        };

        return matcher;
    }

    public static ResultMatcher jsonMatchesContent(String expectedContentFile) {
        return jsonMatchesContentWithCompareMode(expectedContentFile, JSONCompareMode.LENIENT);
    }

    public static ResultMatcher stringMatchesContent(String expectedContentFile) {
        return result -> {
            var actual = result.getResponse().getContentAsString();
            Assertions.assertThat(actual).isEqualTo(getFileContentAsStringOrWriteIfMissing(expectedContentFile, actual));
        };
    }

    public static ResultMatcher jsonMatchesContentWithOrderedArray(String expectedContentFile) {
        return jsonMatchesContentWithCompareMode(expectedContentFile, JSONCompareMode.STRICT_ORDER);
    }

    public static ResultMatcher jsonMatchesContentWithCompareMode(String expectedContentFile, JSONCompareMode compareMode) {
        return result -> {
            var actualContent = result.getResponse().getContentAsString();
            assertFileContentMatch(expectedContentFile, actualContent, compareMode);
        };
    }

    public static void assertFileContentMatch(String expectedContentFile, String actualContent, JSONCompareMode compareMode, String... params) throws JSONException {
        var expectedContent = getFileContentAsStringOrWriteIfMissing(expectedContentFile, actualContent);
        if (params.length > 0) {
            expectedContent = expectedContent.replace(params[0], params[1]);
        }
        try {
            if (expectedContentFile.endsWith(".json") || !expectedContentFile.contains(".")) {
                JSONAssert.assertEquals(expectedContent, actualContent, compareMode);
            } else {
                Assertions.assertThat(expectedContent.trim()).isEqualTo(actualContent.trim());
            }
        } catch (AssertionError assertionError) {
            log.error("Test failed - actual content: {}", actualContent);
            throw assertionError;
        }
    }

    public static String getFileContentAsString(String expectedContentFile) {
        return getFileContentAsStringOrWriteIfMissing(expectedContentFile, null);
    }


    public static String getFileContentAsStringOrWriteIfMissing(String expectedContentFile, String actualContent) {
        try {
            var path = expectedContentFile.startsWith("/") ? expectedContentFile : "/expected/" + expectedContentFile + (expectedContentFile.contains(".") ? "" : ".json");
            var file = TestUtils.class.getResourceAsStream(path);
            if (file == null) {
                if (actualContent != null) {
                    Files.writeString(Path.of("src/test/resources" + path), actualContent);
                    if (GENERATES_NO_FAIL) {
                        return actualContent;
                    }
                    Assert.fail("File not found - generated in " + path);
                } else {
                    Assert.fail("File not found in " + path);
                }
                return null;
            }
            return TestUtils.toString(file);
        } catch (IOException e) {
            log.error("Error with file {}", expectedContentFile, e);
            Assert.fail();
            return null;
        }
    }

    public static UUID tinyUuid(int index) {
        var BASE_UUID = "00000000-0000-0000-0000-0000000%05d";
        return UUID.fromString(String.format(BASE_UUID, index));

    }

    public static String toString(InputStream inputStream) throws IOException {
        return CharStreams.toString(new InputStreamReader(inputStream, Charsets.UTF_8));
    }

    public static SecurityContext getSecurityContext(String[] roles, String username, String id) {
        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        for (var role : roles) {
            if (role.startsWith("ROLE_")) {
                throw new IllegalArgumentException("roles cannot start with ROLE_ Got "
                        + role);
            }
            grantedAuthorities.add(new SimpleGrantedAuthority("ROLE_" + role));
        }

        var jwt = new Jwt("---generated by admin mock---", Instant.now(), null, Map.of("", ""),
                Map.of(
                        JwtClaimNames.SUB, id,
                        "name", username
                )
        );
        var authentication = new UsernamePasswordAuthenticationToken(jwt, "password", grantedAuthorities);
        var context = SecurityContextHolder.createEmptyContext();
        context.setAuthentication(authentication);
        return context;
    }

    public static void mockRoles(String... roles) {
        mockAuthentication("userName", UUID.randomUUID().toString(), roles);
    }

    public static void mockAuthentication(String userName, String userId, String... roles) {
        TestSecurityContextHolder.setContext(TestUtils.getSecurityContext(roles, userName, userId));
    }

    @SneakyThrows
    public static byte[] getFileContentAsByteArray(String filename) {
        return TestUtils.class.getResourceAsStream("/expected/%s".formatted(filename)).readAllBytes();
    }

    @SneakyThrows
    public static ResultMatcher pdfMatchesContent(String filename) {
        return result -> {
            assertThatPdfMatchesContent(filename, result.getResponse().getContentAsByteArray());
        };
    }

    @SneakyThrows
    public static void assertThatPdfMatchesContent(String filename, byte[] data) {
        var fileContentAsByteArray = getFileContentAsByteArray(filename);
        assertThatPdfMatchesContent(fileContentAsByteArray, filename);
    }


    @SneakyThrows
    public static void assertThatPdfMatchesContent(byte[] fileContentAsByteArray, String expectedFilename) {
        var path = Path.of("src/test/resources/expected/%s".formatted(expectedFilename));
        if (!Files.exists(path)) {
            Files.write(path, fileContentAsByteArray);
            Assertions.fail("File not found - generated in %s".formatted(path));
        }
        var actual = new PDFTextStripper().getText(Loader.loadPDF(fileContentAsByteArray)).trim();
        var expected = new PDFTextStripper().getText(Loader.loadPDF(getFileContentAsByteArray(expectedFilename))).trim();
        assertEquals(expected, actual);
    }

    @SneakyThrows
    public static void assertJsonSerializedObjectMatchesContent(Object expectedObject, String filename) {
        var formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm");
        var objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .registerModule(new JavaTimeModule())
                .registerModule(new SimpleModule().addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter)).addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter)));
        assertFileContentMatch(filename, objectMapper.writeValueAsString(expectedObject), JSONCompareMode.LENIENT);

    }

}
