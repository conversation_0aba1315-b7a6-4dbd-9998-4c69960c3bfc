package com.erhgo.migrations.fixtures.esco;

import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.migrations.changes.ImportEscoOccupationsScores;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.repositories.classifications.EscoOccupationRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.stubbing.Answer;
import org.springframework.transaction.support.SimpleTransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.Collections;
import java.util.Set;

public class ImportEscoOccupationsScoresTest {

    @Test
    public void import_esco_scores() throws IOException {
        MockitoAnnotations.initMocks(this);

        var csv = this.getClass().getClassLoader().getResourceAsStream("data/esco_scores.csv");

        var escoOccupationRepository = Mockito.mock(EscoOccupationRepository.class);
        var erhgoOccupationRepository = Mockito.mock(ErhgoOccupationRepository.class);
        var transactionTemplate = Mockito.mock(TransactionTemplate.class);

        Mockito.when(escoOccupationRepository.findAllById(Mockito.anyIterable())).thenAnswer((invocation) -> {
            Set<String> uris = invocation.getArgument(0);
            return uris.stream().map(uri -> EscoOccupation.builder().uri(uri).build()).toList();
        });

        Mockito.when(erhgoOccupationRepository.findAll()).thenAnswer((invocation) -> Collections.emptyList());

        Mockito.when(escoOccupationRepository.saveAll(Mockito.anyIterable())).thenAnswer((invocation) -> invocation.getArgument(0));
        Mockito.when(transactionTemplate.execute(Mockito.any())).thenAnswer((Answer) invocation -> {
            var arg = (TransactionCallback) invocation.getArgument(0);
            return arg.doInTransaction(new SimpleTransactionStatus());
        });

        var service = new ImportEscoOccupationsScores(escoOccupationRepository, erhgoOccupationRepository, transactionTemplate, null);

        var importedCount = service.doImport(csv);

        Assertions.assertEquals(31, importedCount);
        Mockito.verify(escoOccupationRepository).saveAll(Mockito.anyIterable());
    }
}
