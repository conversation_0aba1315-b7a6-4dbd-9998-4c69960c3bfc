package com.erhgo;

import com.erhgo.config.PromptConfig;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.CustomChatCompletionRequest;
import com.erhgo.services.generation.dto.NormalizedTitlesResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import org.springframework.ai.chat.prompt.Prompt;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class OpenAIGenerationMockUtils {

    public static void prepare(GenerationClient mockedClient) {
        var title = "Un titre pertinent pour le métier au masculin";
        var normalizedTitlesResponse = new NormalizedTitlesResponse(title, title.replace("masculin", "féminin"));
        var OPENAI_RESPONSE_NORMALIZEDTITLE = """
                {"masculine": "Un titre pertinent pour le métier au masculin", "feminine": "Un titre pertinent pour le métier au féminin"}
                """;

        when(mockedClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class)))
                .thenAnswer(invocation -> {
                    CustomChatCompletionRequest request = invocation.getArgument(0);
                    var userMessage = request.getInstructions().getLast().getText();
                    if (userMessage.contains("Normalisez")) {
                        return new ChatCompletionResponse(OPENAI_RESPONSE_NORMALIZEDTITLE, new OpenAIResponse<NormalizedTitlesResponse>().setNbTry(1).setResult(normalizedTitlesResponse));
                    }

                    return new ChatCompletionResponse(" ", new OpenAIResponse<NormalizedTitlesResponse>().setNbTry(1).setResult(new NormalizedTitlesResponse()));
                });
    }
}
