package com.erhgo.openaisoftskills;

import com.erhgo.TestUtils;
import com.erhgo.openapi.dto.ChatCompletionResponseDTO;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.GenericPromptTesterService;
import com.erhgo.services.generation.client.MockedGenerationClient;
import com.erhgo.services.generation.dto.GenericOpenAiPromptTesterCommand;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.opencsv.CSVWriter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

import static com.opencsv.ICSVWriter.*;


/**
 * Utils test to validate some prompt.
 * Set:
 * - number of iterations : NB_ITERATIONS field
 * - temperature : TEMPERATURE field
 * - prompt to test in  src/test/resources/data/openai-tools/prompt.yaml
 * - data source inside src/test/resources/data/openai-tools/<firstname>.json
 * <p>
 * Request, per user, for soft skills:
 * ```
 * SELECT JSON_ARRAYAGG(a.res) FROM
 * (SELECT
 * JSON_OBJECT(
 * 'title', ux.jobTitle,
 * 'type',
 * CASE ux.type
 * WHEN 0 THEN 'Emploi'
 * WHEN 1 THEN 'Mission'
 * WHEN 2 THEN 'Stage'
 * WHEN 3 THEN 'Bénévolat'
 * WHEN 4 THEN 'Personnel'
 * ELSE 'Inconnu'
 * END,
 * 'status', IF(ux.durationType = 0, 'En cours', 'Terminé'),
 * 'duration',
 * CASE         ux.duration
 * WHEN 'DURATION_1' THEN "Moins de 6 mois"
 * WHEN 'DURATION_2' THEN "6 mois à 1 an"
 * WHEN 'DURATION_3' THEN "1 an à 5 ans"
 * WHEN 'DURATION_4' THEN "Plus de 5 ans"
 * ELSE ''
 * END,
 * 'employer', ux.organizationName,
 * 'date', ux.createdDate
 * ) as res
 * FROM UserExperience ux
 * inner join ErhgoOccupation o on ux.erhgoOccupation_id = o.id
 * inner join OccupationActivity oa on o.id = oa.occupation_id
 * inner join JobActivityLabel jal on jal.uuid = oa.activity_uuid
 * inner join Activity_Capacity acc on acc.Activity_uuid = jal.activity_uuid
 * inner join Capacity ca on ca.id = acc.inducedCapacities_id
 * WHERE ux.userProfile_uuid = uuidToBin(userId)
 * group by ux.uuid) as a;
 * ```
 */
@Slf4j
@Disabled
class OpenAISoftSkillsGenerationToolTest {

    /**
     * - marc / b329d380-b71d-42a9-b3fb-11daefdcab47 / <EMAIL>
     * - richard / 54d027bb-ff42-4388-97cc-2e26f470bbad / <EMAIL>
     * - camille / e790bc65-2069-47be-b810-c5bddcb2af08 / <EMAIL>
     * - samuel / 7da2ab5f-24bf-4c23-a1ee-438c48bd0ad5 / <EMAIL>
     */

    static final int NB_ITERATIONS = 10;
    public static final double TEMPERATURE = 0.5d;
    GenericPromptTesterService service;
    ObjectMapper objectMapper;

    List<String> users = List.of("marc", "richard", "camille", "samuel");

    @BeforeEach
    void prepare() {
        var openAIClient = new MockedGenerationClient();
        var openaiApikey = System.getenv("OPENAI_APIKEY");
        Assertions.assertNotNull(openaiApikey, "Please set OPENAI_APIKEY var env to proceed");
        var securityService = Mockito.mock(SecurityService.class);
        Mockito.when(securityService.isAdmin()).thenReturn(true);
        service = new GenericPromptTesterService(openAIClient, securityService, new YamlPromptReader(null));
        objectMapper = new ObjectMapper();
    }

    @Test
    @SneakyThrows
    void test() {
        writeHeaders();
        users.stream()
                // ensure files exist
                .peek(n -> {
                    TestUtils.getFileContentAsString("/data/openai-tools/prompt.yaml");
                    TestUtils.getFileContentAsString("/data/openai-tools/%s.json".formatted(n));
                })
                .map(n -> {
                    var prompt = TestUtils.getFileContentAsString("/data/openai-tools/prompt.yaml");
                    var userRelatedData = TestUtils.getFileContentAsString("/data/openai-tools/%s.json".formatted(n)).replaceAll("\\n", " ");
                    return iterateOverSoftSkillsGeneration(prompt, userRelatedData, n);
                })
                .forEach(this::writeToCsv);
    }

    private void writeHeaders() {
        this.writeToCsv(Collections.singletonList(new String[]{"email", "itération", "classement", "titre", "description"}));
    }

    private @NotNull ArrayList<String[]> iterateOverSoftSkillsGeneration(String yaml, String userAsString, String prenom) {
        var prompt = yaml.formatted(prenom, userAsString);
        var iteration = 0;
        var result = new ArrayList<String[]>();
        log.error("{}, Using prompt: {}", prenom, prompt);
        while (iteration++ < NB_ITERATIONS) {

            log.error("{} - Iteration {}", prenom, iteration);
            result.addAll(getSoftSKillsForUserAndPrompt(prompt, "<EMAIL>".formatted(prenom), iteration));
        }
        return result;
    }

    private ArrayList<String[]> getSoftSKillsForUserAndPrompt(String fileContentAsString, String email, int iteration) {

        var result = new ArrayList<String[]>();
        ChatCompletionResponseDTO openAiResult = null;
        try {
            openAiResult = service.testPrompt(new GenericOpenAiPromptTesterCommand(TEMPERATURE, "gpt-4o", 3000, true, fileContentAsString, null));
            var toParse = openAiResult.getContent().replaceAll("```", "").replaceAll("json", "");
            var softSkills = objectMapper.readValue(toParse, new TypeReference<List<Map<String, String>>>() {
            });
            IntStream.range(0, softSkills.size()).forEach(i -> {
                var line = new ArrayList<String>();
                line.add(email);
                line.add(String.valueOf(iteration));
                line.add(String.valueOf(i + 1));
                line.add(softSkills.get(i).get("title"));
                line.add(softSkills.get(i).get("description"));
                result.add(line.toArray(String[]::new));
            });
        } catch (JsonProcessingException | RuntimeException e) {
            log.error("Unable to process for email {} and iteration {}", email, iteration, e);
            result.add(new String[]{email, String.valueOf(iteration + 1), "-1", "ERREUR : impossible de générer pour ce cas-ci", "résultat: %s".formatted(openAiResult)});
        }
        return result;
    }

    private void writeToCsv(List<String[]> result) {
        var path = "src/test/resources/data/openai-tools";
        try (var writer = Files.newBufferedWriter(Path.of(path + "/result.csv"), StandardCharsets.UTF_8, java.nio.file.StandardOpenOption.CREATE, java.nio.file.StandardOpenOption.APPEND);
             var csvWriter = new CSVWriter(
                     writer,
                     '\t',
                     DEFAULT_QUOTE_CHARACTER,
                     DEFAULT_ESCAPE_CHARACTER,
                     DEFAULT_LINE_END)) {
            csvWriter.writeAll(result);
        } catch (IOException e) {
            log.error("Unable to generate CSV", e);
        }
    }

}
