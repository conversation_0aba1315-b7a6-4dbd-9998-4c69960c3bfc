package com.erhgo.domain;

import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.exceptions.UserNotAllowedForEntity;
import com.erhgo.generators.CandidatureGenerator;
import com.erhgo.generators.UserProfileGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;

class CandidatureTest {


    @Test
    void validateOnCreate_fails_private_channel() {
        try {
            CandidatureGenerator.buildCandidature(UserProfileGenerator.buildEmptyUserProfile(), CandidatureRecruitmentState.NEW, "E-35", true);
            fail("Should not be allowed");
        } catch (UserNotAllowedForEntity e) {
            assertThat(e).hasMessageContaining("not allowed to candidate to");
        }
    }

    @Test
    void validateOnCreate_succeed_private_channel() {
        var code = "E-25";
        var candidature = CandidatureGenerator.buildCandidature(UserProfileGenerator.buildEmptyUserProfile(UUID.randomUUID(), code), CandidatureRecruitmentState.NEW, code, true);
        Assertions.assertNotNull(candidature);
    }
}
