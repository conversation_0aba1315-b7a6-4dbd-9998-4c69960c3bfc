package com.erhgo.domain.criteria;

import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.generators.CriteriaMotherObject;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class CriteriaValueTest {

    @Test
    void getHigherValues_excludes_current_value() {
        var criteria = new CriteriaMotherObject()
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("1", "2", "3").build();
        Assertions.assertThat(criteria.getCriteriaValues().get(1).getHigherValues())
                .extracting(CriteriaValue::getCode)
                .containsExactly("3");
    }

    @Test
    void getHigherValues_limit_high() {
        var criteria = new CriteriaMotherObject()
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("1", "2", "3").build();
        Assertions.assertThat(criteria.getCriteriaValues().get(2).getHigherValues())
                .extracting(CriteriaValue::getCode)
                .isEmpty();
    }

    @Test
    void getHigherValues_limit_low() {
        var criteria = new CriteriaMotherObject()
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("1", "2", "3").build();
        Assertions.assertThat(criteria.getCriteriaValues().get(0).getHigherValues())
                .extracting(CriteriaValue::getCode)
                .containsExactly("2", "3");
    }

    @Test
    void getValueOrLower_includes_current_value() {
        var criteria = new CriteriaMotherObject()
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("1", "2", "3").build();
        Assertions.assertThat(criteria.getCriteriaValues().get(1).getValueOrLower())
                .extracting(CriteriaValue::getCode)
                .containsExactly("1", "2");
    }

    @Test
    void getValueOrLower_limit_high() {
        var criteria = new CriteriaMotherObject()
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("1", "2", "3").build();
        Assertions.assertThat(criteria.getCriteriaValues().get(2).getValueOrLower())
                .extracting(CriteriaValue::getCode)
                .containsExactly("1", "2", "3");
    }

    @Test
    void getValueOrLower_limit_low() {
        var criteria = new CriteriaMotherObject()
                .withQuestionType(CriteriaQuestionType.THRESHOLD)
                .withValueCodes("1", "2", "3").build();
        Assertions.assertThat(criteria.getCriteriaValues().get(0).getValueOrLower())
                .extracting(CriteriaValue::getCode)
                .containsExactly("1");
    }
}
