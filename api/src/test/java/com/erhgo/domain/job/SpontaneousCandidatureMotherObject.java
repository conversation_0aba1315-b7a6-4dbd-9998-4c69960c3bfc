package com.erhgo.domain.job;

import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.repositories.SectorRepository;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.HashSet;

@Service
@Scope("prototype")
public class SpontaneousCandidatureMotherObject extends AbstractCandidatureMotherObject<SpontaneousCandidature, SpontaneousCandidatureMotherObject> {

    @Autowired
    @Getter
    SpontaneousCandidatureRepository repository;

    @Autowired
    SectorRepository sectorRepository;
    private String recruiterTitle;
    private AbstractOrganization.OrganizationType organizationType;

    SpontaneousCandidatureMotherObject() {
        super(new SpontaneousCandidature());
    }

    @Override
    void customBuildAndPersist(SpontaneousCandidature candidature) {
        if (recruiter != null) {
            candidature.setRecruiter(recruiter);
        }
        if (candidature.getRecruiter() == null) {
            candidature.setRecruiter(applicationContext.getBean(RecruiterMotherObject.class).buildAndPersist());
        }
        if (recruiterTitle != null) {
            candidature.getRecruiter().setTitle(recruiterTitle);
        }
        if (candidature.getSubmissionDate() == null) {
            candidature.setSubmissionDate(OffsetDateTime.now());
        }
        if (organizationType != null) {
            ReflectionTestUtils.setField(candidature.getRecruiter(), "organizationType", organizationType);
        }
    }


    public SpontaneousCandidatureMotherObject withCustomSectors(Collection<String> customSectors) {
        candidature.setCustomSectors(new HashSet<>(customSectors));
        return this;
    }

    public SpontaneousCandidatureMotherObject withReferentialSectorCodes(Collection<String> sectorCodes) {
        candidature.setReferentialSectors(new HashSet<>(sectorRepository.findByCodeIn(sectorCodes)));
        return this;
    }

    public SpontaneousCandidatureMotherObject withRecruiterTitle(String title) {
        this.recruiterTitle = title;
        return this;
    }

    public SpontaneousCandidatureMotherObject withOrganizationOfType(AbstractOrganization.OrganizationType organizationType) {
        this.organizationType = organizationType;
        return this;
    }
}
