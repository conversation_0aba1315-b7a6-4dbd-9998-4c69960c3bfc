package com.erhgo.domain.job;

import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.externaloffer.ExternalOfferMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Service
@Scope("prototype")
public class RecruitmentCandidatureMotherObject extends AbstractCandidatureMotherObject<RecruitmentCandidature, RecruitmentCandidatureMotherObject> {

    @Getter
    @Autowired
    RecruitmentCandidatureRepository repository;
    private RecruitmentState recruitmentState;
    private String recruiterTitle;
    private String atsCode;
    private String externalOfferEmailToNotify;

    public RecruitmentCandidatureMotherObject() {
        super(new RecruitmentCandidature());
    }

    private AbstractOrganization.OrganizationType organizationType;
    private final Set<JobActivityLabel> selectedActivities = new HashSet<>();
    private CandidatureState candidatureState;

    @Override
    public RecruitmentCandidature build() {
        var previousSubmissionDate = candidature.getSubmissionDate();
        candidature.setState(candidatureState == null ? CandidatureState.VALIDATED : candidatureState);
        candidature.setSubmissionDate(previousSubmissionDate);
        candidature.setCandidatureRecruitmentState(candidature.getGlobalCandidatureState() == null ? CandidatureRecruitmentState.SELECTED : candidature.getGlobalCandidatureState().getCandidatureRecruitmentState());
        candidature.setValid(true);
        return candidature;
    }

    void customBuildAndPersist(RecruitmentCandidature candidatureToPersist) {
        if (candidatureToPersist.getRecruitment() == null) {
            candidatureToPersist.setRecruitment(applicationContext.getBean(RecruitmentMotherObject.class).buildAndPersist());
        }
        if (recruitmentState != null) {
            candidatureToPersist.getRecruitment().setState(recruitmentState);
        }

        if (recruiter != null) {
            candidatureToPersist.getRecruitment().getRecruitmentProfile().getJob().setRecruiter(recruiter);
        }
        if (recruiterTitle != null) {
            candidatureToPersist.getRecruitment().getRecruitmentProfile().getJob().getRecruiter().setTitle(recruiterTitle);
        }
        if (organizationType != null) {
            ReflectionTestUtils.setField(candidatureToPersist.getRecruitment().getJob().getRecruiter(), "organizationType", organizationType);
        }
        if (!selectedActivities.isEmpty()) {
            var mission = candidature.getJob().getMissions().first();

            mission.getActivities().addAll(selectedActivities);
        }
        if (atsCode != null) {
            var offer = applicationContext.getBean(ExternalOfferMotherObject.class)
                    .withATSCode(atsCode)
                    .withRemoteId("offer for " + atsCode + UUID.randomUUID())
                    .withRecruiterCode(recruiterCode)
                    .withCandidatureEmail(externalOfferEmailToNotify)
                    .buildAndPersist();

            offer.setRecruitment(candidature.getRecruitment());
        }
    }


    public RecruitmentCandidatureMotherObject withOrganizationOfType(AbstractOrganization.OrganizationType organizationType) {
        this.organizationType = organizationType;
        return this;
    }


    public RecruitmentCandidatureMotherObject withCandidatureState(CandidatureState state) {
        this.candidatureState = state;
        return this;
    }

    public RecruitmentCandidatureMotherObject withAnswer(String answer) {
        candidature.setCustomAnswer(answer);
        return this;
    }

    public RecruitmentCandidatureMotherObject withJobTitle(String jobTitle) {
        candidature.setRecruitment(
                applicationContext.getBean(RecruitmentMotherObject.class)
                        .withJobTitle(jobTitle)
                        .buildAndPersist());
        return this;
    }

    public RecruitmentCandidatureMotherObject generated(boolean generated) {
        candidature.setGeneratedForSourcing(generated);
        return this;
    }

    public RecruitmentCandidatureMotherObject withModifiedByUser(boolean modifiedByUser) {
        candidature.setModifiedByUser(modifiedByUser);
        return this;
    }

    public RecruitmentCandidatureMotherObject withActivity(JobActivityLabel activity) {
        this.selectedActivities.add(activity);
        return this;
    }

    public RecruitmentCandidatureMotherObject withValid(boolean valid) {
        candidature.setValid(valid);
        return this;
    }

    public RecruitmentCandidatureMotherObject withRecruitment(Recruitment recruitment) {
        candidature.setRecruitment(recruitment);
        return this;
    }

    public RecruitmentCandidatureMotherObject withAvailability(Boolean isAvailable, Integer availabilityDelayInMonth) {
        candidature.updateAvailability(isAvailable, availabilityDelayInMonth);
        return this;
    }

    public RecruitmentCandidatureMotherObject withEffort(Integer effort) {
        this.candidature.setEffort(effort);
        return this;
    }

    public RecruitmentCandidatureMotherObject withRecruitmentState(RecruitmentState value) {
        this.recruitmentState = value;
        return this;
    }

    public RecruitmentCandidatureMotherObject withRecruiterTitle(String recruiterTitle) {
        this.recruiterTitle = recruiterTitle;
        return this;
    }

    public RecruitmentCandidatureMotherObject withATSCode(String atsCode) {
        this.atsCode = atsCode;
        return this;
    }

    public RecruitmentCandidatureMotherObject withState(GlobalCandidatureState state) {
        super.withState(state);
        if (GlobalCandidatureState.NOT_FINALIZED == state) {
            candidatureState = CandidatureState.STARTED;
        }
        return this;
    }

    public RecruitmentCandidatureMotherObject withRefusalState(CandidatureEmailRefusalState state) {
        super.withRefusalData(state, "");
        return this;
    }

    public RecruitmentCandidatureMotherObject withEmailToNotify(String externalOfferEmailToNotify) {
        this.externalOfferEmailToNotify = externalOfferEmailToNotify;
        return this;
    }

    public RecruitmentCandidatureMotherObject withNotifiedOnClosedRecruitment(boolean notified) {
        candidature.setNotifiedOnClosedRecruitment(notified);
        return this;
    }
}
