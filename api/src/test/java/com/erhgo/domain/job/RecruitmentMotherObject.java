package com.erhgo.domain.job;

import com.erhgo.TransactionTestHelper;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.enums.RecruitmentSendNotificationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.repositories.RecruitmentProfileRepository;
import com.erhgo.repositories.RecruitmentRepository;
import jakarta.persistence.EntityManager;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Scope("prototype")
@NoArgsConstructor
public class RecruitmentMotherObject {
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private TransactionTestHelper txHelper;
    @Autowired
    private RecruitmentRepository recruitmentRepository;
    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    private final Recruitment recruitment = new Recruitment();

    private Recruiter recruiter;

    private RecruitmentProfile recruitmentProfile;

    private String jobTitle, question;

    private ErhgoOccupation jobOccupation;

    private AbstractOrganization.OrganizationType recruiterType;
    private String recruiterTitle;
    private Date createdDate;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    public Recruitment buildAndPersist() {
        var atomicReference = new AtomicReference<Recruitment>();
        txHelper.doInTransaction(() -> {
            var hadJob = recruitment.getJob() != null;
            build();
            if (hadJob) applicationContext.getBean(EntityManager.class).merge(recruitment.getJob());
            recruitmentProfileRepository.save(recruitment.getRecruitmentProfile());
            var recruitmentLoc = recruitmentRepository.save(this.recruitment);
            recruitmentLoc.updateCodeOnJobCreate();
            atomicReference.set(recruitmentLoc);
        });
        if (this.createdDate != null) {
            txHelper.doInTransaction(() -> {
                jdbcTemplate.update("UPDATE Recruitment SET createdDate = ? WHERE id = ?", this.createdDate, atomicReference.get().getId().toString());
            });
        }

        return atomicReference.get();
    }

    public Recruitment build() {
        if (recruitment.getTypeContract() == null) {
            recruitment.setTypeContract(ContractType.CDI);
        }
        if (recruitmentProfile == null) {
            recruitmentProfile = (applicationContext == null ? new RecruitmentProfileMotherObject() : applicationContext.getBean(RecruitmentProfileMotherObject.class)).build();
        }
        recruitment.setRecruitmentProfile(recruitmentProfile);
        if (recruiter != null) {
            recruitment.getJob().setRecruiter(recruiter);
        }
        if (recruitment.getState() == null) {
            recruitment.setState(RecruitmentState.PUBLISHED, false);
        }
        if (jobTitle != null) {
            recruitment.getJob().setTitle(jobTitle);
        }
        if (question != null) {
            recruitment.getRecruitmentProfile().setCustomQuestion(question);
        }
        if (jobOccupation != null) {
            recruitment.getJob().setErhgoOccupation(jobOccupation);
        }
        if (recruiterType != null) {
            ReflectionTestUtils.setField(recruitment.getRecruiter(), "organizationType", recruiterType);
        }
        if (recruitment.getManagerUserId() == null) {
            recruitment.setManagerUserId("uuid");
        }
        if (recruiterTitle != null) {
            recruitment.getRecruiter().setTitle(recruiterTitle);
        }
        if (this.createdDate != null)
            ReflectionTestUtils.setField(this.recruitment, "createdDate", this.createdDate);

        return recruitment;
    }

    public RecruitmentMotherObject withTypeContract(ContractType typeContract) {
        recruitment.setTypeContract(typeContract);
        return this;
    }

    public RecruitmentMotherObject withLocation(Location location) {
        this.recruitment.setLocation(location);
        return this;
    }

    public RecruitmentMotherObject withTitle(String title) {
        this.recruitment.setTitle(title);
        return this;
    }

    public RecruitmentMotherObject withRecruiter(Recruiter recruiter) {
        this.recruiter = recruiter;
        return this;
    }

    public RecruitmentMotherObject withRecruiterCode(String recruiterCode) {
        this.recruiter = this.applicationContext.getBean(OrganizationGenerator.class).createRecruiter(recruiterCode, AbstractOrganization.OrganizationType.SOURCING);
        return this;
    }

    public RecruitmentMotherObject withJob(Job job) {
        if (this.recruitment.getRecruitmentProfile() == null) {
            if (applicationContext != null) {
                this.recruitmentProfile = applicationContext.getBean(RecruitmentProfileMotherObject.class).withJob(job).buildAndPersist();
            } else {
                this.recruitmentProfile = new RecruitmentProfileMotherObject().withJob(job).build();
            }
        } else {
            this.recruitmentProfile.setJob(job);
        }
        return this;
    }

    public RecruitmentMotherObject withState(RecruitmentState state) {
        this.recruitment.setState(state, false);
        return this;
    }

    public RecruitmentMotherObject withPublicationDate(Date date) {
        this.recruitment.setPublicationDate(date);
        return this;
    }

    public RecruitmentMotherObject withJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
        return this;
    }

    public RecruitmentMotherObject withRecruitmentProfileQuestion(String question) {
        this.question = question;
        return this;
    }

    public RecruitmentMotherObject withHideSalary(boolean hideSalary) {
        recruitment.setHideSalary(hideSalary);
        return this;
    }

    public RecruitmentMotherObject withRecruiterDescription(String organizationDescription) {
        recruitment.setOrganizationDescription(organizationDescription);
        return this;
    }

    public RecruitmentMotherObject withSourcingStep(int step) {
        recruitment.setSourcingStep(step);
        return this;
    }

    public RecruitmentMotherObject withModularWorkingTime(Boolean modular) {
        recruitment.setModularWorkingTime(modular);
        return this;
    }

    public RecruitmentMotherObject withSalaries(int min, int max) {
        recruitment.setBaseSalary(min);
        recruitment.setMaxSalary(max);
        return this;
    }

    public RecruitmentMotherObject withOccupation(ErhgoOccupation jobOccupation) {
        this.jobOccupation = jobOccupation;
        return this;
    }

    public RecruitmentMotherObject withExternalUrl(String externalUrl) {
        recruitment.setExternalUrl(externalUrl);
        return this;
    }

    public RecruitmentMotherObject withWorkingWeeklyTime(Integer time) {
        recruitment.setWorkingWeeklyTime(time);
        return this;
    }

    public RecruitmentMotherObject withErhgoClassification(Set<ErhgoClassification> erhgoClassification) {
        recruitment.setErhgoClassifications(erhgoClassification);
        return this;
    }

    public RecruitmentMotherObject withSendNotificationState(RecruitmentSendNotificationState sendNotificationState, Instant instant) {
        recruitment.setSendNotificationDate(instant);
        recruitment.setSendNotificationState(sendNotificationState);
        return this;
    }

    public RecruitmentMotherObject withPublicationEndDate(OffsetDateTime offsetDateTime) {
        recruitment.setPublicationEndDate(offsetDateTime);
        return this;
    }

    public RecruitmentMotherObject withSourcingNotifiedUsersIds(String... authenticatedUserIds) {
        recruitment.setSourcingUsersIdToNotify(Set.of(authenticatedUserIds));
        return this;
    }

    public RecruitmentMotherObject withRecruiterType(AbstractOrganization.OrganizationType organizationType) {
        this.recruiterType = organizationType;
        return this;
    }

    public RecruitmentMotherObject withManagerUserId(String userId) {
        recruitment.setManagerUserId(userId);
        return this;
    }

    public RecruitmentMotherObject withId(Long id) {
        recruitment.setId(id);
        return this;
    }

    public RecruitmentMotherObject withDescription(String desc) {
        recruitment.setDescription(desc);
        return this;
    }

    public RecruitmentMotherObject withRecruiterTitle(String recruiter) {
        this.recruiterTitle = recruiter;
        return this;
    }

    public RecruitmentMotherObject withExternalOffer(ExternalOffer externalOffer) {
        this.recruitment.setExternalOffer(externalOffer);
        externalOffer.setRecruitment(recruitment);
        return this;
    }

    public RecruitmentMotherObject withStartingDate(OffsetDateTime offsetDateTime) {
        recruitment.setStartingDate(offsetDateTime);
        return this;
    }

    public RecruitmentMotherObject withCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
        return this;
    }
}
