package com.erhgo.domain;

import com.erhgo.domain.userprofile.Location;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;


class LocationTest {

    @Test
    void getDistance_expect_correct_value_between_two_locations() {
        var location1 = Location.builder().city("Lyon").latitude(43.296482F).longitude(5.36978F).build();
        var location2 = Location.builder().city("Toulouse").latitude(43.604652F).longitude(1.444209F).build();
        var expectedDistance = 318.70444159841134;

        var distance = location1.getDistance(location2);

        assertThat(distance).isEqualTo(expectedDistance);
    }

    @Test
    void getDistance_tolerates_one_location_is_empty() {
        var location1 = Location.builder().city("Lyon").latitude(null).longitude(null).build();
        var location2 = Location.builder().city("Toulouse").latitude(43.604652F).longitude(1.444209F).build();


        var distance = location1.getDistance(location2);

        assertThat(distance).isZero();
    }
}
