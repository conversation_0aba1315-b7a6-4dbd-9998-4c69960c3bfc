package com.erhgo.domain.userprofile;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

class MailConfigurationTest {

    @CsvSource(
            value = {
                    "\"Juliette de #JeNeSuisPASunCV\" <<EMAIL>>,<EMAIL>",
                    "' \"Juliette de #JeNeSuisPASunCV\"   <<EMAIL> >',<EMAIL>",
                    "<EMAIL> ,<EMAIL>",
                    "<EMAIL>,<EMAIL>",
                    ",''",
                    "null,''",
            },
            nullValues = "null",
            ignoreLeadingAndTrailingWhitespace = false
    )
    @ParameterizedTest
    void normalize(String input, String expected) {
        Assertions.assertThat(MailConfiguration.normalize(input)).isEqualTo(expected);
    }
}
