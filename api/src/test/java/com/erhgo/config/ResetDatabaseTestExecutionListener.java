package com.erhgo.config;

import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.generators.UserExperienceGenerator;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.TestContext;
import org.springframework.test.context.support.AbstractTestExecutionListener;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ResetDatabaseTestExecutionListener extends AbstractTestExecutionListener {

    private static final Collection<String> DO_NOT_TRUNCATE_TABLES = List.of(
            "DATABASECHANGELOG",
            "DATABASECHANGELOGLOCK",
            "WorkEnvironment",
            "ErhgoClassification",
            "PersonalEmailDomain",
            "CandidatureSeq",
            "Sector"
    );

    public final static int ORDER = 2001;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private EntityManager em;

    @Autowired
    private KeycloakMockService keycloakMockService;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Override
    public final int getOrder() {
        return ORDER;
    }

    private boolean forceDbReset = false;

    @Override
    public void beforeTestClass(TestContext testContext) throws Exception {
        testContext.getApplicationContext()
                .getAutowireCapableBeanFactory()
                .autowireBean(this);
        cleanupDatabase();
        EventPublisherUtils.resetPublisher(applicationEventPublisher);
        forceDbReset = false;
    }

    @Override
    public void afterTestClass(TestContext testContext) {
        // Avoid side effect on UnitTests
        EventPublisherUtils.resetPublisher(null);
    }

    @Override
    public void beforeTestMethod(TestContext testContext) throws Exception {
        forceDbReset = forceDbReset || (testContext.getTestMethod().getAnnotation(ResetDataBefore.class) != null);
        if (forceDbReset) {
            cleanupDatabase();
            forceDbReset = false;
        }
    }

    @Override
    public void afterTestExecution(TestContext testContext) {
        if (testContext.getTestMethod().getAnnotation(ResetDataAfter.class) != null) {
            forceDbReset = true;
        }
    }

    private void cleanupDatabase() throws SQLException {
        try (
                var c = dataSource.getConnection();
                var s = c.createStatement()
        ) {

            // Disable FK
            s.execute("SET FOREIGN_KEY_CHECKS=0");

            // Find all tables and truncate them
            Set<String> tables = new HashSet<>();
            try (var rs = s.executeQuery("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA <> 'information_schema'")) {
                while (rs.next()) {
                    tables.add(rs.getString(1));
                }
            }
            for (var table : tables) {
                if (!DO_NOT_TRUNCATE_TABLES.contains(table)) {
                    s.executeUpdate("TRUNCATE TABLE " + table);
                }
            }
            s.executeUpdate("DROP SEQUENCE CandidatureSeq");
            s.executeUpdate("CREATE SEQUENCE CandidatureSeq START WITH 1 INCREMENT BY 1");
            // Enable FK
            s.execute("SET FOREIGN_KEY_CHECKS=1");

            em.getEntityManagerFactory().getCache().evictAll();

            resetIndexes();
            resetKeycloakMock();
            s.executeUpdate("INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES ('user_mastery_level_threshold', '1.5'), ('job_mastery_level_threshold', '1.5'), ('default_gdpr_mention', '<p>Pour toute question relative au traitement de vos données personnelles par jenesuisPASunCV et à l’exercice de vos droits sur vos données personnelles</p><ul><li><p>Par email : <a href=\"mailto:<EMAIL>\" title=\"mailto:<EMAIL>\"><u><EMAIL></u></a></p></li><li><p>Par courrier postal adressé à : LE TRAVAIL REEL, Monsieur le Délégué à la protection des données – 40 cours du Docteur Long 69003 Lyon</p></li></ul>'), ('ats.default.max_offer_in_aura', '3'), ('ats.default.max_offer_out_aura', '3')");
        }
    }

    private void resetKeycloakMock() {
        keycloakMockService.reset();
    }

    private void resetIndexes() {
        UserExperienceGenerator.JOB_INDEX = 0;
    }
}
