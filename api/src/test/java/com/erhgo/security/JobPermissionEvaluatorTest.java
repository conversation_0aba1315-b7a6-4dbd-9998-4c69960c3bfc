package com.erhgo.security;

import com.erhgo.generators.DataGeneratorService;
import com.erhgo.generators.JobGenerator;
import com.erhgo.security.evaluator.JobPermissionEvaluator;
import com.erhgo.services.SecurityService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Optional;

public class JobPermissionEvaluatorTest {

    private static DataGeneratorService dataGeneratorService = new DataGeneratorService();

    private JobPermissionEvaluator jobPermissionEvaluator = new JobPermissionEvaluator(new SecurityService(null, null, Optional.empty()));

    private static final String ORGANIZATION_CODE = "E-042";

    @Test
    public void should_not_permit_user_without_organization_code_as_role() {
        var job = JobGenerator.buildEmptyJob(ORGANIZATION_CODE);
        var authentication = dataGeneratorService.authenticateForIdAndRole("not me", Role.ODAS_ADMIN);
        Assertions.assertFalse(jobPermissionEvaluator.hasPermission(authentication, job, PermissionLevel.READ));
    }

    @Test
    public void should_not_permit_RH_user_to_write() {
        var job = JobGenerator.buildEmptyJob(ORGANIZATION_CODE);
        var authentication = dataGeneratorService.authenticateForIdAndRole("not me", Role.ODAS_ADMIN);
        Assertions.assertFalse(jobPermissionEvaluator.hasPermission(authentication, job, PermissionLevel.WRITE));
    }


    @Test
    public void should_permit_sourcing_user_to_read() {
        var job = JobGenerator.buildEmptyJob(ORGANIZATION_CODE);
        var authentication = dataGeneratorService.authenticateForIdAndRole("me!", ORGANIZATION_CODE, Role.SOURCING);
        Assertions.assertTrue(jobPermissionEvaluator.hasPermission(authentication, job, PermissionLevel.READ));
    }
}
