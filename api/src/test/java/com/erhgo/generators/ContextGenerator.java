package com.erhgo.generators;

import com.erhgo.domain.referential.Category;
import com.erhgo.domain.referential.CategoryLevel;
import com.erhgo.domain.referential.Context;
import com.erhgo.repositories.CategoryRepository;
import com.erhgo.repositories.ContextRepository;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class ContextGenerator {

    @Autowired
    private ContextRepository contextRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    private static Long GLOBAL_ID = 1L;

    public static Context buildRandomContext() {
        var context = new Context();
        context.setIndex(GLOBAL_ID++);
        context.setId(UUID.randomUUID());
        context.setCategoryLevel(buildCategoryLevel());
        return context;
    }

    private static CategoryLevel buildCategoryLevel() {
        var categoryLevel = new CategoryLevel();
        categoryLevel.setCategory(new Category());
        categoryLevel.setId(GLOBAL_ID++);
        categoryLevel.setDescription("Level with id " + GLOBAL_ID);
        return categoryLevel;
    }

    public Context createContextWithScore(Long index, int score) {
        Category category = new Category();
        category.setCode("Category for " + index);
        CategoryLevel categoryLevel = new CategoryLevel();
        categoryLevel.setScore(score);
        categoryLevel.setCategory(category);
        categoryLevel.setTitle("level for " + index);

        category.setLevels(Sets.newTreeSet(Sets.newHashSet(categoryLevel)));
        categoryRepository.save(category);

        Context context = new Context();
        context.setCategoryLevel(categoryLevel);
        context.setIndex(index);
        context.setId(UUID.randomUUID());
        context.setTitle("Context " + index);
        return contextRepository.save(context);

    }

    public Context createContextWithLevel(Long index, int level) {

        int scoreForLevel = 5;
        if (level == 2) {
            scoreForLevel = 25;
        }
        if (level == 3) {
            scoreForLevel = 50;
        }
        if (level == 4) {
            scoreForLevel = 80;
        }
        if (level == 5) {
            scoreForLevel = 95;
        }
        return createContextWithScore(index, scoreForLevel);
    }

    public Category createCategory() {
        return createCategoryWithCodeAndScore("Category for " + GLOBAL_ID++, 10);
    }

    public Context createContextWithCategory(Category category) {
        Context context = new Context();
        context.setCategoryLevel(category.getLevels().first());
        context.setTitle("Context " + ++GLOBAL_ID);
        context.setIndex(GLOBAL_ID);
        context.setId(UUID.randomUUID());
        return contextRepository.save(context);
    }

    public Context createContext() {
        return createContextWithCategory(createCategory());
    }

    public Context createContextWithScoreAndCategory(int score, String categoryCode) {
        var category = createCategoryWithCodeAndScore(categoryCode, score);
        return createContextWithCategory(category);
    }

    private Category createCategoryWithCodeAndScore(String categoryCode, int score) {
        var category = new Category();
        category.setCode(categoryCode);
        CategoryLevel categoryLevel = new CategoryLevel();
        categoryLevel.setScore(score);
        categoryLevel.setCategory(category);
        categoryLevel.setTitle("level for " + categoryCode);

        category.setLevels(Sets.newTreeSet(Sets.newHashSet(categoryLevel)));
        return categoryRepository.save(category);
    }
}
