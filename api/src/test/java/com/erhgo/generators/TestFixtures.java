package com.erhgo.generators;

import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.domain.candidature.job.CandidatureNote;
import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.isco.IscoOccupation;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.enums.*;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.recruitment.*;
import com.erhgo.domain.referential.*;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.JobContextMet;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.repositories.*;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.Month;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.erhgo.utils.DateTimeUtils.ZONE_ID;
import static java.util.Collections.singleton;

@Service
@SuppressWarnings("squid:S2696")
public class TestFixtures {

    public static final String E_01_CERA_CODE = "E-01", E_02_SOGILIS_CODE = "E-02", M_03_BOTANIC_CODE = "M-03";

    public static final String KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE = "Normal_GOOD_user";
    public static final String KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE = "Normal_BAD_user";
    public static final String KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE = "Empty_User";

    public static final UUID USER_PROFILE_UUID_WITH_MATCHING_CANDIDATURE = UUID.fromString("7b803dda-b300-4e76-9d1d-c7f7fcde786e");
    public static final UUID USER_PROFILE_UUID_WITHOUT_EXPERIENCE = UUID.fromString("7b803dda-b300-4e76-9d1d-c7f7fcde786d");
    public static final UUID USER_PROFILE_UUID_WITH_EMPTY_EXPERIENCE = UUID.fromString("7b803dda-b300-4e76-9d1d-c7f7fcde786c");

    public static final String ADMIN_USER_ID = "uuid";
    public static final String CUSTOM_CONTEXT_LABEL = "Titre custom de contexte ?";
    public static final KeycloakUserSummary USER_WITH_MATCHING_EXPERIENCE_FOR_CANDIDATURE = new KeycloakUserSummary(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE),
            USER_ADMIN = new KeycloakUserSummary(ADMIN_USER_ID);
    public static QuestionForContexts QUESTION_FOR_CONTEXTS_1, QUESTION_FOR_CONTEXTS_2, QUESTION_FOR_CONTEXTS_3;
    public static UUID USER_EXPERIENCE_MATCHING_UUID;

    public static Recruiter E_02_SOGILIS, E_01_CERA;
    public static Employer M_03_BOTANIC;

    public static EscoOccupation ESCO_OCCUPATION_WITH_REFERENTIAL_DATA,
            ESCO_OCCUPATION_WITH_2_QUALIED_SKILLS_IN_4,
            ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6,
            ESCO_OCCUPATION_WITHOUT_REFERENTIAL_DATA;

    public static IscoOccupation ISCO_666, ISCO_777, ISCO_888, ISCO_999;

    public static ErhgoOccupation ERHGO_OCCUPATION_WITHOUT_ROMES,
            ERHGO_OCCUPATION_WITH_ROMES,
            ERHGO_OCCUPATION_QUALIFIED,
            ERHGO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6;

    public static UUID ERHGO_OCCUPATION_WITHOUT_ROMES_UUID = UUID.fromString("cb8a2922-6824-43d6-9c51-dad73d8cba10");

    public static RomeOccupation R1, R2, R3, R4;

    public static UserExperience EXPERIENCE_EMPTY, EXPERIENCE_MATCHING_CANDIDATURE;

    public static Job J_01, J_02, JOB_MODIFIABLE;

    public static Capacity CA1_01, CA1_02, CA1_03, CA1_04, CA1_05,
            CA1_06, CA1_07, CA1_08, CA1_09, CA1_10,
            CA1_11, CA1_12, CA1_13, CA1_14, CA1_15,
            CA1_16, CA1_17, CA1_18, CA1_19, CA1_20,
            CA1_21, CA1_22, CA1_23, CA1_24, CA1_25,
            CA1_26, CA1_27, CA1_28, CA1_29;

    public static Capacity CA2_01, CA2_02, CA2_03, CA2_04, CA2_05,
            CA2_06, CA2_07, CA2_08, CA2_09, CA2_10,
            CA2_11, CA2_12, CA2_13, CA2_14, CA2_15,
            CA2_16, CA2_17, CA2_18, CA2_19, CA2_20,
            CA2_21, CA2_22, CA2_23;

    public static Capacity CA3_01, CA3_02, CA3_03, CA3_04, CA3_05,
            CA3_07, CA3_08, CA3_09, CA3_10,
            CA3_11, CA3_12, CA3_13, CA3_14;

    public static JobActivityLabel ACT_01, jobActivityLabelUsedOnlyInJobMission, ACT_03, ACT_04, ACT_05,
            ACT_07, ACT_08, ACT_09, ACT_10,
            ACT_11, ACT_12, jobActivityLabelUsedOnlyForEscoSkillClassification, ACT_14, ACT_15,
            ACT_16, ACT_17, ACT_18, ACT_19, ACT_20,
            ACT_21, ACT_22, ACT_23, JOB_ACTIVITY_1_USED_IN_JOB_OF_FORMATION, ACT_25,
            ACT_26, unusedJobActivityLabel_2, ACT_29, ACT_30,
            ACT_31, ACT_32, ACT_33, ACT_34, ACT_35,
            ACT_36, ACT_37, ACT_38, ACT_39, ACT_40,
            ACT_41, ACT_42, ACT_43, ACT_44, ACT_45,
            ACT_46, ACT_47, ACT_48, ACT_49, ACT_50,
            ACT_51, JOB_ACTIVITY_2_USED_IN_JOB_OF_FORMATION, ACT_53, ACT_54, ACT_55,
            ACT_56, unusedJobActivityLabel_1,
            jobActivityLabelUsedInCandidatureExperienceAndJobMission, secondActivityLabelSharingJobActivityUsedInUserExperience;

    public static Category knowledgeCategory,
            proGesturesAndAssociatedMaterialCategory,
            workConditionCategory,
            communicationModeCategory,
            modusOperandisCategory,
            riskExposureCategory,
            associatedAuthorizationCategory,
            workspaceConfigurationCategory,
            languageCategory;

    public static Context MANDATORY_CONTEXT, CT_02, CT_03, CT_04, CT_05,
            CT_06, CT_07, CT_08, CT_09, CT_10,
            CT_11, CT_12, CT_13, CT_14, CT_15,
            CT_16, CT_17, CT_18, CONTEXT_USED_IN_JOB_OF_FORMATION, CONTEXT_USED_IN_JOB_WITH_QUESTION,
            CT_21, CONTEXT_USED_IN_JOB_OF_FORMATION_WITH_CUSTOM_QUESTION, CT_23, CT_24, CT_25,
            CT_26, CT_27, CT_28, CT_29, CT_30;

    public static Behavior B_01, B_02, B_03, B_04, B_05, B_06, B_07, B_08, B_09, B_10;

    public static RecruitmentCandidature CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE, CANDIDATURE_MATCHING, CANDIDATURE_NOT_MATCHING_FOR_USER_WITHOUT_EXPERIENCE, CANDIDATURE_WITH_ANSWER_FOR_USER_PROFILE_WITH_EMPTY_EXPERIENCE;
    public static final String CUSTOM_ANSWER = "Yes, we can";

    public static CategoryLevel categoryLevelGenerales;

    public static String M_01_TITLE = "M_01_TITLE", M_02_TITLE = "M_02_TITLE", M_03_TITLE = "M_03_TITLE";
    public static Mission M_01, M_02, M_03, MISSION_MODIFIABLE_01, MISSION_MODIFIABLE_02;

    public static RecruitmentProfile P_01, MODIFIABLE_PROFILE_OF_JOB_1, P_03, UNMODIFIABLE_PROFILE_WITH_QUESTION;
    public static final String CUSTOM_QUESTION = "Ca va bien ?";

    public static Recruitment RECRUITMENT_WITH_MATCHING_CANDIDATURE, RECRUITMENT_WITHOUT_MATCHING_CANDIDATURES, RECRUITMENT_WITH_QUESTION_AND_CANDIDATURE;

    public static Set<Capacity> USER_CAPACITIES;

    public static UserProfile USER_PROFILE_WITHOUT_EXPERIENCE, USER_PROFILE_WITH_EMPTY_EXPERIENCE, USER_PROFILE_WITH_MATCHING_CANDIDATURE, DEFAULT_USER_PROFILE;

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    private CandidatureNoteRepository candidatureNoteRepository;

    @Autowired
    private UserExperienceRepository userExperienceRepository;

    @Autowired
    private CategoryLevelRepository categoryLevelRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private ContextRepository contextRepository;

    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private OptionalContextRepository optionalContextRepository;

    @Autowired
    private OptionalActivityRepository optionalActivityRepository;

    @Autowired
    private EscoOccupationGenerator escoOccupationGenerator;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private DataGeneratorService dataGeneratorService;

    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private CapacityGenerator capacityGenerator;

    @Autowired
    private BehaviorGenerator behaviorGenerator;

    @Autowired
    private JobActivityLabelGenerator jobActivityLabelGenerator;

    @Autowired
    private QuestionForContextsGenerator questionForContextsGenerator;

    @Autowired
    private UserExperienceGenerator userExperienceGenerator;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Transactional
    public void createFixtures() {
        SecurityContextHolder.clearContext(); // needed so we can manually `setCreatedBy`, else the last mocked user replaces it.
        JobGenerator.JOB_INDEX = 0L;
        UserExperienceGenerator.JOB_INDEX = 0;

        createCapacities();
        createActivities();
        createCategoriesAndContexts();
        createBehaviors();
        applicationContext.getBean(CriteriaMotherObject.class).forDriverLicence().buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class).withWorkingTimeCriteria().buildAndPersist();
        createOrganizationsAndJobs();
        createIscoOccupations();
        createRomeOccupations();
        createEscoOccupations();
        createErhgoOccupations();
        createUserProfiles();
        createRecruitmentProfile();
        createRecruitments();

        createCandidatures();

        createQuestionForContexts();
    }

    private void createQuestionForContexts() {
        var questionForContexts1 = QuestionForContextsGenerator.buildQuestionForContexts("Context question 1",
                CONTEXT_USED_IN_JOB_OF_FORMATION,
                CONTEXT_USED_IN_JOB_OF_FORMATION_WITH_CUSTOM_QUESTION);
        var questionForContexts2 = QuestionForContextsGenerator.buildQuestionForContexts("Context question 2",
                CONTEXT_USED_IN_JOB_WITH_QUESTION,
                CT_08, CT_04);
        var questionForContexts3 = QuestionForContextsGenerator.buildQuestionForContexts("Context question 3",
                CONTEXT_USED_IN_JOB_WITH_QUESTION,
                CONTEXT_USED_IN_JOB_OF_FORMATION,
                CONTEXT_USED_IN_JOB_OF_FORMATION_WITH_CUSTOM_QUESTION,
                CT_16);
        QUESTION_FOR_CONTEXTS_1 = questionForContextsGenerator.createQuestionForContexts(questionForContexts1);
        QUESTION_FOR_CONTEXTS_2 = questionForContextsGenerator.createQuestionForContexts(questionForContexts2);
        QUESTION_FOR_CONTEXTS_3 = questionForContextsGenerator.createQuestionForContexts(questionForContexts3);
    }


    private void createUserProfiles() {
        USER_CAPACITIES = Sets.newHashSet(CA1_01, CA1_12, CA2_02, CA3_05);

        USER_PROFILE_WITH_MATCHING_CANDIDATURE = dataGeneratorService.createUserProfile(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE, USER_PROFILE_UUID_WITH_MATCHING_CANDIDATURE, USER_CAPACITIES);
        var lyonLocation = Location.builder().city("Lyon").postcode("69001").citycode("69123").departmentCode("69").regionName("Auvergne-Rhône-Alpes").longitude((float) 4.835).latitude((float) 45.758).radiusInKm(20).build();
        var marseilleLocation = Location.builder().city("Marseille").postcode("13001").citycode("13055").departmentCode("13").regionName("Provence-Alpes-Côte d'Azur").longitude((float) 5.405).latitude((float) 43.282).build();
        var parisLocation = Location.builder().city("Paris").postcode("75001").citycode("75056").departmentCode("75").regionName("Île-de-France").longitude((float) 2.347).latitude((float) 48.859).build();

        dataGeneratorService
                .createGeneralInformation(
                        "06-07-08-09-10",
                        lyonLocation,
                        ContactTime.AFTERNOON,
                        LocalDate.of(1990, Month.JULY, 20),
                        USER_PROFILE_WITH_MATCHING_CANDIDATURE, null);
        USER_PROFILE_WITHOUT_EXPERIENCE = dataGeneratorService.createUserProfile(KEYCLOAK_USER_ID_WITHOUT_EXPERIENCE, USER_PROFILE_UUID_WITHOUT_EXPERIENCE, Sets.newHashSet());

        dataGeneratorService
                .createGeneralInformation(
                        "07-07-07-07-07",
                        marseilleLocation,
                        ContactTime.MORNING,
                        LocalDate.of(1995, Month.JULY, 1),
                        USER_PROFILE_WITHOUT_EXPERIENCE, null);

        USER_PROFILE_WITH_EMPTY_EXPERIENCE = dataGeneratorService.createUserProfile(KEYCLOAK_USER_ID_WITH_EMPTY_EXPERIENCE, USER_PROFILE_UUID_WITH_EMPTY_EXPERIENCE, Sets.newHashSet());
        USER_PROFILE_WITH_EMPTY_EXPERIENCE.updateJobOfferOptOut(true);

        dataGeneratorService
                .createGeneralInformation(
                        "08-08-08-08-08",
                        parisLocation,
                        ContactTime.EVENING,
                        LocalDate.of(2002, Month.MARCH, 20),
                        USER_PROFILE_WITH_EMPTY_EXPERIENCE, null);

        DEFAULT_USER_PROFILE = dataGeneratorService.createUserProfile(ADMIN_USER_ID, UUID.randomUUID(), Sets.newHashSet());

        dataGeneratorService
                .createGeneralInformation(
                        "09-09-09-09-09",
                        lyonLocation,
                        ContactTime.ALL_DAY,
                        LocalDate.of(1985, Month.OCTOBER, 25),
                        DEFAULT_USER_PROFILE, null);
    }

    private void createRecruitmentProfile() {
        P_01 = recruitmentProfileRepository.save(RecruitmentProfile.builder()
                .uuid(UUID.randomUUID())
                .job(J_01)
                .title("Junior")
                .qualified(true)
                .modifiable(false)
                .build());
        saveOptionalContextsAndActivityForProfile(P_01);
        P_01 = saveCustomContextLabelForProfile(P_01);

        MODIFIABLE_PROFILE_OF_JOB_1 = recruitmentProfileRepository.save(RecruitmentProfile.builder()
                .uuid(UUID.randomUUID())
                .job(J_01)
                .title("Senior A")
                .qualified(false)
                .modifiable(true)
                .build());
        saveOptionalContextsAndActivityForProfile(MODIFIABLE_PROFILE_OF_JOB_1);
        MODIFIABLE_PROFILE_OF_JOB_1 = saveCustomContextLabelForProfile(MODIFIABLE_PROFILE_OF_JOB_1);
        P_03 = RecruitmentProfile.builder()
                .uuid(UUID.randomUUID())
                .job(J_01)
                .title("Senior B")
                .qualified(true)
                .modifiable(true)
                .build();
        P_03.setOptionalContexts(singleton(OptionalContext.builder().id(UUID.randomUUID()).recruitmentProfile(P_03).context(MANDATORY_CONTEXT).acquisitionModality(AcquisitionModality.SELF_LEARNING).build()));
        P_03 = recruitmentProfileRepository.save(P_03);

        UNMODIFIABLE_PROFILE_WITH_QUESTION = recruitmentProfileRepository.save(RecruitmentProfile.builder()
                .uuid(UUID.randomUUID())
                .job(J_02)
                .title("Senior C")
                .qualified(false)
                .modifiable(false)
                .customQuestion(CUSTOM_QUESTION)
                .build());
    }

    private RecruitmentProfile saveCustomContextLabelForProfile(RecruitmentProfile profile) {

        var question = questionForContextsGenerator.createQuestionForContexts("Title for " + CUSTOM_CONTEXT_LABEL, CONTEXT_USED_IN_JOB_WITH_QUESTION);

        profile.setContextQuestions(Stream.of(CONTEXT_USED_IN_JOB_WITH_QUESTION).map(c -> ContextQuestion.builder()
                .question(question)
                .context(CONTEXT_USED_IN_JOB_WITH_QUESTION)
                .recruitmentProfile(profile)
                .build()).collect(Collectors.toSet()));
        return profile;
    }

    private void saveOptionalContextsAndActivityForProfile(RecruitmentProfile recruitmentProfile) {
        var optionalActivity = OptionalActivity.builder().id(UUID.randomUUID()).activityLabel(jobActivityLabelUsedOnlyInJobMission).acquisitionModality(AcquisitionModality.INTEGRATION_PROCESS).recruitmentProfile(recruitmentProfile).build();
        var optionalContext1 = OptionalContext.builder().id(UUID.randomUUID()).context(CT_02).recruitmentProfile(recruitmentProfile).acquisitionModality(AcquisitionModality.TRAINING).build();
        var optionalContext2 = OptionalContext.builder().id(UUID.randomUUID()).context(CONTEXT_USED_IN_JOB_OF_FORMATION).recruitmentProfile(recruitmentProfile).acquisitionModality(AcquisitionModality.TRAINING).build();
        optionalActivityRepository.save(optionalActivity);
        optionalContextRepository.save(optionalContext1);
        optionalContextRepository.save(optionalContext2);
    }

    private void createRecruitments() {

        RECRUITMENT_WITH_MATCHING_CANDIDATURE = createRecruitment("Test Recruitment", P_01);
        RECRUITMENT_WITHOUT_MATCHING_CANDIDATURES = createRecruitment("Test Recruitment without candidatures", P_01);

        RECRUITMENT_WITH_QUESTION_AND_CANDIDATURE = createRecruitment("Test Recruitment with question", UNMODIFIABLE_PROFILE_WITH_QUESTION);
    }

    public static final LocalDate recruitmentsPublicationDate = LocalDate.of(1992, 9, 7);

    private Recruitment createRecruitment(String title, RecruitmentProfile profile) {
        var recruitment = new Recruitment();
        recruitment.setManagerUserId("42");
        recruitment.setState(RecruitmentState.PUBLISHED, false);
        recruitment.setPublicationDate(Date.from(recruitmentsPublicationDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        recruitment.setRecruitmentProfile(profile);
        recruitment.setTitle(title);
        recruitment.setLocation(Location.builder()
                .city("Lyon")
                .postcode("69006")
                .citycode("69123")
                .departmentCode("69")
                .regionName("Auvergne-Rhône-Alpes")
                .latitude(45.758F)
                .longitude(4.835F)
                .build()
        );
        recruitment.setCreatedBy(USER_WITH_MATCHING_EXPERIENCE_FOR_CANDIDATURE);
        recruitment.setTypeContract(ContractType.CDI);

        recruitment = recruitmentRepository.save(recruitment);
        recruitment.updateCodeOnJobCreate();
        recruitment = recruitmentRepository.save(recruitment);

        return recruitment;
    }

    private void createCandidatures() {
        CANDIDATURE_NOT_MATCHING_FOR_USER_WITH_EMPTY_EXPERIENCE = dataGeneratorService.createNotMatchingCandidature(RECRUITMENT_WITH_MATCHING_CANDIDATURE, USER_PROFILE_WITH_EMPTY_EXPERIENCE);
        EXPERIENCE_EMPTY = userExperienceGenerator.createEmptyExperience(USER_PROFILE_WITH_MATCHING_CANDIDATURE);
        createCandidatureMatchingRecruitmentProfile();
        createCandidatureNoteOnMatchingCandidature();

        CANDIDATURE_NOT_MATCHING_FOR_USER_WITHOUT_EXPERIENCE = dataGeneratorService.createNotMatchingCandidature(RECRUITMENT_WITHOUT_MATCHING_CANDIDATURES, USER_PROFILE_WITHOUT_EXPERIENCE);

        CANDIDATURE_WITH_ANSWER_FOR_USER_PROFILE_WITH_EMPTY_EXPERIENCE = dataGeneratorService.createNotMatchingCandidature(RECRUITMENT_WITH_QUESTION_AND_CANDIDATURE, USER_PROFILE_WITH_EMPTY_EXPERIENCE, CUSTOM_ANSWER);
    }

    private void createCandidatureNoteOnMatchingCandidature() {
        var note = CandidatureNote.builder().uuid(UUID.randomUUID()).text("A useful note").candidature(CANDIDATURE_MATCHING).build();
        note.setLastModifiedBy(USER_WITH_MATCHING_EXPERIENCE_FOR_CANDIDATURE);
        candidatureNoteRepository.save(note);
    }


    private void createCandidatureMatchingRecruitmentProfile() {
        var candidature = new RecruitmentCandidature();
        candidature.setRecruitment(RECRUITMENT_WITH_MATCHING_CANDIDATURE);
        candidature.setUserProfile(USER_PROFILE_WITH_MATCHING_CANDIDATURE);
        CANDIDATURE_MATCHING = recruitmentCandidatureRepository.save(candidature);
        candidature.setCreatedBy(USER_WITH_MATCHING_EXPERIENCE_FOR_CANDIDATURE);
        candidature.setModifiedByUser(true);
        CANDIDATURE_MATCHING.updateCodeOnJobCreate();
        CANDIDATURE_MATCHING.setGlobalCandidatureState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO);
        var occupation = new ErhgoOccupationMotherObject()
                .withOptionalActivities(ACT_01, ACT_05, jobActivityLabelUsedInCandidatureExperienceAndJobMission)
                .withId(UUID.fromString("cb8a2922-6824-43d6-9c51-dad73d8cba11"))
                .withTitle("Occupation for xp")
                .withLevel(MasteryLevel.PROFESSIONAL)
                .instance();
        erhgoOccupationGenerator.createErhgoOccupation(occupation);

        var experience = UserExperienceGenerator.generateExperienceWithOccupation(
                USER_PROFILE_WITH_MATCHING_CANDIDATURE,
                occupation
        );


        // Why these contexts ? see createJobWithMissions method (we want here a candidature matching recruitment profile)
        candidature.getUserProfile().contextsMet().addAll(Stream.of(MANDATORY_CONTEXT, CONTEXT_USED_IN_JOB_OF_FORMATION, CONTEXT_USED_IN_JOB_WITH_QUESTION)
                .map(c -> JobContextMet.builder()
                        .userExperiences(Sets.newHashSet(experience))
                        .context(c)
                        .userProfile(candidature.getUserProfile())
                        .frequency(Frequency.HIGH)
                        .build())
                .collect(Collectors.toSet()));

        USER_EXPERIENCE_MATCHING_UUID = experience.getUuid();
        EXPERIENCE_MATCHING_CANDIDATURE = userExperienceRepository.save(experience);

        var otherExperience = UserExperienceGenerator.generateExperienceWithoutOccupation(USER_PROFILE_WITH_MATCHING_CANDIDATURE);

        userExperienceRepository.save(otherExperience);
        userProfileRepository.save(candidature.getUserProfile());

        CANDIDATURE_MATCHING.setValid(true);
        CANDIDATURE_MATCHING.setEffort(42);
        CANDIDATURE_MATCHING.setCandidatureRecruitmentState(CandidatureRecruitmentState.NEW);
        CANDIDATURE_MATCHING.setSubmissionDate(OffsetDateTime.now(ZONE_ID));
        CANDIDATURE_MATCHING.setState(CandidatureState.VALIDATED);

        CANDIDATURE_MATCHING = recruitmentCandidatureRepository.save(CANDIDATURE_MATCHING);
    }


    private void createOrganizationsAndJobs() {
        E_01_CERA = organizationGenerator.createRecruiter(E_01_CERA_CODE, "CERA", "", "", "", "", "116 Cours Lafayette, 69003 Lyon", 45.7632742, 4.8499019, AbstractOrganization.OrganizationType.ENTERPRISE);
        E_02_SOGILIS = organizationGenerator.createRecruiter(E_02_SOGILIS_CODE, "Sogilis Lyon", "", "", "", "", "289 rue Garibaldi, 69007 Lyon", 45.7507249, 4.8520455, AbstractOrganization.OrganizationType.TERRITORIAL);
        M_03_BOTANIC = organizationGenerator.createEmployer(M_03_BOTANIC_CODE, E_02_SOGILIS);

        J_01 = jobGenerator.createJobWithMissions("J-01", E_02_SOGILIS, "Developer Fullstack", "Web", "Lyon", "Pas de commentaire", Arrays.asList("Kevin"), JobEvaluationState.PUBLISHED);

        J_01.setEmployer(M_03_BOTANIC);
        var missions = J_01.getMissions().iterator();
        M_01 = missions.next();
        M_02 = missions.next();
        M_03 = missions.next();

        J_02 = jobGenerator.createJobWithMissions("J-02", E_02_SOGILIS, "Developer Backend", "Web", "Lyon", "Pas de commentaire", Arrays.asList("Kevin"), JobEvaluationState.PUBLISHED);
        JOB_MODIFIABLE = jobGenerator.createJobWithMissions("J-03", E_02_SOGILIS, "Developer Frontend", "Web", "Lyon", "Pas de commentaire", Arrays.asList("Kevin"), JobEvaluationState.MISSIONS_PROVIDED);
        missions = JOB_MODIFIABLE.getMissions().iterator();
        MISSION_MODIFIABLE_01 = missions.next();
        MISSION_MODIFIABLE_02 = missions.next();

    }

    private void createBehaviors() {
        B_01 = behaviorGenerator.createBehavior("B-01", "Etre avenant/souriant", "Faculté à sourire tout le temps", BehaviorCategory.CONSTANCY, 1);
        B_02 = behaviorGenerator.createBehavior("B-02", "Etre pédagogue", "", BehaviorCategory.PRAGMATISM, 2);
        B_03 = behaviorGenerator.createBehavior("B-03", "Etre résilient/capacité à rebondir", "", BehaviorCategory.SOCIABILITY, 1);
        B_04 = behaviorGenerator.createBehavior("B-04", "Gérer son stress", "Faculté à résister à la pression", BehaviorCategory.RIGOR, 3);
        B_05 = behaviorGenerator.createBehavior("B-05", "Fibre commerciale", "", BehaviorCategory.SOCIABILITY, 2);
        B_06 = behaviorGenerator.createBehavior("B-06", "Goût du challenge", "", BehaviorCategory.TENACITY, 2);
        B_07 = behaviorGenerator.createBehavior("B-07", "Aisance rédactionnelle", "", BehaviorCategory.HONESTY, 5);
        B_08 = behaviorGenerator.createBehavior("B-08", "Fort sens de l'organisation", "", BehaviorCategory.PRAGMATISM, 1);
        B_09 = behaviorGenerator.createBehavior("B-09", "Se concentrer", "", BehaviorCategory.HONESTY, 3);
        B_10 = behaviorGenerator.createBehavior("B-10", "Etre méthodique", "Avoir une méthode", BehaviorCategory.CONSTANCY, 2);
    }

    private void createCategoriesAndContexts() {
        categoryLevelGenerales = createNewCategoryLevel(3, "Générales", "Sans spécialisation, communes à tous dans la vie quotidienne");
        knowledgeCategory = createNewCategory("CCT-01", "Connaissances",
                "Permet de décrire ce qu’il faut savoir et comprendre pour réaliser le travail. Les connaissances s'acquièrent par l’étude, la pratique ou les recherches.",
                createNewCategoryLevel(0, "Non significatif", ""),
                categoryLevelGenerales,
                createNewCategoryLevel(9, "Techniques", "Spécialisation relative aux techniques, procédés et matériels utilisés dans le cadre de travail"),
                createNewCategoryLevel(15, "Scientifiques", ""));

        proGesturesAndAssociatedMaterialCategory = createNewCategory("CCT-02", "Gestes pros et matériel associé",
                "Le geste professionnel est un savoir-faire spécifique attaché à un corps de métier. Il peut être lié à l’utilisation de matériels dédiés.",
                createNewCategoryLevel(0, "Non significatif", ""),
                createNewCategoryLevel(3, "Génériques", "Geste qui peut être appliqué dans différents corps de métier. ( ex: gestes d’hygiène élémentaires qui sont communs à la restauration, au soin, à la vente de produits frais…"),
                createNewCategoryLevel(9, "Technique-métier", "Geste qui est lié à un métier ou à un domaine spécifique ( ex: usinage de pièces, utilisation d’un pont de levage… )"),
                createNewCategoryLevel(15, "Expert ou rares", "Nécessite un apprentissage long et une habileté particulière (savoir-faire artisanal, pratique artistique, chirurgie…)"));

        workConditionCategory = createNewCategory("CCT-03", "Rythme et conditions de travail",
                "Permet d’identifier l’intensité et les variations de cadence de l’activité décrite. La répétition des tâches exécutées est également concernée. On peut parler de pénibilité.",
                createNewCategoryLevel(0, "Normaux", "Rythme linéaire, horaires de jour, amplitude inférieure à 8 heures."),
                createNewCategoryLevel(2, "Soumis à variations", "Rythme cadencé (type 2/8, 3/8 ) pouvant intégrer des rotations et des horaires décalés."),
                createNewCategoryLevel(7, "Éprouvants", "Amplitude horaire importante, travail de nuit, encadrement de groupes lors de séjours, travail répétitif (chaîne de montage), inventaires, publics difficiles, emplois saisonniers"),
                createNewCategoryLevel(10, "Exceptionnels", "Mobilisation aléatoire et sur une amplitude non déterminée à l’avance. Ce niveau désigne une sollicitation urgente et aux enjeux majeurs. Configuration type astreinte, garde médicale ou urgences vétérinaires."));

        communicationModeCategory = createNewCategory("CCT-04", "Modes de communication",
                "Permet de décrire les types de communication (orale, écrite), leurs supports (face-à-face, électronique, téléphonique, filmé) et leur registre (familier, courant, soutenu) en fonction des types de public.",
                createNewCategoryLevel(0, "Non significatif", ""),
                createNewCategoryLevel(2, "Grand public", "Communication à destination de non-professionnels ou de professionnels d’autres secteurs d’activité, vulgarisation."),
                createNewCategoryLevel(7, "Professionnel", "Communication à destination de professionnels du secteur d’activité."),
                createNewCategoryLevel(10, "Scientifique", "Communication à destination d’experts et de chercheurs, n’acceptant aucune approximation."));

        modusOperandisCategory = createNewCategory("CCT-05", "Procédures et modes opératoires",
                "Permet de faire la distinction entre les instructions, les règles, et les combinaisons d’opérations attendues.",
                createNewCategoryLevel(0, "Non significatif", ""),
                createNewCategoryLevel(2, "Consignes/modes opératoires", "Instructions de travail, suite d’opérations à accomplir pour garantir l’obtention d’un résultat technique, mesurable et quantifiable"),
                createNewCategoryLevel(7, "Normes", "Ensemble des règles techniques, de critères définissant un type d’objet, un produit, un procédé. Il existe des normes nationales et internationales. Ex: Normes environnementales, normes ISO, normes de l’UE sur l’eau potable."),
                createNewCategoryLevel(10, "Processus/procédures", "Ensemble des opérations et fonctions associées. Combinaison de toutes les opérations et des ressources nécessaires pour atteindre un résultat.)\n" +
                        "Un processus peut regrouper un ensemble de modes opératoires et les coupler avec des normes : Processus de réception de paiement, Procédure de recrutement, Processus de production, qualité, sécurité…."));

        riskExposureCategory = createNewCategory("CCT-06", "Expositions aux risques",
                "Permet de décrire des événements exceptionnels et facteurs de risque au regard de leur probabilité et de leur dangerosité pour les personnes et pour les équipements.",
                createNewCategoryLevel(0, "Nulle", "Aucun risque significatif, environnement de travail très sécurisé"),
                createNewCategoryLevel(2, "Sans conséquence", "Pas ou très peu d’impact pour les biens ou les personnes, sans conséquence financière majeure notamment"),
                createNewCategoryLevel(7, "Impactant", "Pouvant nécessiter un arrêt de travail et/ou de production avec un impact financier pour l’entreprise"),
                createNewCategoryLevel(10, "Trés impactant", "Occasionnant des dommages corporels et matériels majeurs, avec risque vital et/ou de destruction."));

        associatedAuthorizationCategory = createNewCategory("CCT-07", "Habilitations et autorisations associés",
                "Permet de décrire les niveaux d’habilitations souhaités ou rendus obligatoires dans l’exercice de l’activité. ",
                createNewCategoryLevel(0, "Aucunes", "Aucun prérequis"),
                createNewCategoryLevel(2, "Habilitations/autorisations", "Nécessitant une formation, délivrée par l’employeur, et limitée au lieu où se déroule l’activité. Ex: CACES..."),
                createNewCategoryLevel(7, "Habilitations/certifiation", "Nécessitant une formation, délivrée par une instance de tutelle, et reconnue au niveau du métier, branche professionnelle ou au niveau national. Ex: CATEC, CPQ, TITRES PRO, Permis usuels, Diplômes..."),
                createNewCategoryLevel(10, "Permis spéciaux", "Liste définie permettant de décrire des habilitations au permis de conduire pour des véhicules exceptionnels ou nécessitant une formation spécifique. Ex: super lourd, tram, brevet de pilote..."));

        workspaceConfigurationCategory = createNewCategory("CCT-08", "Configurations du lieu de travail",
                "Permet de définir le ou les lieux où s’exerce habituellement l’activité. L’ampleur et/ou le nombre de sites permet d’apprécier l’impact sur le niveau de maitrise.",
                createNewCategoryLevel(0, "Non significatif", ""),
                createNewCategoryLevel(1, "Identifié et sédentaire", "Lieu unique pour l’activité, dans lequel il est facile de s’orienter rapidement"),
                createNewCategoryLevel(3, "Multi-sites et/ou trés ètendus", "Plusieurs sites pour l’activité (toujours au sein de la même entreprise) ou site vaste et complexe (site industriel par exemple)"),
                createNewCategoryLevel(5, "Déporté ou itinérant", "Interventions aléatoires sur des sites non connus à l’avance (dépannage par exemple), en fonction des sollicitations"));

        languageCategory = createNewCategory("CCT-09", "Langues",
                "Permet d’identifier la ou les langues mobilisées dans l’activité. Chaque langue utilisée peut l’être à un niveau d’exigence différent dans une même activité. La langue principale est évaluée, y compris s’il s’agit de la langue officielle (français). Dans le monde professionnel, la norme de maîtrise du français se situe généralement entre B1 et B2, selon les domaines d’activités. On se réfère ici à la classification européenne de maîtrise des langues.",
                createNewCategoryLevel(1, "A1", "Peut comprendre et utiliser des expressions familières et quotidiennes ainsi que des énoncés très simples qui visent à satisfaire des besoins concrets. Peut se présenter ou présenter quelqu'un et poser à une personne des questions la concernant - par exemple, sur son lieu d'habitation, ses relations, ce qui lui appartient, etc. - et peut répondre au même type de questions. Peut communiquer de façon simple si l'interlocuteur parle lentement et distinctement et se montre coopératif."),
                createNewCategoryLevel(3, "A2", "Peut comprendre des phrases isolées et des expressions fréquemment utilisées en relation avec des domaines immédiats de priorité (par exemple, informations personnelles et familiales simples, achats, environnement proche, travail). Peut communiquer lors de tâches simples et habituelles ne demandant qu'un échange d'informations simple et direct sur des sujets familiers et habituels. Peut décrire avec des moyens simples sa formation, son environnement immédiat et évoquer des sujets qui correspondent à des besoins immédiats."),
                createNewCategoryLevel(6, "B1", "Peut comprendre les points essentiels quand un langage clair et standard est utilisé et s'il s'agit de choses familières dans le travail, à l'école, dans les loisirs, etc. Peut se débrouiller dans la plupart des contexts rencontrées en voyage dans une région où la langue cible est parlée. Peut produire un discours simple et cohérent sur des sujets familiers et dans ses domaines d'intérêt. Peut raconter un événement, une expérience ou un rêve, décrire un espoir ou un but et exposer brièvement des raisons ou explications pour un projet ou une idée"),
                createNewCategoryLevel(9, "B2", "Peut comprendre le contenu essentiel de sujets concrets ou abstraits dans un texte complexe, y compris une discussion technique dans sa spécialité. Peut communiquer avec un degré de spontanéité et d'aisance tel qu'une conversation avec un locuteur natif ne comportant de tension ni pour l'un ni pour l'autre. Peut s'exprimer de façon claire et détaillée sur une grande gamme de sujets, émettre un avis sur un sujet d’actualité et exposer les avantages et les inconvénients de différentes possibilités."),
                createNewCategoryLevel(12, "C1", "Peut comprendre une grande gamme de textes longs et exigeants, ainsi que saisir des significations implicites. Peut s'exprimer spontanément et couramment sans trop apparemment devoir chercher ses mots. Peut utiliser la langue de façon efficace et souple dans sa vie sociale, professionnelle ou académique. Peut s'exprimer sur des sujets complexes de façon claire et bien structurée et manifester son contrôle des outils d'organisation, d'articulation et de cohésion du discours."),
                createNewCategoryLevel(15, "C2", "Peut comprendre sans effort pratiquement tout ce qu'il/elle lit ou entend. Peut restituer faits et arguments de diverses sources écrites et orales en les résumant de façon cohérente. Peut s'exprimer spontanément, très couramment et de façon précise et peut rendre distinctes de fines nuances de sens en rapport avec des sujets complexes."));

        MANDATORY_CONTEXT = createNewContext(1L, "Forte charge cognitive", "", knowledgeCategory.getLevels().first());
        CT_02 = createNewContext(2L, "Vocabulaire spécifique", "", knowledgeCategory.getLevels().first());
        CT_03 = createNewContext(3L, "Plan du site", "", knowledgeCategory.getLevels().first());
        CT_04 = createNewContext(4L, "Différentes lignes / différents services", "", knowledgeCategory.getLevels().first());
        CT_05 = createNewContext(5L, "Réaliser des tâches simultanément", "", proGesturesAndAssociatedMaterialCategory.getLevels().first());
        CT_06 = createNewContext(6L, "Apprendre en situation de travail", "", knowledgeCategory.getLevels().first());
        CT_07 = createNewContext(7L, "Pièces automobiles", "", proGesturesAndAssociatedMaterialCategory.getLevels().first());
        CT_08 = createNewContext(8L, "Tâches répétitives", "", proGesturesAndAssociatedMaterialCategory.getLevels().first());
        CT_09 = createNewContext(9L, "Motricité fine", "", proGesturesAndAssociatedMaterialCategory.getLevels().first());
        CT_10 = createNewContext(10L, "Etre debout", "", workConditionCategory.getLevels().first());
        CT_11 = createNewContext(11L, "Avoir de la résistance physique", "", workConditionCategory.getLevels().first());
        CT_12 = createNewContext(12L, "Bruit ambiant", "", workConditionCategory.getLevels().first());
        CT_13 = createNewContext(13L, "Horaires décalés", "", workConditionCategory.getLevels().first());
        CT_14 = createNewContext(14L, "Cadences de travail", "", workConditionCategory.getLevels().first());
        CT_15 = createNewContext(15L, "Poste de travail debout", "", workConditionCategory.getLevels().first());
        CT_16 = createNewContext(16L, "Présentiel", "", workConditionCategory.getLevels().first());
        CT_17 = createNewContext(17L, "Oral", "", communicationModeCategory.getLevels().first());
        CT_18 = createNewContext(18L, "Règles de sécurité (zones d'accès)", "", communicationModeCategory.getLevels().first());
        CONTEXT_USED_IN_JOB_OF_FORMATION = createNewContext(19L, "Règles de sécurité (règles de circulation)", "Ceci est une règle de sécurité", modusOperandisCategory.getLevels().first());
        CONTEXT_USED_IN_JOB_WITH_QUESTION = createNewContext(20L, "Règles de tri des déchets", "", modusOperandisCategory.getLevels().first());
        CT_21 = createNewContext(21L, "Règles de sécurité (utilisation de machines)", "", modusOperandisCategory.getLevels().first());
        CONTEXT_USED_IN_JOB_OF_FORMATION_WITH_CUSTOM_QUESTION = createNewContext(22L, "Règles de sécurité (ne pas fumer)", "", modusOperandisCategory.getLevels().first());
        CT_23 = createNewContext(23L, "Règles de sécurité (ne pas prendre de photo)", "", modusOperandisCategory.getLevels().first());
        CT_24 = createNewContext(24L, "Règles de sécurité (EPI: lunettes, chasuble, chaussures)", "", modusOperandisCategory.getLevels().first());
        CT_25 = createNewContext(25L, "Température des machines", "", modusOperandisCategory.getLevels().first());
        CT_26 = createNewContext(26L, "Permis pont-palan", "", riskExposureCategory.getLevels().first());
        CT_27 = createNewContext(27L, "Travailler en équipe", "", associatedAuthorizationCategory.getLevels().first());
        CT_28 = createNewContext(28L, "Secteur automobile", "", knowledgeCategory.getLevels().first());
        CT_29 = createNewContext(29L, "Français", "", languageCategory.getLevels().first());
        CT_30 = createNewContext(30L, "Chutes de pièces", "", riskExposureCategory.getLevels().first());

    }

    private Context createNewContext(Long index, String title, String description, CategoryLevel categoryLevel) {
        final var context = new Context();
        context.setId(UUID.randomUUID());
        context.setIndex(index);
        context.setTitle(title);
        context.setDescription(description);
        context.setCategoryLevel(categoryLevel);
        return contextRepository.save(context);
    }

    private Category createNewCategory(String code, String title, String description, CategoryLevel... categoriesWeighting) {
        final var category = new Category();
        category.setCode(code);
        category.setTitle(title);
        category.setDescription(description);
        categoryRepository.save(category);
        for (var categoryLevel : categoriesWeighting) {
            categoryLevel.setCategory(category);
            categoryLevelRepository.save(categoryLevel);
            category.getLevels().add(categoryLevel);
        }
        return categoryRepository.save(category);
    }

    private CategoryLevel createNewCategoryLevel(int score, String level, String levelDescription) {
        final var categoryLevel = new CategoryLevel();
        categoryLevel.setScore(score);
        categoryLevel.setTitle(level);
        categoryLevel.setDescription(levelDescription);
        return categoryLevelRepository.save(categoryLevel);
    }

    private void createCapacities() {
        CA1_01 = capacityGenerator.createCapacity("CA1-01", "Regarder avec attention", "capacité à observer son environnement en identifiant les informations pertinentes pour l'action en cours");
        CA1_02 = capacityGenerator.createCapacity("CA1-02", "Reconnaitre les formes", "capacité à identifier et comprendre les différents symboles de l'environnement en fonction de leur forme, leur couleur et leur graphisme");
        CA1_03 = capacityGenerator.createCapacity("CA1-03", "Lire", "capacité à déchiffrer un texte et à en comprendre le sens");
        CA1_04 = capacityGenerator.createCapacity("CA1-04", "Ecrire", "capacité à rédiger un message (manuscrit ou saisi) compréhensible");
        CA1_05 = capacityGenerator.createCapacity("CA1-05", "S'exprimer à l'oral", "capacité à se faire comprendre de son interlocuteur par la parole");
        CA1_06 = capacityGenerator.createCapacity("CA1-06", "Ecouter avec attention", "capacité à se concentrer sur un message oral ou une source sonore (bruit, son, musique) dans le but de le comprendre et l'analyser");
        CA1_07 = capacityGenerator.createCapacity("CA1-07", "Identifier un interlocuteur", "capacité à distinguer la ou les personnes pertinente(s) pour une situation donnée");
        CA1_08 = capacityGenerator.createCapacity("CA1-08", "Reconnaitre les chiffres et les nombres", "capacité à identifier et comprendre les différents signes numéraires");
        CA1_09 = capacityGenerator.createCapacity("CA1-09", "Compter/Dénombrer", "capacité à établir le nombre d'unités présentes dans un ensemble");
        CA1_10 = capacityGenerator.createCapacity("CA1-10", "Respecter les règles de vie collective", "capacité à intéragir avec d'autres individus en respectant les règles de civilité d'usage");
        CA1_11 = capacityGenerator.createCapacity("CA1-11", "Travailler en équipe", "capacité à collaborer avec d'autres individus dans un cadre de travail commun");
        CA1_12 = capacityGenerator.createCapacity("CA1-12", "Analyser (une situation, une information)", "capacité à décomposer une situation (ou une information) et ses éléments essentiels pour en saisir les liens et les implications afin de permettre une prise de décision");
        CA1_13 = capacityGenerator.createCapacity("CA1-13", "Trier (des objets, des informations)", "capacité à identifier différents éléments dans un ensemble, les distinguer, les organiser et les classer");
        CA1_14 = capacityGenerator.createCapacity("CA1-14", "Raisonner avec logique", "capacité à construire ou suivre une idée en suivant un cheminement logique");
        CA1_15 = capacityGenerator.createCapacity("CA1-15", "Se repérer dans l'espace", "capacité à positionner quelque chose ou quelqu'un dans un espace donné et à se représenter un itinéraire ");
        CA1_16 = capacityGenerator.createCapacity("CA1-16", "Décrire/formuler", "capacité à énoncer, représenter une observation, une idée, un problème de manière à la rendre compréhensible pour son interlocuteur");
        CA1_17 = capacityGenerator.createCapacity("CA1-17", "Rechercher une information", "capacité à s'organiser pour localiser et trouver des informations manquantes");
        CA1_18 = capacityGenerator.createCapacity("CA1-18", "Comparer", "capacité à établir des rapports de ressemblance,de différence ou d'écart entre deux éléments");
        CA1_19 = capacityGenerator.createCapacity("CA1-19", "Organiser/structurer", "capacité à préparer, hierarchiser et aménager son activité ou celle d'autrui");
        CA1_20 = capacityGenerator.createCapacity("CA1-20", "Manier avec habileté", "capacité à réaliser une action de précision en utilisant ses membres (mains, bras, pieds, jambes, tête)");
        CA1_21 = capacityGenerator.createCapacity("CA1-21", "Coordonner ses gestes", "capacité à réaliser un geste défini avec maitrise (vitesse, efficacité…)");
        CA1_22 = capacityGenerator.createCapacity("CA1-22", "Conserver l'équilibre", "capacité à agir en maintenant sa propre stabilité et celle des objets manipulés");
        CA1_23 = capacityGenerator.createCapacity("CA1-23", "Se déplacer dans l'espace", "capacité à se mouvoir dans son environnement en adaptant sa position et sa vitesse et en évitant contacts et chutes");
        CA1_24 = capacityGenerator.createCapacity("CA1-24", "Respecter une consigne, une méthode", "capacité à comprendre et appliquer une instruction dans le respect des règles établies en contexte");
        CA1_25 = capacityGenerator.createCapacity("CA1-25", "Concrétiser", "capacité à matérialiser une idée, un concept ou à rendre effective une solution imaginée pour une situation donnée, à rendre un projet réaliste et réalisable");
        CA1_26 = capacityGenerator.createCapacity("CA1-26", "Utiliser un ordinateur", "capacité à se servir des fonctionnalités de base d'un support informatique (PC, portable, tablette) : messagerie, texte, navigation internet");
        CA1_27 = capacityGenerator.createCapacity("CA1-27", "Contextualiser", "capacité à se rappeler d'un contexte, y replacer les événements évoqué par soi ou un tiers et en tenir compte");
        CA1_28 = capacityGenerator.createCapacity("CA1-28", "Dessiner/Schématiser", "capacité à représenter une idée, un concept ou une situation sous la forme d'un ensemble d'éléments liés, abstraits ou figuratifs");
        CA1_29 = capacityGenerator.createCapacity("CA1-29", "Comprendre son environnement de travail", "capacité à analyser de manière systémique son travail et l'ensemble des relations (internes, externes, clients, fournisseurs, marché…) le structurant ");

        CA2_01 = capacityGenerator.createCapacity("CA2-01", "Communiquer (des informations)", "capacité à faire passer un message clair à une ou plusieurs personnes en choisissant les moyens appropriés : voix, texte, geste, dessin", CA1_04, CA1_05, CA1_10, CA1_16, CA1_28);
        CA2_02 = capacityGenerator.createCapacity("CA2-02", "S'adapter à un interlocuteur", "capacité à modifier son niveau de langage, ses explications, ses questions, en fonction de la personne et du contexte de la conversation", CA1_05, CA1_06, CA1_07, CA1_27);
        CA2_03 = capacityGenerator.createCapacity("CA2-03", "Argumenter", "capacité à développer une opinion, une décision dans le but de convaincre ses interlocuteurs", CA1_04, CA1_05, CA1_13, CA1_16);
        CA2_04 = capacityGenerator.createCapacity("CA2-04", "Mettre en valeur", "capacité à réaliser une composition harmonieuse dans le but de valoriser un produit", CA1_01, CA1_02, CA1_19);
        CA2_05 = capacityGenerator.createCapacity("CA2-05", "Vérifier la compréhension/reformuler", "capacité à s'assurer que le message transmis a été compris par son interlocuteur et à reformuler si besoin", CA1_01, CA1_04, CA1_05, CA1_06, CA1_16);
        CA2_06 = capacityGenerator.createCapacity("CA2-06", "Suggérer/proposer", "capacité à proposer un conseil, un bien, un service adapté au besoin exprimé par son interlocuteur", CA1_04, CA1_05, CA1_06);
        CA2_07 = capacityGenerator.createCapacity("CA2-07", "Questionner", "capacité à obtenir des informations recherchées en posant les questions pertinentes", CA1_04, CA1_05, CA1_14, CA1_17);
        CA2_08 = capacityGenerator.createCapacity("CA2-08", "Estimer une mesure", "capacité à évaluer un poids, un volume, une taille, une quantité (…) en utilisant ses sens", CA1_01, CA1_08, CA1_09);
        CA2_09 = capacityGenerator.createCapacity("CA2-09", "Graduer/évaluer", "Capacité à utiliser un(e) critère/unité de mesure pertinent(e) pour évaluer quelque chose", CA1_09, CA1_13, CA1_14, CA1_18);
        CA2_10 = capacityGenerator.createCapacity("CA2-10", "Comprendre le sens d'un message", "capacité à percevoir le sens littéral ou caché d'un message en s'attachant à chaque mot", CA1_03, CA1_06, CA1_12);
        CA2_11 = capacityGenerator.createCapacity("CA2-11", "Evaluer sa production ou celle d'autrui", "capacité à examiner ses propres résultats ou ceux de quelqu'un d'autre pour en apprécier la quantité, la qualité", CA1_01, CA1_09, CA1_12, CA1_24);
        CA2_12 = capacityGenerator.createCapacity("CA2-12", "Calculer", "capacité à réaliser mentalement des opérations mathématiques", CA1_08, CA1_09, CA1_14);
        CA2_13 = capacityGenerator.createCapacity("CA2-13", "Vérifier une information", "capacité à s'assurer de l'exactitude/la véracité d'une information", CA1_12, CA1_13, CA1_17, CA1_18, CA1_27);
        CA2_14 = capacityGenerator.createCapacity("CA2-14", "Anticiper les mouvements autour de soi", "capacité à évaluer la position et le mouvement des objets et des personnes par rapport à soi", CA1_15, CA1_23);
        CA2_15 = capacityGenerator.createCapacity("CA2-15", "S'adapter/Réagir aux contexts", "capacité à adapter son comportement en fonction des contexts rencontrées (aux changements, à l'environnement, aux méthodes ou aux personnes) quand cela est approprié", CA1_01, CA1_12, CA1_27, CA1_29);
        CA2_16 = capacityGenerator.createCapacity("CA2-16", "Gérer des priorités", "capacité à établir un ordre dans les tâches à effectuer en fonction de leur importance et leur urgence", CA1_12, CA1_18, CA1_19);
        CA2_17 = capacityGenerator.createCapacity("CA2-17", "Prendre des décisions", "capacité à faire un choix en situation et à l'appliquer (ou le faire appliquer)", CA1_12, CA1_14, CA1_29);
        CA2_18 = capacityGenerator.createCapacity("CA2-18", "Donner une consigne", "capacité à donner une tâche à faire à une ou plusieurs personnes en expliquant la façon de procéder attendue", CA1_04, CA1_05, CA1_11, CA1_16, CA1_24);
        CA2_19 = capacityGenerator.createCapacity("CA2-19", "Anticiper/prévoir", "capacité à identifier l'évolution d'une action ou d'une situation avant sa réalisation", CA1_01, CA1_12, CA1_29);
        CA2_20 = capacityGenerator.createCapacity("CA2-20", "Mettre en forme un document", "capacité à organiser,classer, illustrer une production écrite informatique pour la rendre claire, compréhensible et agréable à lire", CA1_12, CA1_13, CA1_26);
        CA2_21 = capacityGenerator.createCapacity("CA2-21", "Réaliser une production personnelle", "capacité à formaliser une production (écrite, orale, graphique…) mobilisant des connaissances, une analyse de la situation et exprimant un point de vue personnel", CA1_04, CA1_05, CA1_12, CA1_16, CA1_25);
        CA2_22 = capacityGenerator.createCapacity("CA2-22", "Faire une recherche sur internet", "capacité à mobiliser les fonctionnalités d'internet pour localiser et trouver des informations et à en vérifier l'exactitude", CA1_03, CA1_04, CA1_17, CA1_26);
        CA2_23 = capacityGenerator.createCapacity("CA2-23", "Mémoriser", "capacité à retenir par la mémoire les connaissances, les expériences et à les remobiliser en situation", CA1_01, CA1_03, CA1_06, CA1_12);

        CA3_01 = capacityGenerator.createCapacity("CA3-01", "Synthétiser", "capacité à sélectionner et associer différentes informations pour en extraire le message principal", CA2_21, CA1_13, CA1_17, CA1_19);
        CA3_02 = capacityGenerator.createCapacity("CA3-02", "Conceptualiser", "capacité à créer et utiliser des concepts dans le cadre d'un raisonnement intellectuel", CA2_21, CA2_23, CA1_14);
        CA3_03 = capacityGenerator.createCapacity("CA3-03", "Prendre des initiatives", "capacité à proposer, entreprendre et réaliser des actions non prévues initialement", CA2_15, CA2_17, CA2_19);
        CA3_04 = capacityGenerator.createCapacity("CA3-04", "Optimiser", "capacité à identifier et mettre en oeuvre les actions nécessaires pour une meilleure efficacité, un meilleur rendement", CA2_11, CA1_14, CA1_25, CA1_29);
        CA3_05 = capacityGenerator.createCapacity("CA3-05", "Convaincre", "capacité à emporter l'adhésion et faire changer les comportements d'autrui avec persuasion", CA2_02, CA2_03, CA2_06);
        CA3_07 = capacityGenerator.createCapacity("CA3-07", "Influencer", "capacité à modifier les comportements d'autrui par son attitude, ses conseils", CA2_02, CA2_06, CA1_05, CA1_12);
        CA3_08 = capacityGenerator.createCapacity("CA3-08", "Transformer un usage", "capacité à imaginer une nouvelle utilisation d'un objet ou d'un contenu pour résoudre une situation donnée", CA2_15, CA1_25);
        CA3_09 = capacityGenerator.createCapacity("CA3-09", "Mettre en avant/Promouvoir", "capacité à mettre en lumière, à faire la promotion de quelque chose (produits, services, concepts…)", CA2_01, CA2_02);
        CA3_10 = capacityGenerator.createCapacity("CA3-10", "Représenter dans l'espace", "capacité à visualiser le volume et les dimensions d'un objet à partir de sa représentation en 2 dimensions et en perspective", CA2_08, CA1_02, CA1_15, CA1_28);
        CA3_11 = capacityGenerator.createCapacity("CA3-11", "Apprendre", "capacité à accumuler des connaissances et de l'expérience et en tirer les enseignements pertinents", CA2_07, CA2_10, CA2_13, CA2_23);
        CA3_12 = capacityGenerator.createCapacity("CA3-12", "Apprendre à apprendre", "capacité à formaliser des temps et des modalités d'apprentissages personnels", CA2_11, CA2_15, CA2_16, CA2_17);
        CA3_13 = capacityGenerator.createCapacity("CA3-13", "Analyser un besoin", "capacité à écouter, reformuler et répondre aux attentes d'un individu, qu'il soit physiquement présent ou non", CA2_01, CA2_02, CA1_12, CA1_27);
        CA3_14 = capacityGenerator.createCapacity("CA3-14", "Simplifier", "capacité à rendre accessible et compréhensible un contenu complexe à un public non spécialiste", CA2_01, CA2_02, CA2_05, CA2_15, CA1_12);
    }

    private void createActivities() {
        ACT_01 = jobActivityLabelGenerator.createNewActivity("Je traite des mails entrants", null, CA1_01);
        jobActivityLabelUsedOnlyInJobMission = jobActivityLabelGenerator.createNewActivity("J'assure le suivi des mails", "", CA1_02, CA1_03, CA1_04);
        ACT_03 = jobActivityLabelGenerator.createNewActivity("J'analyse des informations", "", CA1_01);
        ACT_04 = jobActivityLabelGenerator.createNewActivity("Je réponds à des demandes", "", CA1_01);
        ACT_05 = jobActivityLabelGenerator.createNewActivity("Je prépare un RDV Client", "", CA1_12, CA2_02);
        jobActivityLabelUsedInCandidatureExperienceAndJobMission = jobActivityLabelGenerator.createNewActivity("J'organise la logistique de mon rdv", "", CA3_05);
        secondActivityLabelSharingJobActivityUsedInUserExperience = jobActivityLabelGenerator.createSecondActivityLabelSharingSameActivity(jobActivityLabelUsedInCandidatureExperienceAndJobMission);
        ACT_07 = jobActivityLabelGenerator.createNewActivity("Je prends connaissance du dossier de mon client", "", CA1_01);
        ACT_08 = jobActivityLabelGenerator.createNewActivity("Je complète le document de synthèse de mon client", "", CA1_01);
        ACT_09 = jobActivityLabelGenerator.createNewActivity("Je mets à jour les informations d'un dossier client", "", CA1_01);
        ACT_10 = jobActivityLabelGenerator.createNewActivity("J'effectue un RDV client", "", CA1_01);
        ACT_11 = jobActivityLabelGenerator.createNewActivity("Je démarre un RDV Client ", "ça démarre ?", CA1_01);
        ACT_12 = jobActivityLabelGenerator.createNewActivity("Je fais le point sur la situation d'un client", "", CA1_01);
        jobActivityLabelUsedOnlyForEscoSkillClassification = jobActivityLabelGenerator.createNewActivity("Je réponds à des demandes", "", CA1_01);
        ACT_14 = jobActivityLabelGenerator.createNewActivity("Je réponds aux demandes client", "", CA1_01);
        ACT_15 = jobActivityLabelGenerator.createNewActivity("Je promeus une offre", "", CA1_01);
        ACT_16 = jobActivityLabelGenerator.createNewActivity("Je réalise un devis/chiffrage", "", CA1_01);
        ACT_17 = jobActivityLabelGenerator.createNewActivity("Je réponds à des demandes", "", CA1_01);
        ACT_18 = jobActivityLabelGenerator.createNewActivity("Je réponds aux questions", "", CA1_01);
        ACT_19 = jobActivityLabelGenerator.createNewActivity("Je conclue un rdv commercial", "", CA1_01);

        ACT_20 = jobActivityLabelGenerator.createNewActivity("Je prends en charge les appels entrants et/ou sortants.", "", CA1_01);
        ACT_21 = jobActivityLabelGenerator.createNewActivity("Je réalise des campagnes d'appels sortants", "", CA1_01);
        ACT_22 = jobActivityLabelGenerator.createNewActivity("Je prends en charge les appels entrants", "", CA1_01);
        ACT_23 = jobActivityLabelGenerator.createNewActivity("Je prends un appel téléphonique", "", CA1_01);
        JOB_ACTIVITY_1_USED_IN_JOB_OF_FORMATION = jobActivityLabelGenerator.createNewActivity("Je fais du traitement post-appel", "", CA1_01);
        ACT_25 = jobActivityLabelGenerator.createNewActivity("Je collecte et analyse des informations", "", CA1_01);
        ACT_26 = jobActivityLabelGenerator.createNewActivity("Je collecte des informations", "", CA1_01);
        unusedJobActivityLabel_1 = jobActivityLabelGenerator.createNewActivity("J'organise le traitement de l'information", "", CA1_01);
        unusedJobActivityLabel_2 = jobActivityLabelGenerator.createNewActivity("Je vérifie la conformité et réalise le suivi de mes dossiers", "", CA1_01);
        ACT_29 = jobActivityLabelGenerator.createNewActivity("J'effectue des recherches ", "", CA1_01);
        ACT_30 = jobActivityLabelGenerator.createNewActivity("J'applique un mode opératoire / une consigne", "", CA1_01);
        ACT_31 = jobActivityLabelGenerator.createNewActivity("Je suis un mode opératoire", "", CA1_01);
        ACT_32 = jobActivityLabelGenerator.createNewActivity("Je contrôle une production", "", CA1_01);
        ACT_33 = jobActivityLabelGenerator.createNewActivity("J'identifie une difficulté", "", CA1_01);
        ACT_34 = jobActivityLabelGenerator.createNewActivity("Je prends mon poste", "", CA1_01);
        ACT_35 = jobActivityLabelGenerator.createNewActivity("Je monte des pièces", "", CA1_01);
        ACT_36 = jobActivityLabelGenerator.createNewActivity("Je collecte des pièces", "", CA1_01);
        ACT_37 = jobActivityLabelGenerator.createNewActivity("Je contrôle mon travail", "", CA1_01);
        ACT_38 = jobActivityLabelGenerator.createNewActivity("Je prends des consignes", "", CA1_01);
        ACT_39 = jobActivityLabelGenerator.createNewActivity("J'organise mon espace", "", CA1_01);

        ACT_40 = jobActivityLabelGenerator.createNewActivity("Je me déplace dans un environnement complexe", "", CA1_01);
        ACT_41 = jobActivityLabelGenerator.createNewActivity("Je surveille l'état des stocks", "", CA1_01);
        ACT_42 = jobActivityLabelGenerator.createNewActivity("Je manipule des petits objets", "", CA1_01);
        ACT_43 = jobActivityLabelGenerator.createNewActivity("Je monte / assemble", "", CA1_01);
        ACT_44 = jobActivityLabelGenerator.createNewActivity("Je contrôle une production", "", CA1_01);
        ACT_45 = jobActivityLabelGenerator.createNewActivity("Je fais remonter des informations", "", CA1_01);
        ACT_46 = jobActivityLabelGenerator.createNewActivity("J'adapte mon rythme de travail", "", CA1_01);
        ACT_47 = jobActivityLabelGenerator.createNewActivity("Je motive une personne", "", CA2_01, CA2_02);
        ACT_48 = jobActivityLabelGenerator.createNewActivity("Gérer un conflit", "", CA2_01, CA2_02, CA2_05, CA2_15, CA1_12);
        ACT_49 = jobActivityLabelGenerator.createNewActivity("Réaliser des actions simultanément", "", CA1_01);
        ACT_50 = jobActivityLabelGenerator.createNewActivity("Gérer le temps", "", CA2_08, CA2_19, CA1_19);
        ACT_51 = jobActivityLabelGenerator.createNewActivity("Répartir les tâches", "", CA2_18, CA1_19, CA1_29);
        JOB_ACTIVITY_2_USED_IN_JOB_OF_FORMATION = jobActivityLabelGenerator.createNewActivity("Suivre la réalisation d'une activité", "", CA2_11, CA2_19, CA1_01, CA1_12);
        ACT_53 = jobActivityLabelGenerator.createNewActivity("Valoriser le travail d'autrui", "", CA2_01, CA2_11);
        ACT_54 = jobActivityLabelGenerator.createNewActivity("Fixer un objectif", "", CA2_02, CA2_19, CA1_16, CA1_19);
        ACT_55 = jobActivityLabelGenerator.createNewActivity("Transmettre/enseigner", "", CA2_01, CA2_02, CA2_05, CA1_05, CA1_04);
        ACT_56 = jobActivityLabelGenerator.createNewActivity("Formaliser un dispositif d'apprentissage", "", CA2_21, CA1_25, CA1_12, CA1_19);
    }


    private void createIscoOccupations() {

        ISCO_666 = escoOccupationGenerator.createIscoOccupation(666, "Title for ISCO 666");
        ISCO_777 = escoOccupationGenerator.createIscoOccupation(777, "Title for ISCO 777");
        ISCO_888 = escoOccupationGenerator.createIscoOccupation(888, "Title for ISCO 888");
        ISCO_999 = escoOccupationGenerator.createIscoOccupation(999, "Title for ISCO 999");
    }

    private void createRomeOccupations() {
        R1 = erhgoOccupationGenerator.createRomeOccupation("R1", "Title for ROME R1");
        R2 = erhgoOccupationGenerator.createRomeOccupation("R2", "Title for ROME R2");
        R3 = erhgoOccupationGenerator.createRomeOccupation("R3", "Title for ROME R3");
        R4 = erhgoOccupationGenerator.createRomeOccupation("R4", "Title for ROME R4");
        R3.getAccessibleRomeOccupations().add(R1);
        R2.getAccessibleRomeOccupations().add(R4);
        R4.getAccessibleRomeOccupations().add(R1);
        R4.getAccessibleRomeOccupations().add(R2);
    }

    private void createErhgoOccupations() {
        ERHGO_OCCUPATION_WITHOUT_ROMES = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withTitle("Title for ERHGO_OCCUPATION_WITHOUT_ROMES")
                .withLevel(MasteryLevel.PROFESSIONAL)
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_REFERENTIAL_DATA)
                .withId(ERHGO_OCCUPATION_WITHOUT_ROMES_UUID)
                .computeState()
                .instance()
        );
        ERHGO_OCCUPATION_WITH_ROMES = erhgoOccupationGenerator.createErhgoOccupation("Title for ERHGO_OCCUPATION_WITH_ROMES",
                MasteryLevel.TECHNICAL,
                false,
                Sets.newHashSet(ESCO_OCCUPATION_WITH_2_QUALIED_SKILLS_IN_4),
                ESCO_OCCUPATION_WITH_2_QUALIED_SKILLS_IN_4.getSkills(),
                R3, R4);
        ERHGO_OCCUPATION_QUALIFIED = erhgoOccupationGenerator.createErhgoOccupation("Title for ERHGO_OCCUPATION_QUALIFIED",
                MasteryLevel.STRATEGIC,
                true,
                Sets.newHashSet(ESCO_OCCUPATION_WITHOUT_REFERENTIAL_DATA, ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6, ESCO_OCCUPATION_WITH_2_QUALIED_SKILLS_IN_4),
                ESCO_OCCUPATION_WITH_REFERENTIAL_DATA.getSkills()
        );
        ERHGO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6 = erhgoOccupationGenerator.createErhgoOccupation("Title for ERHGO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6",
                MasteryLevel.STRATEGIC,
                true,
                Sets.newHashSet(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6),
                ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6.getSkills()
        );
    }

    private void createEscoOccupations() {

        ESCO_OCCUPATION_WITH_REFERENTIAL_DATA = escoOccupationGenerator.createEscoOccupationWithReferentialData(
                "title for ESCO_OCCUPATION_WITH_REFERENTIAL_DATA/ other title remove for job creation",
                "http://data.odas.app",
                ISCO_666,
                jobActivityLabelUsedOnlyForEscoSkillClassification,
                MasteryLevel.EXPERT, B_05,
                CT_21, CT_29);

        ESCO_OCCUPATION_WITHOUT_REFERENTIAL_DATA = escoOccupationGenerator.createEscoOccupationWithoutReferentialData("http://nodata.odas.app", ISCO_777);

        ESCO_OCCUPATION_WITH_2_QUALIED_SKILLS_IN_4 = escoOccupationGenerator.createEscoOccupationForUriIscoGroupAndSkills("http://2data.odas.app",
                ISCO_888,
                null, escoOccupationGenerator.buildSkillWithoutReferentialData("Skill qualified 1"),
                escoOccupationGenerator.buildSkillWithoutReferentialData("Skill qualified 2"),
                escoOccupationGenerator.buildNotQualifiedSkill("Skill not qualified 1"),
                escoOccupationGenerator.buildNotQualifiedSkill("Skill not qualified 2"));

        ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6 = escoOccupationGenerator.createEscoOccupationForUriIscoGroupAndSkills("http://3data.odas.app",
                ISCO_999,
                null, escoOccupationGenerator.buildSkillWithoutReferentialData("a skill qualified 3"),
                escoOccupationGenerator.buildSkillWithoutReferentialData("Â Skill qualified 4"),
                escoOccupationGenerator.buildSkillWithoutReferentialData("à Skill qualified 5"),
                escoOccupationGenerator.buildNotQualifiedSkill("à Skill not qualified 3"),
                escoOccupationGenerator.buildNotQualifiedSkill("Ä Skill not qualified 4"),
                escoOccupationGenerator.buildNotQualifiedSkill("a Skill not qualified 5"));

    }

}
