package com.erhgo.generators;

import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.isco.IscoOccupation;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.repositories.classifications.*;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Set;
import java.util.UUID;

import static com.google.common.collect.Sets.newHashSet;

@Service
public class EscoOccupationGenerator {

    @Autowired
    private EscoOccupationRepository escoOccupationRepository;

    @Autowired
    private IscoOccupationRepository iscoOccupationRepository;

    public EscoOccupation createEscoOccupationWithReferentialData(String title, String url, IscoOccupation iscoOccupation, JobActivityLabel jobActivityLabel, MasteryLevel level, Behavior behavior, Context... contexts) {

        return escoOccupationRepository.save(EscoOccupation.builder()
                .iscoOccupation(iscoOccupation)
                .skills(Sets.newHashSet(buildSkillWithReferentialData("SK_0 " + url, jobActivityLabel, behavior, contexts),
                        buildSkillWithReferentialData("SK_1 " + url, jobActivityLabel, behavior, contexts),
                        buildSkillWithoutReferentialData("SK_2 " + url)))
                .uri(url)
                .level(level)
                .descriptionEN("Description EN of " + title)
                .descriptionFR("Description FR of " + title)
                .alternativeLabels(Sets.newHashSet("Alt 1 " + title, "Alt 2 " + title))
                .title(title)
                .build());
    }

    public EscoOccupation createEscoOccupationWithoutReferentialData(String url, IscoOccupation iscoOccupation) {
        return  createEscoOccupationWithoutReferentialDataLevelAndOccupation(url, MasteryLevel.STRATEGIC, iscoOccupation);
    }

    public static EscoSkill buildNotQualifiedSkill(String title) {
        return EscoSkill.builder()
                .title(title)
                .uri("URI for " + title)
                .build();
    }

    public static EscoSkill buildSkillWithReferentialData(String title, JobActivityLabel jobActivityLabel, Behavior behavior, Context... contexts) {
        return EscoSkill.builder()
                .activities(Sets.newHashSet(jobActivityLabel))
                .contexts(newHashSet(contexts))
                .behaviors(behavior == null ? Collections.emptySet() : newHashSet(behavior))
                .title(title)
                .uri("URI for " + title)
                .build();
    }

    public static EscoSkill buildSkillWithoutReferentialData(String title) {
        return EscoSkill.builder()
                .title(title)
                .noActivity(true)
                .noContext(true)
                .noBehavior(true)
                .uri("URI for " + title)
                .build();
    }

    public static EscoSkill buildQualifiedSkill() {
        return buildSkillWithoutReferentialData(UUID.randomUUID().toString());
    }

    @Transactional
    public EscoOccupation createEscoOccupationForUriIscoGroupAndSkills(String uri, IscoOccupation iscoOccupation, MasteryLevel level, EscoSkill... skills) {
        return escoOccupationRepository.save(EscoOccupation.builder().title("Title of esco occupation " + uri)
                .uri(uri)
                .level(level)
                .iscoOccupation(iscoOccupation)
                .skills(Set.of(skills))
                .build());
    }

    public IscoOccupation createIscoOccupation(int group, String title) {
        return iscoOccupationRepository.save(IscoOccupation
                .builder()
                .iscoGroup(group)
                .title(title)
                .build());
    }

    public EscoOccupation createEscoOccupationWithoutReferentialDataLevelAndOccupation(String url, MasteryLevel level, IscoOccupation iscoOccupation) {
        return escoOccupationRepository.save(EscoOccupation.builder()
                .iscoOccupation(iscoOccupation)
                .skills(Sets.newHashSet(buildSkillWithoutReferentialData("SK_3 " + url), buildNotQualifiedSkill("SK_4")))
                .uri(url)
                .level(level)
                .title("title for " + url.replaceAll("//", ""))
                .build());
    }


    public EscoOccupation createEscoOccupation(EscoOccupation escoOccupation) {
        return escoOccupationRepository.save(escoOccupation);
    }
}
