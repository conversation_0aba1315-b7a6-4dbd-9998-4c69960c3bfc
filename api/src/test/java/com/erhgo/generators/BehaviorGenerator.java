package com.erhgo.generators;

import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.repositories.BehaviorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class BehaviorGenerator {

    @Autowired
    private BehaviorRepository behaviorRepository;

    private int index = 100;

    public static Behavior buildBehavior(String code, String title, String description, BehaviorCategory behaviorCategory, Integer categoryIndex) {
        final var behavior = new Behavior();
        behavior.setId(UUID.randomUUID());
        behavior.setCode(code);
        behavior.setTitle(title);
        behavior.setDescription(description);
        behavior.setCategoryIndex(categoryIndex);
        behavior.setBehaviorCategory(behaviorCategory);
        return behavior;
    }

    public static Behavior buildBehavior(String code, BehaviorCategory behaviorCategory) {
        return buildBehavior(code, "title for " + code, "description for " + code, behaviorCategory, 1);
    }

    public Behavior createBehavior(String code, String title, String description, BehaviorCategory behaviorCategory, Integer categoryIndex) {
        return behaviorRepository.save(buildBehavior(code, title, description, behaviorCategory, categoryIndex));
    }

    public Behavior createBehaviorForCategory(BehaviorCategory category) {
        return createBehaviorForCategory(category, index++);
    }

    public Behavior createBehaviorForCategory(BehaviorCategory category, int index) {
        var code = "BE-" + index;
        return createBehavior(code, "Title of " + code, "Descr of " + code, category, index);
    }
}
