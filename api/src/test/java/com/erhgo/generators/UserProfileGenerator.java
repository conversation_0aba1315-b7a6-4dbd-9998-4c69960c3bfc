package com.erhgo.generators;

import com.erhgo.TransactionTestHelper;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.ContactTime;
import com.erhgo.domain.enums.DriverLicence;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.CapacityRelatedQuestionMotherObject;
import com.erhgo.domain.userprofile.*;
import com.erhgo.domain.userprofile.capacityrelatedquestion.AnswerForCapacityRelatedQuestion;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.repositories.*;
import com.erhgo.security.Role;
import com.erhgo.services.keycloak.UserRepresentation;
import com.github.javafaker.Faker;
import com.google.common.collect.Sets;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class UserProfileGenerator {

    private final UserProfileRepository userProfileRepository;
    private final KeycloakMockService keycloakMockService;
    private final GeneralInformationRepository generalInformationRepository;
    private final UserExperienceGenerator userExperienceGenerator;
    private final CapacityRelatedQuestionRepository questionRepository;
    private final AnswerForCapacityRelatedQuestionRepository answerRepository;
    private final TransactionTestHelper transactionTestHelper;
    private final CapacityRepository capacityRepository;
    private final CriteriaRepository criteriaRepository;
    private final EntityManager entityManager;

    private Faker faker = new Faker();

    public static UserProfile buildEmptyUserProfile(String... roleAndChannels) {
        return buildEmptyUserProfile(UUID.randomUUID(), roleAndChannels);
    }

    public static UserProfile buildEmptyUserProfile(UUID uuid, String... roleAndChannels) {
        var userProfile = new UserProfile()
                .uuid(uuid)
                .mailConfiguration(MailConfiguration.builder().build())
                .userId(uuid.toString());

        var channels = Stream.of(roleAndChannels)
                .filter(Role::isOrganizationRoleOrGroup)
                .collect(Collectors.toSet());

        if (!channels.isEmpty()) {
            userProfile.updatedChannels(channels, UserChannel.ChannelSourceType.LANDING_PAGE);
        }

        return userProfile;
    }

    public static UserProfile buildUserProfileWithCapacities(Capacity... capacities) {
        var userProfile = buildEmptyUserProfile();
        userProfile.addCapacities(Set.of(capacities));
        return userProfile;
    }

    public static UserProfile buildUserProfileWithCapacitiesAndLevels(List<Capacity> capacities, List<Integer> levels) {
        var userProfile = buildEmptyUserProfile();
        IntStream
                .range(0, capacities.size())
                .forEach(i -> UserExperienceGenerator.buildExperienceWithCapacity(userProfile, levels.get(i), capacities.get(i)));
        return userProfile;
    }

    public UserProfile createUserProfile() {
        return this.createUserProfile(UUID.randomUUID());
    }

    @Transactional
    public UserProfile createUserProfileWithTransactionalBlacklisted(boolean blackListed) {
        return createUserProfile().updateJobOfferOptOut(blackListed);
    }

    @Transactional
    public UserProfile createUserProfileWithSmsBlacklisted() {
        var profile = createUserProfile();
        profile.generalInformation().setSmsBlacklisted(true);
        return profile;
    }

    @Transactional
    public UserProfile createUserProfile(UUID uuid, String... channels) {
        return createUserProfileWithGeneralInfos(uuid, null, channels);
    }

    public UserProfile createUserProfileWithGeneralInfos(UUID uuid, GeneralInformation generalInformation, String... channels) {
        var userProfile = userProfileRepository.save(buildEmptyUserProfile(uuid, channels));

        var userRepresentation = new UserRepresentation();
        userRepresentation.setFirstName("Jean-" + userProfile.userId());
        userRepresentation.setLastName("Dupont Du " + userProfile.userId());
        userRepresentation.setEmail(userProfile.userId() + "@erhgo.fr");
        userRepresentation.setId(userProfile.userId());

        var rolesAndGroups = (channels == null || channels.length == 0 || channels[0] == null) ? Collections.<String>emptyList() : Lists.newArrayList(channels);
        userRepresentation.setGroups(rolesAndGroups);
        userRepresentation.setId(userProfile.userId());

        keycloakMockService.setUserProfile(userProfile.userId(), userRepresentation);

        if (generalInformation == null) {
            generalInformation = GeneralInformation.builder()
                    .userProfile(userProfile)
                    .userId(userProfile.userId())
                    .phoneNumber(faker.phoneNumber().phoneNumber())
                    .contactTime(ContactTime.AFTERNOON)
                    .build();
        } else {
            generalInformation.setUserProfile(userProfile);
        }
        return userProfile.generalInformation(generalInformationRepository.save(generalInformation));
    }

    @Transactional
    public UserProfile createUserProfileWithRoleAndCapacities(UUID uuid, int level, String role, Capacity... matchingCapacities) {
        return createUserProfileWithChannelsAndCapacities(uuid, level, role == null ? null : Collections.singleton(role), matchingCapacities);
    }

    @Transactional
    public UserProfile createUserProfileWithChannelsAndCapacities(UUID uuid, int level, Collection<String> roles, Capacity... matchingCapacities) {

        var userProfile = createUserProfile(uuid, roles == null ? new String[]{} : roles.toArray(String[]::new));
        userExperienceGenerator.createExperienceWithCapacities(userProfile, level, matchingCapacities);
        return userProfile;

    }

    @Transactional
    public UserProfile createUserProfileWithChannelsAndCapacitiesAndGeneralInfos(UUID uuid, int level, Collection<String> roles, GeneralInformation generalInformation, Capacity... matchingCapacities) {
        var userProfile = createUserProfileWithGeneralInfos(uuid, generalInformation, roles == null ? new String[]{} : roles.toArray(String[]::new));
        userExperienceGenerator.createExperienceWithCapacities(userProfile, level, matchingCapacities);
        return userProfile;

    }

    @Transactional
    public UserProfile createUserProfileWithCapacities(Integer level, Capacity... matchingCapacities) {
        return this.createUserProfileWithCapacities(UUID.randomUUID(), level, matchingCapacities);
    }

    @Transactional
    public UserProfile createUserProfileWithCapacities(UUID uuid, Integer level, Capacity... matchingCapacities) {
        return createUserProfileWithRoleAndCapacities(uuid, level, null, matchingCapacities);
    }

    @Transactional
    public UserProfile createUserProfileWithLevelAndCapacities(Integer[] levels, List<Capacity>... matchingCapacitiesForLevels) {
        if (levels.length != matchingCapacitiesForLevels.length) {
            Assert.fail();
        }
        UserProfile userProfile = createUserProfile();
        IntStream.range(0, levels.length).forEach(index -> userExperienceGenerator.createExperienceWithCapacities(userProfile, levels[index], matchingCapacitiesForLevels[index].toArray(new Capacity[]{})));
        return userProfile;
    }

    @Transactional
    public UserProfile createUserProfileWithLevelAndCapacitiesAndCriteria(Integer[] levels, List<CriteriaValue> selectedCriteriaValues, List<CriteriaValue> unselectedCriteriaValues, List<Capacity>... matchingCapacitiesForLevels) {
        if (levels.length != matchingCapacitiesForLevels.length) {
            Assert.fail();
        }
        UserProfile user = createUserProfile();
        IntStream.range(0, levels.length).forEach(index -> userExperienceGenerator.createExperienceWithCapacities(user, levels[index], matchingCapacitiesForLevels[index].toArray(new Capacity[]{})));

        user.userCriteriaValues().addAll(selectedCriteriaValues.stream().map(CriteriaValue::getCode).map(c -> entityManager.find(CriteriaValue.class, c)).map(c -> new UserCriteriaValue(user, c, true)).toList());
        user.userCriteriaValues().addAll(unselectedCriteriaValues.stream().map(CriteriaValue::getCode).map(c -> entityManager.find(CriteriaValue.class, c)).map(c -> new UserCriteriaValue(user, c, false)).toList());

        return user;
    }

    @Transactional
    public UserProfile createUserProfileWithLocation(String userId, Location location) {
        var userProfile = createUserProfile(UUID.fromString(userId));
        userProfile.generalInformation().setLocation(location);
        return userProfile;
    }

    @Transactional
    public UserProfile createUserProfileWithLocationAndCapacities(String userId, Location location, Capacity... capacities) {
        var userProfile = createUserProfileWithCapacities(UUID.fromString(userId), MasteryLevel.MIN_LEVEL.getMasteryLevel(), capacities);
        userProfile.generalInformation().setLocation(location);
        return userProfile;
    }

    @Transactional
    public UserProfile createUserProfileForUserLocationAndPhone(String userId, String postcode, String location, String phoneNumber, String... channels) {
        var user = createUserProfile(UUID.fromString(userId), channels);
        user.generalInformation().setLocation(Location.builder().postcode(postcode).city(location).build());
        user.generalInformation().setPhoneNumber(phoneNumber);
        user.lastConnectionDate(LocalDateTime.of(2018, 12, 18, 21, 52));
        return user;
    }

    @Transactional
    public UserProfile createUserProfileForUserLocationAndPhoneAndSelectedOccupation(String userId, String postcode, String location, String phoneNumber, ErhgoOccupation occupation, String... channels) {
        var user = createUserProfile(UUID.fromString(userId), channels);
        user.generalInformation().setLocation(Location.builder().city(location).postcode(postcode).build());
        user.generalInformation().setPhoneNumber(phoneNumber);
        user.defineUserExperience(occupation, occupation.getTitle());
        user.lastConnectionDate(LocalDateTime.of(2012, 12, 18, 21, 52));
        return user;
    }

    public UserProfile createUserProfileWithCapacitiesFromEPA(UUID userId, Capacity... capacities) {
        var userProfile = createUserProfile(userId);

        Stream.of(capacities).forEach(c -> addCapacityFromEPA(userProfile, c));

        return userProfile;
    }

    private void addCapacityFromEPA(UserProfile userProfile, Capacity capacity) {
        var question = new CapacityRelatedQuestionMotherObject()
                .withAnswerForCapacity(capacity)
                .instance();
        questionRepository.save(question);
        answerRepository.save(AnswerForCapacityRelatedQuestion.builder()
                .userProfile(userProfile)
                .response(question.getResponses().first())
                .build());
        transactionTestHelper.doInTransaction(() -> fixUserProfileCapacities(userProfile, capacity));

    }

    public static UserProfile buildUserProfileWithCapacityFromEPA(Capacity capacity) {
        var userProfile = buildEmptyUserProfile();

        var question = new CapacityRelatedQuestionMotherObject()
                .withAnswerForCapacity(capacity)
                .instance();
        var answer = AnswerForCapacityRelatedQuestion.builder()
                .userProfile(userProfile)
                .response(question.getResponses().first())
                .build();

        userProfile.answerForCapacityRelatedQuestions(Set.of(answer));

        return userProfile;
    }

    @Transactional
    public UserProfile createUserProfileWithCapacitiesFromEPAAndExperience(UUID uuid, Capacity matchingCapacity1, Capacity matchingCapacity2) {
        var userProfile = createUserProfileWithCapacities(uuid, MasteryLevel.MIN_LEVEL.getMasteryLevel(), matchingCapacity2);
        addCapacityFromEPA(userProfile, matchingCapacity1);
        return userProfile;

    }

    private void fixUserProfileCapacities(UserProfile userProfile, Capacity... capacities) {
        transactionTestHelper.doInTransaction(() -> {
            userProfileRepository.findByUserId(userProfile.userId())
                    .orElseThrow()
                    .addCapacities(Sets.newHashSet(capacityRepository.findAllById(Stream.of(capacities).map(Capacity::getId).toList())));
        });
    }

    @Transactional
    public UserProfile createUserProfileWithCapacitiesAndLevel1dot5(UUID uuid, List<Capacity> capacities, String... roles) {
        var userProfile = createUserProfile(uuid, roles);
        IntStream.range(0, capacities.size()).forEach(i -> {
            var capacity = capacities.get(i);
            if (i % 2 == 1) {
                userExperienceGenerator.createExperienceWithCapacities(userProfile, 2, capacity);
            } else {
                userExperienceGenerator.createExperienceWithCapacities(userProfile, 1, capacity);
            }
        });
        return userProfile;
    }

    @Transactional
    public void prepareCriteria(UserProfile userProfile, DriverLicence driverLicence) {
        var selectedDriverLicenceCriteriaCode = CriteriaValue.getValueCodeForDriverLicence(DriverLicence.valueOf(driverLicence.name()));
        var selectedAnswers = new HashSet<>(criteriaRepository.findCriteriaValuesByCriteriaValuesCodeIn(Set.of(selectedDriverLicenceCriteriaCode)));
        userProfile.updateAnswerToCriteria(selectedAnswers, Collections.emptySet());
    }

    @Transactional
    public UserProfile createUserProfileWithoutInformation(UserRepresentation userWithoutInformation) {
        return userProfileRepository.save(buildEmptyUserProfile(UUID.fromString(userWithoutInformation.getId())));
    }


    @Transactional
    public UserProfile createUserProfileWithCapacitiesAndCriteriaValues(UUID uuid, int level, List<CriteriaValue> selectedCriteriaValues, List<CriteriaValue> unselectedCriteriaValues, Capacity... capacities) {
        var user = createUserProfileWithCapacities(uuid, level, capacities);
        user.userCriteriaValues().addAll(selectedCriteriaValues.stream().map(c -> new UserCriteriaValue(user, c, true)).toList());
        user.userCriteriaValues().addAll(unselectedCriteriaValues.stream().map(c -> new UserCriteriaValue(user, c, false)).toList());
        return user;
    }


    @Transactional
    public UserProfile createUserProfileWithChannelLevelAndCapacities(String[] privateChannels, Integer[] levels, List<Capacity>... capacities) {
        var user = createUserProfileWithLevelAndCapacities(levels, capacities);
        user.updatedChannels(Sets.newHashSet(privateChannels), UserChannel.ChannelSourceType.UNKNOWN);
        return user;
    }
}
