package com.erhgo.generators;

import com.erhgo.config.KeycloakMockService;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Employer;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.repositories.EmployerRepository;
import com.erhgo.repositories.RecruiterRepository;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Deprecated
public class OrganizationGenerator {

    public static final String ORGANIZATION_CODE = "E-042";

    @Autowired
    private RecruiterRepository recruiterRepository;

    @Autowired
    private EmployerRepository employerRepository;

    @Autowired
    private KeycloakMockService keycloakMockService;

    public static Recruiter buildRecruiter() {
        return buildRecruiter(ORGANIZATION_CODE, "Entreprise du 42", "", "jenesuisPasunCv.fr", "", "", "", 5d, 55d, AbstractOrganization.OrganizationType.ENTERPRISE);
    }

    public static Recruiter buildRecruiterWithId() {
        var recruiter = buildRecruiter();
        recruiter.setId(1L);
        return recruiter;
    }

    public static Recruiter buildRecruiter(Recruiter.OrganizationType organizationType) {
        return buildRecruiter(ORGANIZATION_CODE, "Entreprise du 42", "", "", "", "", "", 5d, 55d, organizationType);
    }

    public static Recruiter buildRecruiter(String code, Recruiter.OrganizationType organizationType) {
        return buildRecruiter(code, "Entreprise du " + code, "", "", "", "", "", 5d, 55d, organizationType);
    }

    static Recruiter buildRecruiter(String code, String title, String description, String forcedUrl, String siret, String siren, String address, double latitude, double longitude, Recruiter.OrganizationType organizationType) {
        return Recruiter.recruiterBuilder()
                .siret(siret)
                .siren(siren)
                .address(address)
                .latitude(latitude)
                .longitude(longitude)
                .organizationType(organizationType)
                .code(code)
                .title(title)
                .description(description)
                .forcedUrl(forcedUrl)
                .build();
    }

    static Employer buildEmployer(String code, String title, String description, Recruiter referer, Recruiter... consortiums) {
        return Employer.employerBuilder()
                .code(code)
                .title(title)
                .description(description)
                .refererRecruiter(referer)
                .consortiums(List.of(consortiums))
                .build();
    }

    public Recruiter createRecruiter(String code, String title, String description, String forcedUrl, String siret, String siren, String address, double latitude, double longitude, Recruiter.OrganizationType organizationType) {
        return recruiterRepository.save(buildRecruiter(code, title, description, forcedUrl, siret, siren, address, latitude, longitude, organizationType));
    }

    // use createRecruiterWithGeneratedCode() instead
    @Deprecated
    public Recruiter createRecruiter() {
        return createRecruiter(AbstractOrganization.OrganizationType.ENTERPRISE);
    }

    // use createRecruiterWithGeneratedCode() instead
    @Deprecated
    public Recruiter createRecruiter(Recruiter.OrganizationType organizationType) {
        var organization = buildRecruiter(organizationType);
        var existentOrganization = recruiterRepository.findOneByCode(organization.getCode());
        if (existentOrganization == null) {
            organization = recruiterRepository.save(organization);
        } else if (existentOrganization.getOrganizationType() != organizationType) {
            Assert.fail("Wrong recruiter type for " + organization + ", trying to modify to " + organizationType);
        } else {
            organization = existentOrganization;
        }
        return organization;
    }


    public Recruiter createRecruiter(String channel) {
        return createRecruiter(channel, "Organization for channel " + channel, "Descr " + channel, "jenesuisPasunCv.fr", "", "", "", 0.0d, 0.0d, AbstractOrganization.OrganizationType.TERRITORIAL);
    }

    public Recruiter createRecruiterWithGeneratedCode() {
        var count = recruiterRepository.count();
        return createRecruiter("E-" + count, AbstractOrganization.OrganizationType.ENTERPRISE);
    }

    @Transactional
    public Recruiter createRecruiter(String code, Recruiter.OrganizationType organizationType) {
        return createRecruiter(code, organizationType, false, false, false);
    }

    @Transactional
    public Recruiter createRecruiter(String code, String title, Recruiter.OrganizationType organizationType) {
        var recruiter = createRecruiter(code, organizationType, false, false, false);
        recruiter.setTitle(title);
        return recruiter;
    }

    @Transactional
    public Recruiter createRecruiterWithSiretAndTitle(String siret, String title) {
        var recruiter = createRecruiter("S-001", AbstractOrganization.OrganizationType.SOURCING, false, false, false);
        recruiter.setTitle(title);
        recruiter.setSiret(siret);
        return recruiter;
    }

    @Transactional
    public Employer createEmployer(String code, Recruiter referer, Recruiter... consortiums) {
        keycloakMockService.addRoleToGroup(referer.getCode(), code);
        return employerRepository.save(buildEmployer(code, "title " + code, "description " + code, referer, consortiums));
    }

    @Transactional
    public Recruiter createRecruiter(String code, Recruiter.OrganizationType organizationType, boolean privateJobOrga, boolean privateUserOrga, boolean mandatoryIdentity) {
        var orga = recruiterRepository.save(buildRecruiter(code, organizationType));
        orga.setPrivateJobs(privateJobOrga);
        orga.setPrivateUsers(privateUserOrga);
        orga.setMandatoryIdentity(mandatoryIdentity);
        return orga;
    }
}
