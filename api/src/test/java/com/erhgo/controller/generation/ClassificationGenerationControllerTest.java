package com.erhgo.controller.generation;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.PromptConfig;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.repositories.classifications.ErhgoClassificationRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.repositories.classifications.RomeOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.ClassificationGenerationService;
import com.erhgo.services.generation.RomeGenerationService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.stream.IntStream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class ClassificationGenerationControllerTest extends AbstractIntegrationTest {

    @MockBean
    GenerationClient generationClient;

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    ErhgoClassificationRepository erhgoClassificationRepository;

    @Autowired
    RomeOccupationRepository romeOccupationRepository;

    @MockBean
    KeycloakMockService keycloakService;

    static final String OPENAI_RESPONSE_CLASSIFICATION = "['SO-07','SO-06','SO-09','SO-05']";
    static final String OPENAI_RESPONSE_ROME = "['K1902']";
    static final String OPENAI_RESPONSE_ROMES = "['K1902','K1903','K1904']";

    static final String OPENAI_RESPONSE_SPECIFICATION_MASTERY_LEVEL = """
            {"masteryLevel": 1}
            """;

    private ChatCompletionResponse chatCompletionResponse(String choice) {
        return new ChatCompletionResponse(choice, null);
    }

    @BeforeEach
    void prepare() {
        ReflectionTestUtils.setField(RomeGenerationService.class, "MAX_ROME", 4);
        ReflectionTestUtils.setField(ClassificationGenerationService.class, "MIN_CLASSIFICATION", 1);
        ReflectionTestUtils.setField(ClassificationGenerationService.class, "MAX_CLASSIFICATION", 4);
    }


    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateClassificationsOnOccupationWithoutClassifications() {
        var title = "occupation title";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle(title)
                .buildAndPersist();
        var occupationId = occupation.getId();

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(OPENAI_RESPONSE_CLASSIFICATION));
        performPut("/erhgo-occupation-generation/generate-erhgo-classifications/%s".formatted(occupationId), "generatedBehaviors");

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            Assertions.assertThat(updatedOccupation.getErhgoClassifications())
                    .isNotEmpty()
                    .hasSize(4);
            Assertions.assertThat(updatedOccupation.getErhgoClassifications().stream().map(ErhgoClassification::getCode)).containsExactlyInAnyOrder("SO-07", "SO-06", "SO-09", "SO-05");
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateClassificationsOnOccupationWithClassifications_resetClassifications() {
        var title = "occupation title";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle(title)
                .withClassificationsCodes("SO-01", "SO-02", "SO-03", "SO-04")
                .buildAndPersist();
        var occupationId = occupation.getId();

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(OPENAI_RESPONSE_CLASSIFICATION));
        performPut("/erhgo-occupation-generation/generate-erhgo-classifications/%s".formatted(occupationId), "generatedClassification");

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            Assertions.assertThat(updatedOccupation.getErhgoClassifications())
                    .isNotEmpty()
                    .hasSize(4);
            Assertions.assertThat(updatedOccupation.getErhgoClassifications().stream().map(ErhgoClassification::getCode)).containsExactlyInAnyOrder("SO-07", "SO-06", "SO-09", "SO-05");
            Assertions.assertThat(updatedOccupation.getErhgoClassifications().stream().map(ErhgoClassification::getCode)).doesNotContain("SO-01", "SO-02", "SO-03", "SO-04");
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateRomesOnOccupationWithoutRomes() {
        var title = "occupation title";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle(title)
                .buildAndPersist();
        var occupationId = occupation.getId();

        IntStream.range(2, 5)
                .forEach(index -> applicationContext.getBean(ErhgoOccupationGenerator.class).createRomeOccupation("K190" + index, "Rome K190" + index));
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(OPENAI_RESPONSE_ROMES));
        performPut("/erhgo-occupation-generation/generate-erhgo-classifications-romes/%s".formatted(occupationId), null);

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            Assertions.assertThat(updatedOccupation.getRomeOccupations())
                    .isNotEmpty()
                    .hasSize(3);
            Assertions.assertThat(updatedOccupation.getRomeOccupations().stream().map(RomeOccupation::getCode)).containsExactlyInAnyOrder("K1902", "K1903", "K1904");
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateRomesOnOccupationWithRomes() {
        var title = "occupation title";
        var oldRomes = applicationContext.getBean(ErhgoOccupationGenerator.class).createRomeOccupation("K1902-O", "Rome K1902-O");
        var newRomes = applicationContext.getBean(ErhgoOccupationGenerator.class).createRomeOccupation("K1902", "Rome K1902");
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle(title)
                .withRomeOccupations(Collections.singletonList(oldRomes))
                .buildAndPersist();
        var occupationId = occupation.getId();
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(OPENAI_RESPONSE_ROME));
        performPut("/erhgo-occupation-generation/generate-erhgo-classifications-romes/%s".formatted(occupationId), null);

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            Assertions.assertThat(updatedOccupation.getRomeOccupations())
                    .isNotEmpty()
                    .hasSize(1)
                    .doesNotContain(oldRomes);
            Assertions.assertThat(updatedOccupation.getRomeOccupations().stream().map(RomeOccupation::getCode)).containsExactlyInAnyOrder(newRomes.getCode());
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateSpecificationAndMasteryLevelOnOccupation() {
        var title = "occupation title";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle(title)
                .buildAndPersist();
        var occupationId = occupation.getId();

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(OPENAI_RESPONSE_SPECIFICATION_MASTERY_LEVEL));
        performPut("/erhgo-occupation-generation/generate-erhgo-specification-and-mastery-level/%s".formatted(occupationId), null);

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            Assertions.assertThat(updatedOccupation.getLevel()).isEqualTo(MasteryLevel.PROFESSIONAL);
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateSpecificationAndMasteryLevelOnOccupationWithSpecificationAndMasteryLevel() {
        var title = "occupation title";
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle(title)
                .withLevel(MasteryLevel.COMPLEX)
                .buildAndPersist();
        var occupationId = occupation.getId();

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(chatCompletionResponse(OPENAI_RESPONSE_SPECIFICATION_MASTERY_LEVEL));
        performPut("/erhgo-occupation-generation/generate-erhgo-specification-and-mastery-level/%s".formatted(occupationId), null);

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            Assertions.assertThat(updatedOccupation.getLevel()).isEqualTo(MasteryLevel.PROFESSIONAL);
            Assertions.assertThat(updatedOccupation.getLevel()).isNotEqualTo(MasteryLevel.COMPLEX);
        });
    }
}
