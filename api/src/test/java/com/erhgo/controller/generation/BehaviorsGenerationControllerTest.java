package com.erhgo.controller.generation;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.PromptConfig;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.generators.BehaviorGenerator;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class BehaviorsGenerationControllerTest extends AbstractIntegrationTest {

    @MockBean
    GenerationClient generationClient;

    @Autowired
    ApplicationContext applicationContext;


    @Autowired
    BehaviorGenerator behaviorGenerator;


    @MockBean
    KeycloakMockService keycloakService;

    static final String CHAT_COMPLETION_CHOICE = "['CONFIDENTIALITY','TENACITY','PERSEVERANCE']";


    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void generateBehaviorsOnOccupationWithoutBehaviors() {
        var title = "occupation title";
        behaviorGenerator.createBehaviorForCategory(BehaviorCategory.CONFIDENTIALITY);
        behaviorGenerator.createBehaviorForCategory(BehaviorCategory.TENACITY);
        behaviorGenerator.createBehaviorForCategory(BehaviorCategory.PERSEVERANCE);
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withTitle(title)
                .buildAndPersist();
        var occupationId = occupation.getId();

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class))).thenReturn(new ChatCompletionResponse(CHAT_COMPLETION_CHOICE, null));
        performPut("/erhgo-occupation-generation/generate-behaviors/%s".formatted(occupationId), "generatedBehaviors");

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            Assertions.assertThat(updatedOccupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.CONFIDENTIALITY);
            Assertions.assertThat(updatedOccupation.getBehaviorCategory2()).isEqualTo(BehaviorCategory.TENACITY);
            Assertions.assertThat(updatedOccupation.getBehaviorCategory3()).isEqualTo(BehaviorCategory.PERSEVERANCE);
        });
    }

}
