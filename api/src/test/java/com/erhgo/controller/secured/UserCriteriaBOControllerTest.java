package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.openapi.dto.SaveUserCriteriasCommandDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.List.of;
import static org.assertj.core.api.Assertions.assertThat;

class UserCriteriaBOControllerTest extends AbstractIntegrationTest {

    private static final String NEW_USER_ID = "3c2a6fbf-e563-4b0b-9ae0-af62cfa12f8d";

    private static final String CODE = "E-042";

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @BeforeEach
    void init_user_with_criteria() {
        txHelper.doInTransaction(() -> {
            var criteria1 = applicationContext.getBean(CriteriaMotherObject.class)
                    .withCriteriaCode("CR-1")
                    .withCriteriaIndex(0)
                    .withQuestionType(CriteriaQuestionType.MULTIPLE)
                    .withValueCodes("REP-1-1", "REP-1-2", "REP-1-3")
                    .buildAndPersist();

            var criteria2 = applicationContext.getBean(CriteriaMotherObject.class)
                    .withCriteriaCode("CR-2")
                    .withCriteriaIndex(0)
                    .withQuestionType(CriteriaQuestionType.MULTIPLE)
                    .withValueCodes("REP-2-1", "REP-2-2")
                    .buildAndPersist();


            var userProfile = new UserProfile().userId(NEW_USER_ID);
            userProfile.updatedChannels(Set.of(CODE), UserChannel.ChannelSourceType.LANDING_PAGE);
            var criteriaValues1 = criteria1.getCriteriaValues();
            var criteriaValues2 = criteria2.getCriteriaValues();
            userProfile.updateAnswerToCriteria(Set.of(criteriaValues1.get(0), criteriaValues2.get(0)),
                    Stream.concat(criteriaValues1.subList(1, criteriaValues1.size()).stream(), criteriaValues2.subList(1, criteriaValues2.size()).stream()).collect(Collectors.toSet()));
            userProfileRepository.save(userProfile);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = NEW_USER_ID, roles = Role.CANDIDATE)
    void get_user_criteria_as_candidate() throws Exception {
        performGetAndExpect("/user/" + NEW_USER_ID + "/criterias", "userCriteriasAsCandidate", false);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, CODE})
    void get_user_criteria_as_ot() throws Exception {
        performGetAndExpect("/user/" + NEW_USER_ID + "/criterias", "userCriteriasAsOT", false);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void get_user_criteria_as_admin() throws Exception {
        performGetAndExpect("/user/" + NEW_USER_ID + "/criterias", "userCriteriasAsAdmin", false);
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, CODE})
    void user_criteria_updated_by_OT() throws Exception {
        updateCriteriaBOCommon();
    }

    private void updateCriteriaBOCommon() throws Exception {
        var selectedValueCodes = of("REP-1-1", "REP-1-2", "REP-2-1");
        var unselectedValueCodes = of("REP-1-3", "REP-2-2");

        var dto = new SaveUserCriteriasCommandDTO()
                .userId(NEW_USER_ID)
                .selectedValueCodes(selectedValueCodes)
                .unselectedValueCodes(unselectedValueCodes);

        performPost("/user/criterias", dto);
        txHelper.doInTransaction(() -> {
            var userProfile = userProfileRepository.findByUserId(NEW_USER_ID).orElseThrow(() -> new EntityNotFoundException(NEW_USER_ID, UserProfile.class));
            var userCriteriaValues = userProfile.getUserCriteriaValues();
            assertThat(userCriteriaValues).filteredOn(UserCriteriaValue::isSelected).extracting("value.code").containsAll(selectedValueCodes);
            assertThat(userCriteriaValues).filteredOn(Predicate.not(UserCriteriaValue::isSelected)).extracting("value.code").containsAll(unselectedValueCodes);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void user_criteria_updated_by_admin() throws Exception {
        updateCriteriaBOCommon();
    }


}
