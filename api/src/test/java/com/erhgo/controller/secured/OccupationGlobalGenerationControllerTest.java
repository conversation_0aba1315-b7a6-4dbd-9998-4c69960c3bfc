package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.KeycloakMockService;
import com.erhgo.config.PromptConfig;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.OccupationCreationReason;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.generators.BehaviorGenerator;
import com.erhgo.generators.CapacityGenerator;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.openapi.dto.CreateErhgoOccupationCommandDTO;
import com.erhgo.openapi.dto.GenerationReportItemDTO;
import com.erhgo.openapi.dto.OccupationCreationReasonDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.ActivityGenerationService;
import com.erhgo.services.generation.ClassificationGenerationService;
import com.erhgo.services.generation.DescriptionGenerationService;
import com.erhgo.services.generation.RomeGenerationService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.CustomChatCompletionRequest;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;
import org.testcontainers.shaded.com.fasterxml.jackson.core.type.TypeReference;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.UUID;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.util.ReflectionTestUtils.setField;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class OccupationGlobalGenerationControllerTest extends AbstractIntegrationTest {

    public static final String OPEN_AI_WAS_UNABLE_TO_GENERATE_AFTER_3_TRIES = "OpenAI was unable to generate good enough data for input %s after 3 tries";
    public static final String ID = "b91a3254-8e5f-498f-a834-19fff9352045";
    @MockBean
    GenerationClient generationClient;

    @MockBean
    KeycloakMockService keycloakService;

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    BehaviorGenerator behaviorGenerator;

    @Autowired
    CapacityGenerator capacityGenerator;

    static final String OPENAI_RESPONSE_CLASSIFICATION = "['SO-07','SO-06','SO-09','SO-05']";

    static final String OPENAI_RESPONSE_ROMES = "['K1902','K1903','K1904']";
    static final String OPENAI_RESPONSE_SPECIFICATION_MASTERY_LEVEL = """
            {"masteryLevel": 1}
            """;

    static final String OPENAI_RESPONSE_ACTIVITIES = """
            {"capacitiesList": ["CA-08","CA-07","CA-01"],
            "activitiesDetail": [
                {"title": "Transporter des matériaux de construction","capacities": ["CA-08","CA-07"]},
                {"title": "Repérer et traiter des anomalies ou des dysfonctionnements","capacities": ["CA-01"]},
                {"title": "Assembler et fixer des éléments / matériaux entre eux ","capacities": ["CA-07"]}
            ],
            "capacitiesConsistency": "OK",
            "numberOfCapacitiesRule": "OK"}
            """;
    static final String OPENAI_RESPONSE_BEHAVIORS_DESCRIPTION = "Description des comportements";
    static final String OPENAI_RESPONSE_DESCRIPTION = "Description du métier";
    static final String OPENAI_RESPONSE_NORMALIZED_TITLE = "Métier normalisé au masculin";

    static final String OPENAI_RESPONSE_BEHAVIORS = "['CONFIDENTIALITY', 'TENACITY', 'PERSEVERANCE']";

    private ChatCompletionResponse chatCompletionResponse(String choice) {
        return new ChatCompletionResponse(choice, null);
    }

    @BeforeEach
    void setUp() {
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class)))
                .thenAnswer(invocation -> {
                    CustomChatCompletionRequest request = invocation.getArgument(0);
                    var userMessage = request.getInstructions().getLast().getText();
                    if (userMessage.contains("Veuillez qualifier, au format JSON, le métier")) {
                        return chatCompletionResponse(OPENAI_RESPONSE_ACTIVITIES);
                    } else if (userMessage.contains("quels sont, en commençant par le plus important")) {
                        return chatCompletionResponse(OPENAI_RESPONSE_BEHAVIORS);
                    } else if (userMessage.contains("Quels sont les centres d'intérêt")) {
                        return chatCompletionResponse(OPENAI_RESPONSE_CLASSIFICATION);
                    } else if (userMessage.contains("les codes ROME correspondants")) {
                        return chatCompletionResponse(OPENAI_RESPONSE_ROMES);
                    } else if (userMessage.contains("Quel est le niveau de maîtrise")) {
                        return chatCompletionResponse(OPENAI_RESPONSE_SPECIFICATION_MASTERY_LEVEL);
                    } else if (userMessage.contains("Souviens-toi : Donne moi une description")) {
                        return chatCompletionResponse(OPENAI_RESPONSE_DESCRIPTION);
                    }
                    return chatCompletionResponse(OPENAI_RESPONSE_BEHAVIORS_DESCRIPTION);
                });
        ReflectionTestUtils.setField(ActivityGenerationService.class, "MINIMUM_TOLERABLE_NUMBER_OF_CAPACITIES", 2);
        ReflectionTestUtils.setField(ActivityGenerationService.class, "MAXIMUM_TOLERABLE_NUMBER_OF_CAPACITIES", 3);
        ReflectionTestUtils.setField(ActivityGenerationService.class, "MINIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES", 1);
        ReflectionTestUtils.setField(ActivityGenerationService.class, "MAXIMUM_TOLERABLE_NUMBER_OF_ACTIVITIES", 3);

        setField(DescriptionGenerationService.class, "MIN_DESCRIPTION_WORD_COUNT", 2);
        setField(DescriptionGenerationService.class, "MAX_DESCRIPTION_WORD_COUNT", 10);
        setField(DescriptionGenerationService.class, "MIN_BEHAVIOR_DESCRIPTION_WORD_COUNT", 2);
        setField(DescriptionGenerationService.class, "MAX_BEHAVIOR_DESCRIPTION_WORD_COUNT", 10);

        ReflectionTestUtils.setField(RomeGenerationService.class, "MAX_ROME", 4);
        ReflectionTestUtils.setField(ClassificationGenerationService.class, "MIN_CLASSIFICATION", 1);
        ReflectionTestUtils.setField(ClassificationGenerationService.class, "MAX_CLASSIFICATION", 6);


    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void qualifyOccupationFromTitle() {
        IntStream.range(2, 5)
                .forEach(index -> applicationContext.getBean(ErhgoOccupationGenerator.class).createRomeOccupation("K190" + index, "Rome K190" + index));

        behaviorGenerator.createBehaviorForCategory(BehaviorCategory.CONFIDENTIALITY);
        behaviorGenerator.createBehaviorForCategory(BehaviorCategory.TENACITY);
        behaviorGenerator.createBehaviorForCategory(BehaviorCategory.PERSEVERANCE);

        IntStream.range(0, 9)
                .forEach(index -> capacityGenerator.createCapacity("CA-0" + (index + 1)));
        var occupationId = UUID.randomUUID();

        var command = new CreateErhgoOccupationCommandDTO()
                .id(occupationId)
                .occupationCreationReason(OccupationCreationReasonDTO.ESCO)
                .title(OPENAI_RESPONSE_NORMALIZED_TITLE);

        var expectedDescription = OPENAI_RESPONSE_DESCRIPTION;
        var expectedBehaviorsDescription = OPENAI_RESPONSE_BEHAVIORS_DESCRIPTION;

        performPut("/erhgo-occupation-generation/generate-global-qualification", command)
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("qualificationGenerationResult"))
        ;

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            assertThat(updatedOccupation.getErhgoClassifications())
                    .isNotEmpty()
                    .hasSize(4);
            assertThat(updatedOccupation.getErhgoClassifications().stream().map(ErhgoClassification::getCode)).containsExactlyInAnyOrder("SO-07", "SO-06", "SO-09", "SO-05");

            assertThat(updatedOccupation.getActivities())
                    .hasSize(3)
                    .anyMatch(a -> a.getInducedCapacities().stream().map(Capacity::getCode).toList().containsAll(List.of("CA-08", "CA-07")) && "Transporter des matériaux de construction".equals(a.getTitle()))
                    .anyMatch(a -> a.getInducedCapacities().stream().map(Capacity::getCode).toList().contains("CA-01") && "Repérer et traiter des anomalies ou des dysfonctionnements".equals(a.getTitle()))
                    .anyMatch(a -> a.getInducedCapacities().stream().map(Capacity::getCode).toList().contains("CA-07") && "Assembler et fixer des éléments / matériaux entre eux ".equals(a.getTitle()));

            assertThat(updatedOccupation.getBehaviorCategory1()).isEqualTo(BehaviorCategory.CONFIDENTIALITY);
            assertThat(updatedOccupation.getBehaviorCategory2()).isEqualTo(BehaviorCategory.TENACITY);
            assertThat(updatedOccupation.getBehaviorCategory3()).isEqualTo(BehaviorCategory.PERSEVERANCE);
            assertThat(updatedOccupation.getOccupationCreationReason()).isEqualTo(OccupationCreationReason.ESCO);
            assertThat(updatedOccupation.getDescription()).isEqualTo(expectedDescription);
            assertThat(updatedOccupation.getBehaviorsDescription()).isEqualTo(expectedBehaviorsDescription);
            assertThat(updatedOccupation.getTitle()).isEqualTo(OPENAI_RESPONSE_NORMALIZED_TITLE);
            assertThat(updatedOccupation.isTechnical()).isTrue();
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void qualifyOccupation_GenerateReport_EvenErrors() {

        IntStream.range(0, 9)
                .forEach(index -> capacityGenerator.createCapacity("CA-0" + (index + 1)));
        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class)))
                .thenAnswer(invocation -> {
                    CustomChatCompletionRequest request = invocation.getArgument(0);
                    var userMessage = request.getInstructions().getLast().getText();
                    if (userMessage.contains("fournissez directement le ou les codes ROME correspondants")) {
                        return chatCompletionResponse("[NOPE!]");
                    }
                    return chatCompletionResponse(" ");
                });

        var occupationId = UUID.fromString(ID);

        var title = "Title for new ERHGO occupation";
        var command = new CreateErhgoOccupationCommandDTO()
                .id(occupationId)
                .occupationCreationReason(OccupationCreationReasonDTO.ESCO)
                .title(title);

        var result = performPut("/erhgo-occupation-generation/generate-global-qualification", command)
                .andExpect(status().isOk())
                .andReturn();
        List<GenerationReportItemDTO> actualReport = new ObjectMapper().readValue(result.getResponse().getContentAsString(), new TypeReference<List<GenerationReportItemDTO>>() {
        });

        assertThat(actualReport).hasSize(7);

        assertThat(actualReport.get(0)).satisfies(report -> {
            assertThat(report.getSuccess()).isFalse();
            assertThat(report.getNbTry()).isEqualTo(3);
            assertThat(report.getTitle()).isEqualTo("2 - description");
            assertThat(report.getErrorMessage()).isEqualTo(OPEN_AI_WAS_UNABLE_TO_GENERATE_AFTER_3_TRIES.formatted(title));
        });

        assertThat(actualReport.get(1)).satisfies(report -> {
            assertThat(report.getSuccess()).isFalse();
            assertThat(report.getNbTry()).isEqualTo(3);
            assertThat(report.getTitle()).isEqualTo("3 - description des comportements");
            assertThat(report.getErrorMessage()).isEqualTo(OPEN_AI_WAS_UNABLE_TO_GENERATE_AFTER_3_TRIES.formatted(title));
        });

        assertThat(actualReport.get(2)).satisfies(report -> {
            assertThat(report.getSuccess()).isFalse();
            assertThat(report.getNbTry()).isEqualTo(3);
            assertThat(report.getTitle()).isEqualTo("4 - codes rome");
            assertThat(report.getErrorMessage()).isEqualTo(OPEN_AI_WAS_UNABLE_TO_GENERATE_AFTER_3_TRIES.formatted(title));
        });

        assertThat(actualReport.get(3)).satisfies(report -> {
            assertThat(report.getSuccess()).isFalse();
            assertThat(report.getNbTry()).isEqualTo(3);
            assertThat(report.getTitle()).isEqualTo("5 - classifications");
            assertThat(report.getErrorMessage()).isEqualTo(OPEN_AI_WAS_UNABLE_TO_GENERATE_AFTER_3_TRIES.formatted(title));
        });

        assertThat(actualReport.get(4)).satisfies(report -> {
            assertThat(report.getSuccess()).isFalse();
            assertThat(report.getNbTry()).isEqualTo(3);
            assertThat(report.getTitle()).isEqualTo("6 - niveau de maitrise");
            assertThat(report.getErrorMessage()).isEqualTo(OPEN_AI_WAS_UNABLE_TO_GENERATE_AFTER_3_TRIES.formatted(title));
        });

        assertThat(actualReport.get(5)).satisfies(report -> {
            assertThat(report.getSuccess()).isFalse();
            assertThat(report.getNbTry()).isEqualTo(3);
            assertThat(report.getTitle()).isEqualTo("7 - comportements");
            assertThat(report.getErrorMessage()).isEqualTo(OPEN_AI_WAS_UNABLE_TO_GENERATE_AFTER_3_TRIES.formatted(title));
        });

        assertThat(actualReport.get(6)).satisfies(report -> {
            assertThat(report.getSuccess()).isFalse();
            assertThat(report.getNbTry()).isEqualTo(3);
            assertThat(report.getTitle()).isEqualTo("8 - activités");
            assertThat(report.getErrorMessage()).isEqualTo(OPEN_AI_WAS_UNABLE_TO_GENERATE_AFTER_3_TRIES.formatted(ID));
        });

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            assertThat(updatedOccupation.getErhgoClassifications()).isEmpty();
            assertThat(updatedOccupation.getRomeOccupations()).isEmpty();
            assertThat(updatedOccupation.getBehaviorCategory1()).isNull();
            assertThat(updatedOccupation.getBehaviorCategory2()).isNull();
            assertThat(updatedOccupation.getBehaviorCategory3()).isNull();
            assertThat(updatedOccupation.getDescription()).isNull();
            assertThat(updatedOccupation.getTitle()).isEqualTo(title);
            assertThat(updatedOccupation.getActivities())
                    .isNullOrEmpty();
        });
    }

    @Test
    @SneakyThrows
    @ResetDataAfter
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void qualifyOccupation_commit_partial_success() {

        IntStream.range(0, 9)
                .forEach(index -> capacityGenerator.createCapacity("CA-0" + (index + 1)));

        when(generationClient.createChatCompletion(any(Prompt.class), any(PromptConfig.class)))
                .thenAnswer(invocation -> {
                    CustomChatCompletionRequest request = invocation.getArgument(0);
                    var userMessage = request.getInstructions().getLast().getText();
                    if (userMessage.contains("Souviens-toi : Donne moi une description")) {
                        return chatCompletionResponse(OPENAI_RESPONSE_DESCRIPTION);
                    }
                    if (userMessage.contains("fournissez directement le ou les codes ROME correspondants")) {
                        return chatCompletionResponse("[NOPE!]");
                    }
                    return chatCompletionResponse(" ");
                });


        var occupationId = UUID.fromString(ID);

        var title = "Title for new ERHGO occupation";
        var command = new CreateErhgoOccupationCommandDTO()
                .id(occupationId)
                .occupationCreationReason(OccupationCreationReasonDTO.BO)
                .title(title);


        var result = performPut("/erhgo-occupation-generation/generate-global-qualification", command)
                .andExpect(status().isOk())
                .andReturn();
        List<GenerationReportItemDTO> actualReport = new ObjectMapper().readValue(result.getResponse().getContentAsString(), new TypeReference<List<GenerationReportItemDTO>>() {
        });

        assertThat(actualReport.stream().filter(r -> r.getNbTry() == 1 && r.getSuccess() == Boolean.TRUE))
                .hasSize(1);

        assertThat(actualReport.stream().filter(r -> r.getNbTry() == 3 && r.getSuccess() == Boolean.FALSE))
                .hasSize(6);

        txHelper.doInTransaction(() -> {
            var updatedOccupation = applicationContext.getBean(ErhgoOccupationRepository.class).findById(occupationId).orElseThrow();
            assertThat(updatedOccupation.getErhgoClassifications()).isEmpty();
            assertThat(updatedOccupation.getRomeOccupations()).isEmpty();
            assertThat(updatedOccupation.getBehaviorCategory1()).isNull();
            assertThat(updatedOccupation.getBehaviorCategory2()).isNull();
            assertThat(updatedOccupation.getBehaviorCategory3()).isNull();
            assertThat(updatedOccupation.getOccupationCreationReason()).isEqualTo(OccupationCreationReason.BO);
            assertThat(updatedOccupation.getDescription()).isEqualTo(OPENAI_RESPONSE_DESCRIPTION);
            assertThat(updatedOccupation.getTitle()).isEqualTo(title);
            assertThat(updatedOccupation.getActivities())
                    .isNullOrEmpty();
        });
    }


}
