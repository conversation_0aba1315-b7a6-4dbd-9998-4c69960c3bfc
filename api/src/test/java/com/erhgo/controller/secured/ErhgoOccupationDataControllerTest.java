package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.*;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.classifications.workenvironment.WorkEnvironment;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.CriteriaMotherObject;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.generators.EscoOccupationGenerator;
import com.erhgo.generators.UserExperienceGenerator;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.UserExperienceRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.search.ErhgoOccupationFinder;
import com.erhgo.services.search.ErhgoOccupationIndexer;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static com.erhgo.TestUtils.jsonMatchesContent;
import static com.erhgo.generators.TestFixtures.*;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.emptyOrNullString;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class ErhgoOccupationDataControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;

    @Autowired
    private UserProfileRepository userProfileRepository;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Autowired
    private EscoOccupationGenerator escoOccupationGenerator;

    @Autowired
    private UserExperienceRepository userExperienceRepository;

    @MockBean
    private ErhgoOccupationIndexer erhgoOccupationIndexerMock;

    @MockBean
    @SuppressWarnings("unused")
    private ErhgoOccupationFinder erhgoOccupationFinder;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private MockMvc mvc;

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void should_update_occupation_state_qualified() throws Exception {
        // test for update & status preservation
        qualifyOccupation();
        qualifyOccupation();
        unqualifyOccupation();
        unqualifyOccupation();
    }

    private void qualifyOccupation() throws Exception {
        var id = ERHGO_OCCUPATION_WITHOUT_ROMES.getId();
        performPut("/api/odas/erhgo-occupation/qualify",
                null,
                Map.of("id", singletonList(id.toString())))
                .andExpect(status().isOk());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.findById(id).orElseThrow();
            assertThat(updatedOccupation.getQualificationState()).isEqualTo(ErhgoOccupationState.QUALIFIED_V3_CONFIRMED);
        });
    }

    private void unqualifyOccupation() throws Exception {
        var id = ERHGO_OCCUPATION_WITHOUT_ROMES.getId();
        performPut("/api/odas/erhgo-occupation/unqualify",
                null,
                Map.of("id", singletonList(id.toString())))
                .andExpect(status().isOk());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.findById(id).orElseThrow();
            assertThat(updatedOccupation.getQualificationState()).isEqualTo(ErhgoOccupationState.TO_CONFIRM);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void link_rome_to_erhgo_occupation_should_succeed() throws Exception {
        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withExperienceOnOccupation(ERHGO_OCCUPATION_WITHOUT_ROMES)
                .withIndexationRequiredDate(null)
                .buildAndPersist();

        var linkCommand = new LinkToErhgoOccupationCommandDTO()
                .romeCode(R1.getCode())
                .id(ERHGO_OCCUPATION_WITHOUT_ROMES.getId());

        performPut("/api/odas/erhgo-occupation/rome/linkToErhgoOccupation", linkCommand)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedEsco = erhgoOccupationRepository.findById(ERHGO_OCCUPATION_WITHOUT_ROMES.getId()).orElseThrow();
            var fetchedUser = userProfileRepository.findByUserId(user.userId()).orElseThrow();

            assertThat(updatedEsco.getRomeOccupations()).extracting(RomeOccupation::getCode).containsOnly(R1.getCode());
            assertThat(fetchedUser.indexationRequiredDate()).isCloseTo(new Date(), 10_000);
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void link_rome_to_erhgo_occupation_should_fail_for_unknown_romeCode() throws Exception {
        var linkCommand = new LinkToErhgoOccupationCommandDTO()
                .romeCode("Unknown")
                .id(ERHGO_OCCUPATION_WITHOUT_ROMES.getId());

        performPut("/api/odas/erhgo-occupation/rome/linkToEsco", linkCommand)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void link_rome_to_erhgo_occupation_should_fail_for_unknown_id() throws Exception {
        var linkCommand = new LinkToErhgoOccupationCommandDTO()
                .romeCode(R1.getCode())
                .id(UUID.randomUUID());

        performPut("/api/odas/erhgo-occupation/rome/linkToEsco", linkCommand)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void unlink_rome_from_erhgo_occupation_should_succeed() throws Exception {
        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withExperienceOnOccupation(ERHGO_OCCUPATION_WITH_ROMES)
                .withIndexationRequiredDate(null)
                .buildAndPersist();

        var unlinkCommand = new UnlinkRomeFromErhgoOccupationCommandDTO()
                .romeCode(R3.getCode())
                .id(ERHGO_OCCUPATION_WITH_ROMES.getId());

        performPut("/api/odas/erhgo-occupation/rome/unlinkFromErhgoOccupation", unlinkCommand)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedErhgoOccupation = erhgoOccupationRepository.findById(ERHGO_OCCUPATION_WITH_ROMES.getId()).orElseThrow();
            var fetchedUser = userProfileRepository.findByUserId(user.userId()).orElseThrow();

            assertThat(updatedErhgoOccupation.getRomeOccupations()).extracting(RomeOccupation::getCode).containsOnly(R4.getCode());
            assertThat(fetchedUser.indexationRequiredDate()).isCloseTo(new Date(), 10_000);

        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void unlink_rome_from_erhgo_occupation_should_fail_for_unknown_romeCode() throws Exception {
        var unlinkCommand = new UnlinkRomeFromErhgoOccupationCommandDTO()
                .romeCode("Unknown")
                .id(ERHGO_OCCUPATION_WITH_ROMES.getId());

        performPut("/api/odas/erhgo-occupation/rome/unlinkFromErhgoOccupation", unlinkCommand)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void unlink_rome_from_erhgo_occupation_should_fail_for_unknown_id() throws Exception {
        var unlinkCommand = new UnlinkRomeFromErhgoOccupationCommandDTO()
                .romeCode(R3.getCode())
                .id(UUID.randomUUID());

        performPut("/api/odas/erhgo-occupation/rome/unlinkFromErhgoOccupation", unlinkCommand)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void update_mastery_level_should_succeed() throws Exception {
        var updateLevelCommand = new UpdateMasteryLevelCommandDTO()
                .level(MasteryLevelDTO.STRATEGIC)
                .id(ERHGO_OCCUPATION_WITHOUT_ROMES.getId());

        performPut("/api/odas/erhgo-occupation/update-mastery-level", updateLevelCommand)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedErhgoOccupation = erhgoOccupationRepository.findById(ERHGO_OCCUPATION_WITHOUT_ROMES.getId()).orElseThrow();
            assertThat(updatedErhgoOccupation.getLevel()).isEqualTo(MasteryLevel.STRATEGIC);
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void update_mastery_level_should_fail_for_unknown_id() throws Exception {
        var updateLevelCommand = new UpdateMasteryLevelCommandDTO()
                .level(MasteryLevelDTO.PROFESSIONAL)
                .id(UUID.randomUUID());

        performPut("/api/odas/erhgo-occupation/update-mastery-level", updateLevelCommand)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void update_description_should_succeed() throws Exception {
        var updateDescriptionCommand = new UpdateDescriptionCommandDTO()
                .description("new description")
                .id(ERHGO_OCCUPATION_WITHOUT_ROMES.getId());

        performPut("/api/odas/erhgo-occupation/update-description", updateDescriptionCommand)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedErhgoOccupation = erhgoOccupationRepository.findById(ERHGO_OCCUPATION_WITHOUT_ROMES.getId()).orElseThrow();
            assertThat(updatedErhgoOccupation.getDescription()).isEqualTo(updateDescriptionCommand.getDescription());
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void update_description_should_fail_for_unknown_id() throws Exception {
        var updateDescriptionCommand = new UpdateDescriptionCommandDTO()
                .description("new description")
                .id(UUID.randomUUID());

        performPut("/api/odas/erhgo-occupation/update-description", updateDescriptionCommand)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void update_behaviors_description_should_succeed() throws Exception {
        var updateBehaviorsDescriptionCommand = new UpdateBehaviorsDescriptionCommandDTO()
                .description("new description")
                .id(ERHGO_OCCUPATION_WITHOUT_ROMES.getId());

        performPut("/api/odas/erhgo-occupation/update-behaviors-description", updateBehaviorsDescriptionCommand)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedErhgoOccupation = erhgoOccupationRepository.findById(ERHGO_OCCUPATION_WITHOUT_ROMES.getId()).orElseThrow();
            assertThat(updatedErhgoOccupation.getBehaviorsDescription()).isEqualTo(updateBehaviorsDescriptionCommand.getDescription());
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    void update_behaviors_description_should_fail_for_unknown_id() throws Exception {
        var updateBehaviorsDescriptionCommand = new UpdateBehaviorsDescriptionCommandDTO()
                .description("new description")
                .id(UUID.randomUUID());

        performPut("/api/odas/erhgo-occupation/update-behaviors-description", updateBehaviorsDescriptionCommand)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void addActivity_should_succeed() throws Exception {
        addActivitiesToOccupation();

        assertActivityAddedToOccupation();
    }

    private void assertActivityAddedToOccupation() {
        txHelper.doInTransaction(() -> {
            var activity = erhgoOccupationRepository.getOne(ERHGO_OCCUPATION_QUALIFIED.getId()).getOccupationActivities().stream().filter(a -> a.getActivity().equals(ACT_14)).findFirst().orElseThrow();
            assertThat(activity.getSource()).isEqualTo(OccupationQualificationSource.MANUAL);
            assertThat(activity.getState()).isEqualTo(MandatoryState.OPTIONAL);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void addActivity_twice_should_succeed() throws Exception {
        addActivitiesToOccupation();
        addActivitiesToOccupation();

        assertActivityAddedToOccupation();
    }

    private void addActivitiesToOccupation() throws Exception {
        var command = new OccupationReferentialEntitiesEditCommandDTO()
                .referentialEntityIds(List.of(ACT_14.getUuid()))
                .erhgoOccupationId(ERHGO_OCCUPATION_QUALIFIED.getId());
        performPut("/erhgo-occupation/addActivities", command)
                .andExpect(status().isNoContent());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void addActivity_should_fail_unknown_occupation() throws Exception {
        var command = new OccupationReferentialEntitiesEditCommandDTO()
                .referentialEntityIds(List.of(ACT_14.getUuid()))
                .erhgoOccupationId(UUID.randomUUID());
        performPut("/erhgo-occupation/addActivities", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void removeActivity_should_succeed() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithActivities("Occupation with activity",
                Lists.newArrayList(ACT_01),
                Lists.newArrayList(ACT_05, ACT_14)
        );

        var command = new OccupationReferentialEntitiesEditCommandDTO()
                .referentialEntityIds(List.of(ACT_14.getUuid()))
                .erhgoOccupationId(occupation.getId());
        performPut("/erhgo-occupation/removeActivities", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(updatedOccupation.getOccupationActivities()).noneMatch(a -> a.getActivity().getUuid().equals(ACT_14.getUuid()));
            assertThat(updatedOccupation.getOccupationActivities()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void removeActivity_should_succeed_with_unknown_activity() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithActivities("Occupation with activity",
                Lists.newArrayList(ACT_01),
                Lists.newArrayList(ACT_05)
        );

        var command = new OccupationReferentialEntitiesEditCommandDTO()
                .referentialEntityIds(List.of(ACT_14.getUuid()))
                .erhgoOccupationId(occupation.getId());
        performPut("/erhgo-occupation/removeActivities", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(updatedOccupation.getOccupationActivities()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void changeState_should_succeed() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithActivities("Occupation with activity",
                Lists.newArrayList(ACT_01),
                Lists.newArrayList(ACT_05)
        );

        var command = (OccupationReferentialEntityEditWithStateCommandDTO) new OccupationReferentialEntityEditWithStateCommandDTO()
                .referentialEntityId(ACT_01.getUuid())
                .erhgoOccupationId(occupation.getId());
        command.setState(MandatoryStateDTO.ESSENTIAL);
        performPut("/erhgo-occupation/changeActivityOccupationState", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(getOccupationActivity(updatedOccupation, ACT_01).getState()).isEqualTo(MandatoryState.ESSENTIAL);
            assertThat(getOccupationActivity(updatedOccupation, ACT_05).getState()).isEqualTo(MandatoryState.OPTIONAL);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void changeState_should_fail_with_unknown_activity() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithActivities("Occupation with activity",
                Lists.newArrayList(ACT_01),
                Lists.newArrayList(ACT_05)
        );

        var command = (OccupationReferentialEntityEditWithStateCommandDTO) new OccupationReferentialEntityEditWithStateCommandDTO()
                .referentialEntityId(UUID.randomUUID())
                .erhgoOccupationId(occupation.getId());
        command.setState(MandatoryStateDTO.ESSENTIAL);

        performPut("/erhgo-occupation/changeActivityOccupationState", command)
                .andExpect(status().isBadRequest());
    }

    private OccupationActivity getOccupationActivity(ErhgoOccupation updatedOccupation, JobActivityLabel activity) {
        return updatedOccupation.getOccupationActivities()
                .stream()
                .filter(occupationActivity -> occupationActivity.getActivity().getUuid().equals(activity.getUuid()))
                .findFirst()
                .orElseThrow();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void addContext_should_succeed() throws Exception {
        addContextToOccupation();

        assertContextAddedToOccupation();
    }

    private void assertContextAddedToOccupation() {
        txHelper.doInTransaction(() -> {
            var context = erhgoOccupationRepository.getOne(ERHGO_OCCUPATION_QUALIFIED.getId()).getOccupationContexts().stream().filter(c -> c.getContext().equals(CT_08)).findFirst().orElseThrow();
            assertThat(context.getSource()).isEqualTo(OccupationQualificationSource.MANUAL);
            assertThat(context.getState()).isEqualTo(MandatoryState.OPTIONAL);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void addContext_twice_should_succeed() throws Exception {
        addContextToOccupation();
        addContextToOccupation();

        assertContextAddedToOccupation();
    }

    private void addContextToOccupation() throws Exception {
        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(CT_08.getId())
                .erhgoOccupationId(ERHGO_OCCUPATION_QUALIFIED.getId());
        performPut("/erhgo-occupation/addContext", command)
                .andExpect(status().isNoContent());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void addContext_should_fail_unknown_occupation() throws Exception {
        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(CT_08.getId())
                .erhgoOccupationId(UUID.randomUUID());
        performPut("/erhgo-occupation/addContext", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void addContext_should_fail_unknown_context() throws Exception {
        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(UUID.randomUUID())
                .erhgoOccupationId(ERHGO_OCCUPATION_QUALIFIED.getId());
        performPut("/erhgo-occupation/addContext", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void removeContext_should_succeed() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithContexts("Occupation with context",
                Lists.newArrayList(CT_10),
                Lists.newArrayList(CT_15, CT_27)
        );

        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(CT_27.getId())
                .erhgoOccupationId(occupation.getId());
        performPut("/erhgo-occupation/removeContext", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(updatedOccupation.getOccupationContexts()).noneMatch(c -> c.getContext().getId().equals(CT_27.getId()));
            assertThat(updatedOccupation.getOccupationContexts()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void removeContext_should_succeed_with_unknown_context() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithContexts("Occupation with context",
                Lists.newArrayList(CT_10),
                Lists.newArrayList(CT_15)
        );

        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(CT_27.getId())
                .erhgoOccupationId(occupation.getId());
        performPut("/erhgo-occupation/removeContext", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(updatedOccupation.getOccupationContexts()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void changeOccupationContextState_should_succeed() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithContexts("Occupation with context",
                Lists.newArrayList(CT_10),
                Lists.newArrayList(CT_15)
        );

        var command = (OccupationReferentialEntityEditWithStateCommandDTO) new OccupationReferentialEntityEditWithStateCommandDTO()
                .referentialEntityId(CT_10.getId())
                .erhgoOccupationId(occupation.getId());
        command.setState(MandatoryStateDTO.ESSENTIAL);
        performPut("/erhgo-occupation/changeContextOccupationState", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(getOccupationContext(updatedOccupation, CT_10).getState()).isEqualTo(MandatoryState.ESSENTIAL);
            assertThat(getOccupationContext(updatedOccupation, CT_15).getState()).isEqualTo(MandatoryState.OPTIONAL);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void changeOccupationContextState_should_fail_with_unknown_context() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithContexts("Occupation with context",
                Lists.newArrayList(CT_10),
                Lists.newArrayList(CT_15)
        );

        var command = (OccupationReferentialEntityEditWithStateCommandDTO) new OccupationReferentialEntityEditWithStateCommandDTO()
                .referentialEntityId(UUID.randomUUID())
                .erhgoOccupationId(occupation.getId());
        command.setState(MandatoryStateDTO.ESSENTIAL);

        performPut("/erhgo-occupation/changeContextOccupationState", command)
                .andExpect(status().isBadRequest());
    }

    private OccupationContext getOccupationContext(ErhgoOccupation updatedOccupation, Context context) {
        return updatedOccupation.getOccupationContexts()
                .stream()
                .filter(context1 -> context1.getContext().getId().equals(context.getId()))
                .findFirst()
                .orElseThrow();
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void addBehavior_should_succeed() throws Exception {
        addBehaviorToOccupation();
        assertBehaviorAddedToOccupation();
    }

    private void assertBehaviorAddedToOccupation() {
        txHelper.doInTransaction(() -> {
            var behavior = erhgoOccupationRepository.getOne(ERHGO_OCCUPATION_QUALIFIED.getId()).getOccupationBehaviors().stream().filter(c -> c.getBehavior().equals(B_03)).findFirst().orElseThrow();
            assertThat(behavior.getSource()).isEqualTo(OccupationQualificationSource.MANUAL);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void addBehavior_twice_should_succeed() throws Exception {
        addBehaviorToOccupation();
        addBehaviorToOccupation();

        assertBehaviorAddedToOccupation();
    }

    private void addBehaviorToOccupation() throws Exception {
        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(B_03.getId())
                .erhgoOccupationId(ERHGO_OCCUPATION_QUALIFIED.getId());
        performPut("/erhgo-occupation/addBehavior", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.behaviorCategory1", is(BehaviorCategoryDTO.SOCIABILITY.toString())))
                .andExpect(jsonPath("$.behaviorCategory2", is(emptyOrNullString())))
                .andExpect(jsonPath("$.behaviorCategory3", is(emptyOrNullString())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void addBehavior_should_fail_unknown_occupation() throws Exception {
        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(B_03.getId())
                .erhgoOccupationId(UUID.randomUUID());
        performPut("/erhgo-occupation/addBehavior", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void addBehavior_should_fail_unknown_behavior() throws Exception {
        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(UUID.randomUUID())
                .erhgoOccupationId(ERHGO_OCCUPATION_QUALIFIED.getId());
        performPut("/erhgo-occupation/addBehavior", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void removeBehavior_should_succeed() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithBehaviors("Occupation with behaviors",
                Lists.newArrayList(B_04),
                Lists.newArrayList(B_01, B_10)
        );

        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(B_04.getId())
                .erhgoOccupationId(occupation.getId());
        performPut("/erhgo-occupation/removeBehavior", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.behaviorCategory1", is(BehaviorCategoryDTO.CONSTANCY.toString())))
                .andExpect(jsonPath("$.behaviorCategory2", is(emptyOrNullString())))
                .andExpect(jsonPath("$.behaviorCategory3", is(emptyOrNullString())));

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getReferenceById(occupation.getId());
            assertThat(updatedOccupation.getOccupationBehaviors()).noneMatch(c -> c.getBehavior().getId().equals(B_04.getId()));
            assertThat(updatedOccupation.getOccupationBehaviors()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void removeBehavior_should_succeed_with_unknown_behavior() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithBehaviors("Occupation with behaviors",
                Lists.newArrayList(B_04),
                Lists.newArrayList(B_10)
        );

        var command = new OccupationReferentialEntityEditCommandDTO()
                .referentialEntityId(B_03.getId())
                .erhgoOccupationId(occupation.getId());
        performPut("/erhgo-occupation/removeBehavior", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.behaviorCategory1", is(emptyOrNullString())))
                .andExpect(jsonPath("$.behaviorCategory2", is(emptyOrNullString())))
                .andExpect(jsonPath("$.behaviorCategory3", is(emptyOrNullString())));

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(updatedOccupation.getOccupationBehaviors()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void addLabel_should_succeed() throws Exception {
        var expectedTitle = "Title 1";
        var expectedLabel = "Added title";
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithTitleAndLabels(expectedTitle, expectedLabel);

        var command = new EditAlternativeLabelsCommandDTO()
                .id(occupation.getId())
                .title(expectedTitle)
                .alternativeLabels(singletonList(expectedLabel));

        performPut("/erhgo-occupation/editAlternativeLabels", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(updatedOccupation.getTitle()).isEqualTo(expectedTitle);
            assertThat(updatedOccupation.getAlternativeLabels()).containsExactly(expectedLabel);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void updateLabel_does_not_delete_it() throws Exception {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withAlternativeLabel("abc")
                .withAlternativeLabel("Abc")
                .buildAndPersist();

        var command = new EditAlternativeLabelsCommandDTO()
                .id(occupation.getId())
                .title("nope")
                .alternativeLabels(List.of("abc", "abc"));

        performPut("/erhgo-occupation/editAlternativeLabels", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.findById(occupation.getId()).orElseThrow();
            assertThat(updatedOccupation.getAlternativeLabels()).containsExactly("Abc");
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void fixLabels_removes_duplicate() throws Exception {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withAlternativeLabel("abc")
                .buildAndPersist();

        txHelper.doInTransaction(() -> {
            jdbcTemplate.update(
                    "INSERT INTO ErhgoOccupation_alternativeLabels VALUES (uuidToBin(?), 'abc'), (uuidToBin(?), 'Abc'), (uuidToBin(?), 'Abc ') ",
                    occupation.getId().toString(),
                    occupation.getId().toString(),
                    occupation.getId().toString());
        });

        txHelper.doInTransaction(() -> {
            erhgoOccupationRepository.findById(occupation.getId()).orElseThrow().fixTitles();
        });

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.findById(occupation.getId()).orElseThrow();
            assertThat(updatedOccupation.getAlternativeLabels()).containsExactly("Abc");
        });
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void replaceTitle_by_label_should_succeed() throws Exception {
        var expectedTitle = "Title 1";
        var expectedLabel = "Added title";
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithTitleAndLabels(expectedLabel, expectedTitle);

        var command = new EditAlternativeLabelsCommandDTO()
                .id(occupation.getId())
                .title(expectedTitle)
                .alternativeLabels(singletonList(expectedLabel));

        performPut("/erhgo-occupation/editAlternativeLabels", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(updatedOccupation.getTitle()).isEqualTo(expectedTitle);
            assertThat(updatedOccupation.getAlternativeLabels()).containsExactly(expectedLabel);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void remove_labels_should_succeed() throws Exception {
        var expectedTitle = "Title 1";
        var expectedLabel = "Added title";
        var expectedLabel2 = "Added title 2";
        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithTitleAndLabels(expectedTitle, expectedLabel, expectedLabel2);

        var command = new EditAlternativeLabelsCommandDTO()
                .id(occupation.getId())
                .title(expectedTitle)
                .alternativeLabels(emptyList());

        performPut("/erhgo-occupation/editAlternativeLabels", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(occupation.getId());
            assertThat(updatedOccupation.getTitle()).isEqualTo(expectedTitle);
            assertThat(updatedOccupation.getAlternativeLabels()).isEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void removeSkill_should_succeed() throws Exception {

        var skill = ESCO_OCCUPATION_WITH_REFERENTIAL_DATA.getSkills().iterator().next();

        var command = new UnlinkSkillFromErhgoOccupationCommandDTO()
                .id(ERHGO_OCCUPATION_QUALIFIED.getId())
                .skillUri(skill.getUri());

        performPut("/erhgo-occupation/skill/unlinkFromErhgoOccupation", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getOne(ERHGO_OCCUPATION_QUALIFIED.getId());
            assertThat(updatedOccupation.getSkills()).doesNotContain(skill);
            assertThat(updatedOccupation.getSkills()).hasSize(2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void removeSkill_should_fail_for_unknown_occupation_id() throws Exception {

        var skill = ESCO_OCCUPATION_WITH_REFERENTIAL_DATA.getSkills().iterator().next();

        var command = new UnlinkSkillFromErhgoOccupationCommandDTO()
                .id(UUID.randomUUID())
                .skillUri(skill.getUri());

        performPut("/erhgo-occupation/skill/unlinkFromErhgoOccupation", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void removeSkill_should_fail_for_unknown_skill_uri() throws Exception {

        var command = new UnlinkSkillFromErhgoOccupationCommandDTO()
                .id(ERHGO_OCCUPATION_QUALIFIED.getId())
                .skillUri("test");

        performPut("/erhgo-occupation/skill/unlinkFromErhgoOccupation", command)
                .andExpect(status().isNotFound());
    }

    @ParameterizedTest
    @ValueSource(ints = {0, 1, 2})
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void set_occupation_behavior_family_should_succeed(int index) throws Exception {

        var command = new EditOccupationBehaviorCategoryCommandDTO()
                .id(ERHGO_OCCUPATION_QUALIFIED.getId())
                .occupationCategoryIndex(index)
                .behaviorCategory(BehaviorCategoryDTO.CONSTANCY);

        performPut("/erhgo-occupation/changeBehaviorCategory", command)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.behaviorCategory1", is(index == 0 ? BehaviorCategory.CONSTANCY.name() : null)))
                .andExpect(jsonPath("$.isBehaviorCategory1Overloaded", is(index == 0)))
                .andExpect(jsonPath("$.behaviorCategory2", is(index == 1 ? BehaviorCategory.CONSTANCY.name() : null)))
                .andExpect(jsonPath("$.isBehaviorCategory2Overloaded", is(index == 1)))
                .andExpect(jsonPath("$.behaviorCategory3", is(index == 2 ? BehaviorCategory.CONSTANCY.name() : null)))
                .andExpect(jsonPath("$.isBehaviorCategory3Overloaded", is(index == 2)))
        ;

        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.getReferenceById(command.getId());
            assertThat(updatedOccupation.getBehaviorCategory1()).isEqualTo(index == 0 ? BehaviorCategory.CONSTANCY : null);
            assertThat(updatedOccupation.getBehaviorCategory2()).isEqualTo(index == 1 ? BehaviorCategory.CONSTANCY : null);
            assertThat(updatedOccupation.getBehaviorCategory3()).isEqualTo(index == 2 ? BehaviorCategory.CONSTANCY : null);
            assertThat(updatedOccupation.isBehaviorCategory1Overloaded()).isEqualTo(index == 0);
            assertThat(updatedOccupation.isBehaviorCategory2Overloaded()).isEqualTo(index == 1);
            assertThat(updatedOccupation.isBehaviorCategory3Overloaded()).isEqualTo(index == 2);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void set_occupation_behavior_family_should_fail_for_unknown_id() throws Exception {

        var command = new EditOccupationBehaviorCategoryCommandDTO()
                .id(UUID.randomUUID())
                .occupationCategoryIndex(0)
                .behaviorCategory(BehaviorCategoryDTO.CONSTANCY);

        performPut("/erhgo-occupation/changeBehaviorCategory", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void link_esco_to_erhgo_should_succeed() throws Exception {
        var occupation = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withTitle("title")
                .withId(UUID.randomUUID())
                .withActivity(ACT_01, true)
                .withContext(CT_21, true)
                .withBehavior(B_01)
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6)
                .instance());

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withExperienceOnOccupation(occupation)
                .withIndexationRequiredDate(null)
                .buildAndPersist();

        var command = new EditEscoOccupationCommandDTO()
                .id(occupation.getId())
                .escoUri(ESCO_OCCUPATION_WITH_REFERENTIAL_DATA.getUri());

        performPut("/erhgo-occupation/esco/linkToErhgoOccupation", command)
                .andExpect(jsonMatchesContent("linkEscoToErhgo"))
                .andExpect(status().isOk());

        txHelper.doInTransaction(() -> {
            var fetchedUser = userProfileRepository.findByUserId(user.userId()).orElseThrow();
            assertThat(fetchedUser.indexationRequiredDate()).isCloseTo(new Date(), 10_000);
        });

    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void link_esco_to_erhgo_should_fail_for_unknown_erhgoOccupation_id() throws Exception {

        var command = new EditEscoOccupationCommandDTO()
                .id(UUID.randomUUID())
                .escoUri(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6.getUri());

        performPut("/erhgo-occupation/esco/linkToErhgoOccupation", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void link_esco_to_erhgo_should_fail_for_unknown_escoOccupation_uri() throws Exception {

        var command = new EditEscoOccupationCommandDTO()
                .id(ERHGO_OCCUPATION_QUALIFIED.getId())
                .escoUri("unknown");

        performPut("/erhgo-occupation/esco/linkToErhgoOccupation", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void unlink_esco_from_erhgo_should_succeed() throws Exception {

        var skillUsed = ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6.getSkills()
                .stream()
                .filter(escoSkill -> escoSkill.getTitle().equals("a skill qualified 3"))
                .findFirst()
                .orElseThrow();

        var escoWithSkillUsed = escoOccupationGenerator.createEscoOccupation(EscoOccupation.builder()
                .title("Title for ESCO with skill used in another ESCO job")
                .uri("URI for ESCO with skill used in another ESCO job")
                .iscoOccupation(ISCO_666)
                .skills(Sets.newHashSet(skillUsed))
                .build());

        var occupation = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withTitle("title")
                .withId(UUID.randomUUID())
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6)
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_2_QUALIED_SKILLS_IN_4)
                .withEscoOccupationCascadingSkills(escoWithSkillUsed)
                .instance());

        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withExperienceOnOccupation(occupation)
                .withIndexationRequiredDate(null)
                .buildAndPersist();

        var command = new EditEscoOccupationCommandDTO()
                .id(occupation.getId())
                .escoUri(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6.getUri());

        performPut("/erhgo-occupation/esco/unlinkFromErhgoOccupation", command)
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("unlinkEscoFromErhgo"));

        txHelper.doInTransaction(() -> {
            var fetchedUser = userProfileRepository.findByUserId(user.userId()).orElseThrow();
            assertThat(fetchedUser.indexationRequiredDate()).isCloseTo(new Date(), 10_000);
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void unlink_esco_from_erhgo_should_fail_for_unknown_erhgoOccupation_id() throws Exception {

        var command = new EditEscoOccupationCommandDTO()
                .id(UUID.randomUUID())
                .escoUri(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6.getUri());

        performPut("/erhgo-occupation/esco/unlinkFromErhgoOccupation", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void unlink_esco_from_erhgo_should_fail_for_unknown_escoOccupation_uri() throws Exception {

        var command = new EditEscoOccupationCommandDTO()
                .id(ERHGO_OCCUPATION_QUALIFIED.getId())
                .escoUri("unknown");

        performPut("/erhgo-occupation/esco/unlinkFromErhgoOccupation", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void create_occupation_should_succeed() throws Exception {

        var command = new CreateErhgoOccupationCommandDTO()
                .id(UUID.randomUUID())
                .occupationCreationReason(OccupationCreationReasonDTO.ESCO)
                .title("Title for new ERHGO occupation");

        performPut("/erhgo-occupation", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var createdOccupation = erhgoOccupationRepository.findById(command.getId()).orElseThrow();
            assertThat(createdOccupation.getTitle()).isEqualTo(command.getTitle());
            assertThat(createdOccupation.getQualificationState()).isEqualTo(ErhgoOccupationState.NONE);
        });

        Mockito.verify(erhgoOccupationIndexerMock)
                .updateOccupationIndexation(ArgumentMatchers.argThat(occupation -> occupation.getId().equals(command.getId())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void create_occupation_should_fail_for_same_id() throws Exception {

        var command = new CreateErhgoOccupationCommandDTO()
                .id(ERHGO_OCCUPATION_QUALIFIED.getId())
                .occupationCreationReason(OccupationCreationReasonDTO.ESCO)
                .title("Title for new ERHGO occupation");

        performPut("/erhgo-occupation", command)
                .andExpect(status().isConflict());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void merge_occupations_succeed() throws Exception {

        var targetOccupation = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withTitle("title for base occupation")
                .withId(UUID.randomUUID())
                .withActivity(ACT_01, true)
                .withContext(CT_21, true)
                .withBehavior(B_01)
                .withRomeOccupations(Arrays.asList(R1, R2))
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6)
                .withLabels("label for base occupation 1", "label for base occupation 2")
                .withState(ErhgoOccupationState.QUALIFIED_V1)
                .withLevel(MasteryLevel.MAX_LEVEL)
                .instance());

        var occupationToDelete = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withTitle("title for merged occupation")
                .withId(UUID.randomUUID())
                .withActivity(ACT_01, false)
                .withActivity(ACT_04, true)
                .withContext(CT_04, false)
                .withBehavior(B_06)
                .withRomeOccupations(Arrays.asList(R2, R3))
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6)
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_2_QUALIED_SKILLS_IN_4)
                .withLabels("label for merged occupation 1", "label for merged occupation 2")
                .withState(ErhgoOccupationState.QUALIFIED_V1)
                .withLevel(MasteryLevel.MIN_LEVEL)
                .instance());

        var userExperience = userExperienceRepository.save(UserExperienceGenerator.generateExperienceWithOccupation(DEFAULT_USER_PROFILE, occupationToDelete));
        applicationContext.getBean(UserProfileMotherObject.class).withExperienceOnOccupation(occupationToDelete).withRegistrationSelectedOccupation(occupationToDelete).buildAndPersist();
        applicationContext.getBean(JobMotherObject.class).withOccupation(occupationToDelete).buildAndPersist();
        var command = new MergeOccupationsCommandDTO()
                .targetOccupationId(targetOccupation.getId())
                .occupationIdToDelete(occupationToDelete.getId())
                .ignoreActivities(false);

        performPut("/erhgo-occupation/merge", command)
                .andExpect(status().isNoContent());

        mvc.perform(get("/api/odas/erhgo-occupation/detail/" + targetOccupation.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonMatchesContent("mergedOccupation"));

        txHelper.doInTransaction(() -> {
            var deletedOccupation = erhgoOccupationRepository.findById(occupationToDelete.getId());
            assertThat(deletedOccupation).isEmpty();
            var userExperienceUpdated = userExperienceRepository.findById(userExperience.getUuid()).orElseThrow();
            assertThat(userExperienceUpdated.getOccupationId()).isEqualTo(targetOccupation.getId());
        });

        Mockito.verify(erhgoOccupationIndexerMock)
                .removeOccupation(ArgumentMatchers.argThat(occupation -> occupation.getId().equals(occupationToDelete.getId())));
        Mockito.verify(erhgoOccupationIndexerMock)
                .updateOccupationIndexation(ArgumentMatchers.argThat(occupation -> occupation.getId().equals(targetOccupation.getId())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void merge_occupations_should_succeed_with_ignored_activities() throws Exception {

        var targetOccupation = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withTitle("title for base occupation")
                .withId(UUID.randomUUID())
                .withBehavior(B_01)
                .withRomeOccupations(Arrays.asList(R1, R2))
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6)
                .withLabels("label for base occupation 1", "label for base occupation 2")
                .instance());

        var occupationToDelete = erhgoOccupationGenerator.createErhgoOccupation(new ErhgoOccupationMotherObject()
                .withTitle("title for merged occupation")
                .withId(UUID.randomUUID())
                .withActivity(ACT_01, true)
                .withActivity(ACT_04, true)
                .withContext(CT_04, false)
                .withBehavior(B_06)
                .withRomeOccupations(Arrays.asList(R2, R3))
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6)
                .withEscoOccupationCascadingSkills(ESCO_OCCUPATION_WITH_2_QUALIED_SKILLS_IN_4)
                .withLabels("label for merged occupation 1", "label for merged occupation 2")
                .withState(ErhgoOccupationState.QUALIFIED_V1)
                .withLevel(MasteryLevel.MIN_LEVEL)
                .instance());

        var command = new MergeOccupationsCommandDTO()
                .targetOccupationId(targetOccupation.getId())
                .occupationIdToDelete(occupationToDelete.getId())
                .ignoreActivities(true);

        performPut("/erhgo-occupation/merge", command)
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var targetOccupationUpdated = erhgoOccupationRepository.findById(targetOccupation.getId()).orElseThrow();
            assertThat(targetOccupationUpdated.getActivities()).isEmpty();
            var deletedOccupation = erhgoOccupationRepository.findById(occupationToDelete.getId());
            assertThat(deletedOccupation).isEmpty();
        });

    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void merge_occupations_should_fail_for_unknown_target_occupation_id() throws Exception {

        var command = new MergeOccupationsCommandDTO()
                .targetOccupationId(UUID.randomUUID())
                .occupationIdToDelete(ERHGO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6.getId())
                .ignoreActivities(false);

        performPut("/erhgo-occupation/merge", command)
                .andExpect(status().isNotFound());

    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void merge_occupations_should_fail_for_unknown_merged_occupation_id() throws Exception {

        var command = new MergeOccupationsCommandDTO()
                .targetOccupationId(ERHGO_OCCUPATION_WITH_3_QUALIED_SKILLS_IN_6.getId())
                .occupationIdToDelete(UUID.randomUUID())
                .ignoreActivities(false);

        performPut("/erhgo-occupation/merge", command)
                .andExpect(status().isNotFound());

    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void change_working_environment_unknown_environment_should_fail() throws Exception {
        var command = new UpdateWorkEnvironmentsCommandDTO()
                .occupationId(ERHGO_OCCUPATION_WITH_ROMES.getId())
                .workEnvironmentCodes(Collections.singletonList("ENV-99"));

        performPut("/erhgo-occupation/update-work-environments", command)
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void add_working_environment_should_succeed() throws Exception {
        var expectedCodes = Lists.newArrayList("ENV-01", "ENV-03", "ENV-07");
        var command = new UpdateWorkEnvironmentsCommandDTO()
                .occupationId(ERHGO_OCCUPATION_WITHOUT_ROMES_UUID)
                .workEnvironmentCodes(expectedCodes);

        performPut("/erhgo-occupation/update-work-environments", command)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() ->
                assertThat(erhgoOccupationRepository.findById(ERHGO_OCCUPATION_WITHOUT_ROMES_UUID)
                        .orElseThrow()
                        .getWorkEnvironments().stream()
                        .map(WorkEnvironment::getCode))
                        .containsExactlyInAnyOrderElementsOf(expectedCodes)
        );
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void update_working_environment_should_succeed() throws Exception {
        var occupationId = UUID.randomUUID();
        applicationContext.getBean(ErhgoOccupationMotherObject.class).withId(occupationId).withWorkEnvironmentCodes("ENV-01", "ENV-03", "ENV-07").buildAndPersist();

        var expectedCodes = List.of("ENV-03", "ENV-02");
        var command = new UpdateWorkEnvironmentsCommandDTO()
                .occupationId(occupationId)
                .workEnvironmentCodes(expectedCodes);

        performPut("/erhgo-occupation/update-work-environments", command)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() ->
                assertThat(erhgoOccupationRepository.findById(occupationId)
                        .orElseThrow()
                        .getWorkEnvironments().stream()
                        .map(WorkEnvironment::getCode))
                        .containsExactlyInAnyOrderElementsOf(expectedCodes)
        );
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void update_technical_should_succeed() throws Exception {
        var command = new UpdateSpecificationsCommandDTO()
                .occupationId(ERHGO_OCCUPATION_WITHOUT_ROMES_UUID)
                .isTechnical(true);

        performPut("/erhgo-occupation/update-specifications", command)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() -> {
            var updatedOccupation = erhgoOccupationRepository.findById(command.getOccupationId()).orElseThrow();
            assertThat(updatedOccupation.isTechnical()).isTrue();
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void update_criteria_add() throws Exception {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.MULTIPLE).withValueCodes("REP-1-1", "REP-2-1", "REP-3-1", "REP-4-1").buildAndPersist();
        applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.THRESHOLD).withValueCodes("REP-1-2", "REP-2-2", "REP-3-2", "REP-4-2").buildAndPersist();

        var expectedCodes = Lists.newArrayList("REP-2-1", "REP-3-1", "REP-3-2");
        var command = new UpdateCriteriaValuesCommandDTO()
                .erhgoOccupationId(occupation.getId())
                .criteriaValueCodes(expectedCodes);

        performPut("/erhgo-occupation/update-criteria-values", command)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() ->
                assertThat(erhgoOccupationRepository.findById(occupation.getId())
                        .orElseThrow()
                        .getCriteriaValues().stream()
                        .map(CriteriaValue::getCode))
                        .containsExactlyInAnyOrderElementsOf(expectedCodes)
        );
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void update_criteria_remove() throws Exception {
        var multipleCriteria = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.MULTIPLE).withValueCodes("REP-1-1", "REP-2-1", "REP-3-1", "REP-4-1").buildAndPersist();
        var thresholdCriteria = applicationContext.getBean(CriteriaMotherObject.class).withQuestionType(CriteriaQuestionType.THRESHOLD).withValueCodes("REP-1-2", "REP-2-2", "REP-3-2", "REP-4-2").buildAndPersist();
        var initialCriteria = Set.of(multipleCriteria.getCriteriaValues().get(0), multipleCriteria.getCriteriaValues().get(2), thresholdCriteria.getCriteriaValues().get(1));
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withCriteriaValues(initialCriteria)
                .buildAndPersist();

        var expectedCodes = Lists.newArrayList("REP-4-1", "REP-1-1");
        var command = new UpdateCriteriaValuesCommandDTO()
                .erhgoOccupationId(occupation.getId())
                .criteriaValueCodes(expectedCodes);

        performPut("/erhgo-occupation/update-criteria-values", command)
                .andExpect(status().isNoContent());
        txHelper.doInTransaction(() ->
                assertThat(erhgoOccupationRepository.findById(occupation.getId())
                        .orElseThrow()
                        .getCriteriaValues().stream()
                        .map(CriteriaValue::getCode))
                        .containsExactlyInAnyOrderElementsOf(expectedCodes)
        );
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void updateClassifications() throws Exception {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class)
                .withClassificationsCodes("SO-05", "SO-07")
                .buildAndPersist();

        List<String> expectedCodes = List.of("SO-02", "SO-07", "SO-11", "SO-10");
        performPut("/erhgo-occupation/update-erhgo-classifications", new UpdateErhgoClassificationsCommandDTO()
                .id(occupation.getId())
                .erhgoClassifications(expectedCodes))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            assertThat(erhgoOccupationRepository.findById(occupation.getId())
                    .orElseThrow()
                    .getErhgoClassifications().stream()
                    .map(ErhgoClassification::getCode))
                    .containsExactlyInAnyOrderElementsOf(expectedCodes);
        });
    }

}
