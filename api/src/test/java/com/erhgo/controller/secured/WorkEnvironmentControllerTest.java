package com.erhgo.controller.secured;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;

class WorkEnvironmentControllerTest extends AbstractIntegrationTest {

    @Test
    @WithMockKeycloakUser
    void listWorkEnvironments() throws Exception {
        performGetAndExpect("/work-environment/list", "workEnvironments", true);
    }
}
