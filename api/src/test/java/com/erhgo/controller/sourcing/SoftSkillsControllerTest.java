package com.erhgo.controller.sourcing;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.job.SpontaneousCandidatureMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.RecruiterMotherObject;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

class SoftSkillsControllerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;
    static final String USER_ID = "42";
    static final String ORGA_CODE = "S-55";

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    @ResetDataAfter
    void getCandidatePDF() {
        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withFirstname("Pat")
                .withLastname("Toche")
                .withTrimojiPdfUrl("classpath:data/trimoji_src.pdf")
                .buildAndPersist();

        mvc.perform(get(realUrl("/user/soft-skills/%s/pdf".formatted(USER_ID))))
                .andExpect(TestUtils.pdfMatchesContent("trimojiUser.pdf"));
    }

    @SneakyThrows
    @Test
    @WithMockKeycloakUser(roles = {Role.SOURCING, ORGA_CODE})
    @ResetDataAfter
    void getAnonymousCandidatePDF() {
        var user = applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withFirstname("Pat")
                .withLastname("Toche")
                .withTrimojiPdfUrl("classpath:data/trimoji_src.pdf")
                .withChannels(ORGA_CODE)
                .buildAndPersist();

        var recruiter = applicationContext.getBean(RecruiterMotherObject.class).withCode(ORGA_CODE).buildAndPersist();
        var candidature = applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                .withState(GlobalCandidatureState.NEW)
                .withRecruiter(recruiter)
                .withUserProfile(user)
                .withAnonymousCode("PO85")
                .buildAndPersist();
        mvc.perform(get(realUrl("/sourcing/spontaneous-candidature/%d/soft-skills-pdf".formatted(candidature.getId()))))
                .andExpect(TestUtils.pdfMatchesContent("trimojiAnonymous.pdf"));
    }
}
