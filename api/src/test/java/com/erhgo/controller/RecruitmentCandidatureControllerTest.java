package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.CandidatureRecruitmentState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.generators.DataGeneratorService;
import com.erhgo.generators.RecruitmentGenerator;
import com.erhgo.generators.UserProfileGenerator;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import jakarta.validation.constraints.Min;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;

import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.erhgo.TestUtils.jsonMatchesContent;
import static com.erhgo.TestUtils.stringThatMatchesDate;
import static com.erhgo.generators.TestFixtures.*;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class RecruitmentCandidatureControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    private DataGeneratorService dataGeneratorService;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void should_retrieve_recruitment_with_one_matching_candidature_for_recruitment() throws Exception {

        var expectedJson = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("expected/recruitment.json"));
        mvc.perform(get(ApiConstants.API_ODAS_RECRUITMENT + "/" +
                        RECRUITMENT_WITH_MATCHING_CANDIDATURE.getId() + "/matching").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json"))
                .andExpect(content().json(expectedJson, false))
                .andExpect(jsonPath("$.id", is(RECRUITMENT_WITH_MATCHING_CANDIDATURE.getId().intValue())))
                .andExpect(jsonPath("$.code", is(RECRUITMENT_WITH_MATCHING_CANDIDATURE.getCode())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void fetch_anonymous_candidature_should_succeed() throws Exception {
        fetchCandidatureAndExpectVariables("anonymousCandidature")
                .andExpect(jsonPath("$.email", is(emptyOrNullString())))
                .andExpect(jsonPath("$.firstName", is(emptyOrNullString())))
                .andExpect(jsonPath("$.lastName", is(emptyOrNullString())))
                .andExpect(jsonPath("$.phoneNumber", is(emptyOrNullString())))
                .andExpect(jsonPath("$.contactTime", is(emptyOrNullString())));
    }

    private ResultActions fetchCandidatureAndExpectVariables(String expectedJsonFile) throws Exception {
        return mvc.perform(get(ApiConstants.API_ODAS_RECRUITMENT + "/matching-candidature/" + CANDIDATURE_MATCHING.getId()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json"))
                .andExpect(jsonPath("$.code", is(CANDIDATURE_MATCHING.getCode())))
                .andExpect(jsonPath("$.id", is(CANDIDATURE_MATCHING.getId().intValue())))
                .andExpect(jsonPath("$.submissionDate", is(stringThatMatchesDate(CANDIDATURE_MATCHING.getSubmissionDate(), objectMapper))))
                .andExpect(jsonPath("$.recruitment.id", is(RECRUITMENT_WITH_MATCHING_CANDIDATURE.getId().intValue())))
                .andExpect(jsonPath("$.recruitment.jobId", is(J_01.getId().toString())))
                .andExpect(jsonPath("$.notes", iterableWithSize(1)))
                .andExpect(jsonPath("$.candidatureState", is(GlobalCandidatureState.NOT_TREATED_BY_ERHGO.name())))
                .andExpect(jsonMatchesContent(expectedJsonFile));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void should_retrieve_no_matching_candidature_for_recruitment() throws Exception {
        var expectedJson = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("expected/recruitmentWithoutCandidature.json"));

        mvc.perform(get(ApiConstants.API_ODAS_RECRUITMENT + "/" +
                        RECRUITMENT_WITHOUT_MATCHING_CANDIDATURES.getId() + "/matching").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json"))
                .andExpect(content().json(expectedJson, false));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void should_retrieve_not_matching_candidature_for_recruitment() throws Exception {
        mvc.perform(get(ApiConstants.API_ODAS_RECRUITMENT + "/matching-candidature/" + CANDIDATURE_NOT_MATCHING_FOR_USER_WITHOUT_EXPERIENCE.getId()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json"))
                .andExpect(jsonPath("$.id", is(CANDIDATURE_NOT_MATCHING_FOR_USER_WITHOUT_EXPERIENCE.getId().intValue())));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void getAllJobCandidaturesAdmin() {
        getAllJobCandidaturesCommon();
    }

    void getAllJobCandidaturesCommon() {
        var recruitment = dataGeneratorService.createRecruitment(RecruitmentState.PUBLISHED);
        var job = recruitment.getJob();
        var secondRecruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(job);
        var userUuid = "1447748f-40f2-422c-a047-611148d7afb";
        createCandidature(recruitment, CandidatureRecruitmentState.NEW, userProfileGenerator.createUserProfile(UUID.fromString(userUuid + "1"), job.getRecruiterCode()));
        var user2 = userProfileGenerator.createUserProfile(UUID.fromString(userUuid + "2"), job.getRecruiterCode());
        createCandidature(recruitment, CandidatureRecruitmentState.WAITING, user2);
        txHelper.doInTransaction(() -> {
            var c = createCandidature(secondRecruitment, CandidatureRecruitmentState.WAITING, user2);
            c.markAsRefused(CandidatureEmailRefusalState.NONE, "admin");
        });
        createCandidature(secondRecruitment, CandidatureRecruitmentState.SELECTED, userProfileGenerator.createUserProfile(UUID.fromString(userUuid + "3"), E_01_CERA_CODE));
        createCandidature(secondRecruitment, null, userProfileGenerator.createUserProfile(UUID.fromString(userUuid + "4")));

        performGetAndExpect("/job/%s/candidates".formatted(job.getId()), "jobCandidates", false);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void getAllJobCandidaturesOT() {
        getAllJobCandidaturesCommon();
    }

    private void expectMatchingCandidatures(Recruitment recruitment, Set<RecruitmentCandidature> expectedCandidatures, Boolean isMatch) throws Exception {
        mvc.perform(get(ApiConstants.API_ODAS_RECRUITMENT + "/" +
                        recruitment.getId() + "/matching/candidatures/selected?size=10&page=0" + (isMatch != null ? "&isMatch=" + isMatch : "")).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith("application/json"))
                .andExpect(jsonPath("$.content[*]", hasSize(expectedCandidatures.size())))
                .andExpect(jsonPath("$.content[*].candidatureId", containsInAnyOrder(extract(expectedCandidatures, candidature -> candidature.getId().intValue()))))
                .andExpect(jsonPath("$.content[*].userId", containsInAnyOrder(extract(expectedCandidatures, candidature -> candidature.getUserProfile().uuid().toString()))))
                .andExpect(jsonPath("$.content[*].firstName", containsInAnyOrder(extract(expectedCandidatures, candidature -> "Jean-" + candidature.getUserProfile().uuid().toString()))))
                .andExpect(jsonPath("$.content[*].lastName", containsInAnyOrder(extract(expectedCandidatures, candidature -> "Dupont Du " + candidature.getUserProfile().uuid().toString()))))
                .andExpect(jsonPath("$.content[*].phoneNumber", containsInAnyOrder(extract(expectedCandidatures, candidature -> candidature.getUserProfile().generalInformation().getPhoneNumber()))))
                .andExpect(jsonPath("$.content[*].email", containsInAnyOrder(extract(expectedCandidatures, candidature -> candidature.getUserProfile().uuid().toString() + "@erhgo.fr"))))
                .andExpect(jsonPath("$.content[*].candidatureIsArchived", containsInAnyOrder(extract(expectedCandidatures, candidature -> candidature.isArchived()))))
                .andExpect(jsonPath("$.content[*].contactTime", containsInAnyOrder(extract(expectedCandidatures, candidature -> candidature.getUserProfile().generalInformation().getContactTime().name()))));
    }

    private Object[] extract(Set<RecruitmentCandidature> candidatures, Function<RecruitmentCandidature, Object> function) {
        return candidatures.stream().map(function).toArray();
    }

    private Set<RecruitmentCandidature> createCandidature(
            Recruitment recruitment,
            CandidatureRecruitmentState recruitmentState,
            @Min(1) Integer count
    ) {
        userProfileGenerator.createUserProfile(UUID.randomUUID());
        return IntStream.range(0, count).mapToObj(i -> createCandidature(recruitment, recruitmentState, userProfileGenerator.createUserProfile())).collect(Collectors.toSet());

    }

    private RecruitmentCandidature createArchivedCandidature(Recruitment recruitment, CandidatureRecruitmentState recruitmentState) {
        var userProfile = userProfileGenerator.createUserProfile(UUID.randomUUID());

        return applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withRecruitment(recruitment)
                .withUserProfile(userProfile)
                .withValid(true)
                .withIsArchived(true)
                .buildAndPersist();
    }

    private RecruitmentCandidature createCandidature(
            Recruitment recruitment,
            CandidatureRecruitmentState recruitmentState,
            UserProfile userProfile
    ) {
        return dataGeneratorService.createCandidature(
                recruitment,
                userProfile,
                recruitmentState);
    }
}
