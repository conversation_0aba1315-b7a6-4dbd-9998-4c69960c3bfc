package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.Duration;
import com.erhgo.domain.userprofile.UserExperienceGeneratedData;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.openapi.dto.ExperienceTypeDTO;
import com.erhgo.openapi.dto.ForceUserBehaviorDescriptionCommandDTO;
import com.erhgo.openapi.dto.SaveExperienceCommandDTO;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.generation.UserBehaviorDescriptionGenerationService;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.utils.ChecksumUtils;
import org.assertj.core.data.TemporalUnitWithinOffset;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;

import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class UserBehaviorDescriptionControllerTest extends AbstractIntegrationTest {
    @MockBean
    private UserBehaviorDescriptionGenerationService generationService;

    @Mock
    private OpenAIResponse<String> openAIResponse;

    @Autowired
    private ApplicationContext applicationContext;

    static final String USER_ID = "56b64e21-4771-4d01-8cf7-696866d3ae49";

    @Test
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void force_behavior_description() throws Exception {
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withExperience(occupation, Duration.DURATION_1)
                .buildAndPersist();

        mvc.perform(post("/api/odas/user/force-behavior-description")
                        .content(objectMapper.writeValueAsBytes(getforceUserBehaviorDescriptionCommandDTO()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent())
                .andReturn();

        txHelper.doInTransaction(() -> {
            var xpGeneratedData = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow().userExperienceGeneratedData();
            assertThat(xpGeneratedData).isNotNull();
            assertThat(xpGeneratedData.attitude()).isEqualTo("Je suis très pro");
            assertThat(xpGeneratedData.attitudeModifiedByUserInstant()).isCloseToUtcNow(new TemporalUnitWithinOffset(20, ChronoUnit.MINUTES));
            assertThat(xpGeneratedData.checksum()).isNotNull();
        });
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void generate_new_description_when_dirty_if_occupation_has_behavior_description(boolean withBehaviorOnOccupation) throws Exception {
        var generatedDescription = "Je suis super pro !";
        Mockito.when(openAIResponse.getResult()).thenReturn(generatedDescription);
        Mockito.when(generationService.generateUserBehaviorDescription(Mockito.anyString())).thenReturn(openAIResponse);

        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).withBehaviorDescription(withBehaviorOnOccupation ? "behavior description" : null).buildAndPersist();

        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withExperience(occupation, Duration.DURATION_1)
                .buildAndPersist();

        mvc.perform(get(realUrl("/user/%s/behavior-description".formatted(USER_ID))).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.description", is(withBehaviorOnOccupation ? generatedDescription : null)));

        txHelper.doInTransaction(() -> {
            var userBehaviorDescription = applicationContext.getBean(UserProfileRepository.class).findByUserId(USER_ID).orElseThrow().userExperienceGeneratedData();
            if (withBehaviorOnOccupation) {
                assertThat(userBehaviorDescription).isNotNull();
                assertThat(userBehaviorDescription.attitude()).isEqualTo(generatedDescription);
                assertThat(userBehaviorDescription.checksum()).isNotNull();
            } else {
                assertThat(Optional.ofNullable(userBehaviorDescription).map(UserExperienceGeneratedData::attitude)).isEmpty();
            }
        });
    }

    @ParameterizedTest
    @ValueSource(booleans = {false, true})
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    @ResetDataAfter
    void addingExperience_will_dirty_state(boolean sameOccupation) throws Exception {
        var experienceId = UUID.randomUUID();
        var occupation = applicationContext.getBean(ErhgoOccupationMotherObject.class).buildAndPersist();
        var checksum = ChecksumUtils.calculateChecksum(Collections.singleton(sameOccupation ? occupation.getId() : UUID.randomUUID()));

        var generatedAttitude = "Je suis une personne professionnelle";
        applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(USER_ID)
                .withAttitudeText(generatedAttitude, false)
                .withBehaviorDescriptionChecksum(checksum)
                .buildAndPersist();

        performPost(
                "/userExperience/saveExperience",
                new SaveExperienceCommandDTO()
                        .experienceId(experienceId)
                        .experienceType(ExperienceTypeDTO.JOB)
                        .jobTitle("Title of job")
                        .userId(USER_ID)
                        .organizationName("ACME")
                        .erhgoOccupationId(occupation.getId()))
                .andExpect(status().isOk());
        txHelper.doInTransaction(() -> {
            var userBehaviorDescription = applicationContext.getBean(UserProfileRepository.class)
                    .findByUserId(USER_ID)
                    .orElseThrow()
                    .userExperienceGeneratedData();

            var attitude = Optional.ofNullable(userBehaviorDescription).map(UserExperienceGeneratedData::attitude);
            if (sameOccupation) assertThat(attitude).get().isEqualTo(generatedAttitude);
            else assertThat(attitude).isEmpty();
        });
    }

    private ForceUserBehaviorDescriptionCommandDTO getforceUserBehaviorDescriptionCommandDTO() {
        var dto = new ForceUserBehaviorDescriptionCommandDTO();
        return dto.userId(USER_ID).description("Je suis très pro");
    }
}
