package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.MandatoryState;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.erhgooccupation.OccupationQualificationSource;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.referential.AbstractActivityLabel;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.generators.ErhgoOccupationGenerator;
import com.erhgo.generators.EscoOccupationGenerator;
import com.erhgo.generators.JobActivityLabelGenerator;
import com.erhgo.openapi.dto.ActivityLabelDTO;
import com.erhgo.openapi.dto.SaveActivityCommandDTO;
import com.erhgo.repositories.JobActivityLabelRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jakarta.persistence.EntityManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class SaveActivityControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private JobActivityLabelRepository jobActivityLabelRepository;

    @Autowired
    private TransactionTestHelper transactionTestHelper;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private EscoOccupationGenerator escoOccupationGenerator;

    @Autowired
    private JobActivityLabelGenerator jobActivityLabelGenerator;

    @Autowired
    private ErhgoOccupationRepository erhgoOccupationRepository;

    @Autowired
    private ErhgoOccupationGenerator erhgoOccupationGenerator;

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void saveActivityShouldCreateMultipleActivityLabelsWhenActivityIsNew() throws Exception {
        var createActivityCommand = TestUtils.toString(getClass().getResourceAsStream("/data/createActivity.json"));
        // Next UUIDs:  See createActivity.json
        var expectedActivityId = UUID.fromString("c2ed7376-d490-4ccd-8551-485d56364aeb");
        var expectedActivityLabel0Id = UUID.fromString("87ce4220-8820-43ae-ada2-12c3197edbef");
        var expectedActivityLabel1Id = UUID.fromString("0678ecfe-e73c-446b-9813-a895f9ec1cc7");


        mvc.perform(post("/api/odas/activity/JOB/save").content(createActivityCommand).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        var activities = getSortedActivities(expectedActivityId);

        assertThat(activities).hasSize(2);

        assertActivity(activities, 0, expectedActivityLabel0Id);
        assertActivity(activities, 1, expectedActivityLabel1Id);
    }

    @Test
    @WithMockKeycloakUser
    public void saveActivityShouldFailForNonAdmin() throws Exception {
        var activityType = "JOB";
        saveActivityShouldFailForNonAdmin(activityType);
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    public void saveActivityWithOneRemovedActivityLabelAndOneAddedActivityLabelShouldSucceedWhenActivityLabelIsNotUsed() throws Exception {
        var baseActivityLabel = ACT_25;
        var labelUuid = UUID.randomUUID();
        var detailUuid = baseActivityLabel.getActivity().getUuid();
        var command = getSaveActivityCommandWithOneActivityLabelAddedAndOneRemoved(baseActivityLabel, labelUuid);

        mvc.perform(post("/api/odas/activity/JOB/save")
                        .content(objectMapper.writeValueAsString(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        var activities = getSortedActivities(detailUuid);

        assertThat(activities).hasSize(1);

        assertActivity(activities, 0, labelUuid);
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void saveActivityWithOneRemovedActivityLabelShouldFailWhenActivityLabelIsUsed() throws Exception {
        var activityType = "JOB";
        var escoOccupation = escoOccupationGenerator.createEscoOccupationWithReferentialData("Test activity", "http://erhgo.fr/", ISCO_666, jobActivityLabelUsedInCandidatureExperienceAndJobMission, MasteryLevel.COMPLEX, B_01);
        var newLabelUuid = saveActivityWithOneRemovedActivityLabelShouldFailWhenActivityLabelIsUsed(activityType);

        transactionTestHelper.doInTransaction(() -> {
            var oldLabelUuid = jobActivityLabelUsedInCandidatureExperienceAndJobMission.getUuid();
            var mission = entityManager.find(Mission.class, M_02.getId());
            var missionLabels = mission.getActivities().stream().map(JobActivityLabel::getUuid).collect(Collectors.toSet());
            assertThat(missionLabels).contains(newLabelUuid);
            assertThat(missionLabels).doesNotContain(oldLabelUuid);

            var occupation = entityManager.find(EscoOccupation.class, escoOccupation.getUri());
            var skillsActivities = occupation.getSkills()
                    .stream()
                    .flatMap(e -> e.getActivities().stream())
                    .map(JobActivityLabel::getUuid)
                    .toList();
            assertThat(skillsActivities).contains(newLabelUuid);
            assertThat(skillsActivities).doesNotContain(oldLabelUuid);
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void saveActivityWithUpdatedActivityLabelAndNewActivityLabelWhenActivityLabelIsUsedShouldUpdateLabelAndAddActivityLabel() throws Exception {
        var activityType = "JOB";
        saveActivityWithUpdatedActivityLabelAndNewActivityLabelWhenActivityLabelIsUsedShouldUpdateLabelAndAddActivityLabel(activityType);
    }


    public void saveActivityShouldFailForNonAdmin(String activityType) throws Exception {
        var createActivityCommand = TestUtils.toString(getClass().getResourceAsStream("/data/createActivity.json"));

        mvc.perform(post("/api/odas/activity/" + activityType + "/save").content(createActivityCommand).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }


    public UUID saveActivityWithOneRemovedActivityLabelShouldFailWhenActivityLabelIsUsed(String activityType) throws Exception {
        var baseActivityLabel = jobActivityLabelUsedInCandidatureExperienceAndJobMission;
        var labelUuid = UUID.randomUUID();
        var command = getSaveActivityCommandWithOneActivityLabelAddedAndOneRemoved(baseActivityLabel, labelUuid);

        mvc.perform(post("/api/odas/activity/" + activityType + "/save")
                .content(objectMapper.writeValueAsString(command))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        return labelUuid;
    }

    private SaveActivityCommandDTO getSaveActivityCommandWithOneActivityLabelAddedAndOneRemoved(JobActivityLabel baseJobActivityLabel, UUID labelUuid) {
        var label = "Label 0";
        return new SaveActivityCommandDTO()
                .inducedCapacities(baseJobActivityLabel.getInducedCapacities().stream().map(Capacity::getCode).toList())
                .labels(Collections.singletonList(new ActivityLabelDTO().id(labelUuid).title(label).position(0)))
                .description("Description")
                .id(baseJobActivityLabel.getActivity().getUuid());
    }

    public void saveActivityWithUpdatedActivityLabelAndNewActivityLabelWhenActivityLabelIsUsedShouldUpdateLabelAndAddActivityLabel(String activityType) throws Exception {
        var baseActivityLabel = jobActivityLabelUsedInCandidatureExperienceAndJobMission;
        var newLabelUuid = UUID.randomUUID();
        var labelToUpdateUuid = baseActivityLabel.getUuid();
        var detailUuid = baseActivityLabel.getActivity().getUuid();
        var label = "Label 1";
        var command = new SaveActivityCommandDTO()
                .inducedCapacities(baseActivityLabel.getInducedCapacities().stream().map(Capacity::getCode).toList())
                .labels(Lists.newArrayList(
                        new ActivityLabelDTO().id(labelToUpdateUuid).title("Label 0").position(0),
                        new ActivityLabelDTO().id(newLabelUuid).title(label).position(1)
                ))
                .description("Description")
                .id(baseActivityLabel.getActivity().getUuid());

        mvc.perform(post("/api/odas/activity/JOB/save")
                        .content(objectMapper.writeValueAsString(command))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        var activities = getSortedActivities(detailUuid);

        assertThat(activities).hasSize(2);

        assertActivity(activities, 0, labelToUpdateUuid);
        assertActivity(activities, 1, newLabelUuid);
    }

    private List<? extends AbstractActivityLabel> getSortedActivities(UUID detailUuid) {
        return jobActivityLabelRepository.findByActivityUuid(detailUuid).stream().sorted(Comparator.comparing(AbstractActivityLabel::getPosition)).toList();
    }

    private void assertActivity(List<? extends AbstractActivityLabel> activities, int i, UUID expectedActivityId) {
        var activityLabel = activities.get(i);
        assertThat(activityLabel.getPosition()).isEqualTo(i);
        assertThat(activityLabel.getTitle()).isEqualTo("Label " + i);
        assertThat(activityLabel.getUuid()).isEqualTo(expectedActivityId);
        assertThat(activityLabel.getActivity().getDescription()).isEqualTo("Description");
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void saveActivityLabel_should_remove_label_and_update_label_used_in_erhgo_occupation() throws Exception {

        var deletedActivityLabel1 = jobActivityLabelGenerator.createSecondActivityLabelSharingSameActivity(unusedJobActivityLabel_1);
        var deletedActivityLabel2 = jobActivityLabelGenerator.createSecondActivityLabelSharingSameActivity(unusedJobActivityLabel_1);
        var manualDeletedActivityLabel = jobActivityLabelGenerator.createSecondActivityLabelSharingSameActivity(unusedJobActivityLabel_1);
        var unmodifiedActivity = jobActivityLabelUsedOnlyForEscoSkillClassification;

        var command = new SaveActivityCommandDTO()
                .inducedCapacities(Lists.newArrayList(CA1_01.getCode()))
                .labels(Collections.singletonList(new ActivityLabelDTO().id(unusedJobActivityLabel_1.getUuid()).title("Modified").position(0)))
                .description("Description")
                .id(unusedJobActivityLabel_1.getActivity().getUuid());

        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithActivities("Erhgo with activities", Collections.emptyList(), Lists.newArrayList(unmodifiedActivity,
                deletedActivityLabel1,
                deletedActivityLabel2,
                manualDeletedActivityLabel
        ));

        mvc.perform(post("/api/odas/activity/JOB/save").content(objectMapper.writeValueAsString(command))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        assertThat(jobActivityLabelRepository.findAllById(Sets.newHashSet(deletedActivityLabel1.getUuid(), deletedActivityLabel2.getUuid(), manualDeletedActivityLabel.getUuid()))).isNullOrEmpty();
        assertThat(jobActivityLabelRepository.findByActivityUuid(unusedJobActivityLabel_1.getActivity().getUuid())).hasSize(1);

        txHelper.doInTransaction(() -> {
            var nextOccupation = erhgoOccupationRepository.findById(occupation.getId()).orElseThrow();
            assertThat(nextOccupation.getOccupationActivities()).hasSize(2);
            assertThat(nextOccupation.getOccupationActivities()).anyMatch(oa -> oa.getActivity().getUuid().equals(unmodifiedActivity.getUuid()));
            var newOccupationActivities = nextOccupation.getOccupationActivities().stream().filter(a -> a.getActivity().getActivity().getUuid().equals(unusedJobActivityLabel_1.getActivity().getUuid())).toList();
            assertThat(newOccupationActivities).hasSize(1);
            var newOccupationActivity = newOccupationActivities.get(0);
            assertThat(newOccupationActivity.getState()).isEqualTo(MandatoryState.OPTIONAL);
            assertThat(newOccupationActivity.getSource()).isEqualTo(OccupationQualificationSource.MANUAL);
        });
    }

    @Test
    @WithMockKeycloakUser(username = "admin", roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void saveActivityLabel_should_remove_label_and_update_label_used_in_erhgo_occupation_preventing_duplicates() throws Exception {

        var deletedActivityLabel1 = jobActivityLabelGenerator.createSecondActivityLabelSharingSameActivity(unusedJobActivityLabel_1);

        var command = new SaveActivityCommandDTO()
                .inducedCapacities(Lists.newArrayList(CA1_01.getCode()))
                .labels(Collections.singletonList(new ActivityLabelDTO().id(unusedJobActivityLabel_1.getUuid()).title("Modified").position(0)))
                .description("Description")
                .id(unusedJobActivityLabel_1.getActivity().getUuid());

        var occupation = erhgoOccupationGenerator.createErhgoOccupationWithActivities("Erhgo with activities", Collections.emptyList(), Lists.newArrayList(
                deletedActivityLabel1,
                unusedJobActivityLabel_1
        ));

        mvc.perform(post("/api/odas/activity/JOB/save").content(objectMapper.writeValueAsString(command))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        assertThat(jobActivityLabelRepository.findByActivityUuid(unusedJobActivityLabel_1.getActivity().getUuid())).hasSize(1);

        txHelper.doInTransaction(() -> {
            var nextOccupation = erhgoOccupationRepository.findById(occupation.getId()).orElseThrow();
            assertThat(nextOccupation.getOccupationActivities()).hasSize(1);
            assertThat(nextOccupation.getOccupationActivities()).first().matches(a -> a.getActivity().getUuid().equals(unusedJobActivityLabel_1.getUuid()));
        });
    }
}
