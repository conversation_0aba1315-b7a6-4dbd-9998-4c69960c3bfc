package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationMotherObject;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.enums.RecruitmentSendNotificationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.sourcing.SourcingInvitationMotherObject;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserNotificationMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.*;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.RecruitmentProfileRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;

import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

import static com.erhgo.generators.TestFixtures.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class RecruitmentControllerTest extends AbstractIntegrationTestWithFixtures {


    @Autowired
    private OrganizationGenerator organizationGenerator;

    @Autowired
    private RecruitmentRepository recruitmentRepository;

    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private UserProfileGenerator userProfileGenerator;

    @Autowired
    private CandidatureGenerator candidatureGenerator;

    @Autowired
    private RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @Autowired
    private ApplicationContext applicationContext;


    private static final String EMPLOYER_CODE = "M-02";

    private static final LocationDTO LOCATION_DTO = new LocationDTO()
            .city("Lyon")
            .postcode("69001")
            .citycode("69123")
            .departmentCode("69")
            .regionName("Auvergne-Rhône-Alpes")
            .latitude(45.758f)
            .longitude(4.835f)
            .radiusInKm(30);

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void create_recruitment() throws Exception {

        // Ensure profile is modifiable
        assertThat(recruitmentProfileRepository.findById(P_03.getUuid()).orElseThrow().isModifiable()).isTrue();

        var createRecruitmentCommandDTO = new SaveRecruitmentCommandDTO()
                .recruitmentProfileUuid(P_03.getUuid())
                .typeContract(TypeContractDTO.CDI)
                .state(RecruitmentStateDTO.DRAFT)
                .location(LOCATION_DTO);

        performPost("/recruitment/create", createRecruitmentCommandDTO)
                .andExpect(status().isCreated())
                .andExpect(TestUtils.jsonMatchesContent("recruitmentCreation"));

        // Ensure profile is not modifiable anymore
        assertThat(recruitmentProfileRepository.findById(P_03.getUuid()).orElseThrow().isModifiable()).isFalse();
        assertThat(recruitmentRepository.findById(4L).orElseThrow().getManagerUserId()).isEqualTo("uuid");
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void update_recruitment() throws Exception {
        var adminId = Collections.singleton(ADMIN_USER_ID);
        var recruitment = RECRUITMENT_WITH_MATCHING_CANDIDATURE;

        var createRecruitmentCommandDTO = new SaveRecruitmentCommandDTO()
                .state(RecruitmentStateDTO.PUBLISHED)
                .recruitmentProfileUuid(P_01.getUuid())
                .title("Another title") // Champ modifié
                .location(LOCATION_DTO)
                .typeContract(TypeContractDTO.CDI)
                .usersIdToNotify(Collections.singletonList(ADMIN_USER_ID));

        recruitment.setTitle("Another title");
        performPatch(ApiConstants.API_ODAS_RECRUITMENT + ApiConstants.SEPARATOR + recruitment.getId() + "/update", createRecruitmentCommandDTO)
                .andExpect(status().isCreated())
                .andExpect(TestUtils.jsonMatchesContent("recruitmentUpdate"));

        txHelper.doInTransaction(() -> {
            var newRecruitment = recruitmentRepository.findById(recruitment.getId()).orElseThrow();

            assertThat(newRecruitment.getTitle()).isEqualTo("Another title");
            assertThat(newRecruitment.getRecruitmentProfile().getUuid()).isEqualTo(P_01.getUuid());
            assertThat(newRecruitment.getUsersIdToNotify()).isEqualTo(adminId);
        });
    }


    @Test
    @WithMockKeycloakUser
    void should_fail_to_create_when_TypeContract_is_missing() throws Exception {

        var createRecruitmentCommandDTO = new SaveRecruitmentCommandDTO();
        createRecruitmentCommandDTO.setRecruitmentProfileUuid(P_03.getUuid());

        performPost("/recruitment/create", createRecruitmentCommandDTO)
                .andExpect(status().isBadRequest());
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void should_get_recruitment_with_forced_url() throws Exception {
        var recruitmentId = new AtomicLong();
        txHelper.doInTransaction(() -> {
            var recruiter = organizationGenerator.createRecruiter("E-52", AbstractOrganization.OrganizationType.SOURCING);
            recruiter.setForcedUrl("jenesuisPasunCv.fr");
            var job = jobGenerator.createJob("J-42", recruiter, "SOURCING TEST JOB");
            var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(job);
            recruitment.setExternalUrl("yolo.fr");
            recruitmentId.set(recruitment.getId());
        });
        mvc.perform(get("/api/odas/sourcing/recruitment/%s/detail".formatted(recruitmentId.longValue()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.externalUrl", is("jenesuisPasunCv.fr")));
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    void should_list_recruitments() {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withPublicationDate(new Date())
                .withState(RecruitmentState.PUBLISHED)
                .withSendNotificationState(RecruitmentSendNotificationState.DONE, Instant.ofEpochMilli(10_000_000_000L))
                .buildAndPersist();
        var recruitmentBis = applicationContext.getBean(RecruitmentMotherObject.class)
                .withPublicationDate(new Date())
                .withState(RecruitmentState.PUBLISHED)
                .withSendNotificationState(RecruitmentSendNotificationState.DONE, Instant.ofEpochMilli(20_000_000_000L))
                .buildAndPersist();
        applicationContext.getBean(SourcingInvitationMotherObject.class).withGuests(recruitment.getJob().getRecruiter()).withHost(organizationGenerator.createRecruiter("T-0545445")).buildAndPersist();
        var user = userProfileGenerator.createUserProfile();
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withRecruitment(recruitment).withState(GlobalCandidatureState.NOT_TREATED_BY_ERHGO).generated(false).buildAndPersist();
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withRecruitment(recruitment).generated(true).withState(GlobalCandidatureState.NEW).withUserProfile(user).buildAndPersist();
        applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withRecruitment(recruitmentBis).withState(GlobalCandidatureState.REFUSED_ON_CALL).withUserProfile(user).buildAndPersist();
        applicationContext.getBean(UserNotificationMotherObject.class).withRecruitment(recruitment).withUserProfile(user).withNotificationType(NotificationType.EMAIL).buildAndPersist();
        applicationContext.getBean(UserNotificationMotherObject.class).withRecruitment(recruitment).withUserProfile(user).withNotificationType(NotificationType.MOBILE).buildAndPersist();
        applicationContext.getBean(UserNotificationMotherObject.class).withRecruitment(recruitment).withUserProfile(user).withNotificationType(NotificationType.MOBILE).buildAndPersist();
        applicationContext.getBean(UserNotificationMotherObject.class).withRecruitment(recruitment).withUserProfile(user).withNotificationType(NotificationType.BOTH).buildAndPersist();
        applicationContext.getBean(UserNotificationMotherObject.class).withRecruitment(recruitment).withUserProfile(user).withNotificationType(NotificationType.NONE).buildAndPersist();

        performGetAndExpect("/recruitment/list?selectedProjects=%s&selectedProjects=%s&withNewCandidaturesOnly=false&withOpenRecruitmentOnly=false&page=0&size=10&internal=false&by=JOB&direction=ASC&query=".formatted(recruitment.getRecruiterCode(), recruitmentBis.getRecruiterCode()), "listRecruitments", false);

    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void change_recruitment_state() throws Exception {
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withRecruiter(E_02_SOGILIS)
                .withState(RecruitmentState.DRAFT)
                .withOccupation(applicationContext.getBean(ErhgoOccupationMotherObject.class).withCapacities(CA1_12).buildAndPersist())
                .buildAndPersist();

        performPost("/api/odas/recruitment/%s/change-state".formatted(recruitment.getCode()),
                new ChangeRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.PUBLISHED)
        ).andExpect(status().isNoContent());

        assertThat(recruitmentRepository.findById(recruitment.getId()).orElseThrow())
                .matches(r -> r.getState() == RecruitmentState.PUBLISHED
                        && r.getSendNotificationState() == null
                        && r.getPublicationDate() != null
                );

        performPost("/api/odas/recruitment/%s/change-state".formatted(recruitment.getCode()),
                new ChangeRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.SELECTION)
        ).andExpect(status().isNoContent());

        assertThat(recruitmentRepository.findById(recruitment.getId()).orElseThrow().getState()).isEqualTo(RecruitmentState.SELECTION);

        performPost("/api/odas/recruitment/%s/change-state".formatted(recruitment.getCode()),
                new ChangeRecruitmentStateCommandDTO().nextState(RecruitmentStateDTO.CLOSED)
        ).andExpect(status().isNoContent());
        assertThat(recruitmentRepository.findById(recruitment.getId()).orElseThrow().getState()).isEqualTo(RecruitmentState.CLOSED);
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ParameterizedTest
    @EnumSource(RecruitmentSortDTO.class)
    void list_recruitments_orders(RecruitmentSortDTO sortDTO) throws Exception {
        mvc.perform(get("/api/odas/recruitment/list?by=%s&direction=ASC&organizationCode=E-02&size=10&page=0&withNewCandidaturesOnly=false".formatted(sortDTO.name()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("recruitments"));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void list_recruitments_without_selected_project() throws Exception {
        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&organizationCode=E-02&size=10&page=0&withNewCandidaturesOnly=false"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].code", contains("R-1", "R-2", "R-3")));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void list_recruitments_with_selected_project() throws Exception {
        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&organizationCode=E-02&size=10&page=0&withNewCandidaturesOnly=false&selectedProjects=" + M_03_BOTANIC_CODE)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].code", contains("R-1", "R-2")));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void list_recruitments_search_on_sourcing_organization_type() throws Exception {
        var idRef = new AtomicLong();
        txHelper.doInTransaction(() -> {
            var recruiter = organizationGenerator.createRecruiter("E-52", AbstractOrganization.OrganizationType.SOURCING);
            var job = jobGenerator.createJob("J-42", recruiter, "SOURCING TEST JOB");
            var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(job);
            idRef.set(recruitment.getId());
        });

        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&size=10&page=0&withNewCandidaturesOnly=false")
                        .queryParam("organizationTypeFilter", OrganizationTypeDTO.SOURCING.name())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(idRef.intValue())));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void list_recruitments_with_selected_project_admin() throws Exception {
        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&size=10&page=0&withNewCandidaturesOnly=false&selectedProjects=" + M_03_BOTANIC_CODE)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].code", contains("R-1", "R-2")));
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @Test
    void list_recruitments_search_on_job_title() throws Exception {
        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&organizationCode=E-02&size=10&page=0&withNewCandidaturesOnly=false").param("query", "baCKen")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].code", contains("R-3")));
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @Test
    void list_recruitments_search_on_profile() throws Exception {
        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&organizationCode=E-02&size=10&page=0&withNewCandidaturesOnly=false").param("query", "senIor")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].code", contains("R-3")));
    }

    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @Test
    @ResetDataAfter
    void list_recruitments_search_on_city() throws Exception {
        txHelper.doInTransaction(() -> {
            recruitmentRepository.findOneByCode("R-1").setLocation(Location.builder().city("Montélimar").build());
        });
        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&organizationCode=E-02&size=10&page=0&withNewCandidaturesOnly=false").param("query", "téLi")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].code", contains("R-1")));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void list_recruitments_search_on_employer() throws Exception {
        var idRef = new AtomicLong();
        txHelper.doInTransaction(() -> {
            var orga = organizationGenerator.createEmployer("E-52", E_02_SOGILIS);
            var job = jobGenerator.createJob("J-42", orga);
            var rec = recruitmentGenerator.createRecruitmentWithNoRequirement(job);
            orga.setTitle("macDoxx");
            idRef.set(rec.getId());
        });

        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&size=10&page=0&withNewCandidaturesOnly=false")
                        .queryParam("organizationCode", E_02_SOGILIS_CODE)
                        .param("query", "Cdox")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(idRef.intValue())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    void list_all_recruitments() throws Exception {
        var idRef = new AtomicLong();
        var idRef2 = new AtomicLong();
        var codeOrga1 = "E-52";
        var codeOrga2 = "E-53";
        txHelper.doInTransaction(() -> {
            var orga = organizationGenerator.createEmployer(codeOrga1, E_02_SOGILIS);
            var orga2 = organizationGenerator.createRecruiter(codeOrga2);
            var job = jobGenerator.createJob("J-42", orga);
            var job2 = jobGenerator.createJob("J-43", orga2, "macDoxx");
            var rec = recruitmentGenerator.createRecruitmentWithNoRequirement(job);
            var rec2 = recruitmentGenerator.createRecruitmentWithNoRequirement(job2);
            orga.setTitle("macDoxx");
            idRef.set(rec.getId());
            idRef2.set(rec2.getId());
        });
        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&size=10&page=0&withNewCandidaturesOnly=false")
                        .param("query", "Cdox")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", containsInAnyOrder(idRef.intValue(), idRef2.intValue())))
                .andExpect(jsonPath("$.content[*].recruiterCode", containsInAnyOrder(E_02_SOGILIS_CODE, codeOrga2)));
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void list_recruitments_for_candidate() throws Exception {
        var userProfile = userProfileGenerator.createUserProfile();

        var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(J_01);
        candidatureGenerator.createCandidature(userProfile, recruitment);


        // On crée un recrutement non publié pour s'assurer qu'il n'est pas retourné
        recruitmentGenerator.createRecruitmentWithNoRequirement(RecruitmentState.DRAFT, J_01);

        txHelper.doInTransaction(() -> {
            var candidature = recruitmentCandidatureRepository.findByUserProfileUserId(KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE).stream().findFirst().orElseThrow();
            candidature.markAsRefused(CandidatureEmailRefusalState.NONE, "admin");
            var refusedRecruitment = candidature.getRecruitment();
            refusedRecruitment.getLocation().setLatitude(45.766f);
            refusedRecruitment.getLocation().setLongitude(5f);
            refusedRecruitment.getLocation().setCity("Meyzieu");
            refusedRecruitment.getLocation().setRadiusInKm(72);
        });

        mvc.perform(get("/api/odas/recruitment/" + J_01.getId() + "/candidate/" + KEYCLOAK_USER_ID_WITH_MATCHING_CANDIDATURE + "/list?size=10&page=0")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("recruitmentsForCandidate"));
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void list_recruitments_filtered_on_state() throws Exception {
        txHelper.doInTransaction(() ->
                recruitmentRepository.findAll().forEach(r -> r.setState(RecruitmentState.CLOSED, false))
        );
        var expectedRecruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(RecruitmentState.PUBLISHED, J_01);
        Stream.of(RecruitmentState.CLOSED, RecruitmentState.DRAFT, RecruitmentState.UNPUBLISHED, RecruitmentState.SELECTION).forEach(state -> recruitmentGenerator.createRecruitmentWithNoRequirement(state, J_01));
        mvc.perform(get("/api/odas/recruitment/list?by=" + RecruitmentSortDTO.RECRUITER + "&direction=ASC&organizationCode=E-02&size=10&page=0&withNewCandidaturesOnly=false")
                        .param("withOpenRecruitmentOnly", "true")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].code", contains(expectedRecruitment.getCode())));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_recruitment_for_update() throws Exception {
        performGetAndExpect("/recruitment/" + RECRUITMENT_WITH_MATCHING_CANDIDATURE.getCode(), "recruitmentForUpdate", false)
                .andExpect(status().isOk());
    }

    @ResetDataAfter
    @ParameterizedTest
    @ValueSource(strings = {Role.ODAS_ADMIN})
    void should_list_recruitment_for_job(String role) throws Exception {
        TestUtils.mockRoles(role);
        var firstJob = jobGenerator.createJobForEmployer(EMPLOYER_CODE);
        var secondJob = jobGenerator.createJobForEmployer(EMPLOYER_CODE);
        var thirdJob = jobGenerator.createJobForEmployer("M-03");

        var firstRecruitment = recruitmentGenerator.createDefaultRecruitmentForJob(firstJob, RecruitmentState.PUBLISHED);
        recruitmentGenerator.createDefaultRecruitmentForJob(firstJob, RecruitmentState.CLOSED);
        recruitmentGenerator.createDefaultRecruitmentForJob(secondJob, RecruitmentState.PUBLISHED);
        recruitmentGenerator.createDefaultRecruitmentForJob(thirdJob, RecruitmentState.PUBLISHED);

        mvc.perform(get("/api/odas/recruitment/published-for-job/" + firstJob.getId())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()", is(1)))
                .andExpect(jsonPath("$[*].id", hasItem(firstRecruitment.getId().intValue())))
                .andExpect(status().isOk());
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_matching_candidature() throws Exception {
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class)
                .withFirstname("john")
                .withLastname("doe")
                .withEmail("a@a")
                .withPhoneNumber("0123456789")
                .withUserId("USER ID")
                .buildAndPersist();

        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withTitle("recrutement 1")
                .withRecruitmentProfileQuestion("Question profil")
                .buildAndPersist();
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withRecruitment(recruitment)
                .withAnswer("Réponse candidature")
                .withIsArchived(false)
                .withEffort(1)
                .buildAndPersist();
        performGetAndExpect("/recruitment/matching-candidature/%d".formatted(candidature.getId()), "candidatureSummary.json", false)
                .andExpect(status().isOk());
    }

}
