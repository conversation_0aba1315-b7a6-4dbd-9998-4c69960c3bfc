package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.TransactionTestHelper;
import com.erhgo.domain.candidature.job.CandidatureNote;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.UserNote;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.services.notes.ObsoleteNoteHandlerScheduler;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

class ObsoleteNoteHandlerControllerTest extends AbstractIntegrationTest {

    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    private ObsoleteNoteHandlerScheduler scheduler;

    @Autowired
    TransactionTestHelper txHelper;

    @Value("${application.notesLifespanInMonth}")
    private int notesLifespanInMonth;

    @Test
    void checkNotesAreUpToDate_with_outdatedNote() {
        var normalNoteText = "Normal note";
        var now = LocalDateTime.now();
        var limitDateOfNote = now.minusMonths(notesLifespanInMonth).minusDays(1);
        var userWithOutdatedNote = applicationContext.getBean(UserProfileMotherObject.class)
                .withNoteAtDate(normalNoteText + "1", limitDateOfNote)
                .buildAndPersist();
        var candidatureWithOutdatedNote = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withNoteAtDate(normalNoteText + "2", limitDateOfNote)
                .buildAndPersist();

        var userWithNormalNote = applicationContext.getBean(UserProfileMotherObject.class)
                .withNoteAtDate(normalNoteText + "3", now)
                .buildAndPersist();
        var candidatureWithNormalNote = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withNoteAtDate(normalNoteText + "4", now)
                .buildAndPersist();

        scheduler.checkNotesAreUpToDate();

        txHelper.doInTransaction(() -> {
            var outdatedUserNote = findFirstUserNote(userWithOutdatedNote);
            var outdatedCandidatureNote = findFirstCandidatureNote(candidatureWithOutdatedNote);

            var normalUserNote = findFirstUserNote(userWithNormalNote);
            var normalCandidatureNote = findFirstCandidatureNote(candidatureWithNormalNote);

            assertThat(outdatedUserNote.getContent()).isEqualTo(UserNote.REMOVED_NOTE_TEXT);
            assertThat(outdatedCandidatureNote.getText()).isEqualTo(UserNote.REMOVED_NOTE_TEXT);

            assertThat(normalUserNote.getContent()).startsWith(normalNoteText);
            assertThat(normalCandidatureNote.getText()).startsWith(normalNoteText);
        });
    }

    private UserNote findFirstUserNote(UserProfile user) {
        return applicationContext.getBean(UserProfileRepository.class)
                .findByUserId(user.userId())
                .orElseThrow()
                .userNotes()
                .iterator()
                .next();
    }

    private CandidatureNote findFirstCandidatureNote(RecruitmentCandidature candidature) {
        return applicationContext.getBean(RecruitmentCandidatureRepository.class)
                .findById(candidature.getId())
                .orElseThrow()
                .getCandidatureNotes()
                .stream()
                .findFirst()
                .orElse(null);
    }

}
