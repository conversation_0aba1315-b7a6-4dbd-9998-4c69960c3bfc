package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.enums.ContractType;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.MailingListService;
import lombok.SneakyThrows;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Map;
import java.util.Set;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

class CandidatureEmailValidationControllerTest extends AbstractIntegrationTest {


    private static final String USER_ID = "6cf3b6ca-8157-4bdf-b186-2e71644c032e";
    private static final String EMAIL = "a@æ";
    @Autowired
    ApplicationContext applicationContext;

    @MockBean
    MailingListService mailingListService;


    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @SneakyThrows
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    void publish_candidature_sends_confirmation_mail(boolean alreadyPublished) {
        var recruiterTitle = "Le recruteur";
        var jobTitle = "Boulanger";
        var cityTitle = "59000 Lille";
        var firstName = " J.P   ";
        var contract = ContractType.CDI;
        var time = 35;
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).withFirstname(firstName).withEmail(EMAIL).buildAndPersist();
        var recruiter = applicationContext.getBean(OrganizationGenerator.class).createRecruiter("C-42", recruiterTitle, AbstractOrganization.OrganizationType.SOURCING);
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withSalaries(19000, 30000)
                .withWorkingWeeklyTime(time)
                .withLocation(Location.builder().citycode("59000")
                        .city(cityTitle.split(" ")[1])
                        .longitude(42f)
                        .latitude(24f)
                        .postcode(cityTitle.split(" ")[0])
                        .departmentCode("59")
                        .regionName("Là-haut")
                        .radiusInKm(30)
                        .build())
                .withTypeContract(contract)
                .withJobTitle(jobTitle)
                .withRecruiter(recruiter)
                .buildAndPersist();
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withRecruitment(recruitment).withUserProfile(userProfile)
                .withCandidatureState(alreadyPublished ? CandidatureState.VALIDATED : CandidatureState.STARTED)
                .withState(alreadyPublished ? GlobalCandidatureState.INTRODUCE_TO_CLIENT : GlobalCandidatureState.NOT_FINALIZED)
                .withValid(true)
                .buildAndPersist();

        mvc.perform(post("/api/odas/candidature/publish")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("""
                                {"candidatureId": "%d"}
                                """.formatted(candidature.getId())))
                .andExpect(MockMvcResultMatchers.status().isNoContent());

        var expectedParams = Map.of("JOB", jobTitle,
                "RECRUITER", recruiterTitle,
                "CITY", cityTitle,
                "NAME", firstName.trim(),
                "CONTRACT", contract.name(),
                "TIME", "" + time,
                "SALARY", "19 000 € - 30 000 €");
        Mockito.verify(mailingListService, Mockito.times(alreadyPublished ? 0 : 1)).sendMailsForTemplate(Set.of(EMAIL), 308L, expectedParams, null);

    }


    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @ResetDataAfter
    @SneakyThrows
    @WithMockKeycloakUser(roles = Role.CANDIDATE, id = USER_ID)
    void publish_candidature_sends_confirmation_mail_no_data(boolean alreadyPublished) {
        var recruiterTitle = "Le recruteur";
        var jobTitle = "Boulanger";
        var contract = ContractType.CDI;
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID).withFirstname(null).withEmail(EMAIL).buildAndPersist();
        var recruiter = applicationContext.getBean(OrganizationGenerator.class).createRecruiter("C-42", recruiterTitle, AbstractOrganization.OrganizationType.SOURCING);
        var recruitment = applicationContext.getBean(RecruitmentMotherObject.class)
                .withWorkingWeeklyTime(null)
                .withLocation(null)
                .withTypeContract(contract)
                .withJobTitle(jobTitle)
                .withRecruiter(recruiter)
                .buildAndPersist();
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class).withRecruitment(recruitment).withUserProfile(userProfile)
                .withCandidatureState(alreadyPublished ? CandidatureState.VALIDATED : CandidatureState.STARTED)
                .withState(alreadyPublished ? GlobalCandidatureState.INTRODUCE_TO_CLIENT : GlobalCandidatureState.NOT_FINALIZED)
                .withValid(true)
                .buildAndPersist();

        mvc.perform(post("/api/odas/candidature/publish")
                .contentType(MediaType.APPLICATION_JSON)
                .content("""
                        {"candidatureId": "%d"}
                        """.formatted(candidature.getId())));

        var expectedParams = Map.of("JOB", jobTitle,
                "RECRUITER", recruiterTitle,
                "CITY", "Non précisé",
                "NAME", "",
                "CONTRACT", contract.name(),
                "TIME", "",
                "SALARY", "");
        Mockito.verify(mailingListService, Mockito.times(alreadyPublished ? 0 : 1)).sendMailsForTemplate(Set.of(EMAIL), 308L, expectedParams, null);

    }
}
