package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.JobMotherObject;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.OrganizationGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class PrivateJobControllerTest extends AbstractIntegrationTest {

    private static final String USER_ID_ON_NO_CHANNEL = "2fdfe58a-8de9-4e94-9e80-6db0d548a2da";
    private static final String USER_ID_ON_PRIVATE_CHANNEL = "2fdfe58a-8de9-4e94-9e80-6db0d548a2db";

    @Autowired
    private ApplicationContext applicationContext;

    private Recruitment recruitment;

    @BeforeEach
    void before() {
        var channel = "T-52";
        var organization = applicationContext.getBean(OrganizationGenerator.class).createRecruiter(channel, AbstractOrganization.OrganizationType.TERRITORIAL, true, false, false);
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID_ON_NO_CHANNEL).buildAndPersist();
        applicationContext.getBean(UserProfileMotherObject.class).withUserId(USER_ID_ON_PRIVATE_CHANNEL).withChannels(channel).buildAndPersist();
        var job = applicationContext.getBean(JobMotherObject.class).withRecruiter(organization).buildAndPersist();
        recruitment = applicationContext.getBean(RecruitmentMotherObject.class).withState(RecruitmentState.PUBLISHED).withJob(job).buildAndPersist();

    }


    @Test
    @DisplayName("""
            Given a user associated to no channel
             When he accesses to a recruitment
             Then 403 error appends
            """)
    @SneakyThrows
    @WithMockKeycloakUser(id = USER_ID_ON_NO_CHANNEL, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void getRecruitmentForAuthenticatedUser() {
        mvc.perform(get("/api/odas/public/recruitment/" + recruitment.getCode()))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("""
            Given an unauthenticated user
             When he accesses to a recruitment
             Then no error appends
            """)
    @SneakyThrows
    @ResetDataAfter
    void getRecruitmentForUnauthenticatedUser() {
        mvc.perform(get("/api/odas/public/recruitment/" + recruitment.getCode()))
                .andExpect(status().isOk());
    }


    @Test
    @DisplayName("""
            Given a user associated to private channel
             When he accesses to a recruitment
             Then no error appends
            """)
    @SneakyThrows
    @WithMockKeycloakUser(id = USER_ID_ON_PRIVATE_CHANNEL, roles = {Role.CANDIDATE})
    @ResetDataAfter
    void getRecruitmentForAuthenticatedUserOnPrivateChannel() {
        mvc.perform(get("/api/odas/public/recruitment/" + recruitment.getCode()))
                .andExpect(status().isOk());
    }
}
