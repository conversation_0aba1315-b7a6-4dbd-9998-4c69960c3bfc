package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.job.RecruitmentCandidatureMotherObject;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.openapi.dto.UpdateAvailabilityForCandidatureCommandDTO;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.MailingListService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class CandidatureAvailabilityControllerTest extends AbstractIntegrationTest {

    private static final String USER_ID = "56b64e21-4771-4d01-8cf7-696866d3ae49";

    @Autowired
    ApplicationContext applicationContext;

    @Autowired
    RecruitmentCandidatureRepository recruitmentCandidatureRepository;

    @MockBean
    MailingListService mailingListService;


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void setCandidatureAvailability_availableNow() throws Exception {
        // GIVEN
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withSituation(Situation.STANDBY).withEmail(USER_ID).withUserId(USER_ID).buildAndPersist();
        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withAvailability(null, null)
                .buildAndPersist();
        var recruitmentId = candidature.getRecruitment().getId();
        //WHEN
        performPost("/candidature/availability/%s".formatted(recruitmentId), new UpdateAvailabilityForCandidatureCommandDTO().isAvailable(true))
                .andExpect(status().isNoContent())
        ;

        // THEN
        txHelper.doInTransaction(() -> {
            var fetchedCandidature = recruitmentCandidatureRepository.findById(candidature.getId()).orElseThrow();
            assertThat(fetchedCandidature.getIsAvailable()).isTrue();
            assertThat(fetchedCandidature.getAvailabilityDelayInMonth()).isNull();
            assertThat(fetchedCandidature.getUserProfile().generalInformation().getSituation()).isEqualTo(Situation.STANDBY);
        });
    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(id = USER_ID, roles = Role.CANDIDATE)
    void setCandidatureAvailability_availableIn6months_updatesUserSituationIfNull() throws Exception {
        // GIVEN
        var userProfile = applicationContext.getBean(UserProfileMotherObject.class).withEmail(USER_ID).withUserId(USER_ID).buildAndPersist();

        var candidature = applicationContext.getBean(RecruitmentCandidatureMotherObject.class)
                .withUserProfile(userProfile)
                .withAvailability(null, null)
                .buildAndPersist();
        var recruitmentId = candidature.getRecruitment().getId();
        //WHEN
        performPost("/candidature/availability/%s".formatted(recruitmentId), new UpdateAvailabilityForCandidatureCommandDTO().isAvailable(false).availabilityDelayInMonth(6))
                .andExpect(status().isNoContent())
        ;

        // THEN
        txHelper.doInTransaction(() -> {
            var fetchedCandidature = recruitmentCandidatureRepository.findById(candidature.getId()).orElseThrow();
            assertThat(fetchedCandidature.getIsAvailable()).isFalse();
            assertThat(fetchedCandidature.getAvailabilityDelayInMonth()).isEqualTo(6);
            assertThat(fetchedCandidature.getUserProfile().generalInformation().getSituation()).isEqualTo(Situation.RESEARCHING);
            assertThat(fetchedCandidature.getUserProfile().generalInformation().getDelayInMonth()).isEqualTo(6);

        });
    }


}
