package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.TransactionTestHelper;
import com.erhgo.config.ApiConstants;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.enums.AcquisitionModality;
import com.erhgo.domain.job.Mission;
import com.erhgo.domain.referential.Context;
import com.erhgo.generators.JobGenerator;
import com.erhgo.generators.QuestionForContextsGenerator;
import com.erhgo.generators.RecruitmentGenerator;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.RecruitmentProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import org.hamcrest.Matcher;
import org.hamcrest.Matchers;
import org.hamcrest.collection.IsIterableContainingInAnyOrder;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

import static com.erhgo.generators.TestFixtures.*;
import static com.erhgo.openapi.dto.AcquisitionModalityDTO.INTEGRATION_PROCESS;
import static com.erhgo.openapi.dto.AddOptionalsCommandItemDTO.OptionalTypeEnum.ACTIVITY;
import static com.erhgo.openapi.dto.AddOptionalsCommandItemDTO.OptionalTypeEnum.CONTEXT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.collection.IsEmptyIterable.emptyIterable;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class RecruitmentProfileControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private QuestionForContextsGenerator questionForContextsGenerator;

    @Autowired
    private RecruitmentProfileRepository recruitmentProfileRepository;

    @Autowired
    private TransactionTestHelper transactionTestHelper;

    @Autowired
    private JobGenerator jobGenerator;

    @Autowired
    private RecruitmentGenerator recruitmentGenerator;

    private static final String EMPLOYER_CODE = "M-020";

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void shouldSaveContextQuestion() throws Exception {
        var question = "Hey!";

        doSaveContextQuestion(question, MANDATORY_CONTEXT).andExpect(status().isNoContent());

        assertThatTwoContextQuestions(question);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void should_fail_to_define_question_on_context_not_associated_to_profile() throws Exception {
        doSaveContextQuestion("Hey!", CT_11).andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void should_fail_to_define_question_on_optional_context() throws Exception {
        doSaveContextQuestion("Hey!", CONTEXT_USED_IN_JOB_OF_FORMATION).andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void shouldUpdateContextQuestion() throws Exception {
        var customQuestion = "Yo!";

        doSaveContextQuestion("Hey!", MANDATORY_CONTEXT)
                .andExpect(status().isNoContent());
        doSaveContextQuestion(customQuestion, MANDATORY_CONTEXT)
                .andExpect(status().isNoContent());

        assertThatTwoContextQuestions(customQuestion);
    }

    private void assertThatTwoContextQuestions(String question) {
        transactionTestHelper.doInTransaction(() -> {
            var profile = recruitmentProfileRepository.getOne(MODIFIABLE_PROFILE_OF_JOB_1.getUuid());
            assertThat(profile.getContextQuestions()).hasSize(2);
            assertThat(profile.getContextQuestions()).anyMatch(c -> c.getContext().getId().equals(MANDATORY_CONTEXT.getId()) && c.getQuestion().getTitle().equals(question));
        });
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void shouldNotSaveCustomContextLabelForContextNotAssociatedToJob() throws Exception {

        var questionId = questionForContextsGenerator.createQuestionForContexts("Hey !", CT_11).getUuid();
        var setContextTitleCommandDTO = new SetQuestionForContextCommandDTO().contextId(CT_11.getId()).questionId(questionId);
        mvc.perform(post(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "recruitmentProfile" + ApiConstants.SEPARATOR + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + ApiConstants.SEPARATOR + "setContextQuestion")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(setContextTitleCommandDTO))
                )
                .andExpect(status().isBadRequest());
    }

    private ResultActions doSaveContextQuestion(String question, Context context) throws Exception {
        var questionId = questionForContextsGenerator.createQuestionForContexts(question, context).getUuid();
        var setContextTitleCommandDTO = new SetQuestionForContextCommandDTO().contextId(context.getId()).questionId(questionId);

        return mvc.perform(post(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "recruitmentProfile" + ApiConstants.SEPARATOR + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + ApiConstants.SEPARATOR + "setContextQuestion")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(setContextTitleCommandDTO))
        );
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    public void shouldListAllRecruitmentProfiles() throws Exception {

        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + J_01.getId() + ApiConstants.SEPARATOR + "recruitmentProfile" + ApiConstants.SEPARATOR + "list"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, EMPLOYER_CODE})
    public void shouldListRecruitmentProfilesForEmployer() throws Exception {
        var job = jobGenerator.createJobForEmployer(EMPLOYER_CODE);
        var recruitment = recruitmentGenerator.createRecruitmentWithNoRequirement(job);
        mvc.perform(get(ApiConstants.API_ODAS_JOB + ApiConstants.SEPARATOR + job.getId() + ApiConstants.SEPARATOR + "recruitmentProfile" + ApiConstants.SEPARATOR + "list?qualifiedOnly=true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[*].id", Matchers.contains(recruitment.getRecruitmentProfile().getUuid().toString())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    public void shouldGetRecruitmentProfiles() throws Exception {

        mvc.perform(get("/api/odas/job/" + J_02.getId() + "/recruitmentProfile/" + UNMODIFIABLE_PROFILE_WITH_QUESTION.getUuid() + "/detail"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(UNMODIFIABLE_PROFILE_WITH_QUESTION.getUuid().toString())))
                .andExpect(jsonPath("$.optionalActivities", is(emptyIterable())))
                .andExpect(jsonPath("$.optionalContexts", is(emptyIterable())))
                .andExpect(jsonPath("$.contextQuestions", is(emptyIterable())))
                .andExpect(jsonPath("$.modifiable", is(false)))
                .andExpect(jsonPath("$.customQuestion", is(CUSTOM_QUESTION)));
    }

    // FIXME ERHGO-263 Refactor test
    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    public void shouldGetRecruitmentProfilesWithOptionalsAndCustomLabels() throws Exception {
        final var expectedJson = TestUtils.toString(this.getClass().getClassLoader().getResourceAsStream("expected/recruitmentProfile.json"));

        mvc.perform(get("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + P_01.getUuid() + "/detail"))
                .andExpect(status().isOk())
                .andExpect(content().json(expectedJson, false));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.SOURCING, E_01_CERA_CODE})
    public void shouldNotGetRecruitmentProfilesForWrongOrganization() throws Exception {

        mvc.perform(get("/api/odas/job/" + J_02.getId() + "/recruitmentProfile/" + UNMODIFIABLE_PROFILE_WITH_QUESTION.getUuid() + "/detail"))
                .andExpect(status().isForbidden());

    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    public void shouldListRecruitmentProfilesOfJob() throws Exception {

        mvc.perform(get("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/list"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()", is(1)));

    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void shouldSaveRecruitmentProfile() throws Exception {

        var uuid = UUID.randomUUID();
        var title = "TheTitle";
        var customQuestion = "TheQuestion";
        var content = objectMapper.writeValueAsString(new SaveRecruitmentProfileCommandDTO().id(uuid).title(title).customQuestion(customQuestion));

        mvc.perform(post("/api/odas/job/" + J_02.getId() + "/recruitmentProfile/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isNoContent());

        var profile = recruitmentProfileRepository.findById(uuid).get();
        assertThat(profile.getJob().getId()).isEqualTo(J_02.getId());
        assertThat(profile.isModifiable()).isTrue();
        assertThat(profile.getCustomQuestion()).isEqualTo(customQuestion);
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void shouldSaveUpdateAndDeleteOptionalActivity() throws Exception {
        saveOptionalActivity(AcquisitionModality.INTEGRATION_PROCESS);
        saveOptionalActivity(AcquisitionModality.SELF_LEARNING);
        deleteOptionalActivity();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void shouldSaveUpdateAndDeleteOptionalContext() throws Exception {
        saveOptionalContext(MANDATORY_CONTEXT, AcquisitionModality.INTEGRATION_PROCESS);
        saveOptionalContext(MANDATORY_CONTEXT, AcquisitionModality.SELF_LEARNING);
        deleteOptionalContext();
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void save_optional_context_should_delete_question() throws Exception {
        saveOptionalContext(CONTEXT_USED_IN_JOB_WITH_QUESTION, AcquisitionModality.INTEGRATION_PROCESS);
        transactionTestHelper.doInTransaction(() -> {
            var profile = recruitmentProfileRepository.findById(MODIFIABLE_PROFILE_OF_JOB_1.getUuid()).orElseThrow();
            assertThat(profile.getContextQuestions()).isEmpty();
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldFailToUpdateOptionalActivityWhenJobIsNotAssociatedToProfile() throws Exception {
        var content = getUpdateOptionalCommand(INTEGRATION_PROCESS, ACT_11.getUuid(), ACTIVITY);
        mvc.perform(post("/api/odas/job/" + JOB_MODIFIABLE.getId() + "/recruitmentProfile/" + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + "/addOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isBadRequest());
    }

    private String getUpdateOptionalCommand(AcquisitionModalityDTO modality, UUID id, AddOptionalsCommandItemDTO.OptionalTypeEnum type) throws JsonProcessingException {
        return objectMapper.writeValueAsString(
                Lists.newArrayList(new AddOptionalsCommandItemDTO()
                        .acquisitionModality(modality)
                        .optionalId(id)
                        .optionalType(type))
        );
    }

    private String getDeleteOptionalCommand(UUID id, DeleteOptionalsCommandItemDTO.OptionalTypeEnum type) throws JsonProcessingException {
        return objectMapper.writeValueAsString(
                Lists.newArrayList(new DeleteOptionalsCommandItemDTO()
                        .optionalId(id)
                        .optionalType(type))
        );
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldFailToUpdateOptionalContextWhenJobIsNotAssociatedToProfile() throws Exception {
        var content = getUpdateOptionalCommand(INTEGRATION_PROCESS, CT_02.getId(), CONTEXT);
        mvc.perform(post("/api/odas/job/" + JOB_MODIFIABLE.getId() + "/recruitmentProfile/" + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + "/addOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldFailToDeleteOptionalContextWhenContextIsNotOptionalForProfile() throws Exception {
        var content = getDeleteOptionalCommand(CONTEXT_USED_IN_JOB_WITH_QUESTION.getId(), DeleteOptionalsCommandItemDTO.OptionalTypeEnum.CONTEXT);

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + "/deleteOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isBadRequest());

    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldFailToDeleteOptionalActivityWhenActivityIsNotOptionalForProfile() throws Exception {
        var content = getDeleteOptionalCommand(jobActivityLabelUsedInCandidatureExperienceAndJobMission.getUuid(), DeleteOptionalsCommandItemDTO.OptionalTypeEnum.ACTIVITY);

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + "/deleteOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isBadRequest());

    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    @Transactional
    public void shouldSetMissionAsQualifiedAndFailForMissionAlreadyQualified() throws Exception {
        qualifyMission(M_01).andExpect(status().isNoContent());
        var qualifiedMissions = recruitmentProfileRepository.findById(MODIFIABLE_PROFILE_OF_JOB_1.getUuid()).get().getQualifiedMissions();
        assertThat(qualifiedMissions).hasSize(1);
        assertThat(qualifiedMissions.iterator().next().getId()).isEqualTo(M_01.getId());
        qualifyMission(M_01).andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN, E_02_SOGILIS_CODE})
    @ResetDataAfter
    public void shouldSetQualifiedFlagOnRecruitmentProfileWhenAllMissionOfJobAreQualified() throws Exception {
        qualifyMission(M_01).andExpect(status().isNoContent());
        qualifyMission(M_02).andExpect(status().isNoContent());
        qualifyMission(M_03).andExpect(status().isNoContent());

        var profile = recruitmentProfileRepository.findById(MODIFIABLE_PROFILE_OF_JOB_1.getUuid()).get();
        assertThat(profile.isQualified()).isTrue();

        mvc.perform(get("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/list?qualifiedOnly=true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()", is(3)));
    }


    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    @ResetDataAfter
    public void adminShouldForceSaveOptionalContextOnUnmodifiableProfile() throws Exception {

        var content = getUpdateOptionalCommand(AcquisitionModalityDTO.INTEGRATION_PROCESS, MANDATORY_CONTEXT.getId(), CONTEXT);

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + P_01.getUuid() + "/addOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isNoContent());
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldDeleteOptionalContextOnUnmodifiableProfileForAdmin() throws Exception {
        var content = getDeleteOptionalCommand(CT_02.getId(), DeleteOptionalsCommandItemDTO.OptionalTypeEnum.CONTEXT);

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + P_01.getUuid() + "/deleteOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isNoContent());
    }


    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldNotDeleteOptionalActivity() throws Exception {
        var content = getDeleteOptionalCommand(ACT_01.getUuid(), DeleteOptionalsCommandItemDTO.OptionalTypeEnum.ACTIVITY);

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + P_01.getUuid() + "/deleteOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    public void shouldRetrieveProfileCapacities() throws Exception {

        performGetAndExpect("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + P_01.getUuid() + "/capacities", "profileCapacities", false);
    }

    private ResultActions qualifyMission(Mission mission) throws Exception {
        return mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + "/mission/" + mission.getId() + "/endQualification")
                .contentType(MediaType.APPLICATION_JSON));
    }


    private void saveOptionalContext(Context context, AcquisitionModality modality) throws Exception {
        var content = getUpdateOptionalCommand(AcquisitionModalityDTO.valueOf(modality.name()), context.getId(), CONTEXT);
        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + "/addOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isNoContent());

        transactionTestHelper.doInTransaction(() -> {
            var profile = recruitmentProfileRepository.findById(MODIFIABLE_PROFILE_OF_JOB_1.getUuid()).orElseThrow();
            assertThat(profile.getOptionalContexts()).hasSize(3);
            var optionalContext = profile.getOptionalContexts().stream().filter(c -> c.getContext().getId().equals(context.getId())).findFirst().orElseThrow();
            assertThat(optionalContext.getContext().getId()).isEqualTo(context.getId());
            assertThat(optionalContext.getAcquisitionModality()).isEqualTo(modality);
        });
    }

    private void deleteOptionalContext() throws Exception {
        var content = getDeleteOptionalCommand(CT_02.getId(), DeleteOptionalsCommandItemDTO.OptionalTypeEnum.CONTEXT);

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + "/deleteOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isNoContent());
        transactionTestHelper.doInTransaction(() -> {
            var profile = recruitmentProfileRepository.findById(MODIFIABLE_PROFILE_OF_JOB_1.getUuid()).get();
            assertThat(profile.getOptionalContexts()).hasSize(2);
        });
    }

    private void saveOptionalActivity(AcquisitionModality modality) throws Exception {
        var content = getUpdateOptionalCommand(AcquisitionModalityDTO.valueOf(modality.name()), ACT_01.getUuid(), ACTIVITY);
        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + "/addOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isNoContent());

        transactionTestHelper.doInTransaction(() -> {
            var profile = recruitmentProfileRepository.findById(MODIFIABLE_PROFILE_OF_JOB_1.getUuid()).get();
            assertThat(profile.getOptionalActivities()).hasSize(2);
            var optionalActivity = profile.getOptionalActivities().stream().filter(a -> a.getActivityLabel().getUuid().equals(ACT_01.getUuid())).findFirst().get();
            assertThat(optionalActivity.getAcquisitionModality()).isEqualTo(modality);
        });
    }

    private void deleteOptionalActivity() throws Exception {
        var content = getDeleteOptionalCommand(ACT_01.getUuid(), DeleteOptionalsCommandItemDTO.OptionalTypeEnum.ACTIVITY);

        mvc.perform(post("/api/odas/job/" + J_01.getId() + "/recruitmentProfile/" + MODIFIABLE_PROFILE_OF_JOB_1.getUuid() + "/deleteOptionals")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content))
                .andExpect(status().isNoContent());

        transactionTestHelper.doInTransaction(() -> {
            var profile = recruitmentProfileRepository.findById(MODIFIABLE_PROFILE_OF_JOB_1.getUuid()).get();
            assertThat(profile.getOptionalActivities()).hasSize(1);
        });
    }

    public <E> Matcher<Iterable<?>> contains(List<E> items) {
        return IsIterableContainingInAnyOrder.containsInAnyOrder(items.toArray(new Object[items.size()]));
    }
}
