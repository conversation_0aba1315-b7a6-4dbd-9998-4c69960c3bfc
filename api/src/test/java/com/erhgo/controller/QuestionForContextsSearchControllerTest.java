package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.domain.referential.Category;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.QuestionForContexts;
import com.erhgo.generators.ContextGenerator;
import com.erhgo.generators.QuestionForContextsGenerator;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.hamcrest.Matchers.contains;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class QuestionForContextsSearchControllerTest extends AbstractIntegrationTest {

    private static QuestionForContexts QUESTION_1, QUESTION_2, QUESTION_3, QUESTION_4;
    private static Category CATEGORY_1, CATEGORY_2, CATEGORY_3;
    private static Context CONTEXT_1, CONTEXT_2, CONTEXT_3;

    @Autowired
    private QuestionForContextsGenerator questionForContextsGenerator;

    @Autowired
    private ContextGenerator contextGenerator;

    @Before
    public void init() {
        if (CATEGORY_1 == null) {
            CATEGORY_1 = contextGenerator.createCategory();
            CATEGORY_2 = contextGenerator.createCategory();
            CATEGORY_3 = contextGenerator.createCategory();
            CONTEXT_1 = contextGenerator.createContextWithCategory(CATEGORY_1);
            CONTEXT_2 = contextGenerator.createContextWithCategory(CATEGORY_1);
            CONTEXT_3 = contextGenerator.createContextWithCategory(CATEGORY_3);

            QUESTION_1 = questionForContextsGenerator.createQuestionForContexts("Une première question", CONTEXT_1);
            QUESTION_2 = questionForContextsGenerator.createQuestionForContexts("Une deuxième QUestion 1", CONTEXT_1, CONTEXT_2);
            QUESTION_3 = questionForContextsGenerator.createQuestionForContexts("Une deuxième QUestion 2", CONTEXT_1, CONTEXT_2, CONTEXT_3);
            QUESTION_4 = questionForContextsGenerator.createQuestionForContexts("Une autre", CONTEXT_2);
        }
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void should_search_by_category() throws Exception {
        mvc.perform(get("/api/odas/questionContext/list?page=0&size=5&by=title&direction=ASC&categoryId=" + CATEGORY_3.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(QUESTION_3.getUuid().toString())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void should_search_by_context() throws Exception {
        mvc.perform(get("/api/odas/questionContext/list?page=0&size=5&by=title&direction=ASC&contextId=" + CONTEXT_1.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(QUESTION_2.getUuid().toString(), QUESTION_3.getUuid().toString(), QUESTION_1.getUuid().toString())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void should_search_by_title() throws Exception {
        mvc.perform(get("/api/odas/questionContext/list?page=0&size=5&by=title&direction=ASC&filter=" + "qUesTio"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(QUESTION_2.getUuid().toString(), QUESTION_3.getUuid().toString(), QUESTION_1.getUuid().toString())));
    }

    @Test
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    public void should_search_by_title_and_context() throws Exception {
        mvc.perform(get("/api/odas/questionContext/list?page=0&size=5&by=title&direction=DESC&filter=" + "une" + "&contextId=" + CONTEXT_2.getId() + "&categoryId=" + CATEGORY_1.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[*].id", contains(QUESTION_3.getUuid().toString(), QUESTION_2.getUuid().toString(), QUESTION_4.getUuid().toString())));
    }
}
