package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTestWithFixtures;
import com.erhgo.TestUtils;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.landingpage.LandingPage;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.generators.TestFixtures;
import com.erhgo.openapi.dto.SaveLandingPageCommandDTO;
import com.erhgo.repositories.LandingPageRepository;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class LandingPageControllerTest extends AbstractIntegrationTestWithFixtures {

    @Autowired
    private LandingPageRepository landingPageRepository;

    private static final String uuid = "45f3ce6c-f1c9-4356-9d07-bca1b241403";
    private static final UUID DEFAULT_UUID = UUID.fromString(uuid + "1");
    String content = "<p>Bonjour le monde 2 !<img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAEUlEQVR42mNgoDWop7rCkQEAIuIA/zlILfAAAAAASUVORK5CYII=\"></p>";
    String urlKey = "yoman";

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void createLandingPage_should_succeed() throws Exception {
        var createCommand = new SaveLandingPageCommandDTO().id(DEFAULT_UUID).content(content).urlKey(urlKey);

        performSaveLandingPageAndExpectOrganizationCodes(createCommand);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void createLandingPage_with_territorial_organization_should_succeed() throws Exception {
        var createCommand = new SaveLandingPageCommandDTO()
                .id(DEFAULT_UUID)
                .organizationCodes(List.of(TestFixtures.E_02_SOGILIS_CODE))
                .content(content)
                .urlKey(urlKey);

        performSaveLandingPageAndExpectOrganizationCodes(createCommand, TestFixtures.E_02_SOGILIS_CODE);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void createLandingPage_with_enterprise_should_succeed() throws Exception {
        var createCommand = new SaveLandingPageCommandDTO()
                .id(DEFAULT_UUID)
                .organizationCodes(List.of(TestFixtures.E_01_CERA_CODE))
                .content(content)
                .urlKey(urlKey);

        performSaveLandingPageAndExpectOrganizationCodes(createCommand, TestFixtures.E_01_CERA_CODE);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void createLandingPage_with_two_enterprises() throws Exception {
        var createCommand = new SaveLandingPageCommandDTO()
                .id(DEFAULT_UUID)
                .organizationCodes(List.of(TestFixtures.E_02_SOGILIS_CODE, TestFixtures.E_01_CERA_CODE))
                .content(content)
                .urlKey(urlKey);

        performSaveLandingPageAndExpectOrganizationCodes(createCommand, TestFixtures.E_02_SOGILIS_CODE, TestFixtures.E_01_CERA_CODE);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void editLandingPage_with_empty_organizationCode_should_remove_organization() throws Exception {

        createDefaultLandingPage();

        var createCommand = new SaveLandingPageCommandDTO()
                .id(DEFAULT_UUID)
                .content(content)
                .urlKey(urlKey);

        performSaveLandingPageAndExpectOrganizationCodes(createCommand);
    }

    private void performSaveLandingPageAndExpectOrganizationCodes(SaveLandingPageCommandDTO createCommand, String... organizationCodes) throws Exception {
        mvc.perform(put("/api/odas/landing-page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createCommand)))
                .andExpect(status().isNoContent());

        txHelper.doInTransaction(() -> {
            var landingPage = landingPageRepository.findById(DEFAULT_UUID).orElseThrow();

            assertThat(landingPage.getContent()).isEqualTo(content);
            assertThat(landingPage.getUrlKey()).isEqualTo(urlKey);
            txHelper.doInTransaction(() -> assertThat(landingPage.getOrganizations()).extracting(AbstractOrganization::getCode).containsExactlyInAnyOrder(organizationCodes));
        });
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void updateLandingPage_should_succeed() throws Exception {

        createDefaultLandingPage();

        var updatedContent = "updated content";
        var updatedUrlKey = "updatedKey";

        var updateCommand = new SaveLandingPageCommandDTO().id(DEFAULT_UUID).content(updatedContent).urlKey(updatedUrlKey);

        performPut("/landing-page", updateCommand)
                .andExpect(status().isNoContent());

        var result = landingPageRepository.findById(DEFAULT_UUID).orElseThrow();

        assertThat(result.getContent()).isEqualTo(updatedContent);
        assertThat(result.getUrlKey()).isEqualTo(updatedUrlKey);
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void getLandingPage_should_succeed() throws Exception {

        createDefaultLandingPage();

        mvc.perform(get("/api/odas/landing-page/" + DEFAULT_UUID))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("landingPage"));
    }

    @Test
    @WithMockKeycloakUser(roles = Role.ODAS_ADMIN)
    @ResetDataAfter
    void getLandingPages_should_sort_by_key() throws Exception {

        createDefaultLandingPage();
        createLandingPageForKey("avant", UUID.fromString(uuid + "8"));

        mvc.perform(get("/api/odas/landing-page/list?page=0&size=10"))
                .andExpect(status().isOk())
                .andExpect(TestUtils.jsonMatchesContent("landingPages"));
    }

    private void createDefaultLandingPage() {
        createLandingPageForKey(urlKey, DEFAULT_UUID);
    }

    private void createLandingPageForKey(String urlKey, UUID uuid) {
        var landingPage = LandingPage.builder()
                .urlKey(urlKey)
                .id(uuid)
                .content(content)
                .organizations(Set.of(TestFixtures.E_02_SOGILIS))
                .build();
        txHelper.doInTransaction(() -> landingPageRepository.save(landingPage));
    }


}
