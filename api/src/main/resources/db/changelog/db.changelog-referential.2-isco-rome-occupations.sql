--liquibase formatted sql

--changeset eric:1588777091000-2 logicalFilePath:classpath:db/changelog/db.changelog-1.35.sql
-- Sources: https://ec.europa.eu/eurostat/ramon/nomenclatures/index.cfm?TargetUrl=LST_CLS_DLD&StrNom=CL_ISCO08&StrLanguageCode=FR&StrLayoutCode=HIERARCHIC#
INSERT INTO IscoOccupation VALUES
(1111, "Membres des corps législatifs"),
(1112, "Cadres supérieurs de l'administration publique"),
(1113, "Chefs traditionnels et chefs de village"),
(1114, "Dirigeants et cadres supérieurs d'organisations spécialisées"),
(1120, "Directeurs généraux d’entreprise"),
(1211, "Directeurs et cadres de direction, services financiers"),
(1212, "Directeurs et cadres de direction, ressources humaines"),
(1213, "Directeurs et cadres de direction, stratégie et planifications"),
(1219, "Directeurs des services administratifs non classés ailleurs"),
(1221, "Directeurs et cadres de direction, ventes et commercialisation"),
(1222, "Directeurs et cadres de direction, publicité et relations publiques"),
(1223, "Directeurs et cadres de direction, recherche-développement"),
(1311, "Directeurs et cadres de direction, agriculture et sylviculture"),
(1312, "Directeurs et cadres de direction, aquaculture et pêche"),
(1321, "Directeurs et cadres de direction, industrie manufacturière"),
(1322, "Directeurs et cadres de direction, mines"),
(1323, "Directeurs et cadres de direction, bâtiment"),
(1324, "Directeurs et cadres de direction, approvisionnement, distribution et assimilés"),
(1330, "Directeurs et cadres de direction, technologies de l'information et des communications"),
(1341, "Cadres de direction, garde d'enfants"),
(1342, "Cadres de direction, services de santé"),
(1343, "Cadres de direction, services aux personnes âgées"),
(1344, "Cadres de direction, services sociaux"),
(1345, "Cadres de direction, éducation"),
(1346, "Directeurs et cadres de direction, succursales de banque, services financiers et assurances"),
(1349, "Autres cadres de direction, services spécialisés non classés ailleurs"),
(1411, "Directeurs et gérants, hôtellerie"),
(1412, "Directeurs et gérants, restauration"),
(1420, "Directeurs et gérants, commerce de détail et de gros"),
(1431, "Directeurs et gérants, centres sportifs, centres de loisirs et centres culturels"),
(1439, "Directeurs et gérants, services non classés ailleurs"),
(2111, "Physiciens et astronomes"),
(2112, "Météorologues"),
(2113, "Chimistes"),
(2114, "Géologues et géophysiciens"),
(2120, "Mathématiciens, actuaires et statisticiens"),
(2131, "Biologistes, botanistes, zoologistes et assimilés"),
(2132, "Agronomes et assimilés"),
(2133, "Spécialistes de la protection de l'environnement"),
(2141, "Spécialistes, sciences techniques de la production et de l'industrie"),
(2142, "Ingénieurs civils"),
(2143, "Ingénieurs écologistes"),
(2144, "Ingénieurs mécaniciens"),
(2145, "Ingénieurs chimistes"),
(2146, "Ingénieurs des mines, ingénieurs métallurgistes et assimilés"),
(2149, "Spécialistes, sciences techniques non classés ailleurs"),
(2151, "Ingénieurs électriciens"),
(2152, "Ingénieurs électroniciens"),
(2153, "Spécialistes des télécommunications"),
(2161, "Architectes, bâtiment"),
(2162, "Architectes paysagistes"),
(2163, "Concepteurs modélistes de produits et de vêtements"),
(2164, "Urbanistes et ingénieurs de la circulation routière"),
(2165, "Cartographes et géomètres"),
(2166, "Concepteurs graphiques, multimédia - graphistes"),
(2211, "Médecins généralistes"),
(2212, "Médecins spécialistes"),
(2221, "Cadres infirmiers"),
(2222, "Sages-femmes"),
(2230, "Spécialistes des médecines traditionnelles et des médecines complémentaires"),
(2240, "Praticiens paramédicaux"),
(2250, "Vétérinaires"),
(2261, "Dentistes"),
(2262, "Pharmaciens"),
(2263, "Spécialistes de la salubrité de l'environnement, de l'hygiène et de la santé au travail"),
(2264, "Physiothérapeutes"),
(2265, "Diététiciens et spécialistes de la nutrition"),
(2266, "Audiologistes et orthophonistes"),
(2267, "Optométristes"),
(2269, "Spécialistes de la santé non classés ailleurs"),
(2310, "Professeurs d'université et d'établissements d'enseignement supérieur"),
(2320, "Professeurs, enseignement technique et professionnel"),
(2330, "Professeurs, enseignement secondaire"),
(2341, "Instituteurs, enseignement primaire"),
(2342, "Educateurs de la petite enfance"),
(2351, "Spécialistes des méthodes d'enseignement"),
(2352, "Enseignants, éducation spécialisée"),
(2353, "Autres professeurs de langues"),
(2354, "Autres professeurs de musique"),
(2355, "Autres professeurs de disciplines artistiques"),
(2356, "Formateurs en technologies de l'information"),
(2359, "Spécialistes de l'enseignement, non classés ailleurs"),
(2411, "Cadres comptables"),
(2412, "Conseillers en finances et investissements"),
(2413, "Analystes financiers"),
(2421, "Analystes, gestion et organisation"),
(2422, "Spécialistes, administration et politiques"),
(2423, "Spécialistes, ressources humaines et évolution de carrière"),
(2424, "Spécialistes de la formation du personnel"),
(2431, "Spécialistes de la publicité et de la commercialisation"),
(2432, "Spécialistes des relations publiques"),
(2433, "Spécialistes des ventes, secteurs médical et technique (à l'exception des TIC)"),
(2434, "Spécialistes des ventes, technologies de l'information et des communications"),
(2511, "Analystes de systèmes"),
(2512, "Concepteurs de logiciels"),
(2513, "Concepteurs de sites Internet et de multimédia"),
(2514, "Programmeurs d'applications"),
(2519, "Concepteurs et analystes de logiciels, et concepteurs de multimédia non classés ailleurs"),
(2521, "Spécialistes des bases de données"),
(2522, "Administrateurs de systèmes"),
(2523, "Spécialistes des réseaux d'ordinateurs"),
(2529, "Spécialistes des bases de données et des réseaux d'ordinateurs non classés ailleurs"),
(2611, "Avocats"),
(2612, "Magistrats"),
(2619, "Juristes non classés ailleurs"),
(2621, "Archivistes paléographes et conservateurs de musée"),
(2622, "Bibliothécaires, documentalistes et professions assimilées"),
(2631, "Economistes"),
(2632, "Sociologues, anthropologues et assimilés"),
(2633, "Philosophes, historiens et spécialistes des sciences politiques"),
(2634, "Psychologues"),
(2635, "Spécialistes du travail social"),
(2636, "Ministres des cultes"),
(2641, "Auteurs et autres écrivains"),
(2642, "Journalistes"),
(2643, "Traducteurs, interprètes et linguistes"),
(2651, "Artistes plasticiens"),
(2652, "Compositeurs, musiciens et chanteurs"),
(2653, "Danseurs et chorégraphes"),
(2654, "Metteurs en scène de cinéma, de théâtre et d'autres spectacles"),
(2655, "Acteurs"),
(2656, "Annonceurs-présentateurs de radio, de télévision et autres médias"),
(2659, "Artistes créateurs et exécutants non classés ailleurs"),
(3111, "Techniciens des sciences chimiques et physiques"),
(3112, "Techniciens du génie civil"),
(3113, "Techniciens en électricité"),
(3114, "Techniciens en électronique"),
(3115, "Techniciens en construction mécanique"),
(3116, "Techniciens en chimie industrielle"),
(3117, "Techniciens des mines, techniciens métallurgistes"),
(3118, "Dessinateurs industriels"),
(3119, "Techniciens des sciences physiques et techniques non classés ailleurs"),
(3121, "Superviseurs, mines"),
(3122, "Superviseurs, industries manufacturières"),
(3123, "Superviseurs, bâtiment"),
(3131, "Conducteurs d'installations de production d'énergie"),
(3132, "Conducteurs d'incinérateurs et d'installations de traitement de l'eau"),
(3133, "Conducteurs d'installations de traitement chimique"),
(3134, "Conducteurs d'installations de raffinage de pétrole et de gaz naturel"),
(3135, "Contrôleurs des processus industriels, métallurgie"),
(3139, "Techniciens, contrôle de processus industriels non classés ailleurs"),
(3141, "Techniciens des sciences de la vie (à l'exception de la médecine)"),
(3142, "Techniciens, agriculture et élevage"),
(3143, "Techniciens, sylviculture"),
(3151, "Officiers mécaniciens de navires"),
(3152, "Officiers de pont et pilotes"),
(3153, "Pilotes d'avions et assimilés"),
(3154, "Contrôleurs de la circulation aérienne"),
(3155, "Techniciens de la sécurité aérienne"),
(3211, "Techniciens d'appareils électromédicaux"),
(3212, "Techniciens de laboratoire médical"),
(3213, "Techniciens et assistants pharmaciens et préparateurs en pharmacie"),
(3214, "Techniciens de prothèses médicales et dentaires"),
(3221, "Personnel infirmier (niveau intermédiaire)"),
(3222, "Sages-femmes (niveau intermédiaire)"),
(3230, "Praticiens des médecines traditionnelles et des médecines complémentaires"),
(3240, "Techniciens et assistants vétérinaires"),
(3251, "Assistants et thérapeutes en médecine dentaire"),
(3252, "Techniciens de dossiers médicaux"),
(3253, "Agents de santé communautaire"),
(3254, "Opticiens"),
(3255, "Techniciens et assistants en physiothérapie"),
(3256, "Assistants médicaux"),
(3257, "Inspecteurs, salubrité de l'environnement et de la santé au travail, et assimilés"),
(3258, "Ambulanciers"),
(3259, "Professions intermédiaires de la santé non classées ailleurs"),
(3311, "Courtiers en valeurs et cambistes"),
(3312, "Responsables des prêts"),
(3313, "Professions intermédiaires de la comptabilité"),
(3314, "Professions intermédiaires de la statistique , des mathématiques et assimilées"),
(3315, "Commissaires-priseurs et autres experts en évaluation"),
(3321, "Agents d'assurances"),
(3322, "Représentants et techniciens commerciaux"),
(3323, "Acheteurs"),
(3324, "Courtiers en marchandises"),
(3331, "Agents concessionnaires"),
(3332, "Organisateurs de conférences et d'événements"),
(3333, "Agents d'emploi et de recrutement de main-d'œuvre"),
(3334, "Agents immobiliers"),
(3339, "Agents de services commerciaux non classés ailleurs"),
(3341, "Superviseurs, travail de bureau"),
(3342, "Secrétaires, services juridiques"),
(3343, "Secrétaires d'administration et secrétaires exécutifs"),
(3344, "Secrétaires médicaux/ales"),
(3351, "Inspecteurs des douanes et des frontières"),
(3352, "Contrôleurs des impôts"),
(3353, "Agents des services publics accordant des prestations sociales"),
(3354, "Agents des services publics accordant des permis et des licences"),
(3355, "Inspecteurs et enquêteurs de police"),
(3359, "Professions intermédiaires de l'application de la loi et assimilées non classées ailleurs"),
(3411, "Professions juridiques intermédiaires et assimilées"),
(3412, "Professions intermédiaires du travail social"),
(3413, "Professions intermédiaires des religions"),
(3421, "Athlètes et sportifs de compétition"),
(3422, "Entraîneurs sportifs et arbitres de sport"),
(3423, "Instructeurs et animateurs de programmes, loisirs et activités de remise en forme"),
(3431, "Photographes"),
(3432, "Décorateurs et designers d'intérieurs"),
(3433, "Techniciens de galeries d'art, de musées et de bibliothèques"),
(3434, "Chefs cuisiniers"),
(3435, "Autres professions intermédiaires de la culture et de la création artistique"),
(3511, "Techniciens des technologies de l'information et des communications, opérations"),
(3512, "Techniciens des technologies de l'information et des communications, soutien aux utilisateurs"),
(3513, "Techniciens, réseaux et systèmes d'ordinateurs"),
(3514, "Techniciens de l'Internet"),
(3521, "Techniciens de radio-télévision et d'enregistrement audio-visuel"),
(3522, "Techniciens de télécommunications"),
(4110, "Employés de bureau, fonctions générales"),
(4120, "Secrétaires (fonctions générales)"),
(4131, "Dactylographes et opérateurs de traitement de texte"),
(4132, "Opérateurs sur clavier numérique"),
(4211, "Guichetiers de banque et assimilés"),
(4212, "Croupiers et assimilés dans le secteur des jeux de hasard"),
(4213, "Prêteurs sur gages et bailleurs de fonds"),
(4214, "Encaisseurs et assimilés"),
(4221, "Consultants et employés d'agence de voyages"),
(4222, "Employés de centre d'appel"),
(4223, "Téléphonistes-standardistes"),
(4224, "Réceptionnistes, hôtellerie"),
(4225, "Employés, service d'information"),
(4226, "Réceptionnistes (fonctions générales)"),
(4227, "Intervieweurs, enquêtes et études de marché"),
(4229, "Employés chargés d'informer la clientèle non classés ailleurs"),
(4311, "Aides comptables et teneurs de livres"),
(4312, "Employés de services statistiques ou financiers"),
(4313, "Commis, service de paie"),
(4321, "Employés du service des stocks"),
(4322, "Employés du service d'ordonnancement de la production"),
(4323, "Employés du service des transports"),
(4411, "Employés de bibliothèque"),
(4412, "Employés de service du courrier"),
(4413, "Codeurs, correcteurs d'épreuves et assimilés"),
(4414, "Ecrivains publics et assimilés"),
(4415, "Classeurs-archivistes"),
(4416, "Employés, service du personnel"),
(4419, "Employés administratifs non classés ailleurs"),
(5111, "Agents d'accueil et stewards"),
(5112, "Contrôleurs et receveurs de transports publics"),
(5113, "Guides"),
(5120, "Cuisiniers"),
(5131, "Serveurs"),
(5132, "Barmen"),
(5141, "Coiffeurs"),
(5142, "Esthéticiens et assimilés"),
(5151, "Intendants et superviseurs des services de nettoyage de bureaux, des hôtels et d'autres établissements"),
(5152, "Gouvernantes et intendants à domicile"),
(5153, "Concierges"),
(5161, "Astrologues, diseurs de bonne aventure et assimilés"),
(5162, "Personnel de compagnie et valets de chambre"),
(5163, "Agents de pompes funèbres et embaumeurs"),
(5164, "Toiletteurs et gardiens d'animaux"),
(5165, "Moniteurs d'auto-école"),
(5169, "Personnel des services directs aux particuliers, non classé ailleurs"),
(5211, "Vendeurs à l'étal et sur les marchés"),
(5212, "Vendeurs ambulants de comestibles"),
(5221, "Commerçants, magasins"),
(5222, "Superviseurs, magasins"),
(5223, "Vendeurs, magasin"),
(5230, "Caissiers et billettistes"),
(5241, "Mannequins et autres modèles"),
(5242, "Démonstrateurs en magasin"),
(5243, "Vendeurs au porte à porte"),
(5244, "Télévendeurs"),
(5245, "Pompistes"),
(5246, "Commis au comptoir, restauration rapide"),
(5249, "Vendeurs non classés ailleurs"),
(5311, "Gardes d'enfants"),
(5312, "Aides-enseignants"),
(5321, "Aides-soignants en institution"),
(5322, "Aides-soignants à domicile"),
(5329, "Personnel soignant et assimilé, non classé ailleurs"),
(5411, "Pompiers"),
(5412, "Agents de police"),
(5413, "Gardiens de prison"),
(5414, "Agents de sécurité"),
(5419, "Personnel des services de protection et de sécurité, non classé ailleurs"),
(6111, "Agriculteurs et ouvriers qualifiés, cultures de plein champ"),
(6112, "Arboriculteurs et ouvriers qualifiés de l'arboriculture"),
(6113, "Agriculteurs et ouvriers qualifiés de l'horticulture et des pépinières"),
(6114, "Agriculteurs et ouvriers qualifiés, cultures diversifiées"),
(6121, "Eleveurs et ouvriers qualifiés de l'élevage de bétail"),
(6122, "Aviculteurs et ouvriers qualifiés de l'aviculture"),
(6123, "Apiculteurs, sériciculteurs et ouvriers qualifiés de l'apiculture et de la sériciculture"),
(6129, "Eleveurs et ouvriers qualifiés de l'élevage commercial et assimilés, non classés ailleurs"),
(6130, "Agriculteurs et ouvriers qualifiés des cultures et de l'élevage à but commercial"),
(6210, "Exploitants et ouvriers forestiers"),
(6221, "Aquaculteurs et ouvriers de l'aquaculture"),
(6222, "Pêcheurs de la pêche côtière et en eaux intérieures"),
(6223, "Pêcheurs de la pêche en haute mer"),
(6224, "Chasseurs et trappeurs"),
(6310, "Agriculteurs, subsistance"),
(6320, "Eleveurs de bétail, subsistance"),
(6330, "Agriculteurs et éleveurs, subsistance"),
(6340, "Pêcheurs, chasseurs, trappeurs et cueilleurs, subsistance"),
(7111, "Constructeurs de maisons"),
(7112, "Maçons"),
(7113, "Fendeurs et tailleurs de pierre"),
(7114, "Constructeurs en béton armé, maçons ragréeurs et assimilés"),
(7115, "Charpentiers en bois et menuisiers du bâtiment"),
(7119, "Métiers qualifiés du bâtiment (gros oeuvre) et assimilés non classés ailleurs"),
(7121, "Couvreurs et zingueurs"),
(7122, "Poseurs de revêtements de sol et carreleurs"),
(7123, "Plâtriers"),
(7124, "Monteurs en isolation thermique et acoustique"),
(7125, "Vitriers"),
(7126, "Plombiers et tuyauteurs"),
(7127, "Mécaniciens-installateurs réfrigération et climatisation"),
(7131, "Peintres en bâtiment et poseurs de papiers peints"),
(7132, "Laqueurs, vernisseurs et assimilés"),
(7133, "Ravaleurs de façades et ramoneurs"),
(7211, "Mouleurs et noyauteurs de fonderie"),
(7212, "Soudeurs et oxycoupeurs"),
(7213, "Tôliers-chaudronniers"),
(7214, "Charpentiers métalliers et monteurs de charpentes métalliques"),
(7215, "Gréeurs et épisseurs de câbles"),
(7221, "Forgerons, estampeurs et conducteurs de presses à forger"),
(7222, "Outilleurs et assimilés"),
(7223, "Régleurs et conducteurs de machines-outils"),
(7224, "Meuleurs, polisseurs et affûteurs"),
(7231, "Mécaniciens et réparateurs de véhicules à moteur"),
(7232, "Mécaniciens et réparateurs de moteurs d'avion"),
(7233, "Mécaniciens et réparateurs de machines agricoles et industrielles"),
(7234, "Réparateurs de bicyclettes et assimilés"),
(7311, "Mécaniciens-réparateurs d'instruments de précision"),
(7312, "Facteurs et accordeurs d'instruments de musique"),
(7313, "Joailliers et orfèvres"),
(7314, "Potiers et assimilés (produits céramiques et abrasifs)"),
(7315, "Souffleurs, mouleurs, tailleurs, meuleurs et polisseurs de verre"),
(7316, "Peintres d'enseignes, peintres-décorateurs et graveurs"),
(7317, "Métiers de l'artisanat sur bois et sur des matériaux similaires"),
(7318, "Métiers de l'artisanat sur textile, sur cuir et sur des matériaux similaires"),
(7319, "Métiers de l'artisanat non classés ailleurs"),
(7321, "Compositeurs et préparateurs en forme imprimante et assimilés"),
(7322, "Imprimeurs"),
(7323, "Relieurs et assimiles"),
(7411, "Electriciens du bâtiment et assimilés"),
(7412, "Mécaniciens et ajusteurs d'appareils électriques"),
(7413, "Monteurs et réparateurs de lignes électriques"),
(7421, "Mécaniciens et réparateurs d'appareils électroniques"),
(7422, "Monteurs et réparateurs, technologies de l'information et des communications"),
(7511, "Bouchers, poissonniers et assimilés"),
(7512, "Boulangers, pâtissiers et confiseurs"),
(7513, "Fabricants des produits laitiers"),
(7514, "Conserveurs de fruits, de légumes et assimilés"),
(7515, "Dégustateurs et classeurs de denrées alimentaires et de boissons"),
(7516, "Métiers qualifiés de la préparation du tabac et de la fabrication des produits du tabac"),
(7521, "Métiers qualifiés du traitement du bois"),
(7522, "Ebénistes, menuisiers et assimilés"),
(7523, "Régleurs et conducteurs de machines à bois"),
(7531, "Tailleurs, couturiers, fourreurs, modistes et chapeliers"),
(7532, "Métiers qualifiés de la coupe de vêtements et assimilés"),
(7533, "Couseurs, brodeurs et assimilés"),
(7534, "Tapissiers et assimilés"),
(7535, "Tanneurs, peaussiers et mégissiers"),
(7536, "Cordonniers et assimilés"),
(7541, "Scaphandriers et plongeurs"),
(7542, "Boutefeux"),
(7543, "Classeurs et essayeurs de produits (à l'exception des aliments et des boissons)"),
(7544, "Fumigateurs et préposés au contrôle de la vermine et des mauvaises herbes"),
(7549, "Métiers qualifiés de l'industrie et de l'artisanat non classés ailleurs"),
(8111, "Mineurs et conducteurs d'installations de mine"),
(8112, "Conducteurs d'installations de préparation des minerais et de la roche"),
(8113, "Foreurs, sondeurs de puits et assimilés"),
(8114, "Conducteurs de machines à fabriquer du ciment, de la pierre et d'autres produits minéraux"),
(8121, "Conducteurs d'installations de transformation et de traitement des métaux"),
(8122, "Conducteurs d'installations de traitement superficiel des métaux"),
(8131, "Conducteurs d'installations et de machines de traitement chimique"),
(8132, "Conducteurs de machines pour la fabrication des produits photographiques"),
(8141, "Conducteurs de machines pour la fabrication des produits en caoutchouc"),
(8142, "Conducteurs de machines pour la fabrication de produits en matières plastiques"),
(8143, "Conducteurs de machines de papeterie"),
(8151, "Conducteurs de machines à préparer les fibres, à filer et à bobiner"),
(8152, "Conducteurs de métiers mécaniques à tisser et à tricoter"),
(8153, "Conducteurs de machines à coudre"),
(8154, "Conducteurs de machines à blanchir, à teindre et à nettoyer les tissus"),
(8155, "Conducteurs de machines à préparer les fourrures et le cuir"),
(8156, "Conducteurs de machines pour la fabrication des chaussures et assimilés"),
(8157, "Conducteurs de machines de blanchisserie"),
(8159, "Conducteurs de machines pour la fabrication de produits textiles et d'articles en fourrure et en cuir, non classés ailleurs"),
(8160, "Conducteurs de machines pour la fabrication de denrées alimentaires et de produits connexes"),
(8171, "Conducteurs d'installations pour la fabrication du papier et de la pâte à papier"),
(8172, "Conducteurs d'installations pour le travail du bois"),
(8181, "Conducteurs d'installations de verrerie et de céramique"),
(8182, "Chauffeurs de machines à vapeur et de chaudières"),
(8183, "Conducteurs de machines d'emballage, d'embouteillage et d'étiquetage"),
(8189, "Conducteurs de machines et d'installations fixes non classés ailleurs"),
(8211, "Monteurs en construction mécanique"),
(8212, "Monteurs d'appareils électriques et électroniques"),
(8219, "Monteurs et assembleurs non classés ailleurs"),
(8311, "Conducteurs de locomotives"),
(8312, "Serre-freins, aiguilleurs et agents de manœuvre"),
(8321, "Conducteurs de motocycles"),
(8322, "Chauffeurs de taxi et conducteurs d'automobiles et de camionnettes"),
(8331, "Conducteurs d'autobus et de tramways"),
(8332, "Conducteurs de poids lourds et de camions"),
(8341, "Conducteurs d'engins mobiles agricoles et forestiers"),
(8342, "Conducteurs d'engins de terrassement et de matériels similaires"),
(8343, "Conducteurs de grues, d'engins de levage divers et de matériels similaires"),
(8344, "Conducteurs de chariots élévateurs"),
(8350, "Matelots de pont et assimilés"),
(9111, "Aides de ménage à domicile"),
(9112, "Agents d'entretien dans les bureaux, les hôtels et autres établissements"),
(9121, "Laveurs et repasseurs de linge à la main"),
(9122, "Laveurs de véhicules"),
(9123, "Laveurs de vitres"),
(9129, "Autres nettoyeurs"),
(9211, "Manoeuvres de l'agriculture"),
(9212, "Manoeuvres de l'élevage"),
(9213, "Manoeuvres de l'agriculture et de l'élevage"),
(9214, "Manoeuvres, cultures maraîchères et horticulture"),
(9215, "Manoeuvres forestiers"),
(9216, "Manoeuvres pêcheurs et aquaculteurs"),
(9311, "Manoeuvres des mines et des carrières"),
(9312, "Manoeuvres de chantier de travaux publics"),
(9313, "Manoeuvres du bâtiment"),
(9321, "Emballeurs à la main et autres manoeuvres des industries manufacturières"),
(9329, "Manoeuvres des industries manufacturières non classés ailleurs"),
(9331, "Conducteurs de véhicules à bras et à pédales"),
(9332, "Conducteurs de véhicules et de machines à traction animale"),
(9333, "Manutentionnaires"),
(9334, "Garnisseurs de rayons"),
(9411, "Cuisiniers, restauration rapide"),
(9412, "Aides de cuisine"),
(9510, "Travailleurs des petits métiers des rues et assimilés"),
(9520, "Vendeurs ambulants (à l'exception de l'alimentation)"),
(9611, "Manoeuvres, enlèvement des ordures et matériel recyclable"),
(9612, "Trieurs de déchets"),
(9613, "Balayeurs et manoeuvres assimilés"),
(9621, "Messagers, porteurs de bagages et livreurs de colis"),
(9622, "Manoeuvres polyvalents"),
(9623, "Encaisseurs de distributeurs automatiques à prépaiement et releveurs de compteurs"),
(9624, "Porteurs d'eau et ramasseurs de bois de feu"),
(9629, "Professions élémentaires non classés ailleurs"),
(0110, "Officiers des forces armées"),
(0210, "Sous-officiers des forces armées"),
(0310, "Autres membres des forces armées")

--changeset eric:1588777091000-5 logicalFilePath:classpath:db/changelog/db.changelog-1.35.sql
-- Sources: https://ec.europa.eu/eurostat/ramon/nomenclatures/index.cfm?TargetUrl=LST_CLS_DLD&StrNom=CL_ISCO08&StrLanguageCode=FR&StrLayoutCode=HIERARCHIC#
INSERT INTO RomeOccupation VALUES
("A1101","Conduite d'engins agricoles et forestiers"),
("A1201","Bûcheronnage et élagage"),
("A1202","Entretien des espaces naturels"),
("A1203","Aménagement et entretien des espaces verts"),
("A1204","Protection du patrimoine naturel"),
("A1205","Sylviculture"),
("A1301","Conseil et assistance technique en agriculture"),
("A1302","Contrôle et diagnostic technique en agriculture"),
("A1303","Ingénierie en agriculture et environnement naturel"),
("A1401","Aide agricole de production fruitière ou viticole"),
("A1402","Aide agricole de production légumière ou végétale"),
("A1403","Aide d'élevage agricole et aquacole"),
("A1404","Aquaculture"),
("A1405","Arboriculture et viticulture"),
("A1406","Encadrement équipage de la pêche"),
("A1407","Élevage bovin ou équin"),
("A1408","Élevage d'animaux sauvages ou de compagnie"),
("A1409","Élevage de lapins et volailles"),
("A1410","Élevage ovin ou caprin"),
("A1411","Élevage porcin"),
("A1412","Fabrication et affinage de fromages"),
("A1413","Fermentation de boissons alcoolisées"),
("A1414","Horticulture et maraîchage"),
("A1415","Equipage de la pêche"),
("A1416","Polyculture, élevage"),
("A1417","Saliculture"),
("A1501","Aide aux soins animaux"),
("A1502","Podologie animale"),
("A1503","Toilettage des animaux"),
("A1504","Santé animale"),
("B1101","Création en arts plastiques"),
("B1201","Réalisation d'objets décoratifs et utilitaires en céramique et matériaux de synthèse"),
("B1301","Décoration d'espaces de vente et d'exposition"),
("B1302","Décoration d'objets d'art et artisanaux"),
("B1303","Gravure - ciselure"),
("B1401","Réalisation d'objets en lianes, fibres et brins végétaux"),
("B1402","Reliure et restauration de livres et archives"),
("B1501","Fabrication et réparation d'instruments de musique"),
("B1601","Métallerie d'art"),
("B1602","Réalisation d'objets artistiques et fonctionnels en verre"),
("B1603","Réalisation d'ouvrages en bijouterie, joaillerie et orfèvrerie"),
("B1604","Réparation - montage en systèmes horlogers"),
("B1701","Conservation et reconstitution d'espèces animales"),
("B1801","Réalisation d'articles de chapellerie"),
("B1802","Réalisation d'articles en cuir et matériaux souples (hors vêtement)"),
("B1803","Réalisation de vêtements sur mesure ou en petite série"),
("B1804","Réalisation d'ouvrages d'art en fils"),
("B1805","Stylisme"),
("B1806","Tapisserie - décoration en ameublement"),
("C1101","Conception - développement produits d'assurances"),
("C1102","Conseil clientèle en assurances"),
("C1103","Courtage en assurances"),
("C1104","Direction d'exploitation en assurances"),
("C1105","Études actuarielles en assurances"),
("C1106","Expertise risques en assurances"),
("C1107","Indemnisations en assurances"),
("C1108","Management de groupe et de service en assurances"),
("C1109","Rédaction et gestion en assurances"),
("C1110","Souscription d'assurances"),
("C1201","Accueil et services bancaires"),
("C1202","Analyse de crédits et risques bancaires"),
("C1203","Relation clients banque/finance"),
("C1204","Conception et expertise produits bancaires et financiers"),
("C1205","Conseil en gestion de patrimoine financier"),
("C1206","Gestion de clientèle bancaire"),
("C1207","Management en exploitation bancaire"),
("C1301","Front office marchés financiers"),
("C1302","Gestion back et middle-office marchés financiers"),
("C1303","Gestion de portefeuilles sur les marchés financiers"),
("C1401","Gestion en banque et assurance"),
("C1501","Gérance immobilière"),
("C1502","Gestion locative immobilière"),
("C1503","Management de projet immobilier"),
("C1504","Transaction immobilière"),
("D1101","Boucherie"),
("D1102","Boulangerie - viennoiserie"),
("D1103","Charcuterie - traiteur"),
("D1104","Pâtisserie, confiserie, chocolaterie et glacerie"),
("D1105","Poissonnerie"),
("D1106","Vente en alimentation"),
("D1107","Vente en gros de produits frais"),
("D1201","Achat vente d'objets d'art, anciens ou d'occasion"),
("D1202","Coiffure"),
("D1203","Hydrothérapie"),
("D1204","Location de véhicules ou de matériel de loisirs"),
("D1205","Nettoyage d'articles textiles ou cuirs"),
("D1206","Réparation d'articles en cuir et matériaux souples"),
("D1207","Retouches en habillement"),
("D1208","Soins esthétiques et corporels"),
("D1209","Vente de végétaux"),
("D1210","Vente en animalerie"),
("D1211","Vente en articles de sport et loisirs"),
("D1212","Vente en décoration et équipement du foyer"),
("D1213","Vente en gros de matériel et équipement"),
("D1214","Vente en habillement et accessoires de la personne"),
("D1301","Management de magasin de détail"),
("D1401","Assistanat commercial"),
("D1402","Relation commerciale grands comptes et entreprises"),
("D1403","Relation commerciale auprès de particuliers"),
("D1404","Relation commerciale en vente de véhicules"),
("D1405","Conseil en information médicale"),
("D1406","Management en force de vente"),
("D1407","Relation technico-commerciale"),
("D1408","Téléconseil et télévente"),
("D1501","Animation de vente"),
("D1502","Management/gestion de rayon produits alimentaires"),
("D1503","Management/gestion de rayon produits non alimentaires"),
("D1504","Direction de magasin de grande distribution"),
("D1505","Personnel de caisse"),
("D1506","Marchandisage"),
("D1507","Mise en rayon libre-service"),
("D1508","Encadrement du personnel de caisses"),
("D1509","Management de département en grande distribution"),
("E1101","Animation de site multimédia"),
("E1102","Ecriture d'ouvrages, de livres"),
("E1103","Communication"),
("E1104","Conception de contenus multimédias"),
("E1105","Coordination d'édition"),
("E1106","Journalisme et information média"),
("E1107","Organisation d'évènementiel"),
("E1108","Traduction, interprétariat"),
("E1201","Photographie"),
("E1202","Production en laboratoire cinématographique"),
("E1203","Production en laboratoire photographique"),
("E1204","Projection cinéma"),
("E1205","Réalisation de contenus multimédias"),
("E1301","Conduite de machines d'impression"),
("E1302","Conduite de machines de façonnage routage"),
("E1303","Encadrement des industries graphiques"),
("E1304","Façonnage et routage"),
("E1305","Préparation et correction en édition et presse"),
("E1306","Prépresse"),
("E1307","Reprographie"),
("E1308","Intervention technique en industrie graphique"),
("E1401","Développement et promotion publicitaire"),
("E1402","Élaboration de plan média"),
("F1101","Architecture du BTP et du paysage"),
("F1102","Conception - aménagement d'espaces intérieurs"),
("F1103","Contrôle et diagnostic technique du bâtiment"),
("F1104","Dessin BTP et paysage"),
("F1105","Études géologiques"),
("F1106","Ingénierie et études du BTP"),
("F1107","Mesures topographiques"),
("F1108","Métré de la construction"),
("F1201","Conduite de travaux du BTP et de travaux paysagers"),
("F1202","Direction de chantier du BTP"),
("F1203","Direction et ingénierie d'exploitation de gisements et de carrières"),
("F1204","Qualité Sécurité Environnement et protection santé du BTP"),
("F1301","Conduite de grue"),
("F1302","Conduite d'engins de terrassement et de carrière"),
("F1401","Extraction liquide et gazeuse"),
("F1402","Extraction solide"),
("F1501","Montage de structures et de charpentes bois"),
("F1502","Montage de structures métalliques"),
("F1503","Réalisation - installation d'ossatures bois"),
("F1601","Application et décoration en plâtre, stuc et staff"),
("F1602","Électricité bâtiment"),
("F1603","Installation d'équipements sanitaires et thermiques"),
("F1604","Montage d'agencements"),
("F1605","Montage de réseaux électriques et télécoms"),
("F1606","Peinture en bâtiment"),
("F1607","Pose de fermetures menuisées"),
("F1608","Pose de revêtements rigides"),
("F1609","Pose de revêtements souples"),
("F1610","Pose et restauration de couvertures"),
("F1611","Réalisation et restauration de façades"),
("F1612","Taille et décoration de pierres"),
("F1613","Travaux d'étanchéité et d'isolation"),
("F1701","Construction en béton"),
("F1702","Construction de routes et voies"),
("F1703","Maçonnerie"),
("F1704","Préparation du gros oeuvre et des travaux publics"),
("F1705","Pose de canalisations"),
("F1706","Préfabrication en béton industriel"),
("G1101","Accueil touristique"),
("G1102","Promotion du tourisme local"),
("G1201","Accompagnement de voyages, d'activités culturelles ou sportives"),
("G1202","Animation d'activités culturelles ou ludiques"),
("G1203","Animation de loisirs auprès d'enfants ou d'adolescents"),
("G1204","Éducation en activités sportives"),
("G1205","Personnel d'attractions ou de structures de loisirs"),
("G1206","Personnel technique des jeux"),
("G1301","Conception de produits touristiques"),
("G1302","Optimisation de produits touristiques"),
("G1303","Vente de voyages"),
("G1401","Assistance de direction d'hôtel-restaurant"),
("G1402","Management d'hôtel-restaurant"),
("G1403","Gestion de structure de loisirs ou d'hébergement touristique"),
("G1404","Management d'établissement de restauration collective"),
("G1501","Personnel d'étage"),
("G1502","Personnel polyvalent d'hôtellerie"),
("G1503","Management du personnel d'étage"),
("G1601","Management du personnel de cuisine"),
("G1602","Personnel de cuisine"),
("G1603","Personnel polyvalent en restauration"),
("G1604","Fabrication de crêpes ou pizzas"),
("G1605","Plonge en restauration"),
("G1701","Conciergerie en hôtellerie"),
("G1702","Personnel du hall"),
("G1703","Réception en hôtellerie"),
("G1801","Café, bar brasserie"),
("G1802","Management du service en restauration"),
("G1803","Service en restauration"),
("G1804","Sommellerie"),
("H1101","Assistance et support technique client"),
("H1102","Management et ingénierie d'affaires"),
("H1201","Expertise technique couleur en industrie"),
("H1202","Conception et dessin de produits électriques et électroniques"),
("H1203","Conception et dessin produits mécaniques"),
("H1204","Design industriel"),
("H1205","Études - modèles en industrie des matériaux souples"),
("H1206","Management et ingénierie études, recherche et développement industriel"),
("H1207","Rédaction technique"),
("H1208","Intervention technique en études et conception en automatisme"),
("H1209","Intervention technique en études et développement électronique"),
("H1210","Intervention technique en études, recherche et développement"),
("H1301","Inspection de conformité"),
("H1302","Management et ingénierie Hygiène Sécurité Environnement -HSE- industriels"),
("H1303","Intervention technique en Hygiène Sécurité Environnement -HSE- industriel"),
("H1401","Management et ingénierie gestion industrielle et logistique"),
("H1402","Management et ingénierie méthodes et industrialisation"),
("H1403","Intervention technique en gestion industrielle et logistique"),
("H1404","Intervention technique en méthodes et industrialisation"),
("H1501","Direction de laboratoire d'analyse industrielle"),
("H1502","Management et ingénierie qualité industrielle"),
("H1503","Intervention technique en laboratoire d'analyse industrielle"),
("H1504","Intervention technique en contrôle essai qualité en électricité et électronique"),
("H1505","Intervention technique en formulation et analyse sensorielle"),
("H1506","Intervention technique qualité en mécanique et travail des métaux"),
("H2101","Abattage et découpe des viandes"),
("H2102","Conduite d'équipement de production alimentaire"),
("H2201","Assemblage d'ouvrages en bois"),
("H2202","Conduite d'équipement de fabrication de l'ameublement et du bois"),
("H2203","Conduite d'installation de production de panneaux bois"),
("H2204","Encadrement des industries de l'ameublement et du bois"),
("H2205","Première transformation de bois d'oeuvre"),
("H2206","Réalisation de menuiserie bois et tonnellerie"),
("H2207","Réalisation de meubles en bois"),
("H2208","Réalisation d'ouvrages décoratifs en bois"),
("H2209","Intervention technique en ameublement et bois"),
("H2301","Conduite d'équipement de production chimique ou pharmaceutique"),
("H2401","Assemblage - montage d'articles en cuirs, peaux"),
("H2402","Assemblage - montage de vêtements et produits textiles"),
("H2403","Conduite de machine de fabrication de produits textiles"),
("H2404","Conduite de machine de production et transformation des fils"),
("H2405","Conduite de machine de textiles nontissés"),
("H2406","Conduite de machine de traitement textile"),
("H2407","Conduite de machine de transformation et de finition des cuirs et peaux"),
("H2408","Conduite de machine d'impression textile"),
("H2409","Coupe cuir, textile et matériaux souples"),
("H2410","Mise en forme, repassage et finitions en industrie textile"),
("H2411","Montage de prototype cuir et matériaux souples"),
("H2412","Patronnage - gradation"),
("H2413","Préparation de fils, montage de métiers textiles"),
("H2414","Préparation et finition d'articles en cuir et matériaux souples"),
("H2415","Contrôle en industrie du cuir et du textile"),
("H2501","Encadrement de production de matériel électrique et électronique"),
("H2502","Management et ingénierie de production"),
("H2503","Pilotage d'unité élémentaire de production mécanique ou de travail des métaux"),
("H2504","Encadrement d'équipe en industrie de transformation"),
("H2505","Encadrement d'équipe ou d'atelier en matériaux souples"),
("H2601","Bobinage électrique"),
("H2602","Câblage électrique et électromécanique"),
("H2603","Conduite d'installation automatisée de production électrique, électronique et microélectronique"),
("H2604","Montage de produits électriques et électroniques"),
("H2605","Montage et câblage électronique"),
("H2701","Pilotage d'installation énergétique et pétrochimique"),
("H2801","Conduite d'équipement de transformation du verre"),
("H2802","Conduite d'installation de production de matériaux de construction"),
("H2803","Façonnage et émaillage en industrie céramique"),
("H2804","Pilotage de centrale à béton prêt à l'emploi, ciment, enrobés et granulats"),
("H2805","Pilotage d'installation de production verrière"),
("H2901","Ajustement et montage de fabrication"),
("H2902","Chaudronnerie - tôlerie"),
("H2903","Conduite d'équipement d'usinage"),
("H2904","Conduite d'équipement de déformation des métaux"),
("H2905","Conduite d'équipement de formage et découpage des matériaux"),
("H2906","Conduite d'installation automatisée ou robotisée de fabrication mécanique"),
("H2907","Conduite d'installation de production des métaux"),
("H2908","Modelage de matériaux non métalliques"),
("H2909","Montage-assemblage mécanique"),
("H2910","Moulage sable"),
("H2911","Réalisation de structures métalliques"),
("H2912","Réglage d'équipement de production industrielle"),
("H2913","Soudage manuel"),
("H2914","Réalisation et montage en tuyauterie"),
("H3101","Conduite d'équipement de fabrication de papier ou de carton"),
("H3102","Conduite d'installation de pâte à papier"),
("H3201","Conduite d'équipement de formage des plastiques et caoutchoucs"),
("H3202","Réglage d'équipement de formage des plastiques et caoutchoucs"),
("H3203","Fabrication de pièces en matériaux composites"),
("H3301","Conduite d'équipement de conditionnement"),
("H3302","Opérations manuelles d'assemblage, tri ou emballage"),
("H3303","Préparation de matières et produits industriels (broyage, mélange, ...)"),
("H3401","Conduite de traitement d'abrasion de surface"),
("H3402","Conduite de traitement par dépôt de surface"),
("H3403","Conduite de traitement thermique"),
("H3404","Peinture industrielle"),
("I1101","Direction et ingénierie en entretien infrastructure et bâti"),
("I1102","Management et ingénierie de maintenance industrielle"),
("I1103","Supervision d'entretien et gestion de véhicules"),
("I1201","Entretien d'affichage et mobilier urbain"),
("I1202","Entretien et surveillance du tracé routier"),
("I1203","Maintenance des bâtiments et des locaux"),
("I1301","Installation et maintenance d'ascenseurs"),
("I1302","Installation et maintenance d'automatismes"),
("I1303","Installation et maintenance de distributeurs automatiques"),
("I1304","Installation et maintenance d'équipements industriels et d'exploitation"),
("I1305","Installation et maintenance électronique"),
("I1306","Installation et maintenance en froid, conditionnement d'air"),
("I1307","Installation et maintenance télécoms et courants faibles"),
("I1308","Maintenance d'installation de chauffage"),
("I1309","Maintenance électrique"),
("I1310","Maintenance mécanique industrielle"),
("I1401","Maintenance informatique et bureautique"),
("I1402","Réparation de biens électrodomestiques et multimédia"),
("I1501","Intervention en grande hauteur"),
("I1502","Intervention en milieu subaquatique"),
("I1503","Intervention en milieux et produits nocifs"),
("I1601","Installation et maintenance en nautisme"),
("I1602","Maintenance d'aéronefs"),
("I1603","Maintenance d'engins de chantier, levage, manutention et de machines agricoles"),
("I1604","Mécanique automobile"),
("I1605","Mécanique de marine"),
("I1606","Réparation de carrosserie"),
("I1607","Réparation de cycles, motocycles et motoculteurs de loisirs"),
("J1101","Médecine de prévention"),
("J1102","Médecine généraliste et spécialisée"),
("J1103","Médecine dentaire"),
("J1104","Suivi de la grossesse et de l'accouchement"),
("J1201","Biologie médicale"),
("J1202","Pharmacie"),
("J1301","Personnel polyvalent des services hospitaliers"),
("J1302","Analyses médicales"),
("J1303","Assistance médico-technique"),
("J1304","Aide en puériculture"),
("J1305","Conduite de véhicules sanitaires"),
("J1306","Imagerie médicale"),
("J1307","Préparation en pharmacie"),
("J1401","Audioprothèses"),
("J1402","Diététique"),
("J1403","Ergothérapie"),
("J1404","Kinésithérapie"),
("J1405","Optique - lunetterie"),
("J1406","Orthophonie"),
("J1407","Orthoptique"),
("J1408","Ostéopathie et chiropraxie"),
("J1409","Pédicurie et podologie"),
("J1410","Prothèses dentaires"),
("J1411","Prothèses et orthèses"),
("J1412","Rééducation en psychomotricité"),
("J1501","Soins d'hygiène, de confort du patient"),
("J1502","Coordination de services médicaux ou paramédicaux"),
("J1503","Soins infirmiers spécialisés en anesthésie"),
("J1504","Soins infirmiers spécialisés en bloc opératoire"),
("J1505","Soins infirmiers spécialisés en prévention"),
("J1506","Soins infirmiers généralistes"),
("J1507","Soins infirmiers spécialisés en puériculture"),
("K1101","Accompagnement et médiation familiale"),
("K1102","Aide aux bénéficiaires d'une mesure de protection juridique"),
("K1103","Développement personnel et bien-être de la personne"),
("K1104","Psychologie"),
("K1201","Action sociale"),
("K1202","Éducation de jeunes enfants"),
("K1203","Encadrement technique en insertion professionnelle"),
("K1204","Médiation sociale et facilitation de la vie en société"),
("K1205","Information sociale"),
("K1206","Intervention socioculturelle"),
("K1207","Intervention socioéducative"),
("K1301","Accompagnement médicosocial"),
("K1302","Assistance auprès d'adultes"),
("K1303","Assistance auprès d'enfants"),
("K1304","Services domestiques"),
("K1305","Intervention sociale et familiale"),
("K1401","Conception et pilotage de la politique des pouvoirs publics"),
("K1402","Conseil en Santé Publique"),
("K1403","Management de structure de santé, sociale ou pénitentiaire"),
("K1404","Mise en oeuvre et pilotage de la politique des pouvoirs publics"),
("K1405","Représentation de l'Etat sur le territoire national ou international"),
("K1501","Application des règles financières publiques"),
("K1502","Contrôle et inspection des Affaires Sociales"),
("K1503","Contrôle et inspection des impôts"),
("K1504","Contrôle et inspection du Trésor Public"),
("K1505","Protection des consommateurs et contrôle des échanges commerciaux"),
("K1601","Gestion de l'information et de la documentation"),
("K1602","Gestion de patrimoine culturel"),
("K1701","Personnel de la Défense"),
("K1702","Direction de la sécurité civile et des secours"),
("K1703","Direction opérationnelle de la défense"),
("K1704","Management de la sécurité publique"),
("K1705","Sécurité civile et secours"),
("K1706","Sécurité publique"),
("K1707","Surveillance municipale"),
("K1801","Conseil en emploi et insertion socioprofessionnelle"),
("K1802","Développement local"),
("K1901","Aide et médiation judiciaire"),
("K1902","Collaboration juridique"),
("K1903","Défense et conseil juridique"),
("K1904","Magistrature"),
("K2101","Conseil en formation"),
("K2102","Coordination pédagogique"),
("K2103","Direction d'établissement et d'enseignement"),
("K2104","Éducation et surveillance au sein d'établissements d'enseignement"),
("K2105","Enseignement artistique"),
("K2106","Enseignement des écoles"),
("K2107","Enseignement général du second degré"),
("K2108","Enseignement supérieur"),
("K2109","Enseignement technique et professionnel"),
("K2110","Formation en conduite de véhicules"),
("K2111","Formation professionnelle"),
("K2112","Orientation scolaire et professionnelle"),
("K2201","Blanchisserie industrielle"),
("K2202","Lavage de vitres"),
("K2203","Management et inspection en propreté de locaux"),
("K2204","Nettoyage de locaux"),
("K2301","Distribution et assainissement d'eau"),
("K2302","Management et inspection en environnement urbain"),
("K2303","Nettoyage des espaces urbains"),
("K2304","Revalorisation de produits industriels"),
("K2305","Salubrité et traitement de nuisibles"),
("K2306","Supervision d'exploitation éco-industrielle"),
("K2401","Recherche en sciences de l'homme et de la société"),
("K2402","Recherche en sciences de l'univers, de la matière et du vivant"),
("K2501","Gardiennage de locaux"),
("K2502","Management de sécurité privée"),
("K2503","Sécurité et surveillance privées"),
("K2601","Conduite d'opérations funéraires"),
("K2602","Conseil en services funéraires"),
("K2603","Thanatopraxie"),
("L1101","Animation musicale et scénique"),
("L1102","Mannequinat et pose artistique"),
("L1103","Présentation de spectacles ou d'émissions"),
("L1201","Danse"),
("L1202","Musique et chant"),
("L1203","Art dramatique"),
("L1204","Arts du cirque et arts visuels"),
("L1301","Mise en scène de spectacles vivants"),
("L1302","Production et administration spectacle, cinéma et audiovisuel"),
("L1303","Promotion d'artistes et de spectacles"),
("L1304","Réalisation cinématographique et audiovisuelle"),
("L1401","Sportif professionnel"),
("L1501","Coiffure et maquillage spectacle"),
("L1502","Costume et habillage spectacle"),
("L1503","Décor et accessoires spectacle"),
("L1504","Éclairage spectacle"),
("L1505","Image cinématographique et télévisuelle"),
("L1506","Machinerie spectacle"),
("L1507","Montage audiovisuel et post-production"),
("L1508","Prise de son et sonorisation"),
("L1509","Régie générale"),
("L1510","Films d'animation et effets spéciaux"),
("M1101","Achats"),
("M1102","Direction des achats"),
("M1201","Analyse et ingénierie financière"),
("M1202","Audit et contrôle comptables et financiers"),
("M1203","Comptabilité"),
("M1204","Contrôle de gestion"),
("M1205","Direction administrative et financière"),
("M1206","Management de groupe ou de service comptable"),
("M1207","Trésorerie et financement"),
("M1301","Direction de grande entreprise ou d'établissement public"),
("M1302","Direction de petite ou moyenne entreprise"),
("M1401","Conduite d'enquêtes"),
("M1402","Conseil en organisation et management d'entreprise"),
("M1403","Études et prospectives socio-économiques"),
("M1404","Management et gestion d'enquêtes"),
("M1501","Assistanat en ressources humaines"),
("M1502","Développement des ressources humaines"),
("M1503","Management des ressources humaines"),
("M1601","Accueil et renseignements"),
("M1602","Opérations administratives"),
("M1603","Distribution de documents"),
("M1604","Assistanat de direction"),
("M1605","Assistanat technique et administratif"),
("M1606","Saisie de données"),
("M1607","Secrétariat"),
("M1608","Secrétariat comptable"),
("M1609","Secrétariat et assistanat médical ou médico-social"),
("M1701","Administration des ventes"),
("M1702","Analyse de tendance"),
("M1703","Management et gestion de produit"),
("M1704","Management relation clientèle"),
("M1705","Marketing"),
("M1706","Promotion des ventes"),
("M1707","Stratégie commerciale"),
("M1801","Administration de systèmes d'information"),
("M1802","Expertise et support en systèmes d'information"),
("M1803","Direction des systèmes d'information"),
("M1804","Études et développement de réseaux de télécoms"),
("M1805","Études et développement informatique"),
("M1806","Conseil et maîtrise d'ouvrage en systèmes d'information"),
("M1807","Exploitation de systèmes de communication et de commandement"),
("M1808","Information géographique"),
("M1809","Information météorologique"),
("M1810","Production et exploitation de systèmes d'information"),
("N1101","Conduite d'engins de déplacement des charges"),
("N1102","Déménagement"),
("N1103","Magasinage et préparation de commandes"),
("N1104","Manoeuvre et conduite d'engins lourds de manutention"),
("N1105","Manutention manuelle de charges"),
("N1201","Affrètement transport"),
("N1202","Gestion des opérations de circulation internationale des marchandises"),
("N1301","Conception et organisation de la chaîne logistique"),
("N1302","Direction de site logistique"),
("N1303","Intervention technique d'exploitation logistique"),
("N2101","Navigation commerciale aérienne"),
("N2102","Pilotage et navigation technique aérienne"),
("N2201","Personnel d'escale aéroportuaire"),
("N2202","Contrôle de la navigation aérienne"),
("N2203","Exploitation des pistes aéroportuaires"),
("N2204","Préparation des vols"),
("N2205","Direction d'escale et exploitation aéroportuaire"),
("N3101","Encadrement de la navigation maritime"),
("N3102","Equipage de la navigation maritime"),
("N3103","Navigation fluviale"),
("N3201","Exploitation des opérations portuaires et du transport maritime"),
("N3202","Exploitation du transport fluvial"),
("N3203","Manutention portuaire"),
("N4101","Conduite de transport de marchandises sur longue distance"),
("N4102","Conduite de transport de particuliers"),
("N4103","Conduite de transport en commun sur route"),
("N4104","Courses et livraisons express"),
("N4105","Conduite et livraison par tournées sur courte distance"),
("N4201","Direction d'exploitation des transports routiers de marchandises"),
("N4202","Direction d'exploitation des transports routiers de personnes"),
("N4203","Intervention technique d'exploitation des transports routiers de marchandises"),
("N4204","Intervention technique d'exploitation des transports routiers de personnes"),
("N4301","Conduite sur rails"),
("N4302","Contrôle des transports en commun"),
("N4401","Circulation du réseau ferré"),
("N4402","Exploitation et manoeuvre des remontées mécaniques"),
("N4403","Manoeuvre du réseau ferré");


--changeset eric:1603265314000-4 logicalFilePath:classpath:db/changelog/db.changelog-2.14.sql
--validCheckSum: 8:73d465fc5f174a9831e0c1788b2fce85
INSERT INTO QuestionForBehaviors (uuid, createdBy_keycloakId, createdDate, lastModifiedBy_keycloakId, updatedDate,
                                       firstAnswerTitle, secondAnswerTitle, title, firstAnswerBehavior_id,
                                       secondAnswerBehavior_id, phase, questionIndex)
VALUES (0x82EFB93F202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je soutiens toujours l''équipe en cas de conflit avec les autres
(même si je ne suis pas d''accord avec la façon de faire)', 'Je réponds aux gens en fonction de leur besoin
(même si c''est le contraire de ce que fait mon équipe)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250EEE390CB02C43DFDFC46, 0x11EACB552251191490CB02C43DFDFC46, 'FIRST', 0)
     , (0x82EFC219202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je vérifie que tout le monde fait de la bonne façon
(même si ça ralentit mon propre travail)', 'Je m''isole pour mieux faire mon travail
(même si les autres ont peut-être besoin de moi)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250FB1790CB02C43DFDFC46, 0x11EACB552251119E90CB02C43DFDFC46, 'FIRST', 1)
     , (0x82EFC5EB202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je trouve des solutions adaptées à mon problème
(même si elle n''est pas parfaite)', 'Je cherche la meilleure solution pour tous
(même si c''est plus compliqué)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250E76E90CB02C43DFDFC46,
        0x11EACB552250F63090CB02C43DFDFC46, 'FIRST', 2)
     , (0x82EFCA64202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je propose toujours mon aide
(même si je ne sais pas faire)', 'Je fais mon travail comme prévu
(même si je dois refuser mon aide à quelqu''un)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250EEE390CB02C43DFDFC46, 0x11EACB552250FB1790CB02C43DFDFC46, 'FIRST', 3)
     , (0x82EFCE14202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je ne demande pas quelque chose dont je n''ai pas besoin
(même si j''y ai droit)', 'Je demande tout ce que peux avoir
(même si ça peut manquer à quelqu''un d''autre)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251191490CB02C43DFDFC46, 0x11EACB552250E76E90CB02C43DFDFC46, 'FIRST', 4)
     , (0x82EFD186202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je termine ce que j''ai commencé avant de passer à autre chose
(même si ça m''ennuie)', 'Je propose mon aide aux autres spontanément
(même si je risque d''être en retard dans mon travail)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251119E90CB02C43DFDFC46, 0x11EACB552250F63090CB02C43DFDFC46, 'FIRST', 5)
     , (0x82EFD577202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je demande de l''aide aux autres quand j''en ai besoin
(même si je dois les interrompre)', 'Je n''interromps pas le travail des autres
(même si je suis en difficulté)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250EEE390CB02C43DFDFC46,
        0x11EACB552251119E90CB02C43DFDFC46, 'FIRST', 6)
     , (0x82EFDA37202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je fais attention au rythme de travail de tout le monde
(même si on peut faire plus vite)', 'Je propose la façon de faire la plus exigeante
(même si ça perturbe les habitudes des autres)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251191490CB02C43DFDFC46, 0x11EACB552250F63090CB02C43DFDFC46, 'FIRST', 7)
     , (0x82EFDE64202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je travaille dans les règles de l''art
(même si c''est plus long)', 'J''essaie d''être le plus efficace possible
(même si c''est un peu plus risqué)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250FB1790CB02C43DFDFC46,
        0x11EACB552250E76E90CB02C43DFDFC46, 'FIRST', 8)
     , (0x82EFE29C202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je veux être traité comme les autres membres de l''équipe
(même si mon travail mérite plus)', 'Je veux que mon travail soit reconnu à sa juste valeur
(même si ça crée des écarts avec les autres)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250EEE390CB02C43DFDFC46, 0x11EACB552250E76E90CB02C43DFDFC46, 'FIRST', 9)
     , (0x82EFE6BC202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je me rends disponible quand on a besoin de moi
(même si ça complique mon travail)', 'Je ne me laisse déranger sous aucun prétexte
(même si ça empêche quelqu''un d''autre d''avancer)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251191490CB02C43DFDFC46, 0x11EACB552251119E90CB02C43DFDFC46, 'FIRST', 10)
     , (0x82EFEB36202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je veux être formé·e par les meilleurs
(même si je peux apprendre par moi-même)', 'J''essaie de me débrouiller seul·e
(même si je peux être formé·e)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250FB1790CB02C43DFDFC46,
        0x11EACB552250F63090CB02C43DFDFC46, 'FIRST', 11)
     , (0x82EFEED5202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je reste dans mon équipe car elle compte sur moi
(même si j''ai envie de faire autre chose)', 'Je change de métier quand j''en ai envie
(même si l''équipe a besoin de mon expertise)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250EEE390CB02C43DFDFC46, 0x11EACB552250F63090CB02C43DFDFC46, 'FIRST', 12)
     , (0x82EFF295202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je fais ce dont la personne en face de moi a vraiment besoin
(même si je fais une entorse à la règle)', 'Je ne sors pas des procédures mises en place
(même si ça pénalise quelqu''un)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552251191490CB02C43DFDFC46,
        0x11EACB552250FB1790CB02C43DFDFC46, 'FIRST', 13)
     , (0x82EFF60C202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je cherche toujours à avancer plus vite
(même si je risque de faire plus d''erreurs)', 'Je finis toujours une activité avant de passer à la suivante
(même si je dois faire plusieurs fois la même choses)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250E76E90CB02C43DFDFC46, 0x11EACB552251119E90CB02C43DFDFC46, 'FIRST', 14)
     , (0x82EFF9C0202A11EB8FF102C43DFDFC46, null, null, null, null, 'J''apporte toujours du soutien aux autres
(même si je ne suis pas toujours sincère)', 'Je n''hésite pas à dire quand je ne suis pas d''accord
(même si ce n''est pas agréable à entendre)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250F7EA90CB02C43DFDFC46, 0x11EACB5522511C5090CB02C43DFDFC46, 'FIRST', 15)
     , (0x82EFFD7C202A11EB8FF102C43DFDFC46, null, null, null, null, 'J''anticipe les dangers en permanence
(même si ça stresse mon entourage)', 'Je reste calme pour ne pas inquiéter les autres
(même si je suis moi-même inquiet)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552251016190CB02C43DFDFC46,
        0x11EACB55225102F490CB02C43DFDFC46, 'FIRST', 16)
     , (0x82F001B1202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je prends le temps de comprendre ce qu''on attend de moi
(même si ça ne m''arrange pas)', 'Je fais les choses selon mes contraintes
(même si cela demande aux autres de s''adapter aussi)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250F25A90CB02C43DFDFC46, 0x11EACB552250F43390CB02C43DFDFC46, 'FIRST', 17)
     , (0x82F0082C202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je ne laisse personne à l''écart
(même si je dois arrêter ce que je suis en train de faire)', 'Je vérifie que tout est bien sécurisé
(même si je dois empêcher les autres de faire ce qu''ils veulent)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250F7EA90CB02C43DFDFC46, 0x11EACB552251016190CB02C43DFDFC46, 'FIRST', 18)
     , (0x82F00C52202A11EB8FF102C43DFDFC46, null, null, null, null, 'J''explique toujours pourquoi quelque chose n''est pas possible
(même si ça crée de l''insatisfaction)', 'Je cherche toujours à rendre service
(même si je sais que ça n''a aucune chance d''aboutir)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB5522511C5090CB02C43DFDFC46, 0x11EACB552250F25A90CB02C43DFDFC46, 'FIRST', 19)
     , (0x82F00FBB202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je garde toujours une attitude positive
(même si je suis stressé·e)', 'Je recommence autrement quand ça ne marche pas
(même si je dois m''énerver un peu)', 'Dans ma prochaine vie professionnelle...', 0x11EACB55225102F490CB02C43DFDFC46,
        0x11EACB552250F43390CB02C43DFDFC46, 'FIRST', 20)
     , (0x82F05ABF202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je montre mes émotions face aux personnes qui souffrent
(même si ce n''est pas "professionnel")', 'Je reste toujours calme et distant·e
(même si la situation me touche)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250F7EA90CB02C43DFDFC46,
        0x11EACB55225102F490CB02C43DFDFC46, 'FIRST', 21)
     , (0x82F05E45202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je défends mes positions
(même si je peux détendre la situation)', 'Je cherche toujours un terrain d''entente
(même si je ne le souhaite pas vraiment)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB5522511C5090CB02C43DFDFC46, 0x11EACB552250F43390CB02C43DFDFC46, 'FIRST', 22)
     , (0x82F0619E202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je m''assure que chacun respecte les consignes de sécurité
(même si c''est pénible)', 'Je m''inquiète du confort de travail des autres
(même si je prends des libertés avec la sécurité pour ça)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251016190CB02C43DFDFC46, 0x11EACB552250F25A90CB02C43DFDFC46, 'FIRST', 23)
     , (0x82F0651B202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je me montre toujours compréhensif·ve
(même si c''est parfois plus utile de dire ce qui ne va pas)', 'Je suis objectif·ve et direct·e dans mes remarques
(même si cela bouscule les autres)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250F7EA90CB02C43DFDFC46,
        0x11EACB552250F25A90CB02C43DFDFC46, 'FIRST', 24)
     , (0x82F069EB202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je n''hésite pas à dire quand je suis en difficulté
(même si cela donne une mauvaise image de moi)', 'Je prends sur moi pour ne pas montrer ce que je ressens
(même si je suis en difficulté)', 'Dans ma prochaine vie professionnelle...', 0x11EACB5522511C5090CB02C43DFDFC46,
        0x11EACB55225102F490CB02C43DFDFC46, 'FIRST', 25)
     , (0x82F06E4E202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je fais toujours attention au danger potentiel
(même si je maîtrise parfaitement ce genre de situations)', 'Je me fais confiance pour réagir en cas de besoin
(même si je connais les risques)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552251016190CB02C43DFDFC46,
        0x11EACB552250F43390CB02C43DFDFC46, 'FIRST', 26)
     , (0x82F07208202A11EB8FF102C43DFDFC46, null, null, null, null, 'J''explique toujours pourquoi je fais quelque chose
(même si ça ne change rien pour moi)', 'Je choisis la solution la mieux adaptée à la situation
(même si les autres ne comprennnent pas pourquoi)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250F7EA90CB02C43DFDFC46, 0x11EACB552250F43390CB02C43DFDFC46, 'FIRST', 27)
     , (0x82F07574202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je réponds toujours aux questions qu''on me pose
(même si j''ai besoin de rester concentré)', 'Je reste toujours attentif·ve à ce qui se passe autour de moi
(même si on me pose une question urgente)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB5522511C5090CB02C43DFDFC46, 0x11EACB552251016190CB02C43DFDFC46, 'FIRST', 28)
     , (0x82F078D8202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je dis toujours "oui" aux demandes qui me sont faites
(même si la personne n''est pas très polie)', 'Je refuse calmement de répondre à une demande malpolie
(même si la personne est déjà énervée)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250F25A90CB02C43DFDFC46,
        0x11EACB55225102F490CB02C43DFDFC46, 'FIRST', 29)
     , (0x82F07D82202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je creuse les sujets qui m''intéressent
(même si je pose beaucoup de questions)', 'J''accepte de ne pas tout connaitre des autres
(même si j''en ai très envie)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552251178A90CB02C43DFDFC46,
        0x11EACB552250FCAC90CB02C43DFDFC46, 'FIRST', 30)
     , (0x82F0815B202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je cherche à être précis·e dans mon travail
(même si l''on s''impatiente autour de moi)', 'Je suis compréhensif·ve quand les autres ont du retard
(même si ça a un impact sur la qualité de mon travail)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250FE4390CB02C43DFDFC46, 0x11EACB552250FFD390CB02C43DFDFC46, 'FIRST', 31)
     , (0x82F084D8202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je termine vite mon travail
(même si je peux faire mieux avec un peu plus de temps)', 'J''insiste pour obtenir le résultat que j''attends
(même si je prends plus de temps que prévu)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB5522510E3D90CB02C43DFDFC46, 0x11EACB552250F0A790CB02C43DFDFC46, 'FIRST', 32)
     , (0x82F08845202A11EB8FF102C43DFDFC46, null, null, null, null, 'J''essaie d''apprendre d''autres façons de faire
(même si le résultat est moins propre)', 'Je m''applique pour faire à ma façon
(même s''il y a peut-être d''autres façons de faire)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251178A90CB02C43DFDFC46, 0x11EACB552250FE4390CB02C43DFDFC46, 'FIRST', 33)
     , (0x82F08BA8202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je partage les informations quand j''ai obtenu l''autorisation
(même si je dois attendre)', 'Je communique en temps réel pour gagner du temps
(même si je n''ai pas encore de feu vert)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250FCAC90CB02C43DFDFC46, 0x11EACB5522510E3D90CB02C43DFDFC46, 'FIRST', 34)
     , (0x82F08F06202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je sais qu''avec le temps je finirai par trouver une solution
(même si j''ai encore de l''énergie tout de suite)', 'Je m''obstine jusqu''à y arriver
(même si faire une pause aide parfois)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250FFD390CB02C43DFDFC46,
        0x11EACB552250F0A790CB02C43DFDFC46, 'FIRST', 35)
     , (0x82F0926D202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je cherche moi-même l''information qui me manque
(même si je sais que je l''aurai plus tard)', 'J''attends qu''on me partage l''information
(même si je peux la trouver par ailleurs)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251178A90CB02C43DFDFC46, 0x11EACB552250FFD390CB02C43DFDFC46, 'FIRST', 36)
     , (0x82F095D0202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je respecte la volonté des autres
(même si je pense pouvoir les faire changer d''avis)', 'J''argumente jusqu''à obtenir ce que je veux
(même si je suis insistant·e)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250FCAC90CB02C43DFDFC46,
        0x11EACB552250F0A790CB02C43DFDFC46, 'FIRST', 37)
     , (0x82F09939202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je préfère toujours le travail bien fait
(même si le rythme de production est plus lent)', 'J''encourage un rythme de travail soutenu
(même si tout n''est pas parfait)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250FE4390CB02C43DFDFC46,
        0x11EACB5522510E3D90CB02C43DFDFC46, 'FIRST', 38)
     , (0x82F09C9A202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je prends le temps d''expliquer les objectifs
(même si ça demande que tout le monde s''arrête)', 'Je fais en sorte que l''équipe travaille vite
(même si tout le monde ne comprend pas tout)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251178A90CB02C43DFDFC46, 0x11EACB5522510E3D90CB02C43DFDFC46, 'FIRST', 39)
     , (0x82F0A00C202A11EB8FF102C43DFDFC46, null, null, 'e4d525c1-965c-4f40-951f-01e5ba3a3723', '2020-11-10 10:21:28', 'Je me confie très vite aux autres
(même s''ils ne sont pas encore très à l''aise)', 'Je laisse aux autres le temps de venir à moi
(même si cela peut passer pour un manque d''intérêt)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250FCAC90CB02C43DFDFC46, 0x11EACB552250FFD390CB02C43DFDFC46, 'FIRST', 40)
     , (0x82F0A376202A11EB8FF102C43DFDFC46, null, null, null, null, 'J''attends d''être dans les meilleures conditions pour travailler
(même si je dois m''arrêter souvent)', 'Je vais toujours le plus loin possible dans mon travail
(même si le cadre n''est pas parfait)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250FE4390CB02C43DFDFC46,
        0x11EACB552250F0A790CB02C43DFDFC46, 'FIRST', 41)
     , (0x82F0A744202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je tire le fil d''un sujet qui m''intéresse
(même si cela m''entraîne loin de mon travail actuel)', 'Je me concentre sur mon objectif
(même si je survole le sujet)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552251178A90CB02C43DFDFC46,
        0x11EACB552250F0A790CB02C43DFDFC46, 'FIRST', 42)
     , (0x82F0AAC6202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je ne demande que les informations personnelles indispensables
(même si le résultat est moins bon)', 'J''ai toujours des données très complètes
(même si j''insiste pour avoir les informations)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250FCAC90CB02C43DFDFC46, 0x11EACB552250FE4390CB02C43DFDFC46, 'FIRST', 43)
     , (0x82F0AE37202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je valorise les personnes qui vont vite
(même si les autres respectent quand même les délais)', 'Je veux seulement que l''on tienne les délais
(même s''il est toujours possible d''aller plus vite)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB5522510E3D90CB02C43DFDFC46, 0x11EACB552250FFD390CB02C43DFDFC46, 'FIRST', 44)
     , (0x82F0B1A3202A11EB8FF102C43DFDFC46, null, null, 'e4d525c1-965c-4f40-951f-01e5ba3a3723', '2020-11-28 17:18:26', 'Je prends davantage de temps avec les personnes polies
(même si les autres sont pressées)', 'Je ne me vexe pas face à une personne sèche
(même si ce n''est pas agréable)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552251060490CB02C43DFDFC46,
        0x11EACB552250F98390CB02C43DFDFC46, 'FIRST', 45)
     , (0x82F0B512202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je suis toujours à l''heure à mes rendez-vous
(même si ça m''oblige à changer mes plans)', 'Je gère mes priorités moi-même
(même si je suis en décalage avec les autres)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250ECAC90CB02C43DFDFC46, 0x11EACB5522510A9690CB02C43DFDFC46, 'FIRST', 46)
     , (0x82F0B878202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je réponds tout de suite aux demandes
(même si ma réponse est automatique)', 'Je prends le temps d''imaginer une réponse originale et pertinente
(même si ça prend du temps)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552251047D90CB02C43DFDFC46,
        0x11EACB5522510C9D90CB02C43DFDFC46, 'FIRST', 47)
     , (0x82F0BBF9202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je reste jusqu''à la fin des réunions où j''ai été invité·e
(même si cela me met en retard)', 'Je ne dépasse pas les horaires de rendez-vous
(même si je dois partir avant la fin)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552251060490CB02C43DFDFC46,
        0x11EACB552250ECAC90CB02C43DFDFC46, 'FIRST', 48)
     , (0x82F0BF71202A11EB8FF102C43DFDFC46, null, null, null, null, 'J''analyse le véritable besoin derrière chaque demande
(même si ça demande du temps)', 'Je me rends disponible dès qu''on me le demande
(même s''il n''y a pas de véritable urgence)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250F98390CB02C43DFDFC46, 0x11EACB552251047D90CB02C43DFDFC46, 'FIRST', 49)
     , (0x82F0C2DD202A11EB8FF102C43DFDFC46, null, null, 'e4d525c1-965c-4f40-951f-01e5ba3a3723', '2020-11-28 17:18:54', 'Je m''appuie sur mon savoir-faire
(même s''il n''est pas très original)', 'Je vais chercher des avis différents avant de faire quelque chose
(même si je sais le faire depuis longtemps)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB5522510A9690CB02C43DFDFC46, 0x11EACB5522510C9D90CB02C43DFDFC46, 'FIRST', 50)
     , (0x82F0C649202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je veux être tenu·e informé·e en cas d''absence
(même si je ne suis pas directement concerné·e)', 'J''attends que chacun s''organise selon ses contraintes
(même si je ne suis pas au courant de tout)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251060490CB02C43DFDFC46, 0x11EACB5522510A9690CB02C43DFDFC46, 'FIRST', 51)
     , (0x82F0C9B5202A11EB8FF102C43DFDFC46, null, null, 'e4d525c1-965c-4f40-951f-01e5ba3a3723', '2020-11-28 17:19:57', 'Je prends le temps d''analyser un projet
(même s''il est très original)', 'Je cherche des projets différents de ce qu''on voit d''habitude
(même si je ne maîtrise pas tous les enjeux)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250F98390CB02C43DFDFC46, 0x11EACB5522510C9D90CB02C43DFDFC46, 'FIRST', 52)
     , (0x82F0CD1C202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je ne demande pas à mon équipe d''arriver en avance
(même si je suis déjà là)', 'J''attends des réponses rapides
(même si ça pertube les horaires prévus)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250ECAC90CB02C43DFDFC46, 0x11EACB552251047D90CB02C43DFDFC46, 'FIRST', 53)
     , (0x82F0D089202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je demande avant de changer le programme de l''équipe
(même si je suis le·la chef·fe)', 'Je change seul·e les priorités quand il le faut
(même si ça change ce qu''on avait décidé ensemble)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251060490CB02C43DFDFC46, 0x11EACB552251047D90CB02C43DFDFC46, 'FIRST', 54)
     , (0x82F0D3F9202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je demande toujours un regard extérieur sur mon travail
(même si ce n''est pas indispensable)', 'Je me fais confiance pour prendre seul·e une décision
(même si j''ai le temps de demander un autre avis)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250F98390CB02C43DFDFC46, 0x11EACB5522510A9690CB02C43DFDFC46, 'FIRST', 55)
     , (0x82F0D76B202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je propose une solution faisable dans les délais
(même si elle n''est pas très innovante)', 'Je cherche toujours une nouvelle solution
(même si je dois demander un délais)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552250ECAC90CB02C43DFDFC46,
        0x11EACB5522510C9D90CB02C43DFDFC46, 'FIRST', 56)
     , (0x82F0DAD7202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je fais très attention à ma façon de parler aux autres
(même si ça fait un peu vieux jeu)', 'Je tutoie tout le monde pour être plus accessible
(même si des personnes ont du mal)', 'Dans ma prochaine vie professionnelle...', 0x11EACB552251060490CB02C43DFDFC46,
        0x11EACB5522510C9D90CB02C43DFDFC46, 'FIRST', 57)
     , (0x82F0DE48202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je me documente avant de décider
(même si je dois prendre du retard)', 'Je tiens les délais que l''on m''a fixé
(même si cela m''empêche d''aller au bout des choses)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552250F98390CB02C43DFDFC46, 0x11EACB552250ECAC90CB02C43DFDFC46, 'FIRST', 58)
     , (0x82F0E37F202A11EB8FF102C43DFDFC46, null, null, null, null, 'Je demande de l''aide aux personnes disponibles pour aller plus vite
(même si je suis sensé·e faire seul·e)', 'J''assume seul·e mes responsabilités
(même si je dois répondre un peu plus tard)', 'Dans ma prochaine vie professionnelle...',
        0x11EACB552251047D90CB02C43DFDFC46, 0x11EACB5522510A9690CB02C43DFDFC46, 'FIRST', 59)
     , (0x82F0E824202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle fait passer le collectif avant tout."',
        '"Il·elle reste focalisé·e sur son objectif quoi qu''il se passe."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250EEE390CB02C43DFDFC46,
        0x11EACB552251119E90CB02C43DFDFC46, 'SECOND', 60)
     , (0x82F0EBC5202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle fait passer le collectif avant tout."',
        '"Il·elle agit toujours pour le bien de tous."', 'Dans mon prochain métier, j''aimerais qu''on dise de moi...',
        0x11EACB552250EEE390CB02C43DFDFC46, 0x11EACB552251191490CB02C43DFDFC46, 'SECOND', 61)
     , (0x82F0EF79202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle fait passer le collectif avant tout."',
        '"Il·elle respecte toujours les consignes scrupuleusement."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250EEE390CB02C43DFDFC46,
        0x11EACB552250FB1790CB02C43DFDFC46, 'SECOND', 62)
     , (0x82F0F2EB202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle fait passer le collectif avant tout."',
        '"Il·elle aime les missions difficiles et exigeantes."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250EEE390CB02C43DFDFC46,
        0x11EACB552250F63090CB02C43DFDFC46, 'SECOND', 63)
     , (0x82F0F65B202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle fait passer le collectif avant tout."',
        '"Il·elle propose toujours des solutions qui fonctionnent."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250EEE390CB02C43DFDFC46,
        0x11EACB552250E76E90CB02C43DFDFC46, 'SECOND', 64)
     , (0x82F0F9C8202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle reste focalisé·e sur son objectif quoi qu''il se passe."',
        '"Il·elle agit toujours pour le bien de tous."', 'Dans mon prochain métier, j''aimerais qu''on dise de moi...',
        0x11EACB552251119E90CB02C43DFDFC46, 0x11EACB552251191490CB02C43DFDFC46, 'SECOND', 65)
     , (0x82F0FDDF202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle reste focalisé·e sur son objectif quoi qu''il se passe."',
        '"Il·elle respecte toujours les consignes scrupuleusement."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251119E90CB02C43DFDFC46,
        0x11EACB552250FB1790CB02C43DFDFC46, 'SECOND', 66)
     , (0x82F10160202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle reste focalisé·e sur son objectif quoi qu''il se passe."',
        '"Il·elle aime les missions difficiles et exigeantes."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251119E90CB02C43DFDFC46,
        0x11EACB552250F63090CB02C43DFDFC46, 'SECOND', 67)
     , (0x82F104CD202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle reste focalisé·e sur son objectif quoi qu''il se passe."',
        '"Il·elle propose toujours des solutions qui fonctionnent."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251119E90CB02C43DFDFC46,
        0x11EACB552250E76E90CB02C43DFDFC46, 'SECOND', 68)
     , (0x82F10841202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle agit toujours pour le bien de tous."',
        '"Il·elle respecte toujours les consignes scrupuleusement."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251191490CB02C43DFDFC46,
        0x11EACB552250FB1790CB02C43DFDFC46, 'SECOND', 69)
     , (0x82F10D3D202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle agit toujours pour le bien de tous."',
        '"Il·elle aime les missions difficiles et exigeantes."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251191490CB02C43DFDFC46,
        0x11EACB552250F63090CB02C43DFDFC46, 'SECOND', 70)
     , (0x82F1133A202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle agit toujours pour le bien de tous."',
        '"Il·elle propose toujours des solutions qui fonctionnent."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251191490CB02C43DFDFC46,
        0x11EACB552250E76E90CB02C43DFDFC46, 'SECOND', 71)
     , (0x82F11923202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle respecte toujours les consignes scrupuleusement."',
        '"Il·elle aime les missions difficiles et exigeantes."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FB1790CB02C43DFDFC46,
        0x11EACB552250F63090CB02C43DFDFC46, 'SECOND', 72)
     , (0x82F11E82202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle respecte toujours les consignes scrupuleusement."',
        '"Il·elle propose toujours des solutions qui fonctionnent."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FB1790CB02C43DFDFC46,
        0x11EACB552250E76E90CB02C43DFDFC46, 'SECOND', 73)
     , (0x82F1237D202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle aime les missions difficiles et exigeantes."',
        '"Il·elle propose toujours des solutions qui fonctionnent."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F63090CB02C43DFDFC46,
        0x11EACB552250E76E90CB02C43DFDFC46, 'SECOND', 74)
     , (0x82F12895202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle s''intéresse sincèrement à tout le monde."', '"Il·elle ne perd jamais son calme."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F7EA90CB02C43DFDFC46,
        0x11EACB55225102F490CB02C43DFDFC46, 'SECOND', 75)
     , (0x82F12DE0202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle s''intéresse sincèrement à tout le monde."',
        '"Il·elle fait toujours un retour complet sur son travail."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F7EA90CB02C43DFDFC46,
        0x11EACB5522511C5090CB02C43DFDFC46, 'SECOND', 76)
     , (0x82F1330C202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle s''intéresse sincèrement à tout le monde."', '"Il·elle est attentif·ve au moindre danger."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F7EA90CB02C43DFDFC46,
        0x11EACB552251016190CB02C43DFDFC46, 'SECOND', 77)
     , (0x82F1381E202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle s''intéresse sincèrement à tout le monde."',
        '"Il·elle est capable de tout changer pour trouver une solution."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F7EA90CB02C43DFDFC46,
        0x11EACB552250F43390CB02C43DFDFC46, 'SECOND', 78)
     , (0x82F174F2202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle s''intéresse sincèrement à tout le monde."', '"Il·elle cherche toujours à être utile aux autres."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F7EA90CB02C43DFDFC46,
        0x11EACB552250F25A90CB02C43DFDFC46, 'SECOND', 79)
     , (0x82F1789D202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle ne perd jamais son calme."',
        '"Il·elle fait toujours un retour complet sur son travail."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB55225102F490CB02C43DFDFC46,
        0x11EACB5522511C5090CB02C43DFDFC46, 'SECOND', 80)
     , (0x82F17CA8202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle ne perd jamais son calme."',
        '"Il·elle est attentif·ve au moindre danger."', 'Dans mon prochain métier, j''aimerais qu''on dise de moi...',
        0x11EACB55225102F490CB02C43DFDFC46, 0x11EACB552251016190CB02C43DFDFC46, 'SECOND', 81)
     , (0x82F1802D202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle ne perd jamais son calme."',
        '"Il·elle est capable de tout changer pour trouver une solution."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB55225102F490CB02C43DFDFC46,
        0x11EACB552250F43390CB02C43DFDFC46, 'SECOND', 82)
     , (0x82F1855E202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle ne perd jamais son calme."',
        '"Il·elle cherche toujours à être utile aux autres."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB55225102F490CB02C43DFDFC46,
        0x11EACB552250F25A90CB02C43DFDFC46, 'SECOND', 83)
     , (0x82F18A6E202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle fait toujours un retour complet sur son travail."', '"Il·elle est attentif·ve au moindre danger."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB5522511C5090CB02C43DFDFC46,
        0x11EACB552251016190CB02C43DFDFC46, 'SECOND', 84)
     , (0x82F18FA4202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle fait toujours un retour complet sur son travail."',
        '"Il·elle est capable de tout changer pour trouver une solution."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB5522511C5090CB02C43DFDFC46,
        0x11EACB552250F43390CB02C43DFDFC46, 'SECOND', 85)
     , (0x82F194CB202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle fait toujours un retour complet sur son travail."',
        '"Il·elle cherche toujours à être utile aux autres."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB5522511C5090CB02C43DFDFC46,
        0x11EACB552250F25A90CB02C43DFDFC46, 'SECOND', 86)
     , (0x82F19A00202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle est attentif·ve au moindre danger."',
        '"Il·elle est capable de tout changer pour trouver une solution."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251016190CB02C43DFDFC46,
        0x11EACB552250F43390CB02C43DFDFC46, 'SECOND', 87)
     , (0x82F19F88202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle est attentif·ve au moindre danger."',
        '"Il·elle cherche toujours à être utile aux autres."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251016190CB02C43DFDFC46,
        0x11EACB552250F25A90CB02C43DFDFC46, 'SECOND', 88)
     , (0x82F1A4DA202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle est capable de tout changer pour trouver une solution."',
        '"Il·elle cherche toujours à être utile aux autres."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F43390CB02C43DFDFC46,
        0x11EACB552250F25A90CB02C43DFDFC46, 'SECOND', 89)
     , (0x82F1AA18202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle a toujours envie d''apprendre de nouvelles choses."',
        '"Il·elle ne s''agace jamais quand les choses avancent lentement."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251178A90CB02C43DFDFC46,
        0x11EACB552250FFD390CB02C43DFDFC46, 'SECOND', 90)
     , (0x82F1AF2B202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle a toujours envie d''apprendre de nouvelles choses."',
        '"Il·elle sait tenir sa langue quand on lui confie quelque chose."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251178A90CB02C43DFDFC46,
        0x11EACB552250FCAC90CB02C43DFDFC46, 'SECOND', 91)
     , (0x82F1B401202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle a toujours envie d''apprendre de nouvelles choses."',
        '"Il·elle est extrêmement soigneux·euse dans son travail."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251178A90CB02C43DFDFC46,
        0x11EACB552250FE4390CB02C43DFDFC46, 'SECOND', 92)
     , (0x82F1B9CA202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle a toujours envie d''apprendre de nouvelles choses."',
        '"Il·elle ne renonce jamais face aux difficultés."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251178A90CB02C43DFDFC46,
        0x11EACB552250F0A790CB02C43DFDFC46, 'SECOND', 93)
     , (0x82F1BD4D202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle a toujours envie d''apprendre de nouvelles choses."',
        '"Il·elle fait preuve de rapidité et d''efficacité."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251178A90CB02C43DFDFC46,
        0x11EACB5522510E3D90CB02C43DFDFC46, 'SECOND', 94)
     , (0x82F1C0C3202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle ne s''agace jamais quand les choses avancent lentement."',
        '"Il·elle sait tenir sa langue quand on lui confie quelque chose."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FFD390CB02C43DFDFC46,
        0x11EACB552250FCAC90CB02C43DFDFC46, 'SECOND', 95)
     , (0x82F1C42E202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle ne s''agace jamais quand les choses avancent lentement."',
        '"Il·elle est extrêmement soigneux·euse dans son travail."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FFD390CB02C43DFDFC46,
        0x11EACB552250FE4390CB02C43DFDFC46, 'SECOND', 96)
     , (0x82F1C7AC202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle ne s''agace jamais quand les choses avancent lentement."',
        '"Il·elle ne renonce jamais face aux difficultés."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FFD390CB02C43DFDFC46,
        0x11EACB552250F0A790CB02C43DFDFC46, 'SECOND', 97)
     , (0x82F1CB16202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle ne s''agace jamais quand les choses avancent lentement."',
        '"Il·elle fait preuve de rapidité et d''efficacité."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FFD390CB02C43DFDFC46,
        0x11EACB5522510E3D90CB02C43DFDFC46, 'SECOND', 98)
     , (0x82F1CE8C202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle sait tenir sa langue quand on lui confie quelque chose."',
        '"Il·elle est extrêmement soigneux·euse dans son travail."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FCAC90CB02C43DFDFC46,
        0x11EACB552250FE4390CB02C43DFDFC46, 'SECOND', 99)
     , (0x82F1D203202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle sait tenir sa langue quand on lui confie quelque chose."',
        '"Il·elle ne renonce jamais face aux difficultés."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FCAC90CB02C43DFDFC46,
        0x11EACB552250F0A790CB02C43DFDFC46, 'SECOND', 100)
     , (0x82F1D587202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle sait tenir sa langue quand on lui confie quelque chose."',
        '"Il·elle fait preuve de rapidité et d''efficacité."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FCAC90CB02C43DFDFC46,
        0x11EACB5522510E3D90CB02C43DFDFC46, 'SECOND', 101)
     , (0x82F1D8F7202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle est extrêmement soigneux·euse dans son travail."',
        '"Il·elle ne renonce jamais face aux difficultés."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FE4390CB02C43DFDFC46,
        0x11EACB552250F0A790CB02C43DFDFC46, 'SECOND', 102)
     , (0x82F1DC67202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle est extrêmement soigneux·euse dans son travail."',
        '"Il·elle fait preuve de rapidité et d''efficacité."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250FE4390CB02C43DFDFC46,
        0x11EACB5522510E3D90CB02C43DFDFC46, 'SECOND', 103)
     , (0x82F1DFD8202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle ne renonce jamais face aux difficultés."',
        '"Il·elle fait preuve de rapidité et d''efficacité."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F0A790CB02C43DFDFC46,
        0x11EACB5522510E3D90CB02C43DFDFC46, 'SECOND', 104)
     , (0x82F1E348202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle s''adresse aux gens avec respect."',
        '"Il·elle trouve les solutions par lui-même."', 'Dans mon prochain métier, j''aimerais qu''on dise de moi...',
        0x11EACB552251060490CB02C43DFDFC46, 0x11EACB5522510A9690CB02C43DFDFC46, 'SECOND', 105)
     , (0x82F1E6AB202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle s''adresse aux gens avec respect."',
        '"Il·elle n''hésite pas à remettre en cause les mauvaises décisions."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251060490CB02C43DFDFC46,
        0x11EACB552250F98390CB02C43DFDFC46, 'SECOND', 106)
     , (0x82F1EA4D202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle s''adresse aux gens avec respect."',
        '"Il·elle est toujours à l''heure à ses rendez-vous."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251060490CB02C43DFDFC46,
        0x11EACB552250ECAC90CB02C43DFDFC46, 'SECOND', 107)
     , (0x82F1EEAE202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle s''adresse aux gens avec respect."',
        '"Il·elle a une façon différente d''aborder les problèmes."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251060490CB02C43DFDFC46,
        0x11EACB5522510C9D90CB02C43DFDFC46, 'SECOND', 108)
     , (0x82F1F32A202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle s''adresse aux gens avec respect."',
        '"Il·elle se rend disponible tout de suite quand il y a besoin."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552251060490CB02C43DFDFC46,
        0x11EACB552251047D90CB02C43DFDFC46, 'SECOND', 109)
     , (0x82F1F78B202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle trouve les solutions par lui-même."',
        '"Il·elle n''hésite pas à remettre en cause les mauvaises décisions."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB5522510A9690CB02C43DFDFC46,
        0x11EACB552250F98390CB02C43DFDFC46, 'SECOND', 110)
     , (0x82F1FBF6202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle trouve les solutions par lui-même."',
        '"Il·elle est toujours à l''heure à ses rendez-vous."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB5522510A9690CB02C43DFDFC46,
        0x11EACB552250ECAC90CB02C43DFDFC46, 'SECOND', 111)
     , (0x82F200CA202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle trouve les solutions par lui-même."',
        '"Il·elle a une façon différente d''aborder les problèmes."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB5522510A9690CB02C43DFDFC46,
        0x11EACB5522510C9D90CB02C43DFDFC46, 'SECOND', 112)
     , (0x82F2054D202A11EB8FF102C43DFDFC46, null, null, null, null, '"Il·elle trouve les solutions par lui-même."',
        '"Il·elle se rend disponible tout de suite quand il y a besoin."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB5522510A9690CB02C43DFDFC46,
        0x11EACB552251047D90CB02C43DFDFC46, 'SECOND', 113)
     , (0x82F209BF202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle n''hésite pas à remettre en cause les mauvaises décisions."',
        '"Il·elle est toujours à l''heure à ses rendez-vous."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F98390CB02C43DFDFC46,
        0x11EACB552250ECAC90CB02C43DFDFC46, 'SECOND', 114)
     , (0x82F20E47202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle n''hésite pas à remettre en cause les mauvaises décisions."',
        '"Il·elle a une façon différente d''aborder les problèmes."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F98390CB02C43DFDFC46,
        0x11EACB5522510C9D90CB02C43DFDFC46, 'SECOND', 115)
     , (0x82F212A1202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle n''hésite pas à remettre en cause les mauvaises décisions."',
        '"Il·elle se rend disponible tout de suite quand il y a besoin."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250F98390CB02C43DFDFC46,
        0x11EACB552251047D90CB02C43DFDFC46, 'SECOND', 116)
     , (0x82F216F7202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle est toujours à l''heure à ses rendez-vous."',
        '"Il·elle a une façon différente d''aborder les problèmes."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250ECAC90CB02C43DFDFC46,
        0x11EACB5522510C9D90CB02C43DFDFC46, 'SECOND', 117)
     , (0x82F21ACA202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle est toujours à l''heure à ses rendez-vous."',
        '"Il·elle se rend disponible tout de suite quand il y a besoin."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB552250ECAC90CB02C43DFDFC46,
        0x11EACB552251047D90CB02C43DFDFC46, 'SECOND', 118)
     , (0x82F21EFD202A11EB8FF102C43DFDFC46, null, null, null, null,
        '"Il·elle a une façon différente d''aborder les problèmes."',
        '"Il·elle se rend disponible tout de suite quand il y a besoin."',
        'Dans mon prochain métier, j''aimerais qu''on dise de moi...', 0x11EACB5522510C9D90CB02C43DFDFC46,
        0x11EACB552251047D90CB02C43DFDFC46, 'SECOND', 119);

--changeset eric:1688393945-1
-- Sources: https://docs.google.com/spreadsheets/d/16mZDDm9LJ-4GuBLllwR2gKn8Hin28kdpZgjzUPyge5k/edit#gid=452206335 (mobilités ROME sans code appellation source ou cible)
INSERT INTO RomeOccupation_accessibleRomeOccupations VALUES
                                                         ('A1101','A1416'),
                                                         ('A1202','A1203'),
                                                         ('A1203','A1202'),
                                                         ('A1203','A1405'),
                                                         ('A1203','A1414'),
                                                         ('A1205','A1101'),
                                                         ('A1205','A1201'),
                                                         ('A1401','A1402'),
                                                         ('A1401','H3302'),
                                                         ('A1401','N1105'),
                                                         ('A1402','A1401'),
                                                         ('A1402','H3302'),
                                                         ('A1402','N1105'),
                                                         ('A1404','A1415'),
                                                         ('A1405','A1414'),
                                                         ('A1406','N3101'),
                                                         ('A1406','N3103'),
                                                         ('A1407','A1410'),
                                                         ('A1407','A1411'),
                                                         ('A1408','A1409'),
                                                         ('A1408','A1503'),
                                                         ('A1409','A1410'),
                                                         ('A1410','A1407'),
                                                         ('A1410','A1409'),
                                                         ('A1410','A1411'),
                                                         ('A1410','A1501'),
                                                         ('A1411','A1407'),
                                                         ('A1411','A1410'),
                                                         ('A1412','D1107'),
                                                         ('A1413','A1412'),
                                                         ('A1414','A1203'),
                                                         ('A1414','A1405'),
                                                         ('A1415','N3102'),
                                                         ('A1416','A1101'),
                                                         ('A1501','J1303'),
                                                         ('A1502','B1601'),
                                                         ('A1503','A1408'),
                                                         ('A1503','D1210'),
                                                         ('A1504','K2108'),
                                                         ('A1504','M1703'),
                                                         ('B1101','B1201'),
                                                         ('B1101','B1303'),
                                                         ('B1101','B1601'),
                                                         ('B1101','B1602'),
                                                         ('B1101','B1603'),
                                                         ('B1101','E1205'),
                                                         ('B1101','F1612'),
                                                         ('B1101','G1202'),
                                                         ('B1101','H2208'),
                                                         ('B1101','K2105'),
                                                         ('B1101','L1503'),
                                                         ('B1201','B1101'),
                                                         ('B1201','B1302'),
                                                         ('B1201','B1602'),
                                                         ('B1201','H2803'),
                                                         ('B1301','F1102'),
                                                         ('B1302','G1202'),
                                                         ('B1303','B1101'),
                                                         ('B1303','B1302'),
                                                         ('B1501','B1601'),
                                                         ('B1501','D1201'),
                                                         ('B1501','H2902'),
                                                         ('B1601','B1101'),
                                                         ('B1601','B1603'),
                                                         ('B1601','H2901'),
                                                         ('B1601','H2902'),
                                                         ('B1601','H2904'),
                                                         ('B1601','H3401'),
                                                         ('B1601','H3402'),
                                                         ('B1601','H3403'),
                                                         ('B1602','B1101'),
                                                         ('B1602','B1201'),
                                                         ('B1602','B1302'),
                                                         ('B1602','B1303'),
                                                         ('B1603','B1303'),
                                                         ('B1802','B1806'),
                                                         ('B1802','D1206'),
                                                         ('B1802','D1214'),
                                                         ('B1802','H2401'),
                                                         ('B1803','D1207'),
                                                         ('B1803','D1214'),
                                                         ('B1803','H2411'),
                                                         ('B1803','H2412'),
                                                         ('B1803','L1502'),
                                                         ('B1804','D1214'),
                                                         ('B1805','H1205'),
                                                         ('B1806','B1803'),
                                                         ('B1806','H2402'),
                                                         ('C1101','C1204'),
                                                         ('C1101','M1703'),
                                                         ('C1102','C1206'),
                                                         ('C1103','C1107'),
                                                         ('C1104','C1103'),
                                                         ('C1104','C1108'),
                                                         ('C1105','C1101'),
                                                         ('C1105','M1403'),
                                                         ('C1106','C1107'),
                                                         ('C1107','C1106'),
                                                         ('C1107','C1110'),
                                                         ('C1108','C1103'),
                                                         ('C1108','C1104'),
                                                         ('C1201','D1401'),
                                                         ('C1201','D1408'),
                                                         ('C1203','M1704'),
                                                         ('C1204','M1703'),
                                                         ('C1205','C1302'),
                                                         ('C1205','M1704'),
                                                         ('C1206','C1102'),
                                                         ('C1206','C1502'),
                                                         ('C1301','C1302'),
                                                         ('C1302','C1205'),
                                                         ('C1303','C1205'),
                                                         ('C1303','C1301'),
                                                         ('C1401','D1408'),
                                                         ('C1401','M1602'),
                                                         ('C1401','M1607'),
                                                         ('C1501','C1502'),
                                                         ('C1501','C1504'),
                                                         ('C1501','M1206'),
                                                         ('C1502','C1504'),
                                                         ('C1502','M1203'),
                                                         ('C1503','C1203'),
                                                         ('C1503','C1501'),
                                                         ('C1503','C1504'),
                                                         ('C1503','D1402'),
                                                         ('C1503','D1403'),
                                                         ('C1503','M1707'),
                                                         ('C1504','C1206'),
                                                         ('C1504','C1501'),
                                                         ('C1504','C1502'),
                                                         ('C1504','D1402'),
                                                         ('C1504','D1403'),
                                                         ('D1101','D1106'),
                                                         ('D1101','D1107'),
                                                         ('D1102','D1107'),
                                                         ('D1103','D1106'),
                                                         ('D1103','D1107'),
                                                         ('D1104','D1107'),
                                                         ('D1105','D1107'),
                                                         ('D1106','D1107'),
                                                         ('D1106','D1505'),
                                                         ('D1106','D1507'),
                                                         ('D1106','G1603'),
                                                         ('D1107','D1106'),
                                                         ('D1107','D1301'),
                                                         ('D1107','D1401'),
                                                         ('D1107','D1402'),
                                                         ('D1201','D1211'),
                                                         ('D1201','D1301'),
                                                         ('D1202','D1208'),
                                                         ('D1202','L1501'),
                                                         ('D1203','D1208'),
                                                         ('D1203','J1301'),
                                                         ('D1203','K1302'),
                                                         ('D1204','G1101'),
                                                         ('D1204','I1103'),
                                                         ('D1204','M1601'),
                                                         ('D1204','N4203'),
                                                         ('D1204','N4204'),
                                                         ('D1205','H2410'),
                                                         ('D1205','H3302'),
                                                         ('D1206','D1214'),
                                                         ('D1207','D1214'),
                                                         ('D1208','D1202'),
                                                         ('D1208','D1203'),
                                                         ('D1208','D1214'),
                                                         ('D1208','L1501'),
                                                         ('D1209','D1210'),
                                                         ('D1209','D1211'),
                                                         ('D1209','D1212'),
                                                         ('D1209','D1214'),
                                                         ('D1209','D1505'),
                                                         ('D1210','D1209'),
                                                         ('D1210','D1211'),
                                                         ('D1210','D1212'),
                                                         ('D1210','D1214'),
                                                         ('D1210','D1501'),
                                                         ('D1210','D1505'),
                                                         ('D1211','D1106'),
                                                         ('D1211','D1212'),
                                                         ('D1211','D1213'),
                                                         ('D1211','D1214'),
                                                         ('D1211','D1408'),
                                                         ('D1211','D1501'),
                                                         ('D1211','D1505'),
                                                         ('D1212','D1211'),
                                                         ('D1212','D1214'),
                                                         ('D1212','D1501'),
                                                         ('D1212','D1505'),
                                                         ('D1213','D1211'),
                                                         ('D1213','D1212'),
                                                         ('D1213','D1214'),
                                                         ('D1213','D1301'),
                                                         ('D1213','D1401'),
                                                         ('D1213','D1402'),
                                                         ('D1214','D1211'),
                                                         ('D1214','D1212'),
                                                         ('D1214','D1501'),
                                                         ('D1214','D1505'),
                                                         ('D1301','D1401'),
                                                         ('D1301','D1402'),
                                                         ('D1301','D1503'),
                                                         ('D1401','C1201'),
                                                         ('D1401','D1213'),
                                                         ('D1401','D1402'),
                                                         ('D1401','D1404'),
                                                         ('D1401','D1408'),
                                                         ('D1401','M1605'),
                                                         ('D1401','M1607'),
                                                         ('D1401','N1202'),
                                                         ('D1402','C1102'),
                                                         ('D1402','D1213'),
                                                         ('D1402','D1404'),
                                                         ('D1403','D1211'),
                                                         ('D1403','D1212'),
                                                         ('D1403','D1214'),
                                                         ('D1403','D1408'),
                                                         ('D1403','D1501'),
                                                         ('D1404','D1401'),
                                                         ('D1404','D1402'),
                                                         ('D1404','D1403'),
                                                         ('D1405','D1401'),
                                                         ('D1405','D1402'),
                                                         ('D1405','D1407'),
                                                         ('D1406','D1506'),
                                                         ('D1406','D1509'),
                                                         ('D1406','M1701'),
                                                         ('D1406','M1706'),
                                                         ('D1407','C1102'),
                                                         ('D1407','D1402'),
                                                         ('D1408','D1401'),
                                                         ('D1408','D1403'),
                                                         ('D1408','E1101'),
                                                         ('D1408','M1401'),
                                                         ('D1501','D1106'),
                                                         ('D1501','D1210'),
                                                         ('D1501','D1211'),
                                                         ('D1501','D1212'),
                                                         ('D1501','D1214'),
                                                         ('D1501','D1403'),
                                                         ('D1501','M1401'),
                                                         ('D1502','D1301'),
                                                         ('D1502','D1503'),
                                                         ('D1503','D1301'),
                                                         ('D1503','D1502'),
                                                         ('D1504','D1406'),
                                                         ('D1504','M1102'),
                                                         ('D1504','M1302'),
                                                         ('D1504','M1704'),
                                                         ('D1504','M1706'),
                                                         ('D1505','D1507'),
                                                         ('D1505','M1601'),
                                                         ('D1506','B1301'),
                                                         ('D1507','D1106'),
                                                         ('D1507','D1505'),
                                                         ('D1507','G1603'),
                                                         ('D1507','H3303'),
                                                         ('D1507','N1103'),
                                                         ('D1507','N1105'),
                                                         ('D1508','D1301'),
                                                         ('D1509','D1301'),
                                                         ('D1509','D1406'),
                                                         ('D1509','M1704'),
                                                         ('D1509','N1302'),
                                                         ('E1101','D1401'),
                                                         ('E1101','D1408'),
                                                         ('E1102','G1202'),
                                                         ('E1103','E1106'),
                                                         ('E1103','E1401'),
                                                         ('E1103','E1402'),
                                                         ('E1104','E1205'),
                                                         ('E1104','L1510'),
                                                         ('E1105','E1305'),
                                                         ('E1105','L1303'),
                                                         ('E1107','E1103'),
                                                         ('E1201','E1203'),
                                                         ('E1202','E1203'),
                                                         ('E1203','E1202'),
                                                         ('E1205','E1104'),
                                                         ('E1205','L1510'),
                                                         ('E1301','E1302'),
                                                         ('E1302','E1301'),
                                                         ('E1302','H3301'),
                                                         ('E1303','E1308'),
                                                         ('E1303','H2504'),
                                                         ('E1303','H2505'),
                                                         ('E1304','H3302'),
                                                         ('E1304','N1103'),
                                                         ('E1306','E1205'),
                                                         ('E1307','M1602'),
                                                         ('E1308','E1303'),
                                                         ('E1401','E1103'),
                                                         ('E1401','E1402'),
                                                         ('E1401','M1402'),
                                                         ('E1402','E1401'),
                                                         ('E1402','M1101'),
                                                         ('E1402','M1703'),
                                                         ('F1101','F1102'),
                                                         ('F1101','F1106'),
                                                         ('F1102','B1301'),
                                                         ('F1102','H1204'),
                                                         ('F1102','L1503'),
                                                         ('F1103','H1301'),
                                                         ('F1105','F1203'),
                                                         ('F1106','F1103'),
                                                         ('F1106','F1201'),
                                                         ('F1106','H1102'),
                                                         ('F1106','I1101'),
                                                         ('F1107','F1103'),
                                                         ('F1107','M1809'),
                                                         ('F1108','F1106'),
                                                         ('F1203','F1105'),
                                                         ('F1203','F1201'),
                                                         ('F1203','F1202'),
                                                         ('F1204','F1103'),
                                                         ('F1204','F1201'),
                                                         ('F1204','F1202'),
                                                         ('F1204','H1301'),
                                                         ('F1204','H1303'),
                                                         ('F1301','F1302'),
                                                         ('F1301','N1104'),
                                                         ('F1302','A1101'),
                                                         ('F1302','F1702'),
                                                         ('F1302','N1101'),
                                                         ('F1302','N1104'),
                                                         ('F1401','F1402'),
                                                         ('F1401','F1704'),
                                                         ('F1402','F1302'),
                                                         ('F1402','F1401'),
                                                         ('F1402','F1702'),
                                                         ('F1402','F1704'),
                                                         ('F1501','F1502'),
                                                         ('F1501','F1607'),
                                                         ('F1501','H2201'),
                                                         ('F1502','F1501'),
                                                         ('F1502','F1610'),
                                                         ('F1503','F1501'),
                                                         ('F1503','H2201'),
                                                         ('F1503','H2202'),
                                                         ('F1503','H2206'),
                                                         ('F1601','D1213'),
                                                         ('F1601','L1503'),
                                                         ('F1602','F1605'),
                                                         ('F1602','H2602'),
                                                         ('F1602','I1309'),
                                                         ('F1603','I1203'),
                                                         ('F1603','I1308'),
                                                         ('F1604','F1607'),
                                                         ('F1604','H2201'),
                                                         ('F1605','F1602'),
                                                         ('F1605','H2602'),
                                                         ('F1606','F1601'),
                                                         ('F1606','F1609'),
                                                         ('F1606','F1611'),
                                                         ('F1606','I1203'),
                                                         ('F1607','F1501'),
                                                         ('F1607','F1604'),
                                                         ('F1607','H2201'),
                                                         ('F1607','I1203'),
                                                         ('F1608','F1609'),
                                                         ('F1609','I1201'),
                                                         ('F1610','F1501'),
                                                         ('F1610','F1502'),
                                                         ('F1610','F1613'),
                                                         ('F1611','F1606'),
                                                         ('F1611','F1613'),
                                                         ('F1612','F1608'),
                                                         ('F1613','F1611'),
                                                         ('F1613','F1702'),
                                                         ('F1613','F1705'),
                                                         ('F1701','F1611'),
                                                         ('F1701','F1703'),
                                                         ('F1701','F1706'),
                                                         ('F1702','F1704'),
                                                         ('F1702','F1705'),
                                                         ('F1702','F1706'),
                                                         ('F1703','A1203'),
                                                         ('F1703','F1613'),
                                                         ('F1703','F1701'),
                                                         ('F1704','F1702'),
                                                         ('F1704','F1706'),
                                                         ('F1704','H3303'),
                                                         ('F1704','N1105'),
                                                         ('F1704','N4403'),
                                                         ('F1705','F1401'),
                                                         ('F1705','F1402'),
                                                         ('F1705','F1613'),
                                                         ('F1705','F1702'),
                                                         ('F1706','F1701'),
                                                         ('F1706','F1704'),
                                                         ('F1706','H2205'),
                                                         ('F1706','H3302'),
                                                         ('F1706','H3303'),
                                                         ('F1706','N1101'),
                                                         ('F1706','N1105'),
                                                         ('F1706','N3203'),
                                                         ('G1101','D1402'),
                                                         ('G1101','G1703'),
                                                         ('G1101','N2201'),
                                                         ('G1102','G1201'),
                                                         ('G1102','G1301'),
                                                         ('G1102','K1802'),
                                                         ('G1201','G1102'),
                                                         ('G1201','G1204'),
                                                         ('G1202','G1201'),
                                                         ('G1202','G1203'),
                                                         ('G1202','K1206'),
                                                         ('G1203','G1202'),
                                                         ('G1203','K1206'),
                                                         ('G1203','K1303'),
                                                         ('G1204','D1211'),
                                                         ('G1204','D1402'),
                                                         ('G1204','G1201'),
                                                         ('G1204','G1202'),
                                                         ('G1205','G1401'),
                                                         ('G1205','N4402'),
                                                         ('G1206','D1508'),
                                                         ('G1206','G1205'),
                                                         ('G1301','E1107'),
                                                         ('G1301','G1102'),
                                                         ('G1301','G1302'),
                                                         ('G1302','G1301'),
                                                         ('G1302','K2111'),
                                                         ('G1302','M1702'),
                                                         ('G1303','D1204'),
                                                         ('G1303','D1408'),
                                                         ('G1303','G1101'),
                                                         ('G1303','G1201'),
                                                         ('G1303','G1703'),
                                                         ('G1401','G1801'),
                                                         ('G1401','G1802'),
                                                         ('G1402','G1401'),
                                                         ('G1402','G1403'),
                                                         ('G1402','G1404'),
                                                         ('G1402','G1801'),
                                                         ('G1403','D1402'),
                                                         ('G1403','D1407'),
                                                         ('G1403','M1302'),
                                                         ('G1404','G1402'),
                                                         ('G1404','G1403'),
                                                         ('G1404','G1801'),
                                                         ('G1501','D1205'),
                                                         ('G1501','G1502'),
                                                         ('G1501','J1301'),
                                                         ('G1501','K1304'),
                                                         ('G1501','K2204'),
                                                         ('G1502','G1205'),
                                                         ('G1502','G1501'),
                                                         ('G1502','G1702'),
                                                         ('G1502','J1301'),
                                                         ('G1502','K1304'),
                                                         ('G1502','K2204'),
                                                         ('G1503','G1502'),
                                                         ('G1503','G1701'),
                                                         ('G1503','G1703'),
                                                         ('G1601','G1401'),
                                                         ('G1601','G1404'),
                                                         ('G1601','G1801'),
                                                         ('G1602','D1103'),
                                                         ('G1602','G1601'),
                                                         ('G1603','D1106'),
                                                         ('G1603','G1604'),
                                                         ('G1603','G1702'),
                                                         ('G1603','G1801'),
                                                         ('G1603','G1803'),
                                                         ('G1603','J1301'),
                                                         ('G1604','G1603'),
                                                         ('G1605','G1603'),
                                                         ('G1605','G1702'),
                                                         ('G1605','J1301'),
                                                         ('G1701','G1503'),
                                                         ('G1701','G1703'),
                                                         ('G1702','G1501'),
                                                         ('G1702','G1502'),
                                                         ('G1702','G1603'),
                                                         ('G1702','N4102'),
                                                         ('G1702','N4104'),
                                                         ('G1703','G1101'),
                                                         ('G1703','G1401'),
                                                         ('G1703','G1502'),
                                                         ('G1703','G1503'),
                                                         ('G1703','G1701'),
                                                         ('G1703','M1601'),
                                                         ('G1801','G1603'),
                                                         ('G1801','G1803'),
                                                         ('G1803','G1603'),
                                                         ('G1803','G1801'),
                                                         ('G1804','G1801'),
                                                         ('G1804','G1803'),
                                                         ('H1101','H1206'),
                                                         ('H1101','H1207'),
                                                         ('H1101','H1402'),
                                                         ('H1101','I1102'),
                                                         ('H1101','M1101'),
                                                         ('H1102','D1406'),
                                                         ('H1102','H1101'),
                                                         ('H1102','M1707'),
                                                         ('H1201','H1210'),
                                                         ('H1201','H1503'),
                                                         ('H1202','H1203'),
                                                         ('H1202','H1207'),
                                                         ('H1202','H1208'),
                                                         ('H1202','H1209'),
                                                         ('H1202','I1305'),
                                                         ('H1203','F1104'),
                                                         ('H1203','H1202'),
                                                         ('H1203','H1207'),
                                                         ('H1203','H1404'),
                                                         ('H1204','B1805'),
                                                         ('H1204','E1205'),
                                                         ('H1204','M1702'),
                                                         ('H1205','H1404'),
                                                         ('H1206','H1402'),
                                                         ('H1206','H1501'),
                                                         ('H1206','K2402'),
                                                         ('H1207','H1202'),
                                                         ('H1207','H1203'),
                                                         ('H1208','H1202'),
                                                         ('H1208','H1209'),
                                                         ('H1208','I1302'),
                                                         ('H1208','I1304'),
                                                         ('H1209','H1202'),
                                                         ('H1209','H1208'),
                                                         ('H1209','H1210'),
                                                         ('H1209','H1504'),
                                                         ('H1210','H1404'),
                                                         ('H1210','H1503'),
                                                         ('H1210','H1506'),
                                                         ('H1301','F1103'),
                                                         ('H1301','H1303'),
                                                         ('H1301','H1503'),
                                                         ('H1301','H1506'),
                                                         ('H1302','H1501'),
                                                         ('H1302','H1502'),
                                                         ('H1302','K2306'),
                                                         ('H1303','H1301'),
                                                         ('H1303','H1506'),
                                                         ('H1303','K1705'),
                                                         ('H1401','H1402'),
                                                         ('H1401','N1301'),
                                                         ('H1401','N1302'),
                                                         ('H1403','H1404'),
                                                         ('H1403','N1303'),
                                                         ('H1404','H1101'),
                                                         ('H1404','H1202'),
                                                         ('H1404','H1203'),
                                                         ('H1404','H1207'),
                                                         ('H1404','H1403'),
                                                         ('H1404','H1504'),
                                                         ('H1404','H1506'),
                                                         ('H1501','H1302'),
                                                         ('H1501','H1502'),
                                                         ('H1501','H2502'),
                                                         ('H1502','H1302'),
                                                         ('H1502','H1501'),
                                                         ('H1503','H1210'),
                                                         ('H1503','H1506'),
                                                         ('H1503','J1302'),
                                                         ('H1504','H1209'),
                                                         ('H1505','H1210'),
                                                         ('H1506','H1301'),
                                                         ('H1506','H1303'),
                                                         ('H1506','H1404'),
                                                         ('H1506','H1503'),
                                                         ('H1506','H2503'),
                                                         ('H2101','H2102'),
                                                         ('H2101','H2407'),
                                                         ('H2101','H3303'),
                                                         ('H2102','H2301'),
                                                         ('H2102','H2403'),
                                                         ('H2102','H2404'),
                                                         ('H2102','H2603'),
                                                         ('H2102','H2801'),
                                                         ('H2102','H2803'),
                                                         ('H2102','H3102'),
                                                         ('H2102','H3201'),
                                                         ('H2102','H3301'),
                                                         ('H2201','F1604'),
                                                         ('H2201','H2909'),
                                                         ('H2202','H2201'),
                                                         ('H2202','H2203'),
                                                         ('H2202','H3404'),
                                                         ('H2203','H2202'),
                                                         ('H2203','H2205'),
                                                         ('H2203','H3102'),
                                                         ('H2203','H3303'),
                                                         ('H2204','H2209'),
                                                         ('H2205','H2202'),
                                                         ('H2206','D1212'),
                                                         ('H2206','D1213'),
                                                         ('H2206','F1503'),
                                                         ('H2206','H2201'),
                                                         ('H2206','H2202'),
                                                         ('H2206','L1503'),
                                                         ('H2207','H2201'),
                                                         ('H2207','H2206'),
                                                         ('H2207','H2208'),
                                                         ('H2207','L1503'),
                                                         ('H2208','B1303'),
                                                         ('H2209','H2204'),
                                                         ('H2301','H2102'),
                                                         ('H2301','H2203'),
                                                         ('H2301','H2404'),
                                                         ('H2301','H2603'),
                                                         ('H2301','H2701'),
                                                         ('H2301','H2801'),
                                                         ('H2301','H3102'),
                                                         ('H2301','H3201'),
                                                         ('H2301','H3301'),
                                                         ('H2301','H3402'),
                                                         ('H2402','H2410'),
                                                         ('H2402','H2605'),
                                                         ('H2402','H2909'),
                                                         ('H2403','H2404'),
                                                         ('H2403','H2405'),
                                                         ('H2404','H2413'),
                                                         ('H2404','H2415'),
                                                         ('H2405','H2102'),
                                                         ('H2405','H2301'),
                                                         ('H2405','H2403'),
                                                         ('H2405','H2404'),
                                                         ('H2405','H3101'),
                                                         ('H2405','H3102'),
                                                         ('H2405','H3201'),
                                                         ('H2405','H3202'),
                                                         ('H2405','H3301'),
                                                         ('H2406','K2201'),
                                                         ('H2407','H2406'),
                                                         ('H2407','H2415'),
                                                         ('H2407','H3102'),
                                                         ('H2407','K2201'),
                                                         ('H2408','H2102'),
                                                         ('H2408','H2301'),
                                                         ('H2408','H2403'),
                                                         ('H2408','H2404'),
                                                         ('H2408','H3201'),
                                                         ('H2408','H3301'),
                                                         ('H2409','H2407'),
                                                         ('H2409','H2415'),
                                                         ('H2410','D1205'),
                                                         ('H2410','H2415'),
                                                         ('H2410','H3302'),
                                                         ('H2410','K2201'),
                                                         ('H2410','N1103'),
                                                         ('H2411','D1207'),
                                                         ('H2411','H2415'),
                                                         ('H2412','H2409'),
                                                         ('H2413','D1507'),
                                                         ('H2413','H2404'),
                                                         ('H2413','H2415'),
                                                         ('H2413','H2605'),
                                                         ('H2413','H2909'),
                                                         ('H2413','H3302'),
                                                         ('H2413','N1103'),
                                                         ('H2414','D1507'),
                                                         ('H2414','H2415'),
                                                         ('H2414','H2909'),
                                                         ('H2414','H3302'),
                                                         ('H2414','N1103'),
                                                         ('H2415','H2410'),
                                                         ('H2415','H2413'),
                                                         ('H2415','H2414'),
                                                         ('H2415','H3302'),
                                                         ('H2501','H2503'),
                                                         ('H2502','H1302'),
                                                         ('H2502','H1402'),
                                                         ('H2502','H1501'),
                                                         ('H2502','H1502'),
                                                         ('H2502','I1102'),
                                                         ('H2503','H1403'),
                                                         ('H2503','H1404'),
                                                         ('H2503','H1506'),
                                                         ('H2503','H2501'),
                                                         ('H2503','H2504'),
                                                         ('H2503','I1304'),
                                                         ('H2504','H1303'),
                                                         ('H2504','H1403'),
                                                         ('H2504','H1404'),
                                                         ('H2504','H2503'),
                                                         ('H2504','K2306'),
                                                         ('H2505','H1403'),
                                                         ('H2505','H1404'),
                                                         ('H2601','H2602'),
                                                         ('H2601','H2605'),
                                                         ('H2602','H2601'),
                                                         ('H2602','H2605'),
                                                         ('H2602','I1309'),
                                                         ('H2603','H2604'),
                                                         ('H2603','H2605'),
                                                         ('H2604','H2605'),
                                                         ('H2604','H2909'),
                                                         ('H2604','H3302'),
                                                         ('H2605','H2601'),
                                                         ('H2605','H2602'),
                                                         ('H2605','H2603'),
                                                         ('H2605','H2604'),
                                                         ('H2701','H2301'),
                                                         ('H2801','H2102'),
                                                         ('H2801','H2301'),
                                                         ('H2801','H2802'),
                                                         ('H2801','H2803'),
                                                         ('H2801','H2805'),
                                                         ('H2801','H2905'),
                                                         ('H2801','H2907'),
                                                         ('H2801','H3201'),
                                                         ('H2801','H3301'),
                                                         ('H2802','H2102'),
                                                         ('H2802','H2801'),
                                                         ('H2802','H2803'),
                                                         ('H2802','H3201'),
                                                         ('H2802','H3301'),
                                                         ('H2803','H2102'),
                                                         ('H2803','H2802'),
                                                         ('H2803','H3201'),
                                                         ('H2803','H3301'),
                                                         ('H2804','F1302'),
                                                         ('H2804','H2301'),
                                                         ('H2804','H2701'),
                                                         ('H2804','H2802'),
                                                         ('H2804','H2907'),
                                                         ('H2805','H2301'),
                                                         ('H2805','H2701'),
                                                         ('H2805','H2801'),
                                                         ('H2805','H2804'),
                                                         ('H2805','H2907'),
                                                         ('H2901','H2902'),
                                                         ('H2901','H2903'),
                                                         ('H2901','H2909'),
                                                         ('H2901','H2912'),
                                                         ('H2901','H3401'),
                                                         ('H2902','H2905'),
                                                         ('H2902','H2911'),
                                                         ('H2902','H2913'),
                                                         ('H2902','H2914'),
                                                         ('H2903','H2202'),
                                                         ('H2903','H2904'),
                                                         ('H2903','H2905'),
                                                         ('H2903','H2906'),
                                                         ('H2904','H2905'),
                                                         ('H2904','H2906'),
                                                         ('H2904','H2907'),
                                                         ('H2904','H3402'),
                                                         ('H2905','H2801'),
                                                         ('H2905','H2903'),
                                                         ('H2905','H2904'),
                                                         ('H2905','H2906'),
                                                         ('H2905','H2907'),
                                                         ('H2905','H2909'),
                                                         ('H2906','H2102'),
                                                         ('H2906','H2903'),
                                                         ('H2906','H2904'),
                                                         ('H2906','H2905'),
                                                         ('H2906','H2907'),
                                                         ('H2906','H2909'),
                                                         ('H2906','H3301'),
                                                         ('H2906','H3401'),
                                                         ('H2906','H3402'),
                                                         ('H2906','H3404'),
                                                         ('H2907','H2701'),
                                                         ('H2907','H2801'),
                                                         ('H2907','H2804'),
                                                         ('H2907','H2805'),
                                                         ('H2907','H2904'),
                                                         ('H2907','H2905'),
                                                         ('H2907','H2906'),
                                                         ('H2907','H3402'),
                                                         ('H2908','H2910'),
                                                         ('H2909','H2604'),
                                                         ('H2909','H2905'),
                                                         ('H2909','H2906'),
                                                         ('H2909','H3301'),
                                                         ('H2909','H3302'),
                                                         ('H2910','H2908'),
                                                         ('H2911','F1502'),
                                                         ('H2911','H2902'),
                                                         ('H2911','H2913'),
                                                         ('H2911','H2914'),
                                                         ('H2912','H2901'),
                                                         ('H2912','H2903'),
                                                         ('H2912','H3202'),
                                                         ('H2912','I1310'),
                                                         ('H2913','H2902'),
                                                         ('H2913','H2905'),
                                                         ('H2913','H2906'),
                                                         ('H2913','H2909'),
                                                         ('H2913','H2911'),
                                                         ('H2913','H2914'),
                                                         ('H2914','H2902'),
                                                         ('H2914','H2913'),
                                                         ('H3101','E1302'),
                                                         ('H3101','H2405'),
                                                         ('H3101','H2406'),
                                                         ('H3101','H3301'),
                                                         ('H3102','H2203'),
                                                         ('H3102','H2407'),
                                                         ('H3201','H2102'),
                                                         ('H3201','H2301'),
                                                         ('H3201','H2405'),
                                                         ('H3201','H2603'),
                                                         ('H3201','H2801'),
                                                         ('H3201','H2802'),
                                                         ('H3201','H2803'),
                                                         ('H3201','H3101'),
                                                         ('H3201','H3301'),
                                                         ('H3202','H2912'),
                                                         ('H3202','I1310'),
                                                         ('H3203','H3402'),
                                                         ('H3301','E1302'),
                                                         ('H3301','H2102'),
                                                         ('H3301','H2301'),
                                                         ('H3301','H2801'),
                                                         ('H3301','H2802'),
                                                         ('H3301','H2803'),
                                                         ('H3301','H2909'),
                                                         ('H3301','H3201'),
                                                         ('H3301','N1103'),
                                                         ('H3302','A1401'),
                                                         ('H3302','A1402'),
                                                         ('H3302','A1403'),
                                                         ('H3302','E1304'),
                                                         ('H3302','F1706'),
                                                         ('H3302','H2413'),
                                                         ('H3302','H2415'),
                                                         ('H3302','H2909'),
                                                         ('H3302','H3303'),
                                                         ('H3302','K2201'),
                                                         ('H3302','K2304'),
                                                         ('H3302','N1103'),
                                                         ('H3302','N1105'),
                                                         ('H3303','F1706'),
                                                         ('H3303','H3302'),
                                                         ('H3303','N1103'),
                                                         ('H3303','N1105'),
                                                         ('H3401','H2906'),
                                                         ('H3401','H3402'),
                                                         ('H3401','H3403'),
                                                         ('H3401','H3404'),
                                                         ('H3402','H2904'),
                                                         ('H3402','H2906'),
                                                         ('H3402','H2907'),
                                                         ('H3402','H3203'),
                                                         ('H3402','H3401'),
                                                         ('H3402','H3403'),
                                                         ('H3402','H3404'),
                                                         ('H3403','H2906'),
                                                         ('H3403','H2907'),
                                                         ('H3403','H3401'),
                                                         ('H3403','H3402'),
                                                         ('H3404','H2906'),
                                                         ('H3404','H2909'),
                                                         ('H3404','H3203'),
                                                         ('H3404','H3401'),
                                                         ('H3404','H3402'),
                                                         ('H3404','I1606'),
                                                         ('I1101','F1106'),
                                                         ('I1101','F1201'),
                                                         ('I1101','I1102'),
                                                         ('I1102','H1101'),
                                                         ('I1102','H2502'),
                                                         ('I1102','I1304'),
                                                         ('I1103','D1204'),
                                                         ('I1103','D1402'),
                                                         ('I1103','D1404'),
                                                         ('I1103','H2503'),
                                                         ('I1201','I1202'),
                                                         ('I1201','I1203'),
                                                         ('I1201','K2303'),
                                                         ('I1202','A1203'),
                                                         ('I1202','F1702'),
                                                         ('I1202','I1203'),
                                                         ('I1202','K2301'),
                                                         ('I1202','K2303'),
                                                         ('I1202','N4105'),
                                                         ('I1203','I1201'),
                                                         ('I1203','K2204'),
                                                         ('I1203','K2501'),
                                                         ('I1301','I1302'),
                                                         ('I1301','I1304'),
                                                         ('I1301','I1309'),
                                                         ('I1301','I1310'),
                                                         ('I1301','I1603'),
                                                         ('I1302','I1304'),
                                                         ('I1304','H2503'),
                                                         ('I1304','I1302'),
                                                         ('I1304','I1309'),
                                                         ('I1304','I1310'),
                                                         ('I1305','H1504'),
                                                         ('I1305','I1402'),
                                                         ('I1306','F1602'),
                                                         ('I1306','F1603'),
                                                         ('I1306','I1308'),
                                                         ('I1307','F1605'),
                                                         ('I1307','I1401'),
                                                         ('I1308','H1503'),
                                                         ('I1308','H2602'),
                                                         ('I1308','H2701'),
                                                         ('I1308','I1306'),
                                                         ('I1309','F1602'),
                                                         ('I1309','H2602'),
                                                         ('I1309','I1304'),
                                                         ('I1310','H2901'),
                                                         ('I1310','H2912'),
                                                         ('I1310','I1304'),
                                                         ('I1401','I1307'),
                                                         ('I1501','F1502'),
                                                         ('I1501','F1611'),
                                                         ('I1501','G1204'),
                                                         ('I1502','A1415'),
                                                         ('I1502','K1701'),
                                                         ('I1502','K1705'),
                                                         ('I1502','K1706'),
                                                         ('I1502','N3102'),
                                                         ('I1601','D1211'),
                                                         ('I1601','D1213'),
                                                         ('I1602','I1305'),
                                                         ('I1603','I1103'),
                                                         ('I1603','I1604'),
                                                         ('I1603','I1607'),
                                                         ('I1604','I1603'),
                                                         ('I1604','I1607'),
                                                         ('I1605','I1604'),
                                                         ('I1605','N3102'),
                                                         ('I1606','H2902'),
                                                         ('I1607','I1604'),
                                                         ('J1101','J1102'),
                                                         ('J1101','K2108'),
                                                         ('J1101','K2402'),
                                                         ('J1102','D1406'),
                                                         ('J1102','J1101'),
                                                         ('J1102','K2108'),
                                                         ('J1102','K2402'),
                                                         ('J1102','M1703'),
                                                         ('J1102','M1707'),
                                                         ('J1103','D1406'),
                                                         ('J1103','K2108'),
                                                         ('J1103','M1703'),
                                                         ('J1201','K2108'),
                                                         ('J1202','D1406'),
                                                         ('J1202','K2108'),
                                                         ('J1202','K2402'),
                                                         ('J1202','M1703'),
                                                         ('J1202','M1707'),
                                                         ('J1301','D1203'),
                                                         ('J1301','G1502'),
                                                         ('J1301','K1304'),
                                                         ('J1301','K2204'),
                                                         ('J1301','K2601'),
                                                         ('J1302','D1405'),
                                                         ('J1302','H1210'),
                                                         ('J1303','D1203'),
                                                         ('J1303','K2601'),
                                                         ('J1303','M1601'),
                                                         ('J1303','M1609'),
                                                         ('J1304','G1203'),
                                                         ('J1304','J1501'),
                                                         ('J1304','K1301'),
                                                         ('J1305','J1301'),
                                                         ('J1305','J1303'),
                                                         ('J1305','N4102'),
                                                         ('J1306','K1103'),
                                                         ('J1401','D1301'),
                                                         ('J1401','D1403'),
                                                         ('J1401','D1405'),
                                                         ('J1402','D1405'),
                                                         ('J1402','H1502'),
                                                         ('J1404','J1408'),
                                                         ('J1404','K1103'),
                                                         ('J1405','D1301'),
                                                         ('J1405','D1402'),
                                                         ('J1405','D1405'),
                                                         ('J1406','K1103'),
                                                         ('J1408','K1103'),
                                                         ('J1410','D1402'),
                                                         ('J1411','J1410'),
                                                         ('J1501','K1302'),
                                                         ('J1501','K2601'),
                                                         ('J1502','K1103'),
                                                         ('J1503','J1505'),
                                                         ('J1503','J1506'),
                                                         ('J1504','D1405'),
                                                         ('J1504','J1505'),
                                                         ('J1504','J1506'),
                                                         ('J1505','D1405'),
                                                         ('J1505','J1506'),
                                                         ('J1506','D1405'),
                                                         ('J1506','J1505'),
                                                         ('J1507','D1405'),
                                                         ('J1507','J1505'),
                                                         ('J1507','J1506'),
                                                         ('K1101','K1102'),
                                                         ('K1101','K1202'),
                                                         ('K1101','K1205'),
                                                         ('K1101','K1301'),
                                                         ('K1102','K1205'),
                                                         ('K1103','D1301'),
                                                         ('K1103','D1403'),
                                                         ('K1104','K2101'),
                                                         ('K1104','K2111'),
                                                         ('K1201','D1405'),
                                                         ('K1201','K1101'),
                                                         ('K1201','K1102'),
                                                         ('K1201','K1301'),
                                                         ('K1204','K1302'),
                                                         ('K1204','K1303'),
                                                         ('K1204','K2501'),
                                                         ('K1204','K2503'),
                                                         ('K1205','K1204'),
                                                         ('K1205','M1601'),
                                                         ('K1206','G1202'),
                                                         ('K1206','K1205'),
                                                         ('K1207','K1101'),
                                                         ('K1207','K1102'),
                                                         ('K1207','K1203'),
                                                         ('K1301','K1205'),
                                                         ('K1302','J1301'),
                                                         ('K1302','K1303'),
                                                         ('K1302','K1304'),
                                                         ('K1303','J1301'),
                                                         ('K1303','K1302'),
                                                         ('K1303','K1304'),
                                                         ('K1304','G1501'),
                                                         ('K1304','G1502'),
                                                         ('K1304','G1603'),
                                                         ('K1304','J1301'),
                                                         ('K1304','K1303'),
                                                         ('K1304','K2204'),
                                                         ('K1304','K2501'),
                                                         ('K1305','K1205'),
                                                         ('K1305','K1301'),
                                                         ('K1305','K1302'),
                                                         ('K1305','K1303'),
                                                         ('K1402','J1101'),
                                                         ('K1402','K2108'),
                                                         ('K1402','K2402'),
                                                         ('K1403','M1302'),
                                                         ('K1404','M1402'),
                                                         ('K1405','M1301'),
                                                         ('K1502','K1505'),
                                                         ('K1504','K1503'),
                                                         ('K1504','K1505'),
                                                         ('K1505','H1303'),
                                                         ('K1505','K1504'),
                                                         ('K1601','E1101'),
                                                         ('K1602','K1601'),
                                                         ('K1602','M1302'),
                                                         ('K1701','A1101'),
                                                         ('K1701','F1302'),
                                                         ('K1701','K1705'),
                                                         ('K1701','K1706'),
                                                         ('K1701','K2503'),
                                                         ('K1701','M1807'),
                                                         ('K1701','N4101'),
                                                         ('K1702','H1302'),
                                                         ('K1702','K1703'),
                                                         ('K1702','K2502'),
                                                         ('K1703','F1202'),
                                                         ('K1703','K1702'),
                                                         ('K1703','N1302'),
                                                         ('K1703','N2102'),
                                                         ('K1703','N2202'),
                                                         ('K1703','N3101'),
                                                         ('K1703','N4201'),
                                                         ('K1704','K1404'),
                                                         ('K1704','K1505'),
                                                         ('K1704','K2502'),
                                                         ('K1705','H1303'),
                                                         ('K1705','I1502'),
                                                         ('K1705','I1503'),
                                                         ('K1705','J1305'),
                                                         ('K1705','K1701'),
                                                         ('K1705','K1706'),
                                                         ('K1706','K1705'),
                                                         ('K1706','K1707'),
                                                         ('K1706','K2503'),
                                                         ('K1707','A1204'),
                                                         ('K1707','K1706'),
                                                         ('K1707','K2501'),
                                                         ('K1707','K2503'),
                                                         ('K1707','N4302'),
                                                         ('K1801','K2101'),
                                                         ('K1801','K2112'),
                                                         ('K1801','M1502'),
                                                         ('K1802','G1102'),
                                                         ('K1802','K1206'),
                                                         ('K1901','K1902'),
                                                         ('K2101','K2111'),
                                                         ('K2102','K2101'),
                                                         ('K2102','K2111'),
                                                         ('K2103','K1403'),
                                                         ('K2103','M1301'),
                                                         ('K2103','M1302'),
                                                         ('K2105','B1101'),
                                                         ('K2105','K2111'),
                                                         ('K2105','L1201'),
                                                         ('K2105','L1202'),
                                                         ('K2105','L1203'),
                                                         ('K2105','L1204'),
                                                         ('K2106','K2107'),
                                                         ('K2107','K2106'),
                                                         ('K2107','K2108'),
                                                         ('K2107','K2109'),
                                                         ('K2108','K2107'),
                                                         ('K2108','K2401'),
                                                         ('K2108','K2402'),
                                                         ('K2108','M1403'),
                                                         ('K2109','K2102'),
                                                         ('K2109','K2107'),
                                                         ('K2109','K2111'),
                                                         ('K2110','K2111'),
                                                         ('K2111','K1203'),
                                                         ('K2112','K1104'),
                                                         ('K2112','K2101'),
                                                         ('K2112','K2102'),
                                                         ('K2201','H2406'),
                                                         ('K2201','H2407'),
                                                         ('K2201','H2410'),
                                                         ('K2201','H3302'),
                                                         ('K2201','K2304'),
                                                         ('K2201','N1105'),
                                                         ('K2202','I1201'),
                                                         ('K2202','K2204'),
                                                         ('K2203','K2502'),
                                                         ('K2204','G1501'),
                                                         ('K2204','G1502'),
                                                         ('K2204','J1301'),
                                                         ('K2204','K1304'),
                                                         ('K2204','K2202'),
                                                         ('K2301','F1603'),
                                                         ('K2301','F1705'),
                                                         ('K2301','I1202'),
                                                         ('K2301','I1203'),
                                                         ('K2301','I1310'),
                                                         ('K2301','K2305'),
                                                         ('K2302','H1302'),
                                                         ('K2302','K2203'),
                                                         ('K2302','K2306'),
                                                         ('K2303','A1203'),
                                                         ('K2303','F1702'),
                                                         ('K2303','I1201'),
                                                         ('K2303','I1202'),
                                                         ('K2303','I1203'),
                                                         ('K2303','K2204'),
                                                         ('K2303','K2301'),
                                                         ('K2303','K2304'),
                                                         ('K2303','K2305'),
                                                         ('K2303','K2501'),
                                                         ('K2304','H3302'),
                                                         ('K2304','K2201'),
                                                         ('K2304','K2204'),
                                                         ('K2304','K2303'),
                                                         ('K2304','N1103'),
                                                         ('K2304','N1105'),
                                                         ('K2305','K2303'),
                                                         ('K2306','F1203'),
                                                         ('K2306','H1303'),
                                                         ('K2306','H2504'),
                                                         ('K2306','I1304'),
                                                         ('K2401','K2108'),
                                                         ('K2401','K2402'),
                                                         ('K2401','M1403'),
                                                         ('K2402','A1303'),
                                                         ('K2402','F1105'),
                                                         ('K2402','H1206'),
                                                         ('K2402','H1501'),
                                                         ('K2402','J1201'),
                                                         ('K2402','K1402'),
                                                         ('K2402','K1602'),
                                                         ('K2402','K2108'),
                                                         ('K2402','M1403'),
                                                         ('K2402','M1808'),
                                                         ('K2402','M1809'),
                                                         ('K2501','A1203'),
                                                         ('K2501','G1702'),
                                                         ('K2501','I1203'),
                                                         ('K2501','K1707'),
                                                         ('K2501','K2204'),
                                                         ('K2501','K2303'),
                                                         ('K2501','K2503'),
                                                         ('K2502','H1302'),
                                                         ('K2502','K1702'),
                                                         ('K2502','K1704'),
                                                         ('K2503','K1707'),
                                                         ('K2503','K2501'),
                                                         ('K2503','N4302'),
                                                         ('K2601','K2204'),
                                                         ('K2601','N1103'),
                                                         ('K2601','N1105'),
                                                         ('K2602','C1102'),
                                                         ('K2602','D1212'),
                                                         ('K2602','D1213'),
                                                         ('K2602','D1401'),
                                                         ('K2602','D1408'),
                                                         ('K2603','J1303'),
                                                         ('K2603','K2602'),
                                                         ('L1101','L1103'),
                                                         ('L1103','D1501'),
                                                         ('L1103','L1101'),
                                                         ('L1202','G1202'),
                                                         ('L1203','L1201'),
                                                         ('L1203','L1202'),
                                                         ('L1203','L1204'),
                                                         ('L1204','L1401'),
                                                         ('L1301','L1203'),
                                                         ('L1301','L1304'),
                                                         ('L1302','L1303'),
                                                         ('L1401','D1211'),
                                                         ('L1401','D1401'),
                                                         ('L1401','D1402'),
                                                         ('L1401','G1201'),
                                                         ('L1401','K1103'),
                                                         ('L1401','L1203'),
                                                         ('L1502','B1801'),
                                                         ('L1502','B1803'),
                                                         ('L1502','B1805'),
                                                         ('L1502','D1206'),
                                                         ('L1506','N1102'),
                                                         ('L1506','N1105'),
                                                         ('L1510','E1104'),
                                                         ('L1510','E1205'),
                                                         ('M1101','D1509'),
                                                         ('M1101','E1402'),
                                                         ('M1102','D1509'),
                                                         ('M1102','M1701'),
                                                         ('M1201','C1202'),
                                                         ('M1201','M1207'),
                                                         ('M1201','M1403'),
                                                         ('M1202','M1204'),
                                                         ('M1202','M1206'),
                                                         ('M1203','K1504'),
                                                         ('M1203','M1608'),
                                                         ('M1204','M1202'),
                                                         ('M1204','M1205'),
                                                         ('M1205','M1204'),
                                                         ('M1205','M1206'),
                                                         ('M1205','M1207'),
                                                         ('M1206','M1202'),
                                                         ('M1207','M1201'),
                                                         ('M1301','M1205'),
                                                         ('M1301','M1503'),
                                                         ('M1302','G1403'),
                                                         ('M1302','M1503'),
                                                         ('M1401','D1408'),
                                                         ('M1401','D1501'),
                                                         ('M1401','M1606'),
                                                         ('M1402','K2101'),
                                                         ('M1402','M1502'),
                                                         ('M1402','M1802'),
                                                         ('M1403','M1201'),
                                                         ('M1403','M1702'),
                                                         ('M1501','M1604'),
                                                         ('M1502','K1801'),
                                                         ('M1502','K2101'),
                                                         ('M1502','K2102'),
                                                         ('M1503','M1402'),
                                                         ('M1503','M1502'),
                                                         ('M1601','G1101'),
                                                         ('M1601','M1401'),
                                                         ('M1601','M1602'),
                                                         ('M1601','M1606'),
                                                         ('M1602','E1307'),
                                                         ('M1602','M1601'),
                                                         ('M1602','M1606'),
                                                         ('M1603','D1501'),
                                                         ('M1603','E1304'),
                                                         ('M1603','H3302'),
                                                         ('M1603','N1103'),
                                                         ('M1603','N4104'),
                                                         ('M1604','M1605'),
                                                         ('M1605','D1401'),
                                                         ('M1605','M1604'),
                                                         ('M1606','M1601'),
                                                         ('M1606','M1602'),
                                                         ('M1607','D1401'),
                                                         ('M1608','M1607'),
                                                         ('M1609','M1607'),
                                                         ('M1701','D1406'),
                                                         ('M1701','M1704'),
                                                         ('M1702','E1402'),
                                                         ('M1702','M1706'),
                                                         ('M1703','E1402'),
                                                         ('M1703','M1702'),
                                                         ('M1703','M1705'),
                                                         ('M1704','D1509'),
                                                         ('M1704','E1103'),
                                                         ('M1705','E1103'),
                                                         ('M1705','E1401'),
                                                         ('M1705','E1402'),
                                                         ('M1705','M1703'),
                                                         ('M1706','D1406'),
                                                         ('M1706','E1401'),
                                                         ('M1706','M1702'),
                                                         ('M1707','M1704'),
                                                         ('M1707','M1705'),
                                                         ('M1707','M1706'),
                                                         ('M1801','H1101'),
                                                         ('M1801','M1802'),
                                                         ('M1801','M1810'),
                                                         ('M1802','M1801'),
                                                         ('M1802','M1804'),
                                                         ('M1802','M1805'),
                                                         ('M1802','M1806'),
                                                         ('M1803','M1804'),
                                                         ('M1803','M1805'),
                                                         ('M1803','M1806'),
                                                         ('M1804','H1206'),
                                                         ('M1804','M1805'),
                                                         ('M1804','M1806'),
                                                         ('M1805','M1804'),
                                                         ('M1805','M1806'),
                                                         ('M1806','H1102'),
                                                         ('M1806','M1402'),
                                                         ('M1806','M1802'),
                                                         ('M1806','M1804'),
                                                         ('M1806','M1805'),
                                                         ('M1807','I1302'),
                                                         ('M1807','I1304'),
                                                         ('M1807','I1305'),
                                                         ('M1807','I1307'),
                                                         ('M1807','I1309'),
                                                         ('M1807','I1401'),
                                                         ('M1807','I1602'),
                                                         ('M1807','K1701'),
                                                         ('M1807','M1801'),
                                                         ('M1808','F1107'),
                                                         ('M1808','M1809'),
                                                         ('M1809','M1808'),
                                                         ('M1810','M1801'),
                                                         ('M1810','M1806'),
                                                         ('N1101','A1101'),
                                                         ('N1101','N1103'),
                                                         ('N1101','N1104'),
                                                         ('N1101','N2203'),
                                                         ('N1101','N3203'),
                                                         ('N1102','N1101'),
                                                         ('N1102','N1103'),
                                                         ('N1102','N3203'),
                                                         ('N1103','D1507'),
                                                         ('N1103','E1304'),
                                                         ('N1103','H3301'),
                                                         ('N1103','H3302'),
                                                         ('N1103','H3303'),
                                                         ('N1103','K2201'),
                                                         ('N1103','N1101'),
                                                         ('N1103','N1105'),
                                                         ('N1103','N2203'),
                                                         ('N1103','N4105'),
                                                         ('N1104','F1301'),
                                                         ('N1104','F1706'),
                                                         ('N1104','N1101'),
                                                         ('N1104','N3202'),
                                                         ('N1104','N3203'),
                                                         ('N1104','N4402'),
                                                         ('N1104','N4403'),
                                                         ('N1105','A1401'),
                                                         ('N1105','A1402'),
                                                         ('N1105','A1403'),
                                                         ('N1105','D1507'),
                                                         ('N1105','E1304'),
                                                         ('N1105','F1704'),
                                                         ('N1105','H3302'),
                                                         ('N1105','K2201'),
                                                         ('N1105','K2204'),
                                                         ('N1105','K2303'),
                                                         ('N1105','K2304'),
                                                         ('N1105','N1103'),
                                                         ('N1105','N4403'),
                                                         ('N1201','D1402'),
                                                         ('N1201','D1407'),
                                                         ('N1201','M1101'),
                                                         ('N1201','N4201'),
                                                         ('N1201','N4203'),
                                                         ('N1202','D1401'),
                                                         ('N1202','N3201'),
                                                         ('N1202','N3202'),
                                                         ('N1202','N4203'),
                                                         ('N1202','N4204'),
                                                         ('N1301','D1407'),
                                                         ('N1301','H1401'),
                                                         ('N1301','M1101'),
                                                         ('N1301','N1302'),
                                                         ('N1301','N3201'),
                                                         ('N1301','N3202'),
                                                         ('N1302','H1401'),
                                                         ('N1302','M1101'),
                                                         ('N1302','N3201'),
                                                         ('N1302','N3202'),
                                                         ('N1302','N4201'),
                                                         ('N1302','N4202'),
                                                         ('N1303','H1403'),
                                                         ('N1303','N3202'),
                                                         ('N1303','N4203'),
                                                         ('N1303','N4204'),
                                                         ('N2101','D1204'),
                                                         ('N2101','G1101'),
                                                         ('N2101','G1703'),
                                                         ('N2101','M1601'),
                                                         ('N2101','N2201'),
                                                         ('N2101','N4302'),
                                                         ('N2101','N4401'),
                                                         ('N2201','D1204'),
                                                         ('N2201','G1101'),
                                                         ('N2201','G1703'),
                                                         ('N2201','M1601'),
                                                         ('N2201','N4302'),
                                                         ('N2203','N1101'),
                                                         ('N2203','N1103'),
                                                         ('N2203','N1105'),
                                                         ('N2203','N4104'),
                                                         ('N2203','N4403'),
                                                         ('N2204','N1303'),
                                                         ('N2204','N2101'),
                                                         ('N2204','N4203'),
                                                         ('N2204','N4204'),
                                                         ('N2205','G1403'),
                                                         ('N2205','N1302'),
                                                         ('N2205','N4201'),
                                                         ('N2205','N4202'),
                                                         ('N3101','A1406'),
                                                         ('N3101','N3201'),
                                                         ('N3101','N3202'),
                                                         ('N3102','A1415'),
                                                         ('N3102','N3103'),
                                                         ('N3201','D1407'),
                                                         ('N3201','N1303'),
                                                         ('N3201','N3202'),
                                                         ('N3201','N4203'),
                                                         ('N3201','N4204'),
                                                         ('N3202','D1407'),
                                                         ('N3202','N1202'),
                                                         ('N3202','N1303'),
                                                         ('N3202','N3201'),
                                                         ('N3202','N4201'),
                                                         ('N3202','N4203'),
                                                         ('N3202','N4204'),
                                                         ('N3203','N1101'),
                                                         ('N3203','N1102'),
                                                         ('N3203','N1103'),
                                                         ('N3203','N1104'),
                                                         ('N3203','N1105'),
                                                         ('N3203','N4101'),
                                                         ('N4101','N4102'),
                                                         ('N4101','N4103'),
                                                         ('N4101','N4104'),
                                                         ('N4101','N4105'),
                                                         ('N4102','N4104'),
                                                         ('N4102','N4105'),
                                                         ('N4103','N4102'),
                                                         ('N4103','N4104'),
                                                         ('N4103','N4105'),
                                                         ('N4103','N4302'),
                                                         ('N4104','N4102'),
                                                         ('N4104','N4105'),
                                                         ('N4105','N1102'),
                                                         ('N4105','N1103'),
                                                         ('N4105','N4101'),
                                                         ('N4105','N4102'),
                                                         ('N4105','N4104'),
                                                         ('N4201','D1407'),
                                                         ('N4201','I1103'),
                                                         ('N4201','M1101'),
                                                         ('N4201','M1302'),
                                                         ('N4201','N4202'),
                                                         ('N4202','D1407'),
                                                         ('N4202','I1103'),
                                                         ('N4202','M1101'),
                                                         ('N4202','M1302'),
                                                         ('N4202','N4201'),
                                                         ('N4203','D1204'),
                                                         ('N4203','D1402'),
                                                         ('N4203','N1303'),
                                                         ('N4203','N4204'),
                                                         ('N4204','D1204'),
                                                         ('N4204','D1402'),
                                                         ('N4204','N4203'),
                                                         ('N4301','N4302'),
                                                         ('N4301','N4401'),
                                                         ('N4301','N4402'),
                                                         ('N4302','K1707'),
                                                         ('N4302','K2503'),
                                                         ('N4401','N4302'),
                                                         ('N4403','F1702'),
                                                         ('N4403','F1704'),
                                                         ('N4403','N1105'),
                                                         ('N4403','N2203'),
                                                         ('N4403','N3203');
