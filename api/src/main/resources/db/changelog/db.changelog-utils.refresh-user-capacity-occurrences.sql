--liquibase formatted sql
--changeset eric:1663156787779-2
DELETE
FROM CapacityOccurrence;

INSERT INTO CapacityOccurrence
SELECT count(AC.inducedCapacities_id), 0, AC.inducedCapacities_id, UE.userProfile_uuid
FROM UserProfile
         INNER JOIN UserExperience UE on UserProfile.uuid = UE.userProfile_uuid
         INNER JOIN ErhgoOccupation EO on UE.erhgoOccupation_id = EO.id
         INNER JOIN OccupationActivity OA on EO.id = OA.occupation_id
         INNER JOIN JobActivityLabel JAL on OA.activity_uuid = JAL.uuid
         inner JOIN Activity A on JAL.activity_uuid = A.uuid
         INNER JOIN Activity_Capacity AC on A.uuid = AC.Activity_uuid
group by UE.userProfile_uuid, AC.inducedCapacities_id;

INSERT INTO CapacityOccurrence (occurrence, recursiveOccurrence, capacity_id, userProfile_uuid)
SELECT count(CRQR.capacities_id), 0, CRQR.capacities_id, UP.uuid
FROM UserProfile UP
         inner join AnswerForCapacityRelatedQuestion A on UP.uuid = A.userProfile_uuid
         inner join CapacityRelatedQuestionResponse_Capacity CRQR
                    on A.response_id = CRQR.CapacityRelatedQuestionResponse_id
group by UP.uuid, CRQR.capacities_id ON DUPLICATE KEY
UPDATE occurrence = occurrence +
VALUES (occurrence);

INSERT INTO CapacityOccurrence
SELECT 0, count(IAC.induced_capacity_id), IAC.induced_capacity_id, UE.userProfile_uuid
FROM UserProfile
         INNER JOIN UserExperience UE on UserProfile.uuid = UE.userProfile_uuid
         INNER JOIN ErhgoOccupation EO on UE.erhgoOccupation_id = EO.id
         INNER JOIN OccupationActivity OA on EO.id = OA.occupation_id
         INNER JOIN JobActivityLabel JAL on OA.activity_uuid = JAL.uuid
         inner JOIN Activity A on JAL.activity_uuid = A.uuid
         INNER JOIN Activity_Capacity AC on A.uuid = AC.Activity_uuid
         INNER JOIN Capacity_Capacity IAC on IAC.capacity_id = AC.inducedCapacities_id
group by UE.userProfile_uuid, IAC.induced_capacity_id ON DUPLICATE KEY
UPDATE recursiveOccurrence = recursiveOccurrence +
VALUES (recursiveOccurrence);


INSERT INTO CapacityOccurrence (occurrence, recursiveOccurrence, capacity_id, userProfile_uuid)
SELECT 0, count(IAC.induced_capacity_id), IAC.induced_capacity_id, UP.uuid
FROM UserProfile UP
         inner join AnswerForCapacityRelatedQuestion A on UP.uuid = A.userProfile_uuid
         inner join CapacityRelatedQuestionResponse_Capacity CRQR
                    on A.response_id = CRQR.CapacityRelatedQuestionResponse_id
         INNER JOIN Capacity_Capacity IAC on IAC.capacity_id = CRQR.capacities_id
group by UP.uuid, IAC.capacity_id ON DUPLICATE KEY
UPDATE recursiveOccurrence = recursiveOccurrence +
VALUES (recursiveOccurrence);


INSERT INTO CapacityOccurrence
SELECT 0, count(IAC2.induced_capacity_id), IAC2.induced_capacity_id, UE.userProfile_uuid
FROM UserProfile
         INNER JOIN UserExperience UE on UserProfile.uuid = UE.userProfile_uuid
         INNER JOIN ErhgoOccupation EO on UE.erhgoOccupation_id = EO.id
         INNER JOIN OccupationActivity OA on EO.id = OA.occupation_id
         INNER JOIN JobActivityLabel JAL on OA.activity_uuid = JAL.uuid
         inner JOIN Activity A on JAL.activity_uuid = A.uuid
         INNER JOIN Activity_Capacity AC on A.uuid = AC.Activity_uuid
         INNER JOIN Capacity_Capacity IAC on IAC.capacity_id = AC.inducedCapacities_id
         INNER JOIN Capacity_Capacity IAC2 on IAC2.capacity_id = IAC.induced_capacity_id
group by UE.userProfile_uuid, IAC2.induced_capacity_id ON DUPLICATE KEY
UPDATE recursiveOccurrence = recursiveOccurrence +
VALUES (recursiveOccurrence);


INSERT INTO CapacityOccurrence (occurrence, recursiveOccurrence, capacity_id, userProfile_uuid)
SELECT 0, count(IAC2.induced_capacity_id), IAC2.induced_capacity_id, UP.uuid
FROM UserProfile UP
         inner join AnswerForCapacityRelatedQuestion A on UP.uuid = A.userProfile_uuid
         inner join CapacityRelatedQuestionResponse_Capacity CRQR
                    on A.response_id = CRQR.CapacityRelatedQuestionResponse_id
         INNER JOIN Capacity_Capacity IAC on IAC.capacity_id = CRQR.capacities_id
         INNER JOIN Capacity_Capacity IAC2 on IAC2.capacity_id = IAC.induced_capacity_id
group by UP.uuid, IAC2.induced_capacity_id ON DUPLICATE KEY
UPDATE recursiveOccurrence = recursiveOccurrence +
VALUES (recursiveOccurrence);
