-- liquibase formatted sql

-- changeset eric:20241209:1
ALTER TABLE Recruitment
    ADD COLUMN bitmaskCA1 BIGINT,
    ADD COLUMN bitmaskCA2 BIGINT,
    ADD COLUMN bitmaskCA3 BIGINT;

-- changeset eric:20241209:2
CREATE INDEX IDX_bitmaskCA1 ON Recruitment (bitmaskCA1);
-- changeset eric:20241209:3
CREATE INDEX IDX_bitmaskCA2 ON Recruitment (bitmaskCA2);
-- changeset eric:20241209:4
CREATE INDEX IDX_bitmaskCA3 ON Recruitment (bitmaskCA3);

-- changeset eric:20241209:5
UPDATE Recruitment r
    JOIN (SELECT t.id     AS recruitment_id,
                 SUM(CASE
                         WHEN SUBSTRING(c.code, 3, 1) = '1' THEN POW(2, CAST(SUBSTRING(c.code, 5, 2) AS UNSIGNED) - 1)
                         ELSE 0
                     END) AS bitmaskCA1,
                 SUM(CASE
                         WHEN SUBSTRING(c.code, 3, 1) = '2' THEN POW(2, CAST(SUBSTRING(c.code, 5, 2) AS UNSIGNED) - 1)
                         ELSE 0
                     END) AS bitmaskCA2,
                 SUM(CASE
                         WHEN SUBSTRING(c.code, 3, 1) = '3' THEN POW(2, CAST(SUBSTRING(c.code, 5, 2) AS UNSIGNED) - 1)
                         ELSE 0
                     END) AS bitmaskCA3
          FROM (SELECT DISTINCT r1_0.id,
                                ic1_0.inducedCapacities_id
                FROM Recruitment r1_0
                         JOIN RecruitmentProfile rp on r1_0.recruitmentProfile_uuid = rp.uuid
                         JOIN Job j on rp.job_id = j.id
                         JOIN Mission m on j.id = m.job_id
                         JOIN Mission_JobActivityLabel a1_0 ON m.id = a1_0.Mission_id
                         JOIN JobActivityLabel a1_1 ON a1_1.uuid = a1_0.activities_uuid
                         JOIN Activity a2_0 ON a2_0.uuid = a1_1.activity_uuid
                         JOIN Activity_Capacity ic1_0 ON a2_0.uuid = ic1_0.Activity_uuid
                         JOIN Capacity c ON c.id = ic1_0.inducedCapacities_id) AS t
                   JOIN Capacity c ON c.id = t.inducedCapacities_id
          GROUP BY t.id) AS masks ON r.id = masks.recruitment_id
SET r.bitMaskCA1 = masks.bitmaskCA1,
    r.bitMaskCA2 = masks.bitmaskCA2,
    r.bitMaskCA3 = masks.bitmaskCA3;


-- changeset amir:20241211:1
ALTER TABLE ErhgoOccupation
    MODIFY technical BIT DEFAULT 1 NOT NULL;

-- changeset amir:20241211:2
UPDATE ErhgoOccupation
SET technical = 1
WHERE technical = 0;

-- changeset eric:20241219:1
DELETE
FROM ConfigurableProperty
WHERE propertyKey like 'ats.notification-delay-in-hours%';
