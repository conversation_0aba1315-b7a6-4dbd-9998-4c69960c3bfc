--liquibase formatted sql
--changeset eric:12072027-0
DELETE FROM DataHealthChecker;
--changeset eric:12072027-1
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           'Activité sans capacité',
           'select count(*)<=3, count(*) from Activity where uuid not in (select Activity_uuid from Activity_Capacity)',
           'Il existe de nouvelles activités sans capacité' );
--changeset eric:12072027-2
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           'Comptes créés dans les 7 derniers jours',
           'select count(*)>=70, count(*) from UserProfile where createdDate >= SUBDATE(SYSDATE(), 7)',
           'Moins de 70 comptes créés dans les 7 derniers jours');
--changeset eric:12072027-3
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           'Candidatures dans les 7 derniers jours',
           'select count(*)>=10, count(*) from RecruitmentCandidature where submissionDate >= SUBDATE(SYSDATE(), 7)',
           'Moins de 10 candidatures publiées dans les 7 derniers jours');
--changeset eric:12072027-4
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           "Envoi d'emails sourcing",
           "select count(*) = 0, count(*) from Recruitment r where r.sendNotificationState = 'ERROR'",
           "Au moins un envoi d'email sourcing en erreur" );
--changeset eric:12072027-6-1
DELETE FROM DataHealthChecker WHERE title like '%refus de candidature%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           "Envoi d'emails refus de candidature",
           "select count(*) = 0, count(*) from RecruitmentCandidature c WHERE c.emailSent = 'ERROR'",
           "Au moins un envoi d'email de refus de candidature" );
--changeset eric:12072027-7
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           "Utilisateur sans date de dernière connexion",
           "select count(*) <= 80, count(*) from UserProfile up WHERE lastConnectionDate is null",
           "Au moins un nouveau compte sans date de dernière connexion" );

--changeset eric:12072027-8
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           "Utilisateur sans date de création",
           "select count(*) <= 3721, count(*) - 3721 from UserProfile up WHERE createdDate is null",
           "Au moins un nouveau compte sans date de création" );
--changeset eric:12072027-91
DELETE FROM DataHealthChecker WHERE title like '%Réponses manquantes de Trimoji%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           "Réponses manquantes de Trimoji",
           "select count(1) = 0, count(1) from UserProfile up WHERE up.endedDate < NOW() - INTERVAL 1 HOUR AND up.trimojiPdfUrl IS NULL",
           "Au moins un compte sans réponse de Trimoji depuis plus d'une heure" );
--changeset eric:12072027-10
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           "Recrutement sans code",
           "select count(1) = 0, count(1) from Recruitment WHERE code is null",
           "Au moins un recrutement sans code" );
--changeset eric:12072027-111
DELETE FROM DataHealthChecker where title='Envoi d''emails en erreur';
INSERT INTO DataHealthChecker
    VALUE (
        UuidToBin('ba7e6830-c9e1-4799-9b4c-af70eb791423'),
           "Génération de notifications",
           "select count(1) = 0, count(1) from Recruitment WHERE sendNotificationDate < DATE_ADD(NOW(), INTERVAL -3 HOUR) AND sendNotificationState = 'WAITING' AND state=1",
           "La génération des notifications a échoué pour au moins un recrutement" );
--changeset eric:12072028-122
DELETE FROM DataHealthChecker where id=UuidToBin('d179a601-b415-4162-99c9-947686670ab2');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin("d179a601-b415-4162-99c9-947686670ab2"),
           "Synchronisation de candidatures en erreur ou attente depuis plus de 2 jours",
           "select count(distinct rc.id) = 0, count(distinct rc.id) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id inner join UserExperience xp on xp.userProfile_uuid =rc.userProfile_uuid  WHERE rc.globalCandidatureState = 'NOT_TREATED_BY_ERHGO' AND (( rc.synchronizationState = 'ERROR') OR  (eo.atsCode not in ('SUCCESS_FACTORS') AND rc.submissionDate < DATE_ADD(NOW(), INTERVAL -2 DAY) AND rc.synchronizationState IN ('WAITING', 'PENDING')))",
           "Au moins une candidature n'a pas été envoyée " );
--changeset eric:12072027-13
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin(uuid()),
           "Envoi d'annonces pour les candidatures refusées depuis plus de 60h",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc WHERE (rc.emailSent = 'WAITING') AND (rc.refusalDate < DATE_ADD(NOW(), INTERVAL -60 HOUR))",
           "Au moins une candidature refusée n'a pas été annoncée" );
--changeset eric:********-14
DELETE FROM DataHealthChecker where title = 'Offre externe sans recrutement';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin("c1b91c14-c5e2-474e-9a4a-b26257b99532"),
           "Offre externe sans recrutement",
           "select count(*) = 0, count(*) from ExternalOffer e where e.recruitment_id is null AND e.recruitmentCreationState <> 'IGNORE' AND e.lastEventType not in ('SUSPENDED', 'CANCELED') AND e.updatedDate < DATE_SUB(SYSDATE(), INTERVAL 1 HOUR)",
           "Au moins une offre externe n'a pas abouti à la création d'un recrutement" );

--changeset amir:********-15
DELETE FROM DataHealthChecker where id=UuidToBin('da308536-fafe-438c-9f93-f51e2123d1e7');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('da308536-fafe-438c-9f93-f51e2123d1e7'),
           "Candidatures Eolia envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'eolia' AND rc.remoteNotifiedIdentifier NOT LIKE '%@redirection-eolia.com'  AND rc.remoteNotifiedIdentifier NOT LIKE '%@candidature-eolia.com' AND rc.synchronizationState = 'DONE' AND rc.submissionDate > '2024-09-01'",
           "Au moins une candidature Eolia a été envoyée à une adresse incorrecte" );

--changeset amir:12072028-16
DELETE FROM DataHealthChecker where title like 'Candidatures Adecco envoyées à%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('da308536-fafe-438c-9f93-f51e2123d1e6'),
           "Candidatures Adecco envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'adecco' AND rc.remoteNotifiedIdentifier NOT LIKE 'https://api.adecco.fr/%' AND rc.synchronizationState = 'DONE' AND rc.submissionDate > '2024-09-01'",
           "Au moins une candidature Adecco a été envoyée à une adresse incorrecte" );

--changeset amir:12072028-17
DELETE FROM DataHealthChecker where title like 'Candidatures Beetween envoyées à%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('da308536-fafe-438c-9f93-f51e2123d1e5'),
           "Candidatures Beetween envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'beetwee' AND rc.remoteNotifiedIdentifier NOT LIKE 'https://api.beetween.com/%' AND rc.synchronizationState = 'DONE' AND rc.submissionDate > '2024-09-01'",
           "Au moins une candidature Beetween a été envoyée à une adresse incorrecte" );

--changeset amir:12072028-18
DELETE FROM DataHealthChecker where title like 'Candidatures Hello Work envoyées à la mauvaise%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('da308536-fafe-438c-9f93-f51e2123d1e4'),
           "Candidatures Hello Work envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'HELLO_WORK' AND rc.remoteNotifiedIdentifier NOT LIKE '%<EMAIL>' AND rc.synchronizationState = 'DONE'",
           "Au moins une candidature Hello Work a été envoyée à une adresse incorrecte" );

--changeset amir:12072028-19
DELETE FROM DataHealthChecker where title like 'Candidatures Digital Recruiters envoyées%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('da308536-fafe-438c-9f93-f51e2123d1e3'),
           "Candidatures Digital Recruiters envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'DIGITAL_RECRUITERS' AND rc.remoteNotifiedIdentifier NOT LIKE 'https://app.digitalrecruiters.com/api/candidate/apply%' AND rc.synchronizationState = 'DONE'",
           "Au moins une candidature Digital Recruiters a été envoyée à une adresse incorrecte" );

--changeset amir:12072030-20
DELETE FROM DataHealthChecker where id=UuidToBin('152294e0-8d23-11ef-afc8-2e7034683381');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('152294e0-8d23-11ef-afc8-2e7034683381'),
           "Candidatures softy envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'softy' AND rc.remoteNotifiedIdentifier NOT LIKE 'https://sc.softy.pro/api/v1/jenesuispasuncv/apply%' AND rc.synchronizationState = 'DONE' AND r.createdDate > '2025-03-01'",
           "Au moins une candidature softy a été envoyée à une adresse incorrecte" );

--changeset amir:12072028-21
DELETE FROM DataHealthChecker where title like 'Candidatures Talent Plug envoyées à la mauvaise%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('da308536-fafe-438c-9f93-f51e2123d1e2'),
           "Candidatures Talent Plug envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'TALENT_PLUG' AND rc.remoteNotifiedIdentifier NOT LIKE '%@applicount.com' AND rc.synchronizationState = 'DONE'",
           "Au moins une candidature Talent Plug a été envoyée à une adresse incorrecte" );

--changeset amir:12072028-22
DELETE FROM DataHealthChecker where title like 'Candidatures Taleez envoyées à%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('da308536-fafe-438c-9f93-f51e2123d1e1'),
           "Candidatures Taleez envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'taleez' AND rc.remoteNotifiedIdentifier NOT LIKE 'https://app.taleez.com/api/public/applications%' AND rc.synchronizationState = 'DONE'",
           "Au moins une candidature Taleez a été envoyée à une adresse incorrecte" );

--changeset amir:12072028-23
DELETE FROM DataHealthChecker where title like 'Candidatures InRecruiting envoyée%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('ca308536-fafe-438c-9f93-f51e2123d1e1'),
           "Candidatures InRecruiting envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'IN_RECRUITING' AND rc.remoteNotifiedIdentifier NOT LIKE 'https://inrecruitingfr.intervieweb.it%' AND rc.synchronizationState = 'DONE'",
           "Au moins une candidature InRecruiting a été envoyée à une adresse incorrecte" );

--changeset eric:********-24
DELETE FROM DataHealthChecker where title = 'Candidatures Talent Plug BOFROS8%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('ba308536-fafe-438c-9f93-f51e2123d1e1'),
           "Candidatures Talent Plug BOFROST envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'TALENT_PLUG' AND eo.configCode = 'BOFROST' AND rc.remoteNotifiedIdentifier NOT LIKE '%<EMAIL>' AND rc.synchronizationState = 'DONE'",
           "Au moins une candidature Talent Plug BOFROST a été envoyée à une adresse incorrecte" );

--changeset eric:********-25
DELETE FROM DataHealthChecker where title = 'Candidatures Talent Plug AREA%';
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('db308536-fafe-438c-9f93-f51e2123d1e1'),
           "Candidatures Talent Plug AREAS envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'TALENT_PLUG' AND eo.configCode = 'AREAS' AND rc.remoteNotifiedIdentifier LIKE '%<EMAIL>' AND rc.synchronizationState = 'DONE'",
           "Au moins une candidature Talent Plug AREAS a été envoyée à une adresse incorrecte" );

--changeset eric:12072028-26
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin("160ca344-e627-45a0-ac98-c96fbaa66aa5"),
           "Recrutement clos ou suspendu sans date de fin de publication",
           "select count(1) = 0, count(1) from Recruitment WHERE state > 1 AND publicationEndDate IS NULL",
           "Au moins un recrutement clos ou suspendu sans date de fin de publication" );

--changeset eric:12072028-27-1
DELETE FROM DataHealthChecker where id=UuidToBin('ba7e6830-c9e1-4799-9b4c-af70eb791426');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin("ba7e6830-c9e1-4799-9b4c-af70eb791426"),
           "Recrutement publié sur offre externe suspendue",
           "select count(1) = 0, count(1) from Recruitment r inner join ExternalOffer o on o.recruitment_id=r.id WHERE r.state = 1 AND o.lastEventType not in ('CREATED', 'MODIFIED', 'REPUBLISHED') AND o.recruitmentCreationState <> 'IGNORE'",
           "Au moins un recrutement publié sur une offre externe suspendue" );

--changeset eric:12072028-28-1
DELETE FROM DataHealthChecker where id=UuidToBin('ba7e6830-c9e1-4799-9b4c-af70eb791427');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin("ba7e6830-c9e1-4799-9b4c-af70eb791427"),
           "Recrutement suspendu sur offre externe publiée",
           "select count(1) = 0, count(1) from Recruitment r inner join ExternalOffer o on o.recruitment_id=r.id WHERE r.state > 1 AND o.lastEventType in ('CREATED', 'MODIFIED', 'REPUBLISHED') AND o.recruitmentCreationState <> 'IGNORE'",
           "Au moins un recrutement suspendu sur une offre externe publiée" );

--changeset amir:12072030-29
DELETE FROM DataHealthChecker where id=UuidToBin('152294e0-8d23-11ef-afc8-2e7034683382');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('152294e0-8d23-11ef-afc8-2e7034683382'),
           "Candidatures talentsoft envoyées à la mauvaise adresse",
           "select count(1) = 0, count(1) from RecruitmentCandidature rc inner join Recruitment r on rc.recruitment_id=r.id inner join ExternalOffer eo on eo.recruitment_id=r.id WHERE eo.atsCode = 'TALENTSOFT' AND rc.remoteNotifiedIdentifier NOT LIKE 'https://offres.groupama-gan-recrute.com/api/v2/applicants/applicationswithoutaccount' AND rc.synchronizationState = 'DONE' AND eo.configCode='GROUPAMA'",
           "Au moins une candidature softy a été envoyée à une adresse incorrecte" );

--changeset eric:********-30
DELETE FROM DataHealthChecker where id=UuidToBin('c799f2c5-6653-4e7b-90df-8769cf36a610');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('c799f2c5-6653-4e7b-90df-8769cf36a610'),
           "Offres externes mal configurées : offres softy ou taleez avec configCode ou sans computedRecruiterCode",
           "select count(*) = 0, count(*) from ExternalOffer WHERE atsCode in ('softy', 'taleez') AND (configCode IS NOT NULL OR computedRecruiterCode IS NULL)",
           "Au moins une candidature softy a été envoyée à une adresse incorrecte" );

--changeset eric:********-31
DELETE FROM DataHealthChecker where id=UuidToBin('bf45c997-624f-42fa-8ed7-7809694df368');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('bf45c997-624f-42fa-8ed7-7809694df368'),
           "Offres externes mal configurées : offres NON softy ou taleez sans configCode ou computedRecruiterCode",
           "select count(*) = 0, count(*) from ExternalOffer WHERE atsCode not in ('softy', 'taleez') AND (configCode IS NULL OR computedRecruiterCode IS NULL)",
           "Au moins une candidature softy a été envoyée à une adresse incorrecte" );
--changeset eric:********-32
DELETE FROM DataHealthChecker where id=UuidToBin('be11f2f2-51de-4bd9-9210-bc97ad471ce8');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('be11f2f2-51de-4bd9-9210-bc97ad471ce8'),
           "Métier sans activité",
           "select count(*) = 0, count(*) from ErhgoOccupation WHERE id NOT IN (select occupation_id from OccupationActivity)",
           "Au moins un métier sans activité" );
--changeset eric:********-33
DELETE FROM DataHealthChecker where id=UuidToBin('be11f2f2-51de-4bd9-9210-bc97ad471ce9');
INSERT INTO DataHealthChecker
    VALUE (
           UuidToBin('be11f2f2-51de-4bd9-9210-bc97ad471ce9'),
           "Compte sourcing ATS non activé",
           "select  count(distinct o.computedRecruiterCode) = 0, count(distinct o.computedRecruiterCode) from ExternalOffer o inner join Organization orga on orga.code=o.computedRecruiterCode inner join SourcingSubscription s on orga.id = s.recruiter_id where s.invitation_uuid is null",
           "Au moins un compte rattaché à un ATS a été créé dans sourcing mais pas activé - il sera suspendu automatiquement au bout d'un mois" );
