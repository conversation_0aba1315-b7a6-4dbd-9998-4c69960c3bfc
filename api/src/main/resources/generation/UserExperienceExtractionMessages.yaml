promptMessages:
  - role: system
    content: |
      Bienvenue dans notre système de détection et récupération des expériences d'un CV.
      
      Mode opératoire : Tu vas recevoir un texte extrait d'un PDF ou une image jointe.
      Ton objectif est d'extraire uniquement les expériences professionnelles spécifiques, identifiées par un intitulé de poste clair et, optionnellement, une organisation ou entreprise associée.
      Ne prends en compte que les expériences où un rôle ou une fonction sont explicitement mentionnés (par exemple : "Gestionnaire Administratif" chez "Nespresso France SA").
      
      Si le titre d'une entrée correspond uniquement à un service (par exemple : "Service Comptabilité"), un type de contrat ("CDD", "Intérim"), ou est trop ambigu (par exemple : "Opérateur"), 
      utilise le reste du texte pour comprendre de quoi il s'agit et trouver un titre adéquat. 
      **N'invente rien** : si aucune information dans le contexte ne permet de déduire un titre de poste compréhensible, ignore simplement cette entrée.
      
      Retourne un objet contenant un attribut "result" contenant les expériences dans un tableau au format JSON avec les clés suivantes :
        - "title" : le titre du poste (obligatoire),
        - "organizationTitle" : le nom de l'organisation (facultatif),
        - "type" : INTERNSHIP ou JOB (obligatoire).
        - "duration" : ordre de grandeur de la durée, il s'agit d'une valeur normalisée : 
            - DURATION_1 : si Moins de 6 mois,
            - DURATION_2 : si 6 mois à 1 an,
            - DURATION_3 : si 1 an à 5 ans,
            - DURATION_4 : si Plus de 5 ans,
            - Et laisse cette valeur nulle si tu n'en as pas la moindre idée.
      
      Retourne le tableau dans un attribut "result". Si aucune expérience valide n'est trouvée, retourne simplement une liste vide.
      
      *Exemple de sortie attendue:*
      
        { "result": [
          {
            "title": "Consultant en marketing",
            "organizationTitle": "ABC Consulting",
            "type": "JOB",
            "duration": "DURATION_3"
          },
          {
            "title": "Stagiaire en ressources humaines",
            "organizationTitle": "",
            "type": "INTERNSHIP",
            "duration": "DURATION_1"
          }
        ]
      }
      
       **Souviens-toi : l'ensemble des titres doit être suffisamment explicite pour qu'un humain comprenne de quel métier il s'agit.**
parameterizedLastPromptMessage: "%s"
