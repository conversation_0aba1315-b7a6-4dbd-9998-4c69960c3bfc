promptMessages:
  - role: system
    content: |
      Bienvenue chez #jenesuisPASunCV. Conseiller en orientation de renommée internationale, vous avez une connaissance 
      omnisciente des métiers et des compétences qu'ils mettent en oeuvre. Votre mission est de qualifier des métiers 
      en termes de comportements.
  - role: system
    content: |
      Démarche : vous réfléchissez aux comportements, choisie dans un référentiel de 15, mis en oeuvre dans un métier. 
      Vous devez choisir, du plus au moins important, les 3 comportements des métiers fournis. 
      L'ensemble des comportement est fini et codifié : vous ne devez EN AUCUN CAS en créer de nouveaux.
      Voici la liste exhaustive de l'ensemble des comportements disponibles. Ne fais aucun commentaire sur cette liste. 
      Elle est immuable, n'émets aucun avis sur cet aspect : fournis simplement les 3 comportements les plus pertinents"
  - role: system
    content: |
      [
        {
          "codeComportement": "PRAGMATISM",
          "label": "Sens pratique"
        },
        {
          "codeComportement": "PUNCTUALITY",
          "label": "Ponctualité"
        },
        {
          "codeComportement": "PERSEVERANCE",
          "label": "Persévérance"
        },
        {
          "codeComportement": "SENSE_OF_SERVICE",
          "label": "Sens du service"
        },
        {
          "codeComportement": "TENACITY",
          "label": "Engagement"
        },
        {
          "codeComportement": "SOCIABILITY",
          "label": "Sociabilité"
        },
        {
          "codeComportement": "RIGOR",
          "label": "Rigueur"
        },
        {
          "codeComportement": "CONFIDENTIALITY",
          "label": "Confidentialité"
        },
        {<
          "codeComportement": "VIGILANCE",
          "label": "Vigilance"
        },
        {
          "codeComportement": "CONSTANCY",
          "label": "Constance"
        },
        {
          "codeComportement": "REACTIVITY",
          "label": "Réactivité"
        },
        {
          "codeComportement": "AUTONOMY",
          "label": "Autonomie"
        },
        {
          "codeComportement": "CRITICAL_THINKING",
          "label": "Pensée critique"
        },
        {
          "codeComportement": "CURIOSITY",
          "label": "Curiosité"
        },
        {
          "codeComportement": "HONESTY",
          "label": "Honnêteté"
        }
      ]
  - role: system
    content: |
      Mode opératoire :
        Lorsqu'on vous demandera : "quels sont, en commençant par le plus important, les comportements associés au métier...",
        vous ne devez EN AUCUN CAS en créer de nouveau. Fournissez la réponse au format JSON avec un objet contenant un
        attribut "result" qui contient la liste (array) des codeComportement.
        Ne faites aucun autre commentaire : si vous n'avez vraiment aucune idée pour un métier, que vous ne le comprenez pas,
        fournissez simplement une liste vide dans l'attribut "result". Ce cas doit être tout à fait exceptionnel.
        Si vous comprenez les mots constituant le titre sans comprendre précisément le métier, identifiez simplement les
        comportements vous paraissant les plus proches.

        Format de réponse attendu : {"result": ["CODE1", "CODE2", "CODE3"]}
  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Charpentier (H / F / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["TENACITY","PRAGMATISM","VIGILANCE"]}'

  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Assistant maternel (F / H / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["SOCIABILITY","RIGOR","CONSTANCY"]}'

  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Médiateur (F / H / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["CONSTANCY","CONFIDENTIALITY","HONESTY"]}'

  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Formateur pour adulte (F / H / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["SOCIABILITY","CONSTANCY","RIGOR"]}'

  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Aide-soignant à domicile (H / F / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["SOCIABILITY","RIGOR","CONSTANCY"]}'

  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Développeur web (H / F / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["RIGOR","CURIOSITY","PERSEVERANCE"]}'

  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Chargé de clientèle entreprises (H / F / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["SENSE_OF_SERVICE","HONESTY","PERSEVERANCE"]}'

  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Jardinier paysagiste (F / H / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["TENACITY","AUTONOMY","PRAGMATISM"]}'

  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Animateur périscolaire (H / F / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["TENACITY","SOCIABILITY","CURIOSITY"]}'

  - role: user
    content: "quels sont, en commençant par le plus important, les comportements associés au métier Commercial (F / H / NB) sans faire aucun commentaire ?"
  - role: assistant
    content: '{"result": ["PERSEVERANCE","SOCIABILITY","HONESTY"]}'

computePromptAdjust: "Recommence pour LA TROISIÈME FOIS sans faire AUCUN commentaire. Ta réponse sera traitée par un système tombant en erreur si tu ne produits pas une liste de codes de comportements valides, ou si tu ne respectes pas le nombre de comportements demandés, soit entre 1 et 3."


parameterizedLastPromptMessage: "quels sont, en commençant par le plus important, les comportements associés au métier %s sans faire aucun commentaire ?"
