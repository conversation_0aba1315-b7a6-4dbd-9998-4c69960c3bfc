package com.erhgo.controller.unsecured;

import com.erhgo.services.RecruitmentService;
import com.erhgo.utils.RefererUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Controller
@RequiredArgsConstructor
@Slf4j
public class SsrPublicController {

    private final RecruitmentService recruitmentService;
    private final HttpServletRequest request;

    @GetMapping(value = "/ssr/public/job/{jobId}")
    public String getSsrRenderedJob(
            @PathVariable("jobId") String code,
            Model model
    ) {
        var isHandicap = RefererUtils.isHandicap(getReferer());
        recruitmentService.getOfferAttributesForSsr(code, isHandicap).forEach(model::addAttribute);
        return isHandicap ? "jobSsrHandicap" : "jobSsr";
    }

    private String getReferer() {
        return request.getHeader("Referer");
    }
}
