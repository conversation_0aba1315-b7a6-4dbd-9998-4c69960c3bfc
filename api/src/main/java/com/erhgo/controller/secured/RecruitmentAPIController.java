package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.openapi.controller.RecruitmentApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.RecruitmentService;
import com.erhgo.services.mailing.RecruitmentNotificationGenerator;
import com.erhgo.services.recruitmentimporter.OfferCsvImporterService;
import com.google.common.collect.ImmutableSet;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class RecruitmentAPIController implements RecruitmentApi {

    private final RecruitmentService service;
    private final RecruitmentNotificationGenerator sendCandidatureProposalService;
    private final OfferCsvImporterService csvImporterService;

    @Override
    public ResponseEntity<RecruitmentDTO> createRecruitment(SaveRecruitmentCommandDTO createRecruitmentCommand) {
        var recruitmentDTO = service.create(createRecruitmentCommand);
        return buildResponse(recruitmentDTO);
    }

    @Override
    public ResponseEntity<Void> importCsvRecruitments(MultipartFile file) {
        csvImporterService.importRecruitments(file);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<RecruitmentDTO> updateRecruitment(SaveRecruitmentCommandDTO saveRecruitmentCommandDTO, Long recruitmentId) {
        var recruitmentDTO = service.update(recruitmentId, saveRecruitmentCommandDTO);
        return buildResponse(recruitmentDTO);
    }

    private ResponseEntity<RecruitmentDTO> buildResponse(RecruitmentDTO recruitmentDTO) {
        try {
            return ResponseEntity.created(new URI(ApiConstants.API_ODAS_RECRUITMENT + ApiConstants.SEPARATOR + recruitmentDTO.getId()))
                    .body(recruitmentDTO);
        } catch (URISyntaxException e) {
            throw new IllegalStateException(e);
        }
    }

    @Override
    public ResponseEntity<RecruitmentForMatchingDTO> getRecruitmentForMatching(Long recruitmentId) {
        return ResponseEntity.ok(service.getRecruitmentForMatching(recruitmentId));
    }

    @Override
    public ResponseEntity<CandidatureSummaryDTO> getMatchingCandidature(Long candidatureId) {
        return ResponseEntity.ok(service.getMatchingCandidature(candidatureId));
    }


    @Override
    public ResponseEntity<ContactForCandidatureDTO> meetCandidature(Long candidatureId) {
        return ResponseEntity.ok(service.meetCandidature(candidatureId));
    }

    @Override
    public ResponseEntity<Void> refreshMatching(Long recruitmentId) {
        service.refreshMatching(recruitmentId);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<CandidatureContactInfoPageDTO> getCandidaturesForConsultant(Integer size,
                                                                                      Integer page,
                                                                                      Long recruitmentId,
                                                                                      Boolean isMatch) {
        return ResponseEntity.ok(service.getCandidatesForConsultant(size, page, recruitmentId, isMatch));
    }

    @Override
    public ResponseEntity<Void> changeRecruitmentState(ChangeRecruitmentStateCommandDTO command, String recruitmentCode) {
        service.changeState(recruitmentCode, RecruitmentState.valueOf(command.getNextState().name()));

        return ResponseEntity.noContent().build();
    }


    @Override
    public ResponseEntity<RecruitmentPageDTO> listRecruitments(Integer page, Integer size, RecruitmentSortDTO by, String organizationCode, List<String> selectedProjects, Boolean withNewCandidaturesOnly, Boolean withOpenRecruitmentOnly, String organizationTypeFilter, Boolean internal, SortDirectionDTO direction, String query) {
        return ResponseEntity.ok(service.list(organizationCode, selectedProjects, Boolean.TRUE.equals(withNewCandidaturesOnly), Boolean.TRUE.equals(withOpenRecruitmentOnly), Boolean.TRUE.equals(internal), query, page, size, by, direction, Optional.ofNullable(organizationTypeFilter).map(OrganizationTypeDTO::valueOf).orElse(null)));
    }


    @Override
    public ResponseEntity<RecruitmentPageForCandidateDTO> listRecruitmentsForCandidate(UUID jobId, String userId, Integer page, Integer size) {
        return ResponseEntity.ok(service.listRecruitmentsForCandidate(jobId, userId, page, size));
    }

    @Override
    public ResponseEntity<RecruitmentDTO> getRecruitmentForUpdate(String recruitmentCode) {
        return ResponseEntity.ok(service.getRecruitmentForUpdate(recruitmentCode));
    }

    @Override
    public ResponseEntity<Void> sendCandidatureProposal(List<String> usersId, Long recruitmentId) {
        sendCandidatureProposalService.generateNotifications(ImmutableSet.copyOf(usersId), recruitmentId);

        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<RecruitmentDTO>> listRecruitmentsForJob(UUID jobId) {
        return ResponseEntity.ok(service.listRecruitmentsForJobId(jobId));
    }

    @Override
    public ResponseEntity<List<UserRecruitmentReportItemDTO>> listUserRecruitmentReports(String userId) {
        return ResponseEntity.ok(service.createAndListUserRecruitmentReports(userId));
    }

}
