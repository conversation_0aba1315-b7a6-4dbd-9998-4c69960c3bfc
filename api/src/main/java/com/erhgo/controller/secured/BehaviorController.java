package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.BehaviorApi;
import com.erhgo.openapi.dto.BehaviorPageDTO;
import com.erhgo.openapi.dto.SortDirectionDTO;
import com.erhgo.services.BehaviorService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class BehaviorController implements BehaviorApi {
    private final BehaviorService behaviorService;
    @Override
    public ResponseEntity<BehaviorPageDTO> behaviorPage(Integer size, Integer page, String filter, String by, SortDirectionDTO direction) {
        return ResponseEntity.ok(behaviorService.searchBehavior(page, size, by, direction, filter));
    }
}
