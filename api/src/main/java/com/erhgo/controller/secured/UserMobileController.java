package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.UserMobileApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.notifications.NotificationService;
import com.erhgo.services.userprofile.UserMobileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@Slf4j
@RequiredArgsConstructor
public class UserMobileController implements UserMobileApi {
    private final UserMobileService userMobileService;
    private final NotificationService notificationService;


    @Override
    public ResponseEntity<Void> saveUserMobileToken(SaveUserMobileTokenCommandDTO command) {
        userMobileService.setUserMobileToken(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> sendMobileNotificationToUsers(UsersMobileNotificationDTO command) {
        notificationService.sendNotificationToUsers(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<NotifiableUsersCountDTO> countNotifiableUsers(String query, String filters, String aroundLatLng, Integer aroundRadius) {
        // No way to make openAPI generator create object based on query param :/
        var body = notificationService.countNotifiableUsers(new AlgoliaQueryDTO().query(query).filters(filters).aroundRadius(aroundRadius).aroundLatLng(aroundLatLng));
        return ResponseEntity.ok(body);
    }

    @Override
    public ResponseEntity<Void> sendNotificationsToUsersSelection(SendNotificationsToUsersSelectionCommandDTO sendNotificationsToUsersSelectionCommandDTO) {
        notificationService.sendNotificationsToUsersSelection(sendNotificationsToUsersSelectionCommandDTO);
        return ResponseEntity.noContent().build();
    }
}
