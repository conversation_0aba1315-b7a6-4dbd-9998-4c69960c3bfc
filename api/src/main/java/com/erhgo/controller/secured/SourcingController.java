package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.openapi.controller.SourcingApi;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.ErhgoOccupationService;
import com.erhgo.services.JobService;
import com.erhgo.services.RecruitmentService;
import com.erhgo.services.candidature.RecruitmentCandidatureService;
import com.erhgo.services.candidature.SpontaneousCandidatureService;
import com.erhgo.services.dto.criteria.SourcingCandidatesCriteria;
import com.erhgo.services.sourcing.SourcingJobRecruitmentService;
import com.erhgo.services.sourcing.SourcingMailingService;
import com.erhgo.services.sourcing.SourcingService;
import com.erhgo.services.trimoji.TrimojiService;
import com.erhgo.services.userprofile.UserProfileCompetencesExportService;
import com.erhgo.utils.DateTimeUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping(ApiConstants.API_ODAS)
public class SourcingController implements SourcingApi {

    private final SourcingService sourcingService;
    private final SourcingMailingService sourcingMailingService;
    private final RecruitmentService recruitmentService;
    private final SourcingJobRecruitmentService sourcingJobService;
    private final JobService jobService;
    private final TrimojiService trimojiService;
    private final RecruitmentCandidatureService recruitmentCandidatureService;
    private final SpontaneousCandidatureService spontaneousCandidatureService;
    private final ErhgoOccupationService erhgoOccupationService;
    private final ObjectProvider<SourcingCandidatesCriteria> sourcingCandidatesCriteriaProvider;
    private final HttpServletResponse response;
    private final UserProfileCompetencesExportService userProfileCompetenceExportService;

    @Override
    public ResponseEntity<CandidatesCountDTO> countCandidates(
            UUID occupationId,
            Float longitude,
            Float latitude,
            String typeContractCategoryAsString,
            String workingTimeTypeAsString,
            List<String> criteria,
            Integer salaryMin,
            Integer salaryMax,
            List<String> classifications
    ) {
        var typeContractCategory = Optional.ofNullable(typeContractCategoryAsString)
                .filter(StringUtils::isNotBlank)
                .map(TypeContractCategory::valueOf)
                .orElse(null);
        var workingTimeType = Optional.ofNullable(workingTimeTypeAsString)
                .filter(StringUtils::isNotBlank)
                .map(TypeWorkingTime::valueOf)
                .orElse(null);
        var countCriteria = sourcingCandidatesCriteriaProvider.getObject()
                .criteria(criteria != null ? criteria : new ArrayList<>())
                .latitude(latitude)
                .longitude(longitude)
                .occupationId(occupationId)
                .typeContractCategory(typeContractCategory)
                .workingTimeType(workingTimeType)
                .salaryMin(salaryMin)
                .salaryMax(salaryMax)
                .classifications(classifications == null ? new ArrayList<>() : classifications.stream().filter(StringUtils::isNotBlank).collect(Collectors.toCollection(ArrayList::new)));

        return ResponseEntity.ok(sourcingService.countCandidates(countCriteria));
    }

    @Override
    public ResponseEntity<RepublicationCandidatesCountDTO> countMatchingUsers(Long recruitmentId) {
        return ResponseEntity.ok(sourcingService.countMatchingUsers(recruitmentId));
    }


    @Override
    public ResponseEntity<InitializedAccountDTO> initializeSourcingAccount() {
        return ResponseEntity.ok(sourcingService.initializeAccount());
    }

    @Override
    public ResponseEntity<Void> updateSourcingJobContract(UpdateSourcingJobContractCommandDTO updateSourcingJobContractCommandDTO, UUID jobId) {
        sourcingJobService.updateContractOfJob(jobId, updateSourcingJobContractCommandDTO);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateSourcingRecruitmentQuestion(UpdateSourcingRecruitmentQuestionCommandDTO command, Long recruitmentId) {
        sourcingJobService.updateQuestionOnRecruitment(recruitmentId, command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateCriteriaForSourcingJob(List<String> criteriaValueCodes, UUID jobId, SourcingCriteriaStepDTO restrictToSourcingStep) {
        jobService.updateCriteriaForJob(jobId, criteriaValueCodes, restrictToSourcingStep);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateErhgoClassificationsForSourcingRecruitment(SetRecruitmentErhgoClassificationsCommandDTO command) {
        recruitmentService.updateErhgoQualificationForRecruitment(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<SourcingCandidaturePageDTO> getSourcingCandidaturesPage(Integer size, Integer page, SourcingCandidatureSortDTO sourcingCandidatureSort, SortDirectionDTO direction, List<SourcingCandidatureStateDTO> candidatureState, String searchQuery, Long recruitmentId) {
        if (recruitmentId != null) {
            return ResponseEntity.ok(recruitmentCandidatureService.getSourcingCandidaturesPage(size, page, recruitmentId, candidatureState, sourcingCandidatureSort, direction, searchQuery));
        } else {
            return ResponseEntity.ok(spontaneousCandidatureService.getSourcingCandidates(size, page, sourcingCandidatureSort, direction, candidatureState, searchQuery));
        }
    }

    @Override
    public ResponseEntity<List<SourcingInvitationCodeDTO>> listInvitationCodes() {
        return ResponseEntity.ok(sourcingService.listSourcingInvitationCode());
    }

    @Override
    public ResponseEntity<Void> saveInvitationCode(SaveSourcingInvitationCodeDTO command) {
        sourcingService.saveInvitationCode(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<SourcingJobAndRecruitmentDTO> getSourcingJobAndRecruitment(Long recruitmentId) {
        return ResponseEntity.ok(sourcingJobService.getSourcingJobAndRecruitment(recruitmentId));
    }

    @Override
    public ResponseEntity<SourcingJobAndRecruitmentDTO> duplicateSourcingJobAndRecruitment(DuplicateSourcingJobAndRecruitmentCommandDTO duplicateSourcingJobAndRecruitmentCommandDTO) {
        return ResponseEntity.ok(sourcingJobService.duplicateSourcingJobAndRecruitment(duplicateSourcingJobAndRecruitmentCommandDTO.getRecruitmentId()));
    }

    @Override
    public ResponseEntity<Void> updateUsersToNotify(UpdateUsersToNotifyCommandDTO updateUsersToNotifyCommandDTO) {
        sourcingJobService.updateUsersToNotify(updateUsersToNotifyCommandDTO.getUsersIdsToNotify(), updateUsersToNotifyCommandDTO.getRecruitmentId());
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> updateSourcingStep(UpdateSourcingStepCommandDTO command, Long recruitmentId) {
        sourcingJobService.updateStep(recruitmentId, command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<SourcingContactInformationDTO> updateSourcingCandidatureState(UpdateSourcingCandidatureStateCommandDTO command, Long candidatureId) {
        return ResponseEntity.ok(sourcingService.updateCandidatureState(candidatureId, command));
    }

    @Override
    public ResponseEntity<SourcingCandidatureDetailDTO> getSourcingCandidatureDetail(Long candidatureId) {
        return ResponseEntity.ok(sourcingService.getCandidatureDetail(candidatureId));
    }

    @Override
    public ResponseEntity<SourcingOrganizationDTO> getSourcingOrganization() {
        return ResponseEntity.ok(sourcingService.getAuthenticatedSourcingUserOrganization());
    }

    @Override
    public ResponseEntity<UpdateOrganizationResultDTO> updateSourcingOrganization(UpdateSourcingOrganizationCommandDTO command) {
        return ResponseEntity.ok(sourcingService.updateAuthenticatedSourcingUserOrganization(command));
    }

    @Override
    public ResponseEntity<Void> updateSourcingUser(UpdateSourcingUserCommandDTO command) {
        sourcingService.updateAuthenticatedSourcingUser(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> inviteToRecruitment(InviteToRecruitmentCommandDTO command) {
        sourcingService.generateRecruitmentNotifications(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> sendContactForm(SendContactFormCommandDTO command) {
        sourcingMailingService.sendContactForm(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<SourcingRecruitmentItemDTO>> getSourcingRecruitments() {
        return ResponseEntity.ok(sourcingService.getSourcingRecruitmentsForAuthenticatedUsers());
    }

    @Override
    public ResponseEntity<SourcingSubscriptionDTO> getSourcingSubscription() {
        return ResponseEntity.ok(sourcingService.getSourcingSubscription());
    }

    @Override
    public ResponseEntity<Void> updateSubscription(UpdateSourcingSubscriptionCommandDTO command) {
        sourcingService.updateSubscription(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<SourcingUserDTO>> getSourcingUsers(String organizationCode) {
        return ResponseEntity.ok(sourcingService.getSourcingUsersWithOrganizationCode(organizationCode));
    }

    @Override
    public ResponseEntity<SourcingJobAndRecruitmentDTO> createOrUpdateSourcingJobAndRecruitment(CreateSourcingJobAndRecruitmentCommandDTO command) {
        return ResponseEntity.ok(sourcingJobService.createOrUpdateSourcingJobAndRecruitment(command));
    }

    @Override
    public ResponseEntity<List<SourcingJobAndRecruitmentDTO>> getSimilarRecruitments(UUID erhgoOccupationId, Float longitude, Float latitude) {
        return ResponseEntity.ok(sourcingService.getSimilarRecruitments(erhgoOccupationId, longitude, latitude));
    }

    @Override
    public ResponseEntity<Void> inviteAndCreateNewUser(InviteAndCreateNewUserCommandDTO command) {
        sourcingService.inviteAndCreateNewUser(command);
        return ResponseEntity.noContent().build();

    }

    @Override
    public ResponseEntity<List<Long>> changeSourcingRecruitmentState(ChangeSourcingRecruitmentStateCommandDTO command, Long recruitmentId) {
        return ResponseEntity.ok(sourcingJobService.changeRecruitmentState(recruitmentId, command));
    }

    @Override
    public ResponseEntity<SourcingSimulatedResultDTO> simulateSourcingFilters(
            Boolean activeSearch,
            Boolean withDetails,
            Boolean forcedTechnical,
            Boolean showTopTen,
            UUID occupationId,
            List<String> classifications,
            List<String> criteriaCodes,
            String typeContractCategoryAsString,
            String workingTimeTypeAsString,
            Integer masteryLevel,
            Float longitude,
            Float latitude,
            Integer radius,
            Integer salaryMin,
            Integer salaryMax,
            Double capacityTolerance,
            Long lastConnectionTimestamp
    ) {
        var typeContractCategory = Optional.ofNullable(typeContractCategoryAsString)
                .filter(StringUtils::isNotBlank)
                .map(TypeContractCategory::valueOf)
                .orElse(null);
        var workingTimeType = Optional.ofNullable(workingTimeTypeAsString)
                .filter(StringUtils::isNotBlank)
                .map(TypeWorkingTime::valueOf)
                .orElse(null);
        var criteria = sourcingCandidatesCriteriaProvider.getObject()
                .topTen(showTopTen)
                .occupationId(occupationId)
                .classifications(classifications != null ? classifications : new ArrayList<>())
                .criteria(criteriaCodes != null ? criteriaCodes : new ArrayList<>())
                .typeContractCategory(typeContractCategory)
                .workingTimeType(workingTimeType)
                .latitude(latitude)
                .longitude(longitude)
                .masteryLevelAround(masteryLevel)
                .salaryMin(salaryMin)
                .salaryMax(salaryMax)
                .lastConnectionBefore(lastConnectionTimestamp != null ? LocalDateTime.ofInstant(Instant.ofEpochSecond(lastConnectionTimestamp), ZoneId.of(DateTimeUtils.ZONE)) : null)
                .customCapacityTolerance(capacityTolerance)
                .withDetails(withDetails)
                .activeSearch(activeSearch);

        return ResponseEntity.ok(sourcingService.simulateSourcingFilters(criteria, forcedTechnical));
    }

    @Override
    public ResponseEntity<SourcingUserDetailsDTO> getSourcingUser() {
        return ResponseEntity.ok(sourcingService.getSourcingUser());
    }

    @Override
    public ResponseEntity<List<SourcingUserDTO>> getUsersToNotifyOnSpontaneousCandidature() {
        return ResponseEntity.ok(sourcingJobService.getUsersToNotifyOnSpontaneousCandidature());
    }

    @Override
    public ResponseEntity<Resource> getCandidatureSoftSkillsPdfResult(Long candidatureId) {
        try (var outputStream = response.getOutputStream()) {
            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=softSkills.pdf");
            outputStream.write(trimojiService.getPdfForCandidature(candidatureId));
        } catch (IOException e) {
            log.error("failed to export pdf", e);
            throw new GenericTechnicalException("Unable to export profile", e);
        }
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<Void> updateManager(UpdateManagerCommandDTO command) {
        sourcingJobService.updateManagerOfRecruitment(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Long> createOrUpdateRecruitmentByFormSubmission(CreateOrUpdateFullRecruitmentCommandDTO command) {
        return ResponseEntity.ok(sourcingJobService.createOrUpdateRecruitmentWithFormCommand(command));
    }

    @Override
    public ResponseEntity<Resource> getCandidatureUserProfileCompetences(Long candidatureId, Boolean forceAnonymous) {
        try (var outputStream = response.getOutputStream()) {
            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=competences.pdf");
            userProfileCompetenceExportService.getUserProfileForCandidatureFromSourcing(candidatureId, outputStream, forceAnonymous);
        } catch (IOException e) {
            log.error("failed to export pdf", e);
            throw new GenericTechnicalException("Unable to export profile", e);
        }
        return ResponseEntity.ok().build();
    }

}
