package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.ContextApi;
import com.erhgo.openapi.dto.ContextDTO;
import com.erhgo.openapi.dto.ContextsPageDTO;
import com.erhgo.openapi.dto.SaveContextCommandDTO;
import com.erhgo.openapi.dto.SortDirectionDTO;
import com.erhgo.services.ContextService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class ContextController implements ContextApi {

    @Autowired
    private ContextService contextService;

    @Override
    public ResponseEntity<ContextDTO> createNewContext(SaveContextCommandDTO saveContextCommand) {
        return ResponseEntity.ok(this.contextService.createNewContext(saveContextCommand));
    }

    @Override
    public ResponseEntity<ContextDTO> getContext(UUID contextId) {
        return ResponseEntity.ok(this.contextService.getContext(contextId));
    }

    @Override
    public ResponseEntity<ContextDTO> updateContext(SaveContextCommandDTO saveContextCommand, UUID contextId) {
        return ResponseEntity.ok(this.contextService.updateContext(contextId, saveContextCommand));
    }

    @Override
    public ResponseEntity<ContextsPageDTO> listContexts(Integer size,
                                                        Integer page,
                                                        String by,
                                                        SortDirectionDTO direction,
                                                        String filter,
                                                        String userFilter) {
        return ResponseEntity.ok(this.contextService.listContexts(page, size, by, direction, filter, userFilter));
    }

    @Override
    public ResponseEntity<List<ContextDTO>> searchContexts(String query, String categoryCode) {
        return ResponseEntity.ok(contextService.searchContexts(query, categoryCode));
    }
}
