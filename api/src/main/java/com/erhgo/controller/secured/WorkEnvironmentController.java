package com.erhgo.controller.secured;

import com.erhgo.config.ApiConstants;
import com.erhgo.openapi.controller.WorkEnvironmentApi;
import com.erhgo.openapi.dto.WorkEnvironmentDTO;
import com.erhgo.services.WorkEnvironmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(ApiConstants.API_ODAS)
@RequiredArgsConstructor
public class WorkEnvironmentController implements WorkEnvironmentApi {
    private final WorkEnvironmentService workEnvironmentService;

    @Override
    public ResponseEntity<List<WorkEnvironmentDTO>> listWorkEnvironments() {
        return ResponseEntity.ok(workEnvironmentService.listWorkEnvironments());
    }
}
