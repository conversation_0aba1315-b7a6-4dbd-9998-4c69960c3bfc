package com.erhgo.migrations;

import liquibase.change.custom.CustomTaskChange;
import liquibase.database.Database;
import liquibase.exception.ValidationErrors;
import liquibase.resource.ResourceAccessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.util.UUID;

@Slf4j
public abstract class AbstractMigrationService implements CustomTaskChange {
    public static ApplicationContext applicationContext;

    protected abstract void executeUpdate();

    @Override
    public String getConfirmationMessage() {
        return "Registering custom migration: " + getClass();
    }

    @Override
    public void setUp() {}

    @Override
    public void setFileOpener(ResourceAccessor resourceAccessor) {}

    @Override
    public ValidationErrors validate(Database database) {
        return null;
    }

    @Override
    public void execute(Database database) {
        if (AbstractMigrationService.applicationContext == null) {
            throw new IllegalStateException("applicationContext should be statically initialized before migrations");
        }
        var factory = AbstractMigrationService.applicationContext.getAutowireCapableBeanFactory();
        factory.autowireBean(this);
        // Random bean name: migration may be referenced multiple times, and will not be injected anywhere
        factory.initializeBean(this, UUID.randomUUID().toString());

        executeUpdate();
        log.info("Migration finished for {}", getClass());
    }
}
