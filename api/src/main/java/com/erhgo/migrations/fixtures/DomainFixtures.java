package com.erhgo.migrations.fixtures;

import com.erhgo.config.KeycloakRealmsConfig;
import com.erhgo.domain.KeycloakUserSummary;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationState;
import com.erhgo.domain.classifications.erhgooccupation.MandatoryState;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.classifications.isco.IscoOccupation;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.domain.enums.Frequency;
import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.JobType;
import com.erhgo.domain.landingpage.LandingPage;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.*;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.capacityrelatedquestion.AnswerForCapacityRelatedQuestion;
import com.erhgo.domain.userprofile.experience.JobContextMet;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.domain.userprofile.notification.AbstractNotification;
import com.erhgo.domain.userprofile.notification.DefaultNotification;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.repositories.classifications.EscoOccupationRepository;
import com.erhgo.repositories.classifications.IscoOccupationRepository;
import com.erhgo.repositories.classifications.RomeOccupationRepository;
import com.erhgo.services.*;
import com.erhgo.services.candidature.RecruitmentCandidatureService;
import com.erhgo.services.search.UserIndexer;
import com.erhgo.services.userprofile.UserProfileService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URI;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

@Profile({"default", "e2e"})
@Service
@Slf4j
@RequiredArgsConstructor
public class DomainFixtures {

    private static final String SOGILIS_CODE = "E-0001";
    public static final String TITLE_OCCUPATION1 = "Opérateur de centre de données / Opératrice de centre de données";
    public static final String CP_LYON = "69123";
    public static final String ORGANIZATION_NAME = "Micheline";
    private final KeycloakFixtures keycloakFixtures;
    private final JobActivityLabelRepository jobActivityLabelRepository;
    private final BehaviorRepository behaviorRepository;
    private final JobRepository jobRepository;
    private final EscoOccupationRepository escoOccupationRepository;
    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final IscoOccupationRepository iscoOccupationRepository;
    private final RomeOccupationRepository romeOccupationRepository;
    private final ActivityRepository activityRepository;
    private final UserProfileRepository userProfileRepository;
    private final RecruitmentRepository recruitmentRepository;
    private final RecruitmentProfileRepository recruitmentProfileRepository;
    private final UserExperienceRepository userExperienceRepository;
    private final ContextRepository contextRepository;
    private final LandingPageRepository landingPageRepository;
    private final NotificationRepository notificationRepository;
    private final UserProfileService userProfileService;
    private final CapacityRelatedQuestionRepository capacityRelatedQuestionRepository;
    private final AnswerForCapacityRelatedQuestionRepository answerForCapacityRelatedQuestionRepository;
    private final UserIndexer userIndexer;
    private final QuestionForContextsService questionForContextsService;
    private final ActivityService activityService;
    private final AbstractOrganizationRepository organizationRepository;
    private final OrganizationService organizationService;
    private final CategoryService categoryService;
    private final ContextService contextService;
    private final RecruitmentService recruitmentService;
    private final RecruitmentProfileService recruitmentProfileService;
    private final CapacityService capacityService;
    private final MissionService missionService;
    private final CapacityRelatedQuestionService capacityRelatedQuestionService;
    private final AnswerForCapacityRelatedQuestionService answerForCapacityRelatedQuestionService;
    private final Keycloak keycloak;
    private final KeycloakRealmsConfig config;
    private final RecruitmentCandidatureService candidatureService;
    private final EntityManager entityManager;
    private UUID extraProQuestionId;
    private EscoOccupation escoOccupation;
    private ErhgoOccupation erhgoOccupation;
    private JobActivityLabel eatChipsActivity;
    private JobActivityLabel docActivity;
    private ContextDTO context1, context2, context3;
    private QuestionForContexts questionForContext1;


    protected void createOrgaAndUser() {
        if (organizationRepository.findOneByCode(SOGILIS_CODE) != null) {
            log.info("Recruiter creation is NOT required");
            return;
        }
        log.info("Create SOGILIS recruiter");

        final var sogilis = createEnterprise("Sogilis Lyon");
        var hlm = createProject("HLM");
        final var mmie = createTerritorialOrganization("MMIE", hlm.getId());

        log.info("Create SOGILIS job");
        var recruitment = createJobAndRecruitments(sogilis);
        createCandidateMatchingWithInOwnActivity(recruitment.getId());
        log.info("Create Ready user");
        createReadyUserWithOwnActivity();

        log.info("Create MMIE job");
        createJob(mmie);

        log.info("Create HLM job");
        createJobAndRecruitments(hlm);
    }

    private RecruitmentDTO createJobAndRecruitments(Recruiter recruiter) {
        final var developer = createJob(recruiter);

        final var mission = createMission(eatChipsActivity, developer, context1);
        final var missionWithMultipleContexts = createMission(eatChipsActivity, developer, context2, context3);

        developer.setState(JobEvaluationState.PUBLISHED);
        log.info("Create recruitment profile with prerequisites");
        final var recruitmentProfile = createRecruitmentProfile(developer, mission, missionWithMultipleContexts);

        log.info("Create recruitment");
        var recruitment = createRecruitment(recruitmentProfile, "");

        log.info("Create recruitment profile without prerequisites");
        final var recruitmentProfileNoPrerequisites = recruitmentProfileRepository.save(RecruitmentProfile.buildWithAllOptional(developer, null));

        createRecruitment(recruitmentProfileNoPrerequisites, " (sans prérequis)");
        return recruitment;

    }

    private void createCandidateWithExperienceOnOccupation(List<ErhgoOccupation> occupations) {
        var randomUUID = UUID.randomUUID();

        var userProfile = new UserProfile()
                .uuid(randomUUID)
                .userId(randomUUID.toString())
                .capacityOccurrences(Collections.emptySet())
                .generalInformation(GeneralInformation.builder().phoneNumber("0612345678").userId(randomUUID.toString()).build());
        userProfile.generalInformation().setUserProfile(userProfile);

        userProfileRepository.save(userProfile);

        occupations.forEach(o -> {
            var userExperience = UserExperience.builder()
                    .erhgoOccupation(o)
                    .type(ExperienceType.JOB)
                    .jobTitle("Cuistot")
                    .userProfile(userProfile)
                    .uuid(UUID.randomUUID())
                    .organizationName(ORGANIZATION_NAME)
                    .build();

            userExperienceRepository.save(userExperience);
        });
    }

    /**
     * Candidate: Lyon(69123), postcode(69001), Activité: EatChips, Expérience: Cuisto chez Micheline(Trois contextes), Une candidature: Rockstar developer Sogilis
     * ReadyCandidate: Marseille, Activité: Bronzer, Expérience: Pétanque chez Gérard, Répondu à toutes les questions du BIBA, Répondu à toutes les questions AEP, Accepté deux critères, refusé un seul
     */

    private void createCandidateMatchingWithInOwnActivity(Long recruitmentId) {
        String userId = keycloakFixtures.getCandidateUserId().get(0);

        var userProfile = new UserProfile()
                .uuid(UUID.randomUUID())
                .userId(userId)
                .capacityOccurrences(Collections.emptySet())
                .generalInformation(
                        GeneralInformation.builder()
                                .userId(userId)
                                .location(
                                        Location.builder().city("Lyon 9")
                                                .citycode(CP_LYON)
                                                .latitude(45.774f)
                                                .longitude(4.85f)
                                                .postcode("69009")
                                                .build())
                                .build());
        userProfile.generalInformation().setUserProfile(userProfile);

        userProfile = userProfileRepository.save(userProfile);

        createExperiences(userProfile, "Cuistot", ORGANIZATION_NAME);
        var recruitment = recruitmentRepository.findById(recruitmentId).orElseThrow();
        createCandidature(userProfile, recruitment);
        var notifications = List.of(
                createNotification(userProfile, recruitment),
                createNotification(userProfile, recruitment),
                createNotification(userProfile, recruitment),
                createDefaultNotification(userProfile, "Un nouvel événement !", "Ça va être bien, venez", "http://jenesuispasuncv.fr"),
                createDefaultNotification(userProfile, "Un autre événement !", "Ça va être vraiment bien !", null));
        notificationRepository.flush();
        IntStream.range(0, notifications.size()).forEach(i ->
                entityManager
                        .createNativeQuery("UPDATE Notification SET createdDate = :date WHERE id = :id")
                        .setParameter("date", LocalDateTime.of(2000 + i, 5, 5, 5, 5, 5))
                        .setParameter("id", notifications.get(i).getId())
                        .executeUpdate()
        );

    }

    private AbstractNotification createDefaultNotification(UserProfile userProfile, String title, String content, String url) {
        return notificationRepository.save(DefaultNotification.builder().content(content).subject(title).uri(url == null ? null : URI.create(url)).userProfile(userProfile).build());
    }

    private AbstractNotification createNotification(UserProfile userProfile, Recruitment recruitment) {
        return notificationRepository.save(RecruitmentNotification.builder().recruitment(recruitment).userProfile(userProfile).build());
    }

    private void createReadyUserWithOwnActivity() {
        String userId = keycloakFixtures.getCandidateUserId().get(1);

        var userProfile = new UserProfile()
                .uuid(UUID.randomUUID())
                .userId(userId)
                .capacityOccurrences(Collections.emptySet())
                .generalInformation(GeneralInformation.builder().userId(userId).location(Location.builder().city("Marseille").citycode("13055").postcode("13000").build()).build());
        userProfile.generalInformation().setUserProfile(userProfile);

        userProfile = userProfileRepository.save(userProfile);

        createExperiences(userProfile, "Pétanque", "Gérard");
        answerExtraProfessionalQuestion(userProfile);
    }

    private void answerExtraProfessionalQuestion(UserProfile userProfile) {
        var question = capacityRelatedQuestionRepository.findById(extraProQuestionId).orElseThrow(() -> new EntityNotFoundException(extraProQuestionId, CapacityRelatedQuestion.class));
        var secondResponse = question.getResponses().first();
        var selectedAnswer = AnswerForCapacityRelatedQuestion.builder().response(secondResponse).userProfile(userProfile).build();

        answerForCapacityRelatedQuestionRepository.save(selectedAnswer);
        userProfile.addCapacities(selectedAnswer.getRelatedCapacitiesRecursively());
    }

    private RecruitmentCandidature createCandidature(UserProfile userProfile, Recruitment recruitment) {
        var candidature = candidatureService.createOrRetrieve(recruitment.getCode(), userProfile.userId());
        candidatureService.changeStateTo(candidature.getId(), CandidatureState.VALIDATED);
        candidature.setSubmissionDate(OffsetDateTime.of(1981, 8, 10, 13, 40, 25, 0, ZoneOffset.UTC));
        return candidature;
    }

    private void createExperiences(UserProfile userProfile, String jobTitle, String organizationName) {
        var userExperience = UserExperience.builder()
                .erhgoOccupation(erhgoOccupation)
                .type(ExperienceType.JOB)
                .jobTitle(jobTitle)
                .userProfile(userProfile)
                .uuid(UUID.randomUUID())
                .organizationName(organizationName)
                .build();

        userExperienceRepository.save(userExperience);
        userProfile.experiences(Sets.newHashSet(userExperience));

        userProfile.contextsMet(Stream.of(context1, context2, context3)
                .map(c -> contextRepository.findById(c.getId()).orElseThrow())
                .map(c -> JobContextMet.builder().userExperiences(Sets.newHashSet(userExperience)).context(c).userProfile(userProfile).frequency(Frequency.HIGH).build())
                .collect(Collectors.toSet())
        );
        userProfile.refreshMasteryLevelAndMarkAsIndexationRequired();
        userProfileRepository.save(userProfile);
        userIndexer.index(userProfile);
    }

    private RecruitmentDTO createRecruitment(RecruitmentProfile recruitmentProfile, String titleSuffix) {
        return recruitmentService.create(new SaveRecruitmentCommandDTO()
                .title("recrutement pour %s".formatted(recruitmentProfile.getJob().getTitle()))
                .description("Tu aimes coder ? Viens chez nous !")
                .recruitmentProfileUuid(recruitmentProfile.getUuid())
                .organizationDescription("Une petite boite qui monte !")
                .baseSalary(35000)
                .maxSalary(45000)
                .typeContract(TypeContractDTO.CDI)
                .state(RecruitmentStateDTO.PUBLISHED)
                .workingWeeklyTime(37)
                .location(new LocationDTO().postcode("69004")
                        .city("Lyon")
                        .departmentCode("69")
                        .latitude(45.773904f)
                        .longitude(4.8350112f)
                        .citycode(CP_LYON)
                        .regionName("Auvergne-Rhône-Alpes"))
                .city("Lyon")
                .externalUrl("https://sogilis.com/"));
    }

    private RecruitmentProfile createRecruitmentProfile(Job developer, MissionDTO... missions) {
        var junior = recruitmentProfileService.createOrUpdate(developer.getId(), new SaveRecruitmentProfileCommandDTO()
                .id(UUID.randomUUID())
                .title("Junior")
                .customQuestion("Savez-vous travailler ?")
        );

        recruitmentProfileService.setCustomContextLabelForProfile(developer.getId(), junior.getUuid(),
                new SetQuestionForContextCommandDTO().contextId(context1.getId()).questionId(questionForContext1.getUuid()));

        Stream.of(missions).forEach(mission -> recruitmentProfileService.endQualification(developer.getId(), junior.getUuid(), mission.getId()));

        return junior;
    }

    private MissionDTO createMission(JobActivityLabel jobActivityLabel, Job developer, ContextDTO... contexts) {
        return missionService.create(new CreateMissionCommandDTO()
                .title("Développer des logiciels de qualité")
                .activitiesIds(Lists.newArrayList(jobActivityLabel.getUuid()))
                .contextsForCategory(StreamSupport.stream(categoryService.findAll().spliterator(), false).map(category -> {
                    var contextsIdForCategory = Stream.of(contexts)
                            .filter(c -> c.getCategoryLevel().getCategory().getId().equals(category.getId()))
                            .map(ContextDTO::getId)
                            .toList();

                    var dto = new ContextsForCategoryDTO();
                    dto.setCategoryId(category.getId());
                    if (!contextsIdForCategory.isEmpty()) {
                        dto.setNoContextForCategory(false);
                        dto.setContextsIds(contextsIdForCategory);
                    } else {
                        dto.setNoContextForCategory(true);
                    }

                    return dto;
                }).toList())
                .jobId(developer.getId())
        );
    }

    private Job createJob(Recruiter recruiter) {
        final var developer = new Job();
        developer.setJobType(JobType.OBSERVED);
        developer.setId(UUID.randomUUID());
        developer.setRecruiter(recruiter);
        developer.setTitle("Développeur - façon " + recruiter.getTitle());
        developer.setErhgoOccupation(erhgoOccupation);
        developer.setService("SI");
        developer.setLocation(Location.builder()
                .regionName("Auvergne Rhône Alpes")
                .departmentCode("69")
                .city("Lyon")
                .postcode("69000")
                .citycode(CP_LYON)
                .latitude(45.76404f)
                .longitude(4.835659f)
                .build());
        developer.setObservators(Collections.singleton("Jean-Paul"));
        developer.setState(JobEvaluationState.INFOS_PROVIDED);
        developer.setCreatedBy(new KeycloakUserSummary("user_id"));

        return jobRepository.save(developer);
    }

    private JobActivityLabel createActivityLabel(String title, String... capacities) {
        var activity = activityService.saveActivity(ActivityTypeDTO.JOB,
                new SaveActivityCommandDTO()
                        .id(UUID.randomUUID())
                        .description("Description of " + title + ".\nMultiligne !")
                        .inducedCapacities(Lists.newArrayList(capacities))
                        .labels(Lists.newArrayList(
                                new ActivityLabelDTO()
                                        .id(UUID.randomUUID())
                                        .position(0)
                                        .title(title)
                        )));

        return jobActivityLabelRepository.findByActivityUuid(activity.getUuid()).iterator().next();
    }

    private ContextDTO createContext(String title, String description, String categoryCode) {
        return contextService.createNewContext(new SaveContextCommandDTO()
                .title(title)
                .description(description)
                .categoryLevelId(categoryService.findOneByCode(categoryCode).getLevels().last().getId())
        );
    }

    private Recruiter createEnterprise(String title) {
        final var organization = new SaveOrganizationCommandDTO()
                .address("289 rue Garibaldi, 69007 Lyon")
                .organizationType(OrganizationTypeDTO.ENTERPRISE)
                .description("Parce que " + title + " est le nouveau plus bel endroit du monde.")
                .title(title);

        return organizationService.save(organization);
    }

    private Recruiter createTerritorialOrganization(String title, Long projectId) {
        final var organization = new SaveOrganizationCommandDTO()
                .address("42, rue du Plat 69 100 VILLEURBANNE")
                .organizationType(OrganizationTypeDTO.TERRITORIAL)
                .description("Parce que " + title + " est le nouveau plus bel endroit du monde.")
                .projectIds(Collections.singletonList(projectId))
                .title(title);

        return organizationService.save(organization);
    }

    private Recruiter createProject(String title) {
        var project = new SaveOrganizationCommandDTO()
                .address("240 avenue Félix Faure, 69 003 LYON")
                .organizationType(OrganizationTypeDTO.PROJECT)
                .title(title);

        return organizationService.save(project);
    }

    protected void createISCOccupations() {
        log.info("Create ISCO from Fixtures");
        iscoOccupationRepository.save(IscoOccupation
                .builder()
                .iscoGroup(3511)
                .title("Techniciens des technologies de l'information et des communications, opérations")
                .build());
        iscoOccupationRepository.save(IscoOccupation
                .builder()
                .iscoGroup(3435)
                .title("Autres professions intermédiaires de la culture et de la création artistique")
                .build());
    }

    private void createROMEOccupation() {
        log.info("Create ROME from Fixtures");

        romeOccupationRepository.save(RomeOccupation.builder().code("D1506").title("Marchandisage").build());
        romeOccupationRepository.save(RomeOccupation.builder().code("L1302").title("Production et administration spectacle, cinéma et audiovisuel").build());
        romeOccupationRepository.save(RomeOccupation.builder().code("N4301").title("Conduite sur rails").build());
    }

    protected void createESCOOccupations() {
        log.info("Create ESCO from Fixtures");

        createESCOOccupation1();
        createESCOOccupation2();
    }

    protected void createESCOOccupation1() {
        var title = TITLE_OCCUPATION1;
        if (!escoOccupationRepository.search(title, Pageable.unpaged()).isEmpty()) {
            return;
        }

        escoOccupation = new EscoOccupation();
        escoOccupation.setTitle(title);
        escoOccupation.setUri("http://data.europa.eu/esco/occupation/9729c0f3-c9bc-482e-ac59-a82ec3b67ba3");
        escoOccupation.setIscoOccupation(iscoOccupationRepository.findById(3511).orElseThrow());
        escoOccupation.setDescriptionFR("Les opérateurs de centre de données gèrent les opérations informatiques au sein du centre de données. Ils gèrent les activités quotidiennes au sein du centre pour résoudre les problèmes, maintenir la disponibilité du système et évaluer les performances du système.");
        escoOccupation.setDescriptionEN("Data centre operators maintain computer operations within the data centre. They manage daily activities within the centre to solve problems, maintain the system availability, and evaluate the system's performance.");
        escoOccupation.setAlternativeLabels(new HashSet<>(Arrays.asList("opératrice de data centers", "opérateur de centres informatiques", "opératrice de centre de données de calcul", "opératrice de centre de données", "opératrice de centre de traitement de données", "opérateur de centre de données de calcul", "opérateur de data centers", "opératrice de centres informatiques", "opérateur de centre de données", "opérateur de centre de traitement de données")));
        escoOccupation.setLevel(MasteryLevel.COMPLEX);

        var activity = activityRepository.save(Activity.builder()
                .description("[ESCO] Préparer une documentation sur les produits ou services existants et à venir, en décrivant leurs fonctionnalités et leur composition, de manière à ce qu'elle soit compréhensible pour un large public sans connaissances techniques et conforme aux exigences et normes définies. Gardez la documentation à jour.")
                .origin(Origin.ESCO)
                .inducedCapacities(Stream.of("CA3-10", "CA3-09", "CA3-08", "CA3-07")
                        .map(capacityService::findOneByCode)
                        .collect(Collectors.toSet()))
                .build());

        docActivity = jobActivityLabelRepository.save(JobActivityLabel.builder()
                .title("Fournir une documentation technique")
                .position(0)
                .activity(activity)
                .build());

        escoOccupation.setSkills(new HashSet<>(Collections.singletonList(
                EscoSkill.builder()
                        .uri("http://data.europa.eu/esco/skill/04dfd9fb-e0cf-40f6-96c6-9d2280c4347e")
                        .title("fournir une documentation technique")
                        .descriptionFR("Préparer une documentation sur les produits ou services existants et à venir, en décrivant leurs fonctionnalités et leur composition, de manière à ce qu'elle soit compréhensible pour un large public sans connaissances techniques et conforme aux exigences et normes définies. Gardez la documentation à jour.")
                        .descriptionEN("Prepare documentation for existing and upcoming products or services, describing their functionality and composition in such a way that it is understandable for a wide audience without technical background and compliant with defined requirements and standards. Keep documentation up to date.")
                        .skillType("aptitude")
                        .contexts(Stream.of(context1, context2, context3)
                                .map(c -> contextRepository.findById(c.getId()).orElseThrow())
                                .collect(Collectors.toSet()))
                        .activities(Sets.newHashSet(docActivity))
                        .noBehavior(true)
                        .noActivity(false)
                        .noContext(false)
                        .build()
        )));

        escoOccupationRepository.save(escoOccupation);
    }

    protected void createESCOOccupation2() {
        var title = "Accessoiriste";
        if (!escoOccupationRepository.search(title, Pageable.unpaged()).isEmpty()) {
            return;
        }

        escoOccupation = new EscoOccupation();
        escoOccupation.setTitle(title);
        escoOccupation.setUri("http://data.europa.eu/esco/occupation/7a18e959-44fe-49ae-9757-124f46a7487c");
        escoOccupation.setIscoOccupation(iscoOccupationRepository.findById(3435).orElseThrow());
        escoOccupation.setDescriptionEN("Prop makers construct, build, prepare, adapt and maintain props used on stage and for filming movies or television programs. Props may be simple imitations of real life objects, or may include electronic, pyrotechnical, or other effects. Their work is based on artistic vision, sketches and plans. They work in close cooperation with the designers involved in the production.");
        escoOccupation.setAlternativeLabels(new HashSet<>());
        escoOccupation.setLevel(MasteryLevel.TECHNICAL);

        escoOccupation.setSkills(new HashSet<>(Collections.singletonList(
                EscoSkill.builder()
                        .uri("http://data.europa.eu/esco/skill/c4885c4e-f979-4966-8fc7-6c4350eaa86a")
                        .title("construire des accessoires")
                        .descriptionFR("")
                        .descriptionEN("Build props from a variety of materials, working with the design staff to create the appropriate prop for the production.")
                        .skillType("aptitude")
                        .build()
        )));

        escoOccupationRepository.save(escoOccupation);
    }

    protected void createErhgoOccupations() {
        log.info("Create ERHGO from Fixtures");

        createErhgoOccupation1();
        createErhgoOccupation2();
        createCandidateWithExperienceOnOccupation(IntStream.range(0, 15)
                .mapToObj(this::createErhgoOccupationN)
                .toList()
        );
    }

    protected ErhgoOccupation createErhgoOccupationN(int n) {
        var title = "Test occupation " + n;

        return erhgoOccupationRepository.save(ErhgoOccupation
                .builder()
                .id(UUID.randomUUID())
                .title(title)
                .level(MasteryLevel.COMPLEX)
                .qualificationState(ErhgoOccupationState.QUALIFIED_V2)
                .build());
    }

    protected void createErhgoOccupation1() {
        var title = TITLE_OCCUPATION1;

        if (!erhgoOccupationRepository.search(title, null, Pageable.unpaged()).isEmpty()) {
            return;
        }

        var escoOccupationPage = escoOccupationRepository.search(title, Pageable.unpaged());
        var escoOccupation = escoOccupationPage.getContent().get(0);

        var occupation = erhgoOccupationRepository.save(ErhgoOccupation
                .builder()
                .id(UUID.randomUUID())
                .title(title)
                .level(MasteryLevel.COMPLEX)
                .description(escoOccupation.getDescriptionEN())
                .escoOccupations(Sets.newHashSet(escoOccupation))
                .skills(new HashSet<>(escoOccupation.getSkills()))
                .qualificationState(ErhgoOccupationState.QUALIFIED_V2)
                .alternativeLabels(new TreeSet<>(Arrays.asList(title.split("/"))))
                .build());
        occupation.addRome(romeOccupationRepository.findById("D1506").orElseThrow());
        occupation.addRome(romeOccupationRepository.findById("N4301").orElseThrow());
        occupation.addActivity(eatChipsActivity);
        occupation.addActivity(docActivity);
        occupation.setEntityMandatoryState(eatChipsActivity.getUuid(), MandatoryState.ESSENTIAL);

        occupation.addContext(contextRepository.findById(context1.getId()).orElseThrow());
        occupation.setEntityMandatoryState(context1.getId(), MandatoryState.ESSENTIAL);

        occupation.addBehavior(behaviorRepository
                .findAll()
                .stream()
                .filter(behavior -> behavior.getCode().equals("CO-01262"))
                .findFirst()
                .orElseThrow());
        erhgoOccupation = occupation;
    }

    protected void createErhgoOccupation2() {
        var title = "Accessoiriste";

        if (!erhgoOccupationRepository.search(title, null, Pageable.unpaged()).isEmpty()) {
            return;
        }

        var escoOccupationPage = escoOccupationRepository.search(title, Pageable.unpaged());
        var escoOccu = escoOccupationPage.getContent().get(0);

        var erhgoOccu = ErhgoOccupation
                .builder()
                .id(UUID.randomUUID())
                .title(title)
                .level(MasteryLevel.TECHNICAL)
                .description(escoOccu.getDescriptionEN())
                .escoOccupations(Sets.newHashSet(escoOccu))
                .skills(new HashSet<>(escoOccu.getSkills()))
                .qualificationState(ErhgoOccupationState.NONE)
                .build();

        erhgoOccu.addRome(romeOccupationRepository.findById("L1302").orElseThrow());
        erhgoOccupationRepository.save(erhgoOccu.computeQualificationState());
    }

    @Transactional
    public void createDomainFixtures() {
        createReferential();
        createISCOccupations();
        createROMEOccupation();
        createESCOOccupations();
        createErhgoOccupations();
        createExtraProfessionalQuestions();
        createOrgaAndUser();
        createLandingPages();
        userProfileRepository.findAll().forEach(u -> {
            u.refreshProfessionalAndCapacityRelatedQuestionCapacityOccurrences();
            u.refreshMasteryLevelAndMarkAsIndexationRequired();
        });
    }

    private void createExtraProfessionalQuestions() {
        extraProQuestionId = UUID.randomUUID();

        var command = new SaveCapacityRelatedQuestionCommandDTO()
                .id(extraProQuestionId)
                .questionType(QuestionTypeDTO.EXTRAPROFESSIONAL)
                .title("Comment ça va ?")
                .responses(
                        List.of(
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(UUID.randomUUID())
                                        .title("Bien")
                                        .capacities(Collections.emptyList()),
                                new ResponseForCapacityRelatedQuestionDTO()
                                        .id(UUID.randomUUID())
                                        .title("Bien bien")
                                        .capacities(Collections.emptyList())
                        )
                );

        capacityRelatedQuestionService.save(command);
    }

    private void createReferential() {
        log.info("Create contexts from domain fixtures");
        context1 = createContext("Connaitre le cycle de l'eau", "Évaporation, nuage, pluie, tout ça tout ça.", "CCT-01");
        context2 = createContext("Connaitre le cycle de l'air", "C'est étonnant.", "CCT-01");
        context3 = createContext("Utiliser un clavier azerty", "Et pqs aue un qwerty.", "CCT-02");
        questionForContext1 = questionForContextsService.saveContextQuestion(new SaveQuestionForContextsCommandDTO()
                .contexts(Lists.newArrayList(context1.getId()))
                .id(UUID.randomUUID())
                .title("Connaissez-vous le cycle de l'eau ?")
                .suggestedAnswers(new SuggestedAnswersDTO().high("A la folie").medium("Beaucoup").none("Pas du tout").low("Un peu")));
        log.info("Fix capacities from domain fixtures");
        fixCapacities();
        log.info("End of referential fixtures creation");
    }

    private void fixCapacities() {


        eatChipsActivity = createActivityLabel("Manger des chips", IntStream.range(1, 10).mapToObj(i -> ("CA1-0" + i)).toArray(String[]::new));
        createActivityLabel("Bronzer", IntStream.range(0, 10).mapToObj(i -> ("CA1-1" + i)).toArray(String[]::new));
    }


    private void createLandingPages() {
        if (landingPageRepository.count() < 1) {
            log.info("Create landing pages");
            landingPageRepository.saveAll(Arrays.asList(
                    LandingPage
                            .builder()
                            .id(UUID.randomUUID())
                            .content("<h1 class=\"ql-align-center\"><strong>Lorem Ipsum</strong> is simply dummy text of the printing and typesetting industry.</h1><p class=\"ql-align-center\"><br></p>" +
                                    "<p class=\"ql-align-center\"> Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, " +
                                    "when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. " +
                                    "It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>")
                            .urlKey("loremIpsum").build(),
                    LandingPage
                            .builder()
                            .id(UUID.randomUUID())
                            .content("<p>Bonjour le monde !<img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAEUlEQVR42mNgoDWop7rCkQEAIuIA/zlILfAAAAAASUVORK5CYII=\"></p>")
                            .urlKey("landingPageWithImage").build())
            );
        } else {
            log.info("Do NOT create landing pages - some already exist");
        }
    }
}
