package com.erhgo.migrations.changes;

import com.erhgo.config.KeycloakRealmsConfig;
import com.erhgo.config.SMTPConfig;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.security.Role;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.representations.idm.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static com.google.common.collect.Lists.newArrayList;

@Service
@Slf4j
public class CreateKeycloakSourcingRealm extends AbstractMigrationService {

    private static final String SOURCING_THEME_ID = "sourcing";
    @Autowired
    private Keycloak keycloak;
    @Autowired
    private KeycloakRealmsConfig config;
    @Autowired
    private SMTPConfig smtpConfig;

    @Override
    protected void executeUpdate() {
        var realm = new RealmRepresentation();
        realm.setId(config.getSourcingRealmId());
        realm.setRealm(config.getSourcingRealmId());
        realm.setPasswordPolicy(config.getPasswordPolicy());

        addSourcingDefaultGroupToRealm(realm);

        var webClient = buildSourcingWebClient();
        var apiClient = buildSourcingAPIClient();

        realm.setClients(newArrayList(webClient, apiClient));

        realm.setSmtpServer(smtpConfig.toStringMap());

        setCommonRealmSettings(realm, SOURCING_THEME_ID);
        realm.setRegistrationAllowed(true);

        this.keycloak.realms().create(realm);

        disableOTP(config.getSourcingRealmId());
    }

    private void addSourcingDefaultGroupToRealm(RealmRepresentation realmRepresentation) {
        var rolesRepresentation = new RolesRepresentation();
        rolesRepresentation.setRealm(Stream.of(Role.SOURCING, Role.ODAS_ADMIN)
                .map(name -> {
                    var role = new RoleRepresentation();
                    role.setName(name);
                    return role;
                }).toList());
        realmRepresentation.setRoles(rolesRepresentation);
        var group = new GroupRepresentation();
        group.setName(Role.SOURCING);
        group.setPath("/" + Role.SOURCING);
        group.setRealmRoles(List.of(Role.SOURCING, Role.ODAS_ADMIN));
        realmRepresentation.setGroups(List.of(group));
        realmRepresentation.setDefaultGroups(List.of(Role.SOURCING));
    }

    private ClientRepresentation buildSourcingWebClient() {
        var webClient = new ClientRepresentation();
        webClient.setName("web-sourcing");
        webClient.setClientId("web-sourcing");
        webClient.setBaseUrl(config.getSourcingBaseURL() + "/");
        webClient.setEnabled(true);
        webClient.setPublicClient(true);
        webClient.setRedirectUris(newArrayList(config.getSourcingBaseURL() + "/*"));
        webClient.setWebOrigins(newArrayList(config.getSourcingBaseURL()));
        return webClient;
    }

    private ClientRepresentation buildSourcingAPIClient() {
        var apiClient = new ClientRepresentation();
        apiClient.setName("api");
        apiClient.setClientId("api");
        apiClient.setAdminUrl(config.getApiBaseURL() + "/");
        apiClient.setEnabled(true);
        apiClient.setBearerOnly(true);
        apiClient.setSecret(config.getFrontApiClientSecret());
        return apiClient;
    }

    private void setCommonRealmSettings(RealmRepresentation realm, String themeId) {
        realm.setEnabled(true);
        realm.setAccountTheme("odas");
        realm.setAdminTheme("odas");
        realm.setEmailTheme("odas");
        realm.setLoginTheme(themeId);
        realm.setDefaultLocale("fr");
        realm.setSupportedLocales(Collections.singleton("fr"));
        realm.setInternationalizationEnabled(true);
        realm.setRegistrationEmailAsUsername(true);
        realm.setResetPasswordAllowed(true);
        realm.setVerifyEmail(false);
        realm.setRememberMe(true);
        realm.setLoginWithEmailAllowed(true);
    }

    private void disableOTP(String realmId) {
        switchAction(realmId, "CONFIGURE_TOTP", false);
    }

    private void switchAction(String realmId, String termAlias, boolean b) {
        var data = keycloak.realm(realmId).flows().getRequiredAction(termAlias);
        data.setDefaultAction(b);
        data.setEnabled(b);
        keycloak.realm(realmId).flows().updateRequiredAction(termAlias, data);
    }

}
