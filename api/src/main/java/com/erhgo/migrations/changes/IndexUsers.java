package com.erhgo.migrations.changes;

import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.services.SecurityService;
import com.erhgo.services.search.UserIndexer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IndexUsers extends AbstractMigrationService {

    @Autowired
    private UserIndexer userIndexer;

    @Autowired
    private SecurityService securityService;

    @Override
    protected void executeUpdate() {
        log.info("All users indexation starts...");
        securityService.doAsAdmin(() -> userIndexer.indexAllFOUsers());
        log.info("All users indexation launched");
    }
}
