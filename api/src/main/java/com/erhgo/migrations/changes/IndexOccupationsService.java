package com.erhgo.migrations.changes;

import com.erhgo.services.ErhgoOccupationService;
import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class IndexOccupationsService {

    private final ErhgoOccupationService erhgoOccupationService;
    private final SecurityService securityService;

    @Async
    protected void indexOccupations() {
        securityService.doAsAdmin(() -> {
            log.info("About to index erhgo occupations...");
            erhgoOccupationService.indexAll();
            log.info("erhgo occupations indexation ended");
        });
    }

}
