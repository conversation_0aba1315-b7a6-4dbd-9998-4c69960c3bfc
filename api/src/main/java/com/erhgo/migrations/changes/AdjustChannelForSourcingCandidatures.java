package com.erhgo.migrations.changes;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.migrations.AbstractMigrationService;
import com.erhgo.services.SecurityService;
import com.erhgo.services.userprofile.UserProfileService;
import jakarta.persistence.EntityManager;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

@NoArgsConstructor
@Service
@Slf4j
// See ERHGO-1582
public class AdjustChannelForSourcingCandidatures extends AbstractMigrationService {

    public static final String ADJUST_CHANNEL_REQUEST = """
                SELECT c
                FROM RecruitmentCandidature c
                JOIN c.recruitment r
                JOIN r.recruitmentProfile rp
                JOIN rp.job j
                JOIN j.recruiter rec
                WHERE rec.code LIKE 'S-%'
            """;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private UserProfileService userProfileService;
    @Autowired
    private SecurityService securityService;

    @Override
    @Transactional
    public void executeUpdate() {
        securityService.doAsAdmin(() -> {
            log.info("Update ERHGO organization on candidatures starts...");
            var candidatures = entityManager.createQuery(
                            ADJUST_CHANNEL_REQUEST, RecruitmentCandidature.class)
                    .getResultList();
            log.info("Updating {} candidatures", candidatures.size());
            candidatures.forEach(this::assignToChannelErrorProof);
            log.info("{} candidatures updated, update ends", candidatures.size());
        });
    }

    private void assignToChannelErrorProof(RecruitmentCandidature c) {
        var channel = c.getCodeOfRecruiter();
        var userId = c.getUserProfile().userId();
        try {
            userProfileService.addUserToChannels(userId, Set.of(channel));
        } catch (GenericTechnicalException e) {
            log.error("Unable to assign user {} to channel {}", userId, channel, e);
        }
    }
}
