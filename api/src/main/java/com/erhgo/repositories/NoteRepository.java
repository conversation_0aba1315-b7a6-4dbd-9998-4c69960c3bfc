package com.erhgo.repositories;

import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

public interface NoteRepository<T> {
    List<T> findByUpdatedDateLessThanEqual(Date date);

    default List<T> findByLastUpdatedWithinLastMonths(int months) {
        var zonedDateTime = ZonedDateTime.now().minusMonths(months);
        return findByUpdatedDateLessThanEqual(Date.from(zonedDateTime.toInstant()));
    }
}
