package com.erhgo.repositories;

import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.dto.CandidateDetailDTO;
import com.erhgo.services.dto.criteria.SourcingCandidatesCriteria;
import org.intellij.lang.annotations.Language;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.UUID;

public interface SourcingUserRepository extends JpaRepository<UserProfile, UUID> {
    @Language("MySQL")
    String CAPACITY_TOLERANCE_AND_EXPERIENCE_COUNT_CPE =
            """
                    WITH CapacityToleranceCalculation AS (
                        SELECT
                        capacities.userProfile_uuid as userUuid,
                        COUNT(capacities.capacity_id) as capacityScore
                        FROM CapacityOccurrence capacities
                        WHERE capacities.capacity_id IN (:#{#criteria.capacitiesIds()})
                        GROUP BY capacities.userProfile_uuid
                        HAVING capacityScore >=  (:#{#criteria.capacityTolerance()} * :#{#criteria.capacitiesIds().size()})
                    ),
                    RomeCountForUserProfiles AS (
                            SELECT
                                ue.userProfile_uuid,
                                COALESCE(COUNT(DISTINCT ue.uuid), 0) AS experienceCount
                            FROM UserExperience ue
                            INNER JOIN ErhgoOccupation eo ON ue.erhgoOccupation_id = eo.id
                            INNER JOIN RomeOfErhgoOccupation eoro ON eo.id = eoro.erhgoOccupation_id
                            WHERE (
                                :#{#criteria.mandatoryRomeCodesPrefix().isEmpty()} IS FALSE
                                AND LEFT(eoro.romeOccupation_code, 3) IN (:#{#criteria.mandatoryRomeCodesPrefix()})
                            )
                            OR (
                                :#{#criteria.optionalRomeCodes().isEmpty()} IS FALSE
                                AND eoro.romeOccupation_code IN (:#{#criteria.optionalRomeCodes()})
                            )
                            GROUP BY ue.userProfile_uuid
                    )
                    """;

    @Language("MySQL")
    String JOIN_CLAUSES = """
            FROM UserProfile as up
            LEFT JOIN GeneralInformation gi
            ON (
                    :#{#criteria.longitude} IS NOT NULL
                    OR :#{#criteria.salaryMinCalculated()} IS NOT NULL
                    OR :#{#criteria.activeSearch} IS TRUE
                )
                AND up.uuid = gi.userProfile_uuid
            LEFT JOIN CapacityToleranceCalculation capacityTolerance
                ON :#{#criteria.capacitiesIds().isEmpty()} IS FALSE
                AND :#{#criteria.hasZeroTolerance()} IS FALSE
                AND capacityTolerance.userUuid = up.uuid
            LEFT JOIN RomeCountForUserProfiles romeCount
                ON (
                    :#{#criteria.mandatoryRomeCodesPrefix().isEmpty()} IS FALSE
                    OR :#{#criteria.optionalRomeCodes().isEmpty()} IS FALSE
                )
                AND up.uuid = romeCount.userProfile_uuid
            """;
    @Language("MySQL")
    String WHERE_CLAUSES = """
            WHERE
                (
                    :#{#criteria.occupationId} IS NULL
                    OR NOT EXISTS (
                        SELECT 1
                        FROM UserProfile_ErhgoOccupation upeo
                        WHERE up.uuid = upeo.UserProfile_uuid
                        AND upeo.blacklistedOccupations_id = :#{#criteria.occupationId}
                    )
                )
                AND (
                    :#{#criteria.recruitmentId} IS NULL
                    OR (
                        NOT EXISTS(
                            SELECT 1
                            FROM RecruitmentCandidature c
                            WHERE c.userProfile_uuid = up.uuid
                            AND c.recruitment_id = :#{#criteria.recruitmentId}
                            AND (:#{#criteria.topTen} IS TRUE OR c.modifiedByUser IS TRUE)
                        ) AND (
                            :#{#criteria.excludesNotified} IS FALSE
                            OR NOT EXISTS(
                                SELECT 1
                                FROM Notification n
                                WHERE up.uuid = n.userProfile_uuid
                                AND n.recruitment_id = :#{#criteria.recruitmentId}
                                AND n.DTYPE = 'RECRUITMENT'
                            )
                        )
                    )
                )
                AND (
                   :#{#criteria.refusedChannels.isEmpty()} IS TRUE
                   OR NOT EXISTS (
                        SELECT 1
                        FROM ChannelAffectation ca
                        WHERE up.uuid = ca.UserProfile_uuid
                        AND ca.channel IN (:#{#criteria.refusedChannels})
                   )
                )
                AND (
                    :#{#criteria.capacitiesIds().isEmpty()} IS TRUE
                    OR :#{#criteria.hasZeroTolerance()} IS TRUE
                    OR capacityTolerance.userUuid IS NOT NULL
                )
                AND (
                    :#{#criteria.longitude} IS NULL
                    OR :#{#criteria.latitude} IS NULL
                    OR ST_DISTANCE_SPHERE(POINT(gi.longitude, gi.latitude), POINT(:#{#criteria.longitude()}, :#{#criteria.latitude()})) <= gi.radiusInKm * 1000
                )
                AND (
                    :#{#criteria.criteria.isEmpty()} IS TRUE
                    OR NOT EXISTS (
                        SELECT 1 FROM CriteriaValue cv
                        INNER JOIN Criteria crit ON crit.code = cv.criteria_code
                        INNER JOIN UserCriteriaValue ucv ON ucv.value_code IN (
                            SELECT code FROM CriteriaValue WHERE criteria_code = crit.code
                        )
                        INNER JOIN CriteriaValue userCV ON ucv.value_code = userCV.code
                        WHERE cv.code in (:#{#criteria.criteria})
                        AND (
                            (crit.questionType = 'THRESHOLD' AND userCV.valueIndex < cv.valueIndex)
                            OR (crit.questionType = 'MULTIPLE' AND ucv.value_code = cv.code AND ucv.selected IS FALSE)
                        )
                        AND ucv.userProfile_uuid = up.uuid
                    )
                )
                AND (
                    :#{#criteria.criteriaForContracts.isEmpty()} IS TRUE
                    OR NOT EXISTS (
                            SELECT 1
                            FROM CriteriaValue cv
                            INNER JOIN Criteria crit ON crit.code = cv.criteria_code
                            INNER JOIN UserCriteriaValue ucv ON ucv.value_code IN (
                                SELECT code FROM CriteriaValue WHERE criteria_code = crit.code
                            )
                            WHERE ucv.value_code in (:#{#criteria.criteriaForContracts()})
                            AND ucv.selected IS FALSE
                            AND ucv.userProfile_uuid = up.uuid
                            HAVING COUNT(distinct ucv.value_code) = :#{#criteria.criteriaForContracts().size()}
                    )
                )
                AND (
                    :#{#criteria.classifications.isEmpty()} IS TRUE
                    OR NOT EXISTS (
                        SELECT 1 FROM UserErhgoClassification uec
                        WHERE uec.erhgoClassification_code IN (:#{#criteria.classifications})
                        AND uec.accepted IS FALSE
                        AND uec.userProfile_uuid = up.uuid
                    )
                )
                AND  (
                    :#{#criteria.lastConnectionBefore} IS NULL
                    OR up.lastConnectionDate > :#{#criteria.lastConnectionBefore}
                )
                AND (
                    :#{#criteria.masteryLevelAround} IS NULL
                    OR (up.masteryLevel >= :#{#criteria.masteryLevelMin()} AND up.masteryLevel <= :#{#criteria.masteryLevelMax()})
                )
                AND (
                    :#{#criteria.salaryMinCalculated()} IS NULL
                    OR (
                        (gi.salary IS NULL OR gi.salary = 0 OR gi.salary >= :#{#criteria.salaryMinCalculated()})
                        AND (gi.salary IS NULL OR gi.salary <= :#{#criteria.salaryMaxCalculated()})
                    )
                )
                AND (
                    :#{#criteria.mandatoryRomeCodesPrefix().isEmpty()} IS TRUE
                    OR romeCount.experienceCount > 0
                )
                AND (
                    :#{#criteria.activeSearch} IS FALSE
                    OR gi.situation IS NULL
                    OR gi.situation <> 'EMPLOYEE'
                )
            ORDER BY
                IF(:#{#criteria.optionalRomeCodes().isEmpty()} IS FALSE, romeCount.experienceCount, NULL) DESC,
                IF(:#{#criteria.topTen()} IS TRUE, capacityTolerance.capacityScore, NULL) DESC
            LIMIT :#{#criteria.rowLimit()}
            """;

    @Query(value =
            CAPACITY_TOLERANCE_AND_EXPERIENCE_COUNT_CPE +
            """
                    SELECT DISTINCT up.userId
                    """ +
            JOIN_CLAUSES + WHERE_CLAUSES
            , nativeQuery = true)
    List<String> getCandidates(SourcingCandidatesCriteria criteria);

    @Query(value =
            CAPACITY_TOLERANCE_AND_EXPERIENCE_COUNT_CPE +
            """
                    SELECT DISTINCT COUNT(up.uuid)
                    """ +
            JOIN_CLAUSES + WHERE_CLAUSES, nativeQuery = true)
    Integer countCandidates(SourcingCandidatesCriteria criteria);

    @Query(value =
            CAPACITY_TOLERANCE_AND_EXPERIENCE_COUNT_CPE +
            """
                    SELECT DISTINCT
                         up.userId as id,
                         gi.city as city,
                         up.masteryLevel as masteryLevelAsFloat,
                         romeCount.experienceCount as romeExperiencesCount,
                         capacityTolerance.capacityScore as capacityScore,
                         refusedClassifications.codes as refusedClassifications
                    """ +
            JOIN_CLAUSES +
            """
                    LEFT JOIN (
                            SELECT uec.userProfile_uuid, GROUP_CONCAT(uec.erhgoClassification_code) AS codes
                            FROM UserErhgoClassification uec
                            WHERE uec.accepted = FALSE
                            GROUP BY uec.userProfile_uuid
                        ) AS refusedClassifications ON up.uuid = refusedClassifications.userProfile_uuid
                    """ +
            WHERE_CLAUSES, nativeQuery = true)
    List<CandidateDetailDTO> getCandidatesDetail(SourcingCandidatesCriteria criteria);
}
