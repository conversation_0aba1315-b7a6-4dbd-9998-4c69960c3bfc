package com.erhgo.repositories.classifications;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

public interface ErhgoOccupationRepository extends JpaRepository<ErhgoOccupation, UUID> {

    String COUNT_SKILLS_URI = "count(distinct skills.uri)";

    String QUALIFIED_SKILL_SIZE = "sum (case when skills.noActivity IS NOT NULL and skills.noBehavior IS NOT NULL and skills.noContext IS NOT NULL then 1 else 0 end )";


    // This query means: 'get all ERHGO occupations which do not have too many non-matching capacities'
    // ('too many' according to given threshold)
    @Query("SELECT c AS occupation, " +
            QUALIFIED_SKILL_SIZE + " AS qualifiedSkillNumber, " +
            COUNT_SKILLS_URI + " AS skillSize, " +
            COUNT_SKILLS_URI + " - " + QUALIFIED_SKILL_SIZE + " AS notQualifiedSkillNumber " +
            "FROM ErhgoOccupation AS c " +
            // Caution: retrieves counts on skills => do not join entities falsifying these counts, due to cartesian product; use subquery
            "LEFT JOIN c.skills AS skills " +
            "WHERE (:activityLabelId IS NULL OR EXISTS (" +
            "   SELECT oa FROM OccupationActivity oa " +
            "   WHERE oa.occupation = c " +
            "   AND oa.activity.uuid = :activityLabelId)) " +
            "AND (:query IS NULL " +
            "   OR LOWER(c.title) LIKE CONCAT('%', LOWER(:query), '%') " +
            "   OR EXISTS (SELECT al FROM c.alternativeLabels al where LOWER(al) LIKE CONCAT('%', LOWER(:query), '%'))" +
            "   OR EXISTS (SELECT escos " +
            "       FROM c.escoOccupations escos " +
            "       LEFT JOIN escos.iscoOccupation AS isco " +
            "       WHERE (CAST(isco.iscoGroup AS string) = :query OR escos.uri = :query))" +
            "   ) " +
            "GROUP BY c.id")
    Page<SearchErhgoOccupationDTO> search(String query, UUID activityLabelId, Pageable pageable);

    List<ErhgoOccupation> findBySkills(EscoSkill skill);


    List<ErhgoOccupation> findByRomeOfErhgoOccupationsRomeOccupationIn(Collection<RomeOccupation> romeOccupationsCode, Pageable pageRequest);


    @Query("""
            SELECT DISTINCT o
            FROM ErhgoOccupation o
            LEFT JOIN FETCH o.alternativeLabels
            """)
    Iterable<ErhgoOccupation> findAllForIndexation();


    interface SearchErhgoOccupationDTO {

        ErhgoOccupation getOccupation();

        int getQualifiedSkillNumber();

        int getSkillSize();

        int getNotQualifiedSkillNumber();
    }

    @Query("SELECT DISTINCT eo " +
            "FROM ErhgoOccupation eo " +
            "INNER JOIN OccupationActivity oa ON eo = oa.occupation " +
            "INNER JOIN oa.activity jal " +
            "INNER JOIN jal.activity a " +
            "WHERE a.id IN (:activitiesId) " +
            "ORDER BY oa.state, eo.qualificationState DESC, eo.level, eo.title"
    )
    List<ErhgoOccupation> findByActivityIdIn(Collection<UUID> activitiesId, Pageable pageRequest);


    @Query("SELECT distinct o " +
            "from ErhgoOccupation o " +
            "LEFT JOIN o.alternativeLabels alternativeLabels " +
            "where LOWER(alternativeLabels) in (:labels) " +
            "OR LOWER(o.title) in (:labels)")
    List<ErhgoOccupation> getErhgoOccupationsWithLabels(List<String> labels);
}

