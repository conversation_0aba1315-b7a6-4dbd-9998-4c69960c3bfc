package com.erhgo.repositories.classifications;

import com.erhgo.domain.classifications.workenvironment.WorkEnvironment;
import org.springframework.data.repository.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface WorkEnvironmentRepository extends Repository<WorkEnvironment, String> {
    Optional<WorkEnvironment> findByCode(String code);

    List<WorkEnvironment> findAllByOrderByCode();

    List<WorkEnvironment> findByCodeIn(Collection<String> workEnvironmentsCodes);
}
