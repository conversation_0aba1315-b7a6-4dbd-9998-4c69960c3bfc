package com.erhgo.repositories.classifications;

import com.erhgo.domain.classifications.esco.EscoOccupation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface EscoOccupationRepository extends JpaRepository<EscoOccupation, String> {

    @Query("SELECT c " +
            "FROM EscoOccupation AS c " +
            "LEFT JOIN c.iscoOccupation AS isco " +
            "WHERE :query IS NULL OR CAST(isco.iscoGroup AS string) = :query OR c.uri = :query OR LOWER(c.title) LIKE CONCAT('%', LOWER(:query), '%')")
    Page<EscoOccupation> search(String query, Pageable pageable);
}
