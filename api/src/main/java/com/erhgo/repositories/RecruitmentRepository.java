package com.erhgo.repositories;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.RecruitmentSendNotificationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.dto.RecruitmentStatsDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

public interface RecruitmentRepository extends PagingAndSortingRepository<Recruitment, Long>, CrudRepository<Recruitment, Long> {

    String REFUSED_CANDIDATURE_COUNT = "COALESCE(" +
            "   SUM(CASE WHEN c.globalCandidatureState IN (" +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS, " +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_MEETING_CLIENT, " +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_ON_CALL" +
            "   ) then 1 ELSE 0 END), 0) ";
    String GENERATED_CANDIDATURE_COUNT = "COUNT(CASE WHEN c.valid = true AND c.generatedForSourcing = true THEN 1 END)";

    String TOTAL_FROM_USER = "COALESCE(" +
            "SUM(CASE WHEN c.generatedForSourcing = FALSE AND c.globalCandidatureState NOT IN (" +
            "            com.erhgo.domain.candidature.job.GlobalCandidatureState.MISSING_PREREQUISITE," +
            "             com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_FINALIZED" +
            "             ) AND c.globalCandidatureState IS NOT NULL THEN 1 ELSE 0 END), 0)";
    String TOTAL_GENERATED = "COALESCE(" +
            "SUM(CASE WHEN c.generatedForSourcing = TRUE AND c.globalCandidatureState NOT IN (" +
            "            com.erhgo.domain.candidature.job.GlobalCandidatureState.MISSING_PREREQUISITE," +
            "             com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_FINALIZED" +
            "             ) AND c.globalCandidatureState IS NOT NULL THEN 1 ELSE 0 END), 0)";
    String NEW_FROM_USER = "COALESCE(" +
            "   SUM(CASE WHEN c.globalCandidatureState IN (" +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_TREATED_BY_ERHGO," +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.NEW," +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.STAND_BY" +
            "   ) AND c.generatedForSourcing = FALSE  then 1 ELSE 0 END), 0)";
    String NEW_GENERATED = "COALESCE(" +
            "   SUM(CASE WHEN c.globalCandidatureState IN (" +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_TREATED_BY_ERHGO," +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.NEW," +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.STAND_BY" +
            "   ) AND c.generatedForSourcing = TRUE  then 1 ELSE 0 END), 0)";
    String TO_CONTACT_CANDIDATURE_COUNT = "COALESCE(" +
            "   SUM(CASE WHEN c.globalCandidatureState = com.erhgo.domain.candidature.job.GlobalCandidatureState.INTERNAL_POSITION then 1 ELSE 0 END), 0)";

    String CONTACTED_CANDIDATURE_COUNT = "COALESCE(" +
            "   SUM(CASE WHEN c.globalCandidatureState IN (" +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.INTRODUCE_TO_CLIENT," +
            "      com.erhgo.domain.candidature.job.GlobalCandidatureState.SUMMARY_SHEET_SENT" +
            "   ) then 1 ELSE 0 END), 0)";


    Recruitment findOneByCode(String code);

    List<Recruitment> findByRecruitmentProfileIn(Iterable<RecruitmentProfile> profiles);

    void deleteByRecruitmentProfileJobId(UUID jobId);

    Recruitment findFirstByRecruitmentProfileJobId(UUID jobId);

    List<Recruitment> findBySendNotificationDateBeforeAndSendNotificationStateAndState(Instant instant, RecruitmentSendNotificationState sendNotificationState, RecruitmentState state);

    Integer countByRecruitmentProfileJobRecruiter(Recruiter recruiter);

    List<Recruitment> findByExternalOfferIsNullAndRecruitmentProfileJobRecruiterInAndState(Collection<Recruiter> recruiters, RecruitmentState state);

    List<Recruitment> findByPublicationEndDateBeforeAndState(OffsetDateTime date, RecruitmentState state);

    List<Recruitment> findRecruitmentByState(RecruitmentState state);


    @Query("""
            SELECT r
            FROM Recruitment r
            JOIN r.recruitmentProfile rp
            JOIN rp.job j
            JOIN j.recruiter rec
            WHERE rec.organizationType = com.erhgo.domain.referential.AbstractOrganization$OrganizationType.SOURCING
            AND r.state = com.erhgo.domain.enums.RecruitmentState.PUBLISHED
            """)
    List<Recruitment> findRecruitmentsToIndex();

    List<Recruitment> findRecruitmentByStateAndCreatedDateAfter(RecruitmentState recruitmentState, Date after);

    interface RecruitmentWithCandidatureCount {
        Recruitment getRecruitment();

        int getNewFromUser();

        int getNewGenerated();

        int getTotalFromUser();

        int getTotalGenerated();

        int getToContactCandidatureCount();

        int getContactedCandidatureCount();

        OffsetDateTime getLastProcessingDate();

        Recruitment.ProcessingType getLastProcessingType();

        Integer getCandidatureCount();

        Integer getMatchingCandidatureCount();

        Integer getNewMatchingCandidatureCount();

        Integer getGeneratedCandidatureForSourcing();

        Integer getRefusedCandidatureCount();

        UUID getProfileUuid();

        String getProfileTitle();

        String getJobTitle();

        String getCity();
    }

    @Query("SELECT " +
            "r AS recruitment, " +
            "rp.uuid AS profileUuid," +
            "rp.title AS profileTitle," +
            "job.title AS jobTitle," +
            "r.location.city AS city," +
            "r.publicationDate AS publicationDate," +
            "r.lastProcessingDate AS lastProcessingDate," +
            "r.lastProcessingType AS lastProcessingType," +
            "COUNT(c) AS candidatureCount, " +
            "COUNT(CASE WHEN c.valid = true THEN 1 END) AS matchingCandidatureCount, " +
            GENERATED_CANDIDATURE_COUNT + " AS generatedCandidatureForSourcing, " +
            REFUSED_CANDIDATURE_COUNT + " AS refusedCandidatureCount," +
            NEW_MATCHING_CANDIDATURE_COUNT + " AS newMatchingCandidatureCount, " +
            TOTAL_FROM_USER + " AS totalFromUser, " +
            TOTAL_GENERATED + " AS totalGenerated, " +
            NEW_FROM_USER + " AS newFromUser, " +
            TO_CONTACT_CANDIDATURE_COUNT + " AS toContactCandidatureCount, " +
            CONTACTED_CANDIDATURE_COUNT + " AS contactedCandidatureCount, " +
            NEW_GENERATED + " AS newGenerated " +
            "FROM Recruitment AS r " +
            "LEFT JOIN RecruitmentCandidature AS c ON c.recruitment = r AND c.state = com.erhgo.domain.enums.CandidatureState.VALIDATED " +
            "INNER JOIN r.recruitmentProfile as rp " +
            "INNER JOIN rp.job as job " +
            "INNER JOIN job.recruiter as rec " +
            "LEFT JOIN job.employer as emp " +
            "WHERE (" +
            "   false = :hasOrganization" +
            "   OR rec.code in (:organizationCodes) " +
            "   OR emp.code in (:organizationCodes) " +
            " ) " +
            "AND (:publishedAfterDate IS NULL OR r.publicationDate >= :publishedAfterDate) " +
            "AND (:publishedBeforeDate IS NULL OR r.publicationDate < :publishedBeforeDate) " +
            "AND (" +
            "    :query IS NULL " +
            "    OR LOWER(job.title) LIKE CONCAT('%', LOWER(:query), '%') " +
            "    OR LOWER(rp.title) LIKE CONCAT('%', LOWER(:query), '%') " +
            "    OR LOWER(r.location.city) LIKE CONCAT('%', LOWER(:query), '%') " +
            "    OR LOWER(emp.title) LIKE CONCAT('%', LOWER(:query), '%') " +
            ") " +
            "AND (false = :withOpenRecruitmentOnly OR r.state = com.erhgo.domain.enums.RecruitmentState.PUBLISHED) " +
            "AND (false = :internal OR rec.internal = true) " +
            "AND (:organizationType IS null OR rec.organizationType = :organizationType) " +
            "GROUP BY r.id, rp.id, job.id " +
            "HAVING (false = :withNewCandidaturesOnly OR " + NEW_MATCHING_CANDIDATURE_COUNT + " > 0)"
    )
    Page<RecruitmentWithCandidatureCount> _findWithCandidatureCountByOrganizationCodeIn(
            Collection<String> organizationCodes,
            boolean withNewCandidaturesOnly,
            boolean withOpenRecruitmentOnly,
            AbstractOrganization.OrganizationType organizationType,
            boolean internal,
            boolean hasOrganization,
            Timestamp publishedAfterDate,
            Timestamp publishedBeforeDate,
            String query,
            Pageable pageable);

    String NEW_MATCHING_CANDIDATURE_COUNT = "COUNT(CASE WHEN c.valid = true AND c.candidatureRecruitmentState = com.erhgo.domain.candidature.job.CandidatureRecruitmentState.NEW THEN 1 END)";

    default Page<RecruitmentWithCandidatureCount> findWithCandidatureCountByOrganizationCodeIn(
            Collection<String> organizationCodes,
            boolean withNewCandidaturesOnly,
            boolean withOpenRecruitmentOnly,
            AbstractOrganization.OrganizationType organizationType,
            boolean internal,
            boolean hasOrganization,
            LocalDate publishedAfterDate,
            LocalDate publishedBeforeDate,
            String query,
            Pageable pageable
    ) {
        var afterDateTime = publishedAfterDate == null ? null : Timestamp.valueOf(publishedAfterDate.atStartOfDay());
        var beforeDateTime = publishedBeforeDate == null ? null : Timestamp.valueOf(publishedBeforeDate.plusDays(1).atStartOfDay());
        return this._findWithCandidatureCountByOrganizationCodeIn(organizationCodes, withNewCandidaturesOnly, withOpenRecruitmentOnly, organizationType, internal, hasOrganization, afterDateTime, beforeDateTime, query, pageable);

    }

    interface NotificationCount {

        Long getRecruitmentId();

        Long getNbNotifications();

        Long getMobileNotificationsCount();

        Long getEmailNotificationsCount();

    }

    @Query("""
                SELECT n.recruitment.id as recruitmentId,
                COUNT(n.id) as nbNotifications,
                COUNT(CASE WHEN n.type IN (com.erhgo.domain.enums.NotificationType.MOBILE, com.erhgo.domain.enums.NotificationType.BOTH) THEN 1 END) AS mobileNotificationsCount,
                COUNT(CASE WHEN n.type IN (com.erhgo.domain.enums.NotificationType.EMAIL, com.erhgo.domain.enums.NotificationType.BOTH) THEN 1 END) AS emailNotificationsCount
                FROM RecruitmentNotification n
                where n.recruitment.id in (:recruitmentIds)
                GROUP BY n.recruitment.id
            """)
    List<NotificationCount> findNotificationsForRecruitment(Set<Long> recruitmentIds);


    interface RecruitmentWithCandidatureForJob {
        Recruitment getRecruitment();

        RecruitmentCandidature getCandidature();
    }

    @Query("SELECT " +
            "r AS recruitment, " +
            "c AS candidature " +
            "FROM Recruitment AS r " +
            "LEFT JOIN RecruitmentCandidature AS c ON c.recruitment = r " +
            "   AND c.state = com.erhgo.domain.enums.CandidatureState.VALIDATED " +
            "   AND c.userProfile = :userProfile " +
            "LEFT JOIN c.userProfile AS up " +
            "INNER JOIN r.recruitmentProfile AS rp " +
            "INNER JOIN rp.job AS job " +
            "WHERE job.id = :jobId " +
            "AND r.state = com.erhgo.domain.enums.RecruitmentState.PUBLISHED"
    )
    Page<RecruitmentWithCandidatureForJob> findWithCandidatureByJobId(UUID jobId, UserProfile userProfile, Pageable pageable);

    interface RecruitmentWithUserDistance {
        Long getRecruitmentId();

        Float getUserDistance();
    }

    @Query(nativeQuery = true,
            value = "SELECT r.id AS recruitmentId, " +
                    "       ST_Distance_Sphere(ST_GeomFromText(CONCAT('Point(', :userLatitude, ' ', :userLongitude, ')')), ST_GeomFromText(CONCAT('Point(', r.latitude, ' ', r.longitude, ')'))) as userDistance " +
                    "FROM Recruitment r " +
                    "WHERE r.id IN (:recruitmentIds)")
    List<RecruitmentWithUserDistance> findWithUserDistance(Float userLatitude, Float userLongitude, List<Long> recruitmentIds);


    @Query("""
            SELECT r
            FROM Recruitment AS r
            INNER JOIN r.recruitmentProfile AS rp
            INNER JOIN rp.job AS job
            INNER JOIN job.erhgoOccupation AS eo
            WHERE eo.id = :erhgoOccupationId
            AND job.recruiter.id = :recruiterId
            AND ST_DISTANCE_SPHERE(POINT(r.location.longitude, r.location.latitude), POINT(?#{#longitude}, ?#{#latitude})) <= 25000
            """)
    List<Recruitment> findSimilarRecruitments(UUID erhgoOccupationId, float longitude, float latitude, Long recruiterId);

    @Query("SELECT r " +
            "FROM Recruitment AS r " +
            "INNER JOIN r.recruitmentProfile AS rp " +
            "INNER JOIN rp.job AS job " +
            "WHERE job.id = :jobId " +
            "AND r.state = com.erhgo.domain.enums.RecruitmentState.PUBLISHED")
    List<Recruitment> findPublishedByJobId(UUID jobId);

    @AllArgsConstructor
    enum RecruitmentSort {

        PROFILE("(rp.title)"),

        JOB("(job.title)"),

        STATE("(r.state)"),

        CANDIDATURE_COUNT("(count(c))"),

        NEW_CANDIDATURE_COUNT("(%s)".formatted(NEW_MATCHING_CANDIDATURE_COUNT)),

        REFUSED_CANDIDATURE_COUNT("(%s)".formatted(RecruitmentRepository.REFUSED_CANDIDATURE_COUNT)),

        ID("(r.id)"),

        EMPLOYER("(emp.code)"),

        RECRUITER("(rec.code)"),

        CITY("(r.location.city)"),

        GENERATED_CANDIDATURE_FOR_SOURCING("(%s)".formatted(GENERATED_CANDIDATURE_COUNT)),

        PUBLICATION_DATE("(r.publicationDate)"),

        LAST_PROCESSING_DATE("(r.lastProcessingDate)"),

        SEND_NOTIFICATION_STATE("(r.sendNotificationState)"),

        SEND_NOTIFICATION_DATE("(r.sendNotificationDate)");

        @Getter
        String by;
    }

    @Query("""
            SELECT c
            FROM RecruitmentCandidature c
            JOIN c.recruitment re
            JOIN re.recruitmentProfile rp
            JOIN rp.job j
            JOIN j.recruiter r
            WHERE r.organizationType =  com.erhgo.domain.referential.AbstractOrganization$OrganizationType.SOURCING
            AND re.state IN (com.erhgo.domain.enums.RecruitmentState.PUBLISHED, com.erhgo.domain.enums.RecruitmentState.SELECTION)
            AND c.globalCandidatureState in (:#{T(com.erhgo.domain.candidature.job.GlobalCandidatureState).finalizedValues()})
            AND c.archived = FALSE
            AND EXISTS (
                SELECT 1
                FROM RecruitmentCandidature c2
                JOIN c2.recruitment re2
                JOIN re2.recruitmentProfile rp2
                JOIN rp2.job j2
                WHERE c2.globalCandidatureState IN (
                    com.erhgo.domain.candidature.job.GlobalCandidatureState.NEW,
                    com.erhgo.domain.candidature.job.GlobalCandidatureState.NOT_TREATED_BY_ERHGO,
                    com.erhgo.domain.candidature.job.GlobalCandidatureState.STAND_BY,
                    com.erhgo.domain.candidature.job.GlobalCandidatureState.INTERNAL_POSITION,
                    com.erhgo.domain.candidature.job.GlobalCandidatureState.INTRODUCE_TO_CLIENT,
                    com.erhgo.domain.candidature.job.GlobalCandidatureState.SUMMARY_SHEET_SENT
                    )
                AND j2.recruiter.id = r.id
                )
            """)
    List<RecruitmentCandidature> findCandidaturesOnOpenRecruitmentsWithAtLeastOneUntreatedCandidatureOnRecruiter();

    default Page<SimpleRecruitment> findRecruitments(RecruitmentState recruitmentState,
                                                     Pageable pageable
    ) {
        return _findRecruitmentsInternal(
                pageable,
                recruitmentState
        );
    }

    default Page<SimpleRecruitment> findRecruitments(MasteryLevel jobMaxLevel,
                                                     Collection<Capacity> userCapacities,
                                                     int capacityThreshold,
                                                     RecruitmentState recruitmentState,
                                                     UserProfile userProfile,
                                                     Collection<CriteriaValue> refusedCriteriaValues,
                                                     Pageable pageable,
                                                     Location candidateLocation,
                                                     Collection<ErhgoClassification> refusedClassifications,
                                                     Collection<ErhgoOccupation> blacklistedOccupations,
                                                     String jobTitleQuery
    ) {
        var bitmaskPerLevel = Capacity.buildBitmaskPerLevelForCapacities(userCapacities);

        return _findRecruitmentsInternal(
                pageable,
                jobMaxLevel,
                capacityThreshold,
                recruitmentState,
                userProfile.uuid(),
                refusedCriteriaValues.stream().map(CriteriaValue::getCode).toList(),
                candidateLocation != null ? candidateLocation.getLongitude() : null,
                candidateLocation != null ? candidateLocation.getLatitude() : null,
                !refusedClassifications.isEmpty(),
                refusedClassifications.stream().map(ErhgoClassification::getCode).collect(Collectors.toSet()),
                !blacklistedOccupations.isEmpty(),
                blacklistedOccupations.stream().map(ErhgoOccupation::getId).collect(Collectors.toSet()),
                (userProfile.generalInformation() == null || userProfile.generalInformation().getSalary() == null) ? 0 : userProfile.generalInformation().getSalary(),
                trimQueryForExpansionSearch(jobTitleQuery),
                trimQueryForBooleanSearch(jobTitleQuery),
                trimQueryForLike(jobTitleQuery),
                bitmaskPerLevel.get(1),
                bitmaskPerLevel.get(2),
                bitmaskPerLevel.get(3)
        );
    }

    String JOIN_RECRUITMENT_SELECTOR = """
                        FROM Recruitment r
                        INNER JOIN RecruitmentProfile rp on r.recruitmentProfile_uuid = rp.uuid
                        INNER JOIN Job j on j.id=rp.job_id
                        INNER JOIN Organization e on e.id=j.recruiter_id
                        INNER JOIN ErhgoOccupation occupation on occupation.id=j.erhgoOccupation_id
                        LEFT JOIN RecruitmentCandidature c ON c.userProfile_uuid = :userProfileUuid AND c.recruitment_id = r.id
            """;

    String JOB_TITLE_SCORE = """
                    CASE
                        WHEN LOWER(j.title) = LOWER(:jobTitleQuery) THEN 400
                        WHEN LOWER(j.title) LIKE CONCAT(LOWER(:jobTitleQuery), '%') THEN 350
                        WHEN LOWER(j.title) LIKE CONCAT('%', LOWER(:jobTitleQuery), '%') THEN 300
                        ELSE 0
                    END
                    +
            """;

    String LOCATION_SCORE = """
                    CASE
                        WHEN :candidateLatitude IS NULL OR :candidateLongitude IS NULL THEN 0
                        ELSE
                            300 * EXP(-ST_DISTANCE_SPHERE(
                                POINT(:candidateLongitude, :candidateLatitude),
                                POINT(r.longitude, r.latitude)
                            ) / 50000)
                    END
                    +
            """;

    String CAPACITY_MATCH_SCORE = """                    
                   CASE        WHEN r.bitMaskCA1 IS NULL OR r.bitMaskCA2 IS NULL OR r.bitMaskCA3 IS NULL
                                THEN 0
                               WHEN (
                                   (BIT_COUNT(r.bitMaskCA1 & :level1CapacityMask) +
                                    BIT_COUNT(r.bitMaskCA2 & :level2CapacityMask) +
                                    BIT_COUNT(r.bitMaskCA3 & :level3CapacityMask))
                                   / (BIT_COUNT(r.bitMaskCA1) + BIT_COUNT(r.bitMaskCA2) + BIT_COUNT(r.bitMaskCA3))
                               ) >= (:capacityThreshold / 100.0) THEN 200
                               ELSE (
                                   (BIT_COUNT(r.bitMaskCA1 & :level1CapacityMask) +
                                    BIT_COUNT(r.bitMaskCA2 & :level2CapacityMask) +
                                    BIT_COUNT(r.bitMaskCA3 & :level3CapacityMask))
                                   / (BIT_COUNT(r.bitMaskCA1) + BIT_COUNT(r.bitMaskCA2) + BIT_COUNT(r.bitMaskCA3))
                               ) * (:capacityThreshold / 100.0) * 200.0
                           END
                    +
            """;

    String MASTERY_LEVEL_SCORE = """
                    CASE
                        WHEN j.masteryLevel <= :jobMaxLevel THEN 100
                        ELSE 0
                    END
            """;

    String WHERE_PART = """
            WHERE
                (:recruitmentState IS NULL OR r.state = :recruitmentState)
                AND c.id IS NULL
                AND (:hasRefusedClassifications = false
                     OR r.id NOT IN (
                        SELECT cl.recruitment_id
                        FROM Recruitment_ErhgoClassification cl
                        WHERE cl.erhgoClassifications_code IN (:refusedClassifications)
                     ))
                AND (:hasBlacklistedOccupations = false
                     OR occupation.id NOT IN (:blacklistedOccupations))
                AND NOT EXISTS (
                    SELECT 1
                    FROM Job_CriteriaValue jcv
                    WHERE jcv.criteriaValues_code IN (:refusedCriteriaValues)
                    AND jcv.job_id = j.id
                ) AND (
                    :queryForLike IS NULL
                    OR j.title LIKE :queryForLike
                    OR e.title LIKE :queryForLike
                    OR r.description LIKE :queryForLike
                    OR r.organizationDescription LIKE :queryForLike
                ) AND (
                    :queryForLike IS NOT NULL
                    OR :jobTitleQuery IS NULL
                    OR MATCH(j.title) AGAINST (:jobTitleQuery WITH QUERY EXPANSION) > 0
                    OR MATCH(j.title) AGAINST (:queryForBooleanSearch IN BOOLEAN MODE) > 0
                    OR MATCH(e.title) AGAINST (:queryForBooleanSearch IN BOOLEAN MODE) > 0
                    OR MATCH(r.description, r.organizationDescription) AGAINST (:queryForBooleanSearch IN BOOLEAN MODE) > 0
                ) AND (
                    r.startingDate IS NULL
                    OR r.startingDate <= NOW()
                ) AND (
                    :salary <= 0
                    OR (r.baseSalary IS NULL AND r.maxSalary IS NULL)
                    OR (r.maxSalary IS NOT NULL AND r.maxSalary <= :salary * 1.4)
                    OR (r.baseSalary IS NOT NULL AND r.baseSalary >= :salary * 0.7)
                )
            """;

    @Query(value =
            """
                    SELECT
                        r.code as code,
                        e.title as organizationName,
                        j.title as jobTitle,
                        r.city as city,
                        r.hideSalary as hideSalary,
                        r.baseSalary as baseSalary,
                        r.maxSalary as maxSalary,
                        r.typeContract as typeContract,
                     (""" + JOB_TITLE_SCORE + LOCATION_SCORE + CAPACITY_MATCH_SCORE + MASTERY_LEVEL_SCORE + """
                    ) as matchingScore
                    """
                    + JOIN_RECRUITMENT_SELECTOR + WHERE_PART +
                    """
                                GROUP BY r.id
                                ORDER BY matchingScore DESC, r.publicationDate DESC, r.title desc
                            """, countQuery = FIND_RECRUITMENTS_COUNT, nativeQuery = true)
    Page<SimpleRecruitment> _findRecruitmentsInternal(
            Pageable pageable,
            MasteryLevel jobMaxLevel,
            int capacityThreshold,
            RecruitmentState recruitmentState,
            UUID userProfileUuid,
            Collection<String> refusedCriteriaValues,
            Float candidateLongitude,
            Float candidateLatitude,
            boolean hasRefusedClassifications,
            Collection<String> refusedClassifications,
            boolean hasBlacklistedOccupations,
            Collection<UUID> blacklistedOccupations,
            int salary,
            String jobTitleQuery,
            String queryForBooleanSearch,
            String queryForLike,
            Long level1CapacityMask,
            Long level2CapacityMask,
            Long level3CapacityMask
    );

    String FIND_RECRUITMENTS_COUNT = """
                            SELECT COUNT(1)
            """ + JOIN_RECRUITMENT_SELECTOR + WHERE_PART;

    default int findRecruitmentsCount(RecruitmentState recruitmentState,
                                                     UserProfile userProfile,
                                                     Collection<CriteriaValue> refusedCriteriaValues,
                                                     Collection<ErhgoClassification> refusedClassifications,
                                                     Collection<ErhgoOccupation> blacklistedOccupations,
                                                     String jobTitleQuery
    ) {
        return _findRecruitmentsCountInternal(
                recruitmentState,
                userProfile.uuid(),
                refusedCriteriaValues.stream().map(CriteriaValue::getCode).toList(),
                !refusedClassifications.isEmpty(),
                refusedClassifications.stream().map(ErhgoClassification::getCode).collect(Collectors.toSet()),
                !blacklistedOccupations.isEmpty(),
                blacklistedOccupations.stream().map(ErhgoOccupation::getId).collect(Collectors.toSet()),
                (userProfile.generalInformation() == null || userProfile.generalInformation().getSalary() == null) ? 0 : userProfile.generalInformation().getSalary(),
                jobTitleQuery,
                trimQueryForBooleanSearch(jobTitleQuery),
                trimQueryForLike(jobTitleQuery)
        );
    }

    static String trimQueryForBooleanSearch(String jobTitleQuery) {
        return StringUtils.trimToNull(jobTitleQuery) == null ? null : "*" + sanitizeForSearch(jobTitleQuery).replace(" ", "* *") + "*";
    }

    static String trimQueryForExpansionSearch(String jobTitleQuery) {
        return StringUtils.trimToNull(jobTitleQuery) == null ? null : sanitizeForSearch(jobTitleQuery);
    }

    private static @NotNull String sanitizeForSearch(String jobTitleQuery) {
        return jobTitleQuery.trim().replaceAll("[+\\-><()~*\"@.']", " ");
    }

    static String trimQueryForLike(String jobTitleQuery) {
        return StringUtils.isNotBlank(jobTitleQuery) && StringUtils.trimToEmpty(jobTitleQuery).length() < 4 ? "%" + jobTitleQuery.trim() + "%" : null;
    }

    @Query(value = FIND_RECRUITMENTS_COUNT, nativeQuery = true)
    int _findRecruitmentsCountInternal(
            RecruitmentState recruitmentState,
            UUID userProfileUuid,
            Collection<String> refusedCriteriaValues,
            boolean hasRefusedClassifications,
            Collection<String> refusedClassifications,
            boolean hasBlacklistedOccupations,
            Collection<UUID> blacklistedOccupations,
            int salary,
            String jobTitleQuery,
            String queryForBooleanSearch,
            String queryForLike
    );

    @Query(value = """
                    SELECT
                            r.code as code,
                            e.title as organizationName,
                            j.title as jobTitle,
                            r.location.city as city,
                            r.hideSalary as hideSalary,
                            r.baseSalary as baseSalary,
                            r.maxSalary as maxSalary,
                            r.typeContract as typeContract
                    FROM Recruitment r
                    INNER JOIN r.recruitmentProfile rp
                    INNER JOIN rp.job j
                    INNER JOIN j.recruiter e
                    WHERE (:recruitmentState IS NULL OR r.state = :recruitmentState)
                                AND (r.startingDate IS NULL OR r.startingDate <= NOW())
                    GROUP BY r.id
                    ORDER BY r.publicationDate DESC
            """, countQuery = """
                    SELECT COUNT(1) FROM Recruitment r
                    WHERE (:recruitmentState IS NULL OR r.state = :recruitmentState)
                    GROUP BY r.id
            """)
    Page<SimpleRecruitment> _findRecruitmentsInternal(
            Pageable pageable,
            RecruitmentState recruitmentState
    );

    interface SimpleRecruitment {
        String getCode();

        String getOrganizationName();

        String getJobTitle();

        String getCity();

        Boolean getHideSalary();

        Integer getBaseSalary();

        Integer getMaxSalary();

        Integer getTypeContract();

        Float getMatchingScore();
    }

    @Query("""
                WITH ats AS
                    (SELECT o.computedRecruiterCode as code, GROUP_CONCAT(distinct o.atsCode) as ats FROM ExternalOffer o group by o.computedRecruiterCode)
                SELECT
                    rec.id as organizationId,
                    rec.title as organizationTitle,
                    rec.code as organizationCode,
                    MAX(r.publicationDate) as lastRecruitmentPublicationDate,
                    COUNT(rc) as totalCandidatures,
                    (SELECT COUNT(DISTINCT up.userId) FROM UserProfile up JOIN up.affectationsHistory ah WHERE ah.userChannel.channel = rec.code) AS totalUsersInChannel,
                    COUNT(DISTINCT (CASE WHEN r.state = com.erhgo.domain.enums.RecruitmentState.PUBLISHED THEN r.id ELSE null END)) as openRecruitmentsCount,
                    COUNT(DISTINCT (CASE WHEN r.state = com.erhgo.domain.enums.RecruitmentState.CLOSED THEN r.id ELSE null END)) as closedRecruitmentsCount,
                    SUM(CASE WHEN rc.synchronizationState = com.erhgo.domain.enums.CandidatureSynchronizationState.DONE THEN 1 ELSE 0 END) as totalTransmittedCandidatures,
                    SUM(CASE WHEN rc.archived = TRUE THEN 1 ELSE 0 END) as totalCandidaturesArchived,
                    SUM(CASE WHEN rc.globalCandidatureState IN (
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.ON_RECRUITMENT_CLIENT,
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.RECRUITMENT_VALIDATED
                    ) then 1 ELSE 0 END) AS selectedCandidaturesCount,
                    SUM(CASE WHEN rc.globalCandidatureState IN (
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.INTRODUCE_TO_CLIENT,
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.SUMMARY_SHEET_SENT
                    ) THEN 1 ELSE 0 END) as contactedCandidaturesCount,
                    SUM(CASE WHEN rc.globalCandidatureState IN (
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_MEETING_CLIENT,
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_ON_CALL,
                        com.erhgo.domain.candidature.job.GlobalCandidatureState.REFUSED_BY_CLIENT_WITH_SHEETS
                    ) THEN 1 ELSE 0 END) as refusedCandidaturesCount,
                    (SELECT COUNT(s) FROM SpontaneousCandidature s WHERE s.recruiter = rec) AS spontaneousCandidaturesCount,
                    (SELECT COUNT(s) FROM SpontaneousCandidature s WHERE s.recruiter = rec AND s.archived = TRUE) AS spontaneousCandidaturesArchived,
                    A.ats AS connectedAts
                FROM Recruitment r
                JOIN r.recruitmentProfile rp
                JOIN rp.job j
                JOIN j.recruiter rec
                LEFT JOIN RecruitmentCandidature rc ON rc.recruitment = r
                LEFT JOIN ats A ON A.code = rec.code
                WHERE rec.organizationType = com.erhgo.domain.referential.AbstractOrganization$OrganizationType.SOURCING
                GROUP BY rec.id
            """)
    List<RecruitmentStatsDTO> getRecruitmentStats();


    @Query("SELECT r FROM Recruitment r WHERE r.managerUserId = :userId AND r.state = com.erhgo.domain.enums.RecruitmentState.PUBLISHED")
    List<Recruitment> findActiveRecruitmentsByManagerUserId(String userId);


    @Query("""
            SELECT r
            FROM Recruitment r
            WHERE :sourcingUserId IN elements(r.sourcingUsersIdToNotify)
            """)
    List<Recruitment> findRecruitmentsByNotifiedUser(String sourcingUserId);
}
