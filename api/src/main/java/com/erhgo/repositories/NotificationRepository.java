package com.erhgo.repositories;

import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.notification.AbstractNotification;
import com.erhgo.domain.userprofile.notification.RecruitmentNotification;
import com.erhgo.repositories.dto.CountForUserDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public interface NotificationRepository extends JpaRepository<AbstractNotification, UUID> {

    @Query("""
            SELECT notification
            FROM AbstractNotification notification
            WHERE notification.userProfile.uuid = :userUuid
            AND (
                    type(notification) = com.erhgo.domain.userprofile.notification.RecruitmentNotification OR
                    type(notification) = com.erhgo.domain.userprofile.notification.DefaultNotification
            )
            ORDER BY notification.createdDate DESC
            """)
    List<AbstractNotification> findByUserProfileUuidOrderByCreatedDateDesc(UUID userUuid);

    List<AbstractNotification> findByUserProfileUuid(UUID userUuid);

    @Query("FROM RecruitmentNotification WHERE userProfile.uuid = :userUuid")
    List<RecruitmentNotification> findRecruitmentNotificationByUserProfileUuid(UUID userUuid);

    @Query("""
            SELECT notification.userProfile.uuid as userId, count(DISTINCT notification) as countForUser
            FROM AbstractNotification notification
            WHERE notification.userProfile.uuid in (:userUuids)
            AND notification.state = com.erhgo.domain.enums.NotificationState.NEW
            AND (
                    type(notification) = com.erhgo.domain.userprofile.notification.RecruitmentNotification OR
                    type(notification) = com.erhgo.domain.userprofile.notification.DefaultNotification
            )
            GROUP BY notification.userProfile.uuid
            """)
    List<CountForUserDTO> getUnreadNotificationsCount(Collection<UUID> userUuids);

    List<RecruitmentNotification> findByRequiresMailSendingIsTrue();

    List<RecruitmentNotification> findByRecruitmentAndUserProfile(Recruitment recruitment, UserProfile userProfile);

    @Query("DELETE FROM AbstractNotification n WHERE n.createdDate < :cutoffDate")
    @Modifying
    int deleteByCreatedDateBefore(Date cutoffDate);
}
