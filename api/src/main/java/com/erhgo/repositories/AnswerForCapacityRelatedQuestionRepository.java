package com.erhgo.repositories;

import com.erhgo.domain.enums.QuestionType;
import com.erhgo.domain.referential.CapacityRelatedQuestionResponse;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.capacityrelatedquestion.AnswerForCapacityRelatedQuestion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

public interface AnswerForCapacityRelatedQuestionRepository extends JpaRepository<AnswerForCapacityRelatedQuestion, AnswerForCapacityRelatedQuestion.ID> {

    @Query("" +
            "SELECT a " +
            "FROM AnswerForCapacityRelatedQuestion a " +
            "JOIN a.response r " +
            "JOIN r.question q " +
            "WHERE a.userProfile = :userProfile " +
            "AND q.questionType = :questionType ")
    List<AnswerForCapacityRelatedQuestion> findUserAnswers(UserProfile userProfile, QuestionType questionType);

    @Query("DELETE FROM AnswerForCapacityRelatedQuestion a WHERE a.response = :response")
    @Modifying
    void deleteAllByResponse(CapacityRelatedQuestionResponse response);

    List<AnswerForCapacityRelatedQuestion> findByResponseIn(Collection<CapacityRelatedQuestionResponse> responses);

    List<AnswerForCapacityRelatedQuestion> findByUserProfileAndResponseIn(UserProfile userProfile, Collection<CapacityRelatedQuestionResponse> responses);
}
