package com.erhgo.repositories.dto;

import com.erhgo.domain.userprofile.GeneralInformationHolder;
import com.erhgo.domain.userprofile.Location;
import com.google.common.base.Strings;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface UserMatchingSummary extends GeneralInformationHolder {

    Integer getNumberOfCapacities();

    Long getCandidatureId();

    String getChannelsAsStringFromDB();

    String getCity();

    String getPostcode();

    String getMissingCriteriaAsStringFromDB();

    String getMatchingCriteriaAsStringFromDB();

    @Override
    Boolean getSmsBlacklisted();

    // Little trick to allow UserProfileRepository.getUserMatchingCapacities to return a Location object with a native query
    @Override
    default Location getLocation() {
        return Location.builder()
                .city(getCity())
                .postcode(getPostcode())
                .build();
    }

    @Override
    default Set<String> getChannels() {
        var channelsFromDb = getChannelsAsStringFromDB();
        return (Strings.isNullOrEmpty(channelsFromDb)) ? Collections.emptySet() : Stream.of(channelsFromDb.split(",")).collect(Collectors.toSet());
    }

}
