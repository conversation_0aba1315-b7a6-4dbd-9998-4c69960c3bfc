package com.erhgo.repositories;

import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.repositories.dto.CriteriaValueForUserDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

public interface CriteriaRepository extends JpaRepository<Criteria, String> {

    List<Criteria> findAllByOrderByCriteriaIndex();

    @Query("" +
            "SELECT DISTINCT cv " +
            "FROM CriteriaValue cv " +
            "WHERE cv.code IN (:codes)")
    List<CriteriaValue> findCriteriaValuesByCriteriaValuesCodeIn(Collection<String> codes);

    @Query("" +
            "SELECT ucv " +
            "FROM UserCriteriaValue ucv " +
            "WHERE ucv.userProfile.userId = :userId")
    List<UserCriteriaValue> findUserCriteriaValuesByUserId(String userId);

    @Query("""
            SELECT v as criteriaValue, (SELECT ucv.selected IS TRUE from UserCriteriaValue ucv WHERE ucv.userProfile.userId=:userId AND ucv.value=v) as selected
            FROM CriteriaValue v
            WHERE v.criteria.code IN (:codes)
            """)
    List<CriteriaValueForUserDTO> findCriteriaValuesByUserIdAndCriteriaCodes(String userId, Collection<String> codes);

    default CriteriaValue findCriteriaValueByCode(String code) {
        return findCriteriaValuesByCriteriaValuesCodeIn(List.of(code)).stream().findFirst().orElse(null);
    }
}
