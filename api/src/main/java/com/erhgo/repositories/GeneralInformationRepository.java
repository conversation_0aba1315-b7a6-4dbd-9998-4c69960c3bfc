package com.erhgo.repositories;

import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.repositories.dto.UserAndPhoneDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

public interface GeneralInformationRepository extends PagingAndSortingRepository<GeneralInformation, Long>, CrudRepository<GeneralInformation, Long> {

    GeneralInformation findByUserProfileUuid(UUID uuid);

    @Query(value = "SELECT userId as userId, phoneNumber as phoneNumber FROM GeneralInformation " +
            "WHERE REGEXP_REPLACE(phoneNumber, '[^0-9]', '') IN (:phoneNumbers)",
            nativeQuery = true)
    List<UserAndPhoneDTO> findByPhoneNumber(Collection<String> phoneNumbers);


}
