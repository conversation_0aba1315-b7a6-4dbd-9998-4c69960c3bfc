package com.erhgo.repositories;

import com.erhgo.domain.sourcing.SourcingPreferences;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface SourcingPreferencesRepository extends JpaRepository<SourcingPreferences, UUID> {

    Optional<SourcingPreferences> findByUserId(String userId);

    List<SourcingPreferences> findByMailFrequencyAndUserIdIn(SourcingPreferences.MailFrequency mailFrequency, Collection<String> userIds);

    List<SourcingPreferences> findByUserIdInAndNotifyOnSpontaneousCandidatureIsTrue(Collection<String> sourcingUsersIds);

    List<SourcingPreferences> findByMailFrequencyAndUserIdInAndNotifyOnSpontaneousCandidatureIsTrue(SourcingPreferences.MailFrequency mailFrequency, Collection<String> sourcingUsersIds);

    List<SourcingPreferences> findByUserIdIn(Collection<String> userIds);
}
