package com.erhgo.utils;

import com.erhgo.openapi.dto.GenerationReportItemDTO;
import com.erhgo.services.generation.dto.OpenAIResponse;

public class ReportUtils {
    public static GenerationReportItemDTO updateReportWithResponse(String generationItemTitle, GenerationReportItemDTO report, OpenAIResponse<?> response) {
        return report
                .success(response.isSuccess())
                .nbTry(response.getNbTry())
                .title(generationItemTitle)
                .durationInMs(response.getTotalDurationInMs())
                .model(response.getModel());
    }
}
