package com.erhgo.utils;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.text.WordUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.util.MultiValueMapAdapter;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.security.SecureRandom;
import java.text.Collator;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.Normalizer;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.MatchResult;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static org.apache.commons.lang3.RandomStringUtils.random;
import static org.apache.commons.lang3.StringUtils.trimToEmpty;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StringUtils {
    private static final Set<String> AURA_DEPARTMENT_CODE = Set.of("01", "03", "07", "15", "26", "38", "42", "43", "63", "69", "73", "74");
    private static final int MAX_EMAIL_LENGTH = 254;

    private static final SecureRandom RANDOM = new SecureRandom();
    private static final DateTimeFormatter DATE_TIME_FORMATTER;
    public static final char CSV_FIELD_SEPARATOR = ',';
    public static final String TMP_EMAIL_PREFIX = "TMP_";
    public static final String TMP_EMAIL_SUFFIX = "@jenesuispasuncv.fr";

    private static final Pattern SIMPLE_EMAIL_REGEXP = Pattern.compile("^(?!.*\\.\\..*)([0-9a-zA-Zà-ÿÀ-ß-_.+]+@[0-9a-zA-Zà-ÿÀ-ß-_.]+\\.[0-9a-zA-Z-]{2,})$");

    private static final String UPPER_LETTERS = IntStream.rangeClosed('A', 'Z')
            .mapToObj(c -> (char) c)
            .map(String::valueOf)
            .collect(Collectors.joining());
    private static final String LOWER_LETTERS = UPPER_LETTERS.toLowerCase();
    public static final char SMALL_UNBREAKABLE_SPACE = '\u202F';
    private static final DecimalFormat MONEY_FORMATER;
    private static final Logger log = LoggerFactory.getLogger(StringUtils.class);
    private static final String ARRONDISSEMENT = "arrondissement";
    private static final String ADECCO_ACCENT_ALLOWED_CHARS = "-áàâäãåçéèêëíìîïñ'óòôöõúùûüýÿæœÁÀÂÄÃÅÇÉÈÊËÍÌÎÏÑÓÒÔÖÕÚÙÛÜÝŸ";
    public static final String ADECCO_ALLOWED_NAME_REGEXP = "[^a-zA-Z\\s%s]".formatted(ADECCO_ACCENT_ALLOWED_CHARS);
    public static final String ADECCO_ALLOWED_ADDRESS_REGEXP = "[^a-zA-Z0-9.\\s%s]".formatted(ADECCO_ACCENT_ALLOWED_CHARS);

    static {
        var symbols = new DecimalFormatSymbols();
        symbols.setGroupingSeparator(SMALL_UNBREAKABLE_SPACE);
        MONEY_FORMATER = new DecimalFormat("#,##0", symbols);
        DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
    }

    public static String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        return DATE_TIME_FORMATTER.format(DateTimeUtils.millisToOffsetDateTime(timestamp));
    }

    public static String formatTimestamp(Instant instant) {
        if (instant == null) {
            return "";
        }
        return DATE_TIME_FORMATTER.format(DateTimeUtils.instantToOffsetDateTime(instant));
    }


    public static String removeHtmlTag(String htmlString) {
        return trimToEmpty(htmlString).replaceAll("<[^>]++>", " ").trim();
    }

    public static String formatTimestamp(OffsetDateTime dateTime) {
        return DATE_TIME_FORMATTER.format(dateTime.atZoneSimilarLocal(DateTimeUtils.ZONE_ID));
    }

    public static String formatTimestamp(LocalDateTime dateTime) {
        return DATE_TIME_FORMATTER.format(dateTime);
    }

    public static String anonymize(String name) {
        return Strings.isNullOrEmpty(name) ? "" : (name.charAt(0) + ".");
    }

    public static boolean isTemporaryEmail(String email) {
        return email.startsWith(TMP_EMAIL_PREFIX) && email.endsWith(TMP_EMAIL_SUFFIX);
    }

    public static String getTemporaryEmail(String phoneNumber) {
        return "%s%s%s".formatted(TMP_EMAIL_PREFIX, phoneNumber, TMP_EMAIL_SUFFIX);
    }

    public static String normalizeLowerCase(String string) {
        return Normalizer.normalize(string, Normalizer.Form.NFD)
                .replaceAll("\\p{InCombiningDiacriticalMarks}+", "")
                .toLowerCase();
    }

    public static String firstNameCase(String firstName) {
        return WordUtils.capitalizeFully(firstName, ' ', '-', '\'', '_');
    }

    public static String lastNameCase(String lastName) {
        return trimToEmpty(lastName).toUpperCase();
    }

    public static String normalizeURL(String url) {
        if (org.apache.commons.lang3.StringUtils.isBlank(url)) {
            return url;
        }
        if (url.startsWith("http")) {
            return url;
        }
        return "https://%s".formatted(url);
    }

    public static String abbreviateFirstAndConcatenate(String firstname, String lastname) {
        if (firstname == null) {
            return "";
        }
        var firstnameFirstLetters = Stream.of(org.apache.commons.lang3.StringUtils.split(firstname, "-"))
                .map(a -> a.isEmpty() ? "" : a.substring(0, 1))
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .collect(Collectors.joining("."));
        return "%s. %s".formatted(firstnameFirstLetters, trimToEmpty(lastname)).toUpperCase();
    }

    // This will make inconsistent result for non-french number
    public static String toFrenchFormat(String phoneNumber) {
        var res = removeNonDigitChars(trimToEmpty(phoneNumber));
        if (res.startsWith("33")) {
            res = res.substring(2);
        } else if (res.startsWith("0033")) {
            res = res.substring(4);
        }
        if (!res.startsWith("0")) {
            res = "0" + res;
        }
        if (res.length() != 10) {
            log.warn("Caution: wrong number {}, transformed to {}", phoneNumber, res);
        }
        return res;
    }

    public static boolean isFrenchPhone(String s) {
        return s != null && s.length() == 10 && s.startsWith("0");
    }

    public static String normalizeAddressForAdecco(String city) {
        if (city == null)
            return null;
        var normalizedCity = city.toLowerCase().contains(ARRONDISSEMENT) ? city.split(" ")[0] : city;
        return normalizedCity.replaceAll(ADECCO_ALLOWED_ADDRESS_REGEXP, " ").trim();

    }

    public static String normalizeIdentityForAdecco(String name) {
        if (name == null)
            return null;
        return name.replaceAll(ADECCO_ALLOWED_NAME_REGEXP, " ").trim();
    }

    public static String sanitizeOpenAIResponse(String content) {
        return org.apache.commons.lang3.StringUtils.trimToEmpty(content)
                .replaceAll("(?s)^```json\\s*(.*?)\\s*```$", "$1")
                .replaceAll("```", "");
    }

    public static String trimToDefault(String value, String defaultStr) {
        return Optional.ofNullable(org.apache.commons.lang3.StringUtils.trimToNull(value)).orElse(defaultStr);
    }

    public static String normalizeForBAN(String cityAndPostcodeIn) {
        var result = org.apache.commons.lang3.StringUtils.trimToEmpty(cityAndPostcodeIn)
                .replaceAll("^[^\\p{L}\\p{N}]+", "")
                .replace("75000", "75001")
                .replace("69000", "69001")
                .replace("13000", "13001");
        if (result.length() < 3) {
            throw new GenericTechnicalException("Invalid input for BAN call %s".formatted(result));
        }
        return result;
    }

    public static String lastValueOfSplitString(String fileUrl, char s) {
        return Optional.ofNullable(fileUrl)
                .map(url -> url.substring(url.lastIndexOf(s) + 1))
                .orElse("");
    }

    public static String getContentType(String fileName) {
        return MediaTypeFactory.getMediaType(fileName).map(MediaType::toString).orElse(MediaType.APPLICATION_OCTET_STREAM.toString());
    }

    public static final class DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR implements Comparator<String> {

        public static final DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR INSTANCE = new DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR();

        private static final Comparator<String> COMPARATOR = (o1, o2) -> {
            var collator = Collator.getInstance();
            // Ignore accent, spaces & case
            collator.setDecomposition(Collator.NO_DECOMPOSITION);
            collator.setStrength(Collator.PRIMARY);
            return collator.compare(o1, o2);
        };

        @Override
        public int compare(String o1, String o2) {
            return COMPARATOR.compare(o1, o2);
        }
    }

    public static String normalizePhone(String originalNumberParam) {
        var internationalPrefix = "0033";
        var originalNumber = org.apache.commons.lang3.StringUtils.trimToNull(originalNumberParam);
        if (originalNumber != null) {
            var number = removeNonDigitChars(originalNumber);
            if (number.startsWith(internationalPrefix) || number.startsWith("00")) {
                return number;
            } else if (number.startsWith("33")) {
                number = "00" + number;
                return number;
            } else if (number.startsWith("0")) {
                number = internationalPrefix + number.substring(1);
                return number;
            } else {
                number = internationalPrefix + number;
                return number;
            }
        }
        return null;

    }

    private static @NotNull String removeNonDigitChars(String originalNumber) {
        return originalNumber.replaceAll("\\D+", "");
    }

    public static String normalizeSalary(Integer originalSalaryParam) {
        if (originalSalaryParam != null) {
            return "%s%s€".formatted(MONEY_FORMATER.format(originalSalaryParam), SMALL_UNBREAKABLE_SPACE);
        } else {
            return null;
        }
    }

    public static String generateRandomPassword() {
        var upperLettersPart = secureStringRandom(1, UPPER_LETTERS);
        var lowerLettersPart = secureStringRandom(3, LOWER_LETTERS);
        var numericPart = RANDOM.nextInt(1000);
        return "%s%s:%03d".formatted(upperLettersPart, lowerLettersPart, numericPart);
    }

    private static String secureStringRandom(int count, String upperLetters) {
        return random(count, 0, upperLetters.length(), false, false, upperLetters.toCharArray(), RANDOM);
    }

    public static String getPhoneLabel(boolean forcePhone, Boolean hidePhone, String phoneNumber) {
        var showPhone = !BooleanUtils.isTrue(hidePhone);
        if (phoneNumber == null) {
            return null;
        }
        var phoneLabel = (showPhone || forcePhone) ? phoneNumber : "";
        phoneLabel += showPhone ? "" : " (Ne souhaite pas être contacté.e par téléphone)";
        return phoneLabel;
    }

    public static boolean isEmail(String email) {
        if (email == null) {
            return false;
        }
        return SIMPLE_EMAIL_REGEXP.matcher(email).matches() && email.length() < MAX_EMAIL_LENGTH;
    }

    public static String generateRandom4CharactersString() {
        return "" + generateRandomLetter()
                + generateRandomDigit()
                + generateRandomLetter()
                + generateRandomDigit();
    }

    public static String getRandomColorIndex() {
        return RANDOM.nextInt(0, 20) + "";
    }

    private static char generateRandomLetter() {
        return (char) RANDOM.nextInt('A', 'Z' + 1);
    }

    private static char generateRandomDigit() {
        return (char) RANDOM.nextInt('0', '9' + 1);
    }

    public static String addWordBreaksInHashtag(String hashtag) {
        return hashtag.replaceAll("(?<=\\B\\p{Lu})(\\p{Lu}+)(?=\\p{Lu}\\p{Ll})", "$1<wbr/>")
                .replaceAll("(?<=\\p{Ll})(\\p{Lu})", "<wbr/>$1");
    }

    public static boolean oneWordMatch(String s1, String s2) {
        var setS1 = setOfDistinctNormalizedWords(s1);
        var setS2 = setOfDistinctNormalizedWords(s2);
        return !Sets.intersection(setS1, setS2).isEmpty();
    }

    private static Set<String> setOfDistinctNormalizedWords(String s1) {
        return Stream.of(normalizeLowerCase(s1).split("[^a-z]+")).filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toSet());
    }

    public static boolean isInsideAURA(String location) {
        return AURA_DEPARTMENT_CODE.stream().anyMatch(c -> Optional.ofNullable(location).filter(o -> o.matches(".*[^a-zA-Z0-9]" + c + "(?:\\)?$| ?[0-9]{3}(?:[^0-9].*|$))")).isPresent());
    }

    public static String extractPostcode(String input) {
        var regex = "\\b\\d{5}\\b";
        var pattern = Pattern.compile(regex);
        var matcher = pattern.matcher(input);
        var allMatches = matcher.results()
                .map(MatchResult::group)
                .toList();
        return allMatches.isEmpty() ? null : allMatches.getLast();
    }

    public static URI buildUri(String url, Optional<Map<String, List<String>>> queryParams) {
        var remoteUrl = UriComponentsBuilder.fromHttpUrl(url);
        queryParams.map(MultiValueMapAdapter::new).ifPresent(remoteUrl::queryParams);
        UriComponents uri;
        try {
            uri = remoteUrl.build(true);
        } catch (IllegalArgumentException iae) {
            log.warn("Failed to build URI from URL: {} - please encode url params correctly in configuration", url, iae);
            uri = remoteUrl.build();
        }
        log.debug("Using {} as uri", uri);
        return uri.toUri();
    }

    public static Integer extractInt(String stringContainingInt) {
        return Pattern.compile("\\d+")
                .matcher(stringContainingInt)
                .results()
                .map(MatchResult::group)
                .findFirst()
                .map(a -> {
                    try {
                        return Integer.parseInt(a);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .orElse(null);
    }

    public static Optional<String> trimQuote(String value) {
        return Optional.ofNullable(org.apache.commons.lang3.StringUtils.trimToNull(value)).map(s -> org.apache.commons.lang3.StringUtils.strip(value, "\""));
    }

    public static String toStringifiedBase64(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * Converts a string representing a salary to a float.
     * Handles various formats like:
     * - "30000"
     * - " 30 000 €"
     * - "  30 000 euros "
     * - "30 000,54"
     * - "30 000 $"
     * - "£30,000.50"
     *
     * @param value the string to parse
     * @return the float, null if not parseable
     */
    public static Float parseFloat(String value) {
        if (org.apache.commons.lang3.StringUtils.isBlank(value)) {
            return null;
        }
        try {
            var normalized = org.apache.commons.lang3.StringUtils.trimToEmpty(value)
                    .replaceAll("[^0-9,.]", "")
                    .replace(',', '.');
            return Float.parseFloat(normalized);
        } catch (NumberFormatException e) {
            log.warn("Failed to parse float from string: {}", value, e);
            return null;
        }
    }
}
