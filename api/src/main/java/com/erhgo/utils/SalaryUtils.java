package com.erhgo.utils;

import com.erhgo.domain.recruitment.Recruitment;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@AllArgsConstructor
@NoArgsConstructor
public class SalaryUtils {
    @Value("${sourcing.salaryMinToleranceRatio}")
    @Getter
    private float salaryMinToleranceRatio;
    @Value("${sourcing.salaryMaxToleranceRatio}")
    @Getter
    private float salaryMaxToleranceRatio;

    public boolean userMatchesSalary(Integer desiredSalary, Integer baseSalary, Integer maxSalary) {
        return desiredSalary != null
                && (baseSalary == null || desiredSalary >= salaryMinToleranceRatio * baseSalary)
                && (maxSalary == null || desiredSalary <= salaryMaxToleranceRatio * maxSalary);
    }

    public static String getSalaryLabel(Recruitment recruitment) {
        if (!BooleanUtils.isTrue(recruitment.getHideSalary())) {
            if (recruitment.getBaseSalary() != null && recruitment.getMaxSalary() != null) {
                return "Entre %s et %s".formatted(StringUtils.normalizeSalary(recruitment.getBaseSalary()), StringUtils.normalizeSalary(recruitment.getMaxSalary()));
            } else if (recruitment.getBaseSalary() != null || recruitment.getMaxSalary() != null) {
                return Optional.ofNullable(recruitment.getBaseSalary()).or(() -> Optional.of(recruitment.getMaxSalary())).map(StringUtils::normalizeSalary).orElse("");
            }
        }
        return "";
    }
}
