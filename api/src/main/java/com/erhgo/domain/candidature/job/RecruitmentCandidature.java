package com.erhgo.domain.candidature.job;

import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.services.externaloffer.successfactors.SuccessFactorsJob;
import com.erhgo.services.matching.MatchingResult;
import com.google.common.annotations.VisibleForTesting;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.erhgo.utils.DateTimeUtils.ZONE_ID;

@Data
@Entity
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@ToString(callSuper = true)
@Slf4j
public class RecruitmentCandidature extends AbstractCandidature {

    public static final String ENTITY_GRAPH_WITH_RECRUITER = "ENTITY_GRAPH_WITH_RECRUITER ";
    private String code;

    @ManyToOne(optional = false)
    @EqualsAndHashCode.Include
    private Recruitment recruitment;

    private CandidatureState state = CandidatureState.STARTED;

    // Flag true when candidature matches recruitment profile
    private Boolean valid;

    private Integer effort;

    private CandidatureRecruitmentState candidatureRecruitmentState;

    @Column(columnDefinition = "LONGTEXT")
    @Getter
    private String customAnswer;

    private boolean generatedForSourcing;

    private boolean notifiedOnClosedRecruitment = false;

    @Setter(AccessLevel.PRIVATE)
    private Boolean isAvailable;

    @Setter(AccessLevel.PRIVATE)
    private Integer availabilityDelayInMonth;

    @Builder
    public RecruitmentCandidature(
            Long id,
            String code,
            Recruitment recruitment,
            List<CandidatureNote> candidatureNotes,
            UserProfile userProfile,
            OffsetDateTime submissionDate,
            CandidatureState state,
            Boolean valid,
            Integer effort,
            CandidatureRecruitmentState candidatureRecruitmentState,
            String customAnswer,
            GlobalCandidatureState globalCandidatureState,
            boolean generated,
            boolean modifiedByUser,
            boolean notifiedOnClosedRecruitment

    ) {
        super(id, candidatureNotes, userProfile, submissionDate, globalCandidatureState);
        this.code = code;
        this.recruitment = recruitment;
        this.valid = valid;
        this.effort = effort;
        this.candidatureRecruitmentState = candidatureRecruitmentState;
        this.customAnswer = customAnswer;
        this.generatedForSourcing = generated;
        this.notifiedOnClosedRecruitment = notifiedOnClosedRecruitment;
        this.setModifiedByUser(modifiedByUser);
        if (state != null) {
            setState(state);
        }
        this.validate();
    }

    @VisibleForTesting
    void validate() {
        getUserProfile().ensureUserHasAccessToJob(getJob());
    }

    public void updateCodeOnJobCreate() {
        setCode("C-" + StringUtils.leftPad(getId().toString(), 5, "0"));
    }

    public void matches(MatchingResult matches) {
        var wasValid = valid;
        valid = matches.matches();
        if (candidatureRecruitmentState == null) {
            candidatureRecruitmentState = CandidatureRecruitmentState.NEW;
        }
        if (!Objects.equals(wasValid, valid)) {
            setGlobalCandidatureState(BooleanUtils.isTrue(valid) ? GlobalCandidatureState.NOT_TREATED_BY_ERHGO : GlobalCandidatureState.MISSING_PREREQUISITE);
        }
        effort = matches.getEffort();
    }


    public void meet() {
        this.candidatureRecruitmentState = CandidatureRecruitmentState.SELECTED;
    }

    public Collection<Context> getMandatoryContexts() {
        return getRecruitmentProfile().getMandatoryContexts();
    }

    public RecruitmentProfile getRecruitmentProfile() {
        return recruitment.getRecruitmentProfile();
    }

    public boolean isSubmitted() {
        return state == CandidatureState.VALIDATED;
    }

    public Job getJob() {
        return getRecruitmentProfile() == null ? null : getRecruitmentProfile().getJob();
    }

    public boolean isSelected() {
        return candidatureRecruitmentState == CandidatureRecruitmentState.SELECTED;
    }

    public String getOrganizationName() {
        return getRecruitment().getOrganizationName();
    }

    @Override
    public String getCodeOfRecruiter() {
        return recruitment.getRecruiterCode();
    }

    public String getJobTitle() {
        return recruitment.getJob().getTitle();
    }

    @Override
    public String getRecruiterTitle() {
        return getJob().getRecruiterTitle();
    }

    public String getEmployerTitle() {
        return getJob().getEmployerTitle();
    }


    @Override
    public boolean isSourcing() {
        return recruitment.isSourcing();
    }

    @Override
    public String getTitle() {
        return getJobTitle();
    }

    public boolean isVisibleForUser() {
        return isModifiedByUser();
    }

    @Override
    public boolean isRecruitmentCandidature() {
        return true;
    }

    public boolean setState(CandidatureState state) {
        var changed = this.state != state;
        this.state = state;
        if (state == CandidatureState.VALIDATED) {
            setSubmissionDate(OffsetDateTime.now(ZONE_ID));
            if (changed) {
                this.setSynchronizationState(CandidatureSynchronizationState.WAITING);
            }
        }
        return changed;
    }

    @Override
    public void setGlobalCandidatureState(GlobalCandidatureState nextState) {
        super.setGlobalCandidatureState(nextState);
        if (nextState != null) {
            if (shouldUpdateRecruitmentLastProcessingData(nextState)) {
                this.recruitment.updateLastProcessingData(nextState.getRecruitmentActionType());
            }
            this.candidatureRecruitmentState = nextState.getCandidatureRecruitmentState();
        }
    }

    private boolean shouldUpdateRecruitmentLastProcessingData(GlobalCandidatureState nextState) {
        return this.recruitment != null /* this first clause Should always be 'true', for test purpose only */
                && this.getGlobalCandidatureState() != GlobalCandidatureState.NOT_FINALIZED // do NOT notify recruitment on candidate's action
                && nextState != GlobalCandidatureState.MISSING_PREREQUISITE;
    }

    public boolean isGeneratedAndNotRefused() {
        return !this.isRefused() && this.generatedForSourcing;
    }

    @Override
    public Recruiter getRecruiter() {
        return getRecruitment().getRecruiter();
    }

    @Override
    public void updateLastProcessingData(Recruitment.ProcessingType processingType) {
        this.recruitment.updateLastProcessingData(processingType);
    }

    // "isAvailable = true" means candidate is available now
    public void updateAvailability(Boolean isAvailable, Integer availabilityDelayInMonth) {
        this.isAvailable = isAvailable;
        this.availabilityDelayInMonth = availabilityDelayInMonth;
        getUserProfile().updateSituation(availabilityDelayInMonth);
    }

    @Override
    public boolean relatesToATS(String atsCode) {
        return Optional.ofNullable(recruitment.getExternalOffer())
                .map(ExternalOffer::getAtsCode)
                .filter(atsCode::equalsIgnoreCase)
                .isPresent();
    }

    public String getExternalOfferId() {
        return Optional.ofNullable(recruitment.getExternalOffer()).map(ExternalOffer::getRemoteId).orElse("");
    }

    public boolean isHandledByAts() {
        // FIXME: hack for only ATS not receiving candidatures
        return recruitment.relatesToExternalOffer() && !SuccessFactorsJob.ATS_CODE.equals(recruitment.getAtsCode());
    }
}
