package com.erhgo.domain.enums;

import lombok.Getter;

/**
 * Used in the capacitor to rate the frequency at which a candidate engages in an activity or a context
 */
public enum Frequency {
    NONE(false, "Jamais"),
    LOW(false, "Rarement"),
    MEDIUM(true, "Régulièrement"),
    HIGH(true, "Tous les jours");

    @Getter
    private final boolean matches;

    // This default label used for legacy recruitment profile, including context without question
    // FIXME ERHGO-270
    @Getter
    private final String defaultLabel;

    Frequency(boolean matches, String defaultLabel) {
        this.matches = matches;
        this.defaultLabel = defaultLabel;
    }

}
