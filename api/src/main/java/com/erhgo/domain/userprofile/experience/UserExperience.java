package com.erhgo.domain.userprofile.experience;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.erhgooccupation.OccupationActivity;
import com.erhgo.domain.enums.Duration;
import com.erhgo.domain.enums.DurationType;
import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.domain.userprofile.UserProfile;
import com.google.common.collect.Sets;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(of = {"uuid", "jobTitle"})
@ToString(of = {"uuid", "jobTitle"})
@Builder
@Accessors(chain = true)
public class UserExperience extends AbstractAuditableEntity implements Comparable<UserExperience> {


    public static final Comparator<UserExperience> RELEVANCE_COMPARATOR = Comparator.nullsLast(Comparator.comparing(UserExperience::getType, Comparator.nullsLast(Comparator.naturalOrder())))
            .thenComparing(UserExperience::getDurationType, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(UserExperience::getDuration, Comparator.nullsLast(Comparator.reverseOrder()))
            .thenComparing(UserExperience::getUuid, Comparator.nullsLast(Comparator.naturalOrder()));


    @Id
    @GenericGenerator(name = "UseUuidOrGenerate", type = com.erhgo.domain.utils.UseUuidOrGenerate.class)
    @GeneratedValue(generator = "UseUuidOrGenerate")
    @Column(columnDefinition = "BINARY(16)")
    private UUID uuid;

    @ManyToOne(optional = false)
    private UserProfile userProfile;

    @NotNull
    private String jobTitle;

    private String organizationName;

    @NotNull
    private ExperienceType type;

    @Enumerated(EnumType.STRING)
    private Duration duration;

    @Enumerated(EnumType.STRING)
    private DurationType durationType;

    @ManyToOne
    @JoinColumn
    @Nullable
    private ErhgoOccupation erhgoOccupation;

    public Collection<JobActivityLabel> getAllActivities(boolean distinct) {
        var stream = getJobActivityLabelStream();
        if (distinct) {
            stream = stream.distinct();
        }
        return stream.toList();
    }

    private Stream<JobActivityLabel> getJobActivityLabelStream() {
        return erhgoOccupation == null ? Stream.empty() : erhgoOccupation.getActivities().stream()
                .filter(Objects::nonNull);
    }

    public void delete() {
        getUserProfile().removeExperience(this);
        this.userProfile = null;
    }

    public boolean isOccupationBlacklisted() {
        return erhgoOccupation != null && userProfile.getBlacklistedOccupations().contains(erhgoOccupation);
    }

    public UUID getOccupationId() {
        return erhgoOccupation == null ? null : erhgoOccupation.getId();
    }

    public MasteryLevel getMasteryLevel() {
        return getErhgoOccupation() == null || getErhgoOccupation().getLevel() == null ? MasteryLevel.MIN_LEVEL : getErhgoOccupation().getLevel();
    }

    public int getLevel() {
        return getMasteryLevel().getMasteryLevel();
    }

    public Set<Capacity> getTopLevelCapacities() {
        return getAllActivities(true).stream().map(JobActivityLabel::getActivity).flatMap(a -> a.getInducedCapacities().stream()).collect(Collectors.toSet());
    }

    public String getErhgoOccupationTitle() {
        return this.erhgoOccupation == null ? null : erhgoOccupation.getTitle();
    }

    public UserExperience setErhgoOccupation(ErhgoOccupation erhgoOccupation) {
        if (!Objects.equals(erhgoOccupation, this.erhgoOccupation)) {
            this.userProfile.dirtiesUserProfile();
        }
        this.erhgoOccupation = erhgoOccupation;
        return this;
    }

    /**
     * Compare experience based on "importance" :
     * - Internship is lower than Contract
     * - Previous experiences are lower than current experience
     * - Short experiences are lower than long running experiences
     * - Then compare stuff to ensure comparaison is coherent with equals
     *
     * @param o
     * @return
     */
    @Override
    public int compareTo(UserExperience o) {
        return Comparator
                .comparing(UserExperience::getType, Comparator.nullsFirst(Comparator.reverseOrder()))
                .thenComparing(UserExperience::getDurationType, Comparator.nullsFirst(Comparator.reverseOrder()))
                .thenComparing(UserExperience::getDuration, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(UserExperience::getJobTitle, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(UserExperience::getOrganizationName, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(UserExperience::getUuid, Comparator.nullsFirst(Comparator.naturalOrder()))
                .compare(this, o);
    }

    public Set<JobActivityLabel> getActivitiesWithCapacitiesMatchingActivityAndMinimumLevel(JobActivityLabel jobActivity, Integer level) {
        var shouldConsiderLevel = level != null;
        if (shouldConsiderLevel && getLevel() < level) {
            return Collections.emptySet();
        }
        var considerInducedCapacities = !shouldConsiderLevel || level.compareTo(MasteryLevel.DEFAULT_LEVEL_AS_INT) <= 0;
        return getAllActivities(true)
                .stream()
                .filter(userActivity -> isUserActivityMatchingAnyCapacityOfActivity(userActivity, jobActivity, considerInducedCapacities))
                .collect(Collectors.toSet());
    }

    public boolean isUserActivityMatchingAnyCapacityOfActivity(JobActivityLabel userActivity, JobActivityLabel otherActivity, boolean considerInducedCapacities) {
        var capacities = considerInducedCapacities ? userActivity.getInducedCapacitiesRecursively(true) : userActivity.getInducedCapacities();
        return !Sets.intersection(Sets.newHashSet(capacities), otherActivity.getInducedCapacities()).isEmpty();
    }

    public static UserExperience forOccupation(
            ErhgoOccupation occupation,
            String title,
            UserProfile userProfile
    ) {
        if (occupation != null) {
            userProfile.addCapacities(occupation.getAllCapacitiesWithDuplicates());
        }
        return builder()
                .userProfile(userProfile)
                .erhgoOccupation(occupation)
                .type(ExperienceType.JOB)
                .jobTitle(title)
                .build();

    }


    public List<Capacity> getAllCapacitiesWithDuplicates() {
        return getJobActivityLabelStream().flatMap(a -> a.getInducedCapacities().stream()).toList();
    }

    public List<JobActivityLabel> getEssentialActivities() {
        if (erhgoOccupation == null || erhgoOccupation.getOccupationActivities() == null) {
            return Collections.emptyList();
        }
        return erhgoOccupation.getOccupationActivities().stream().filter(OccupationActivity::isEssential)
                .map(OccupationActivity::getActivity)
                .toList();
    }

}
