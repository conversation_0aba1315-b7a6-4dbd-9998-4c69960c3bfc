package com.erhgo.domain.userprofile.criteria;

import com.erhgo.domain.criteria.Criteria;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.CriteriaQuestionType;
import com.erhgo.domain.exceptions.ThresholdValueCanNotBeUnselected;
import com.erhgo.domain.userprofile.UserProfile;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serializable;
import java.util.UUID;

@Entity
@EqualsAndHashCode(of = {"id", "selected"})
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UserCriteriaValue {
    @Embeddable
    @EqualsAndHashCode
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    @AllArgsConstructor(access = AccessLevel.PUBLIC)
    public static class ID implements Serializable {

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private String valueCode;

        @NotNull
        @Getter(AccessLevel.PRIVATE)
        private UUID userProfileId;
    }

    @EmbeddedId
    private UserCriteriaValue.ID id;

    @MapsId("valueCode")
    @ManyToOne(optional = false)
    @Getter
    private CriteriaValue value;

    @MapsId("userProfileId")
    @ManyToOne(optional = false)
    @Getter
    private UserProfile userProfile;

    @Getter
    @Setter
    private boolean selected;

    public UserCriteriaValue(UserProfile userProfile, CriteriaValue criteriaValue, boolean selected) {
        if (!selected && criteriaValue.getCriteria().getQuestionType() == CriteriaQuestionType.THRESHOLD) {
            throw new ThresholdValueCanNotBeUnselected(criteriaValue.getCode());
        }
        this.id = new ID(criteriaValue.getCode(), userProfile.uuid());
        this.selected = selected;
        this.userProfile = userProfile;
        this.value = criteriaValue;
    }

    public Criteria getCriteria() {
        return this.getValue().getCriteria();
    }
}
