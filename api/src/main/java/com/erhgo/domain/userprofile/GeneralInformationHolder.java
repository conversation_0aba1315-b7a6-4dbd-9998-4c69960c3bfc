package com.erhgo.domain.userprofile;

import com.erhgo.domain.enums.ContactTime;
import com.erhgo.domain.enums.Situation;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

public interface GeneralInformationHolder {

    String getUserId();

    String getPhoneNumber();

    Location getLocation();

    default ContactTime getContactTime() {
        return getContactTimeOrdinal() == null ? null : ContactTime.values()[getContactTimeOrdinal()];
    }

    Integer getContactTimeOrdinal();

    Set<String> getChannels();

    LocalDate getBirthDate();

    Integer getSalary();

    Situation getSituation();

    Boolean getTransactionalBlacklisted();

    Boolean getSmsBlacklisted();

    LocalDateTime getLastConnectionDate();

    LocalDateTime getLastMobileConnectionDate();

    String getSource();

    Integer getDelayInMonth();

    Integer getRadiusInKm();
}
