package com.erhgo.domain.userprofile;

import com.erhgo.domain.dto.EmailVerificationResultDTO;
import com.google.common.annotations.VisibleForTesting;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;

import static java.time.OffsetDateTime.now;


@Embeddable
@Accessors(chain = true)
public class MailVerification {

    @VisibleForTesting
    public static final int MAX_NUMBER_OF_VERIFICATION_PER_ACCOUNT = 3;
    private static final Duration MIN_UNKNOWN_DURATION = Duration.of(6, ChronoUnit.HOURS);


    public enum MailVerificationState {
        REQUIRES_VERIFICATION, VERIFIED, UNKNOWN, FORCED, TOO_MANY_TRIES
    }

    @VisibleForTesting
    @Setter
    @Getter
    private int numberOfVerificationsForAccount = 0;

    @Getter
    private OffsetDateTime lastVerificationDate;

    @Getter
    @Setter
    @Enumerated(EnumType.STRING)
    private MailVerificationState state = MailVerificationState.REQUIRES_VERIFICATION;

    public boolean requiresConfirmation() {
        return state == MailVerificationState.REQUIRES_VERIFICATION;
    }

    public void updateWith(EmailVerificationResultDTO verificationResult) {
        numberOfVerificationsForAccount++;
        lastVerificationDate = now();
        state = switch (verificationResult.getEmailStatus()) {
            case VALID -> MailVerificationState.VERIFIED;
            case VERIFIER_ERROR, UNKNOWN -> MailVerificationState.UNKNOWN;
            case INVALID_MAIL, INVALID_SERVER -> numberOfVerificationsForAccount < MAX_NUMBER_OF_VERIFICATION_PER_ACCOUNT ? MailVerificationState.REQUIRES_VERIFICATION : MailVerificationState.TOO_MANY_TRIES;
        };
    }

    public boolean requiresVerificationNow() {
        return state == MailVerificationState.UNKNOWN && (lastVerificationDate == null || Duration.between(lastVerificationDate, now()).compareTo(MIN_UNKNOWN_DURATION) > 0);
    }

    public void markAsForced() {
        this.state = MailVerificationState.FORCED;
    }
}
