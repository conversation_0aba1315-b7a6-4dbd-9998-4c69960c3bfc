package com.erhgo.domain.userprofile;

import com.erhgo.domain.enums.HardSkillType;
import com.erhgo.services.generation.dto.TitleAndDescription;
import com.erhgo.utils.ChecksumUtils;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Embeddable;
import jakarta.persistence.OrderColumn;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.time.OffsetDateTime;
import java.util.*;

@Embeddable
@NoArgsConstructor
@Accessors(fluent = true)
@EqualsAndHashCode
@ToString
@Getter
public class UserExperienceGeneratedData {

    private String attitude;

    @ElementCollection
    private Set<String> hashtags = new HashSet<>();

    @ElementCollection
    private Map<HardSkillType, String> hardSkills = new HashMap<>();

    @ElementCollection
    @OrderColumn
    private List<SoftSkill> softSkills = new ArrayList<>();

    private String checksum;

    private OffsetDateTime attitudeModifiedByUserInstant;

    private boolean isAttitudeModifiedByUser() {
        return attitudeModifiedByUserInstant != null && StringUtils.isNotBlank(attitude);
    }

    public boolean requiresAttitudeGeneration() {
        return StringUtils.isBlank(attitude);
    }

    public boolean refreshChecksum(Set<UUID> experiencesId) {
        var calculatedChecksum = ChecksumUtils.calculateChecksum(experiencesId);
        var modified = checksum == null || !checksum.equals(calculatedChecksum);
        checksum = calculatedChecksum;
        return modified;
    }

    public void resetIfRequired(Set<UUID> experiencesId) {
        if (refreshChecksum(experiencesId)) {
            if (!isAttitudeModifiedByUser()) {
                attitude = null;
            }
            softSkills = new ArrayList<>();
            hashtags = new HashSet<>();
        }
    }

    public void updateAttitude(String updatedDescription, boolean forcedByUser) {
        attitude = updatedDescription;
        if (forcedByUser) {
            attitudeModifiedByUserInstant = OffsetDateTime.now();
        }
        if (!forcedByUser || StringUtils.isBlank(updatedDescription)) {
            attitudeModifiedByUserInstant = null;
        }
    }

    public void setGeneratedAttitude(String updatedDescription) {
        updateAttitude(updatedDescription, false);
    }

    public UserExperienceGeneratedData hashtags(Set<String> hashtags) {
        if (this.hashtags != null) {
            this.hashtags.removeIf(h -> !hashtags.contains(h));
        } else {
            this.hashtags = new HashSet<>();
        }
        this.hashtags.addAll(hashtags);
        return this;
    }

    public void setForcedAttitude(String forcedAttitude) {
        updateAttitude(forcedAttitude, true);
    }

    public UserExperienceGeneratedData generatedSoftSkillDescription(List<TitleAndDescription> generatedDescriptions) {
        if (this.softSkills == null) {
            this.softSkills = new ArrayList<>();
        } else {
            this.softSkills.clear();
        }
        generatedDescriptions.forEach(kv -> softSkills.add(new SoftSkill(kv.title(), kv.description())));
        return this;
    }

    public void resetHardSkills(Map<HardSkillType, String> hardSkills) {
        this.hardSkills.clear();
        this.hardSkills.putAll(hardSkills);
    }
}
