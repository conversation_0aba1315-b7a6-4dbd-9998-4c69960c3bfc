package com.erhgo.domain.recruitment;

import com.erhgo.domain.enums.AcquisitionModality;
import com.erhgo.domain.referential.Context;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.util.UUID;

@Data
@Entity
@Builder
@ToString(exclude = "recruitmentProfile")
@EqualsAndHashCode(exclude = "recruitmentProfile")
@NoArgsConstructor
@AllArgsConstructor
public class OptionalContext {

    @Id
    @GenericGenerator(name = "UseUuidOrGenerate", type = com.erhgo.domain.utils.UseUuidOrGenerate.class)
    @GeneratedValue(generator = "UseUuidOrGenerate")
    @Column(columnDefinition = "BINARY(16)")
    private UUID id;

    @ManyToOne
    private RecruitmentProfile recruitmentProfile;

    @ManyToOne
    private Context context;

    @NotNull
    private AcquisitionModality acquisitionModality;
}
