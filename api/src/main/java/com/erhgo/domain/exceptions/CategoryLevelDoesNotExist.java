package com.erhgo.domain.exceptions;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CategoryLevelDoesNotExist extends AbstractFunctionalException {
    private final Long categoryLevelId;

    public CategoryLevelDoesNotExist(Long categoryLevelId) {
        super("CategoryLevel #" + categoryLevelId + " does not exist");
        this.categoryLevelId = categoryLevelId;
    }
}
