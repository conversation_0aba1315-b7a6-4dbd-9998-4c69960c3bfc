package com.erhgo.domain.sourcing;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;

import java.time.LocalDate;
import java.time.temporal.ChronoField;
import java.util.UUID;

@Entity
@Table(uniqueConstraints = @UniqueConstraint(columnNames = "userId"))
@Accessors(fluent = true)
@Getter
@Setter
@ToString
public class SourcingPreferences {

    public boolean isNotImmediate() {
        return mailFrequency != MailFrequency.IMMEDIATELY;
    }

    public enum MailFrequency {
        IMMEDIATELY,
        DAILY,
        WEEKLY,
        NEVER
    }

    private static final SourcingPreferences DEFAULT_PREFERENCE = new SourcingPreferences().mailFrequency(MailFrequency.IMMEDIATELY).notifyOnSpontaneousCandidature(true);

    public static SourcingPreferences fromDefault(String userId) {
        return new SourcingPreferences().isoWeekDay(DEFAULT_PREFERENCE.isoWeekDay).mailFrequency(DEFAULT_PREFERENCE.mailFrequency).userId(userId);
    }

    public boolean isActiveToday() {
        if (mailFrequency == MailFrequency.NEVER) {
            return false;
        }
        var todayIsoWeekDay = LocalDate.now().get(ChronoField.DAY_OF_WEEK);
        return mailFrequency == MailFrequency.DAILY || (mailFrequency == MailFrequency.WEEKLY && isoWeekDay != null && isoWeekDay.equals(todayIsoWeekDay));
    }

    public boolean hasRefuseNotification() {
        return mailFrequency == MailFrequency.NEVER;
    }

    @Id
    @GenericGenerator(name = "UseUuidOrGenerate", type = com.erhgo.domain.utils.UseUuidOrGenerate.class)
    @GeneratedValue(generator = "UseUuidOrGenerate")
    @Column(columnDefinition = "BINARY(16)")
    private UUID uuid;

    @NotNull
    @Column(nullable = false)
    private String userId;
    private Integer isoWeekDay;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @NotNull
    private MailFrequency mailFrequency;

    private boolean notifyOnSpontaneousCandidature;

}
