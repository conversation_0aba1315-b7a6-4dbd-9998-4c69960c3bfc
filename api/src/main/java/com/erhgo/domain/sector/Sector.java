package com.erhgo.domain.sector;

import com.erhgo.domain.AbstractAuditableEntity;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import java.util.UUID;

@Entity
@Table(uniqueConstraints = @UniqueConstraint(name = "UK_SECTOR_CODE", columnNames = "code"))
@Getter
@EqualsAndHashCode(callSuper = false)
@ToString
public class Sector extends AbstractAuditableEntity {

    private static final String ABBREVIATION_FOR_CUSTOM_SECTOR = "?";
    @Id
    @GenericGenerator(name = "UseUuidOrGenerate", type = com.erhgo.domain.utils.UseUuidOrGenerate.class)
    @GeneratedValue(generator = "UseUuidOrGenerate")
    @Column(columnDefinition = "BINARY(16)")
    private UUID uuid;
    private String code;
    private String label;
    private String abbreviation;

}
