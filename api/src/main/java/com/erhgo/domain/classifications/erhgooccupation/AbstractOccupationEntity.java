package com.erhgo.domain.classifications.erhgooccupation;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.classifications.esco.EscoSkill;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.util.Set;
import java.util.UUID;
import java.util.function.Predicate;

@Data
@NoArgsConstructor
@Entity
@ToString(callSuper = true, exclude = "occupation")
@EqualsAndHashCode(callSuper = true, exclude = "occupation")
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
public abstract class AbstractOccupationEntity extends AbstractAuditableEntity {
    @Id
    @GenericGenerator(name = "UseUuidOrGenerate", type = com.erhgo.domain.utils.UseUuidOrGenerate.class)
    @GeneratedValue(generator = "UseUuidOrGenerate")
    @Column(columnDefinition = "BINARY(16)")
    private UUID id;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private OccupationQualificationSource source;

    @ManyToOne(optional = false)
    @Getter
    @Setter(value = AccessLevel.PACKAGE)
    private ErhgoOccupation occupation;

    protected AbstractOccupationEntity(OccupationQualificationSource source, ErhgoOccupation occupation) {
        this.source = source;
        this.occupation = occupation;
    }

    public void setState(MandatoryState mandatoryState) {
        throw new IllegalStateException();
    }

    public abstract UUID getEntityUuid();

    public void remove() {
        getOccupation().getEntities().removeIf(Predicate.isEqual(this));
        this.setOccupation(null);
    }

    public boolean shouldRemoveFromOccupation() {
            return getSource() == OccupationQualificationSource.SKILL
                    && !hasSkillWithEntity(occupation.getSkills());

    }

    protected boolean hasSkillWithEntity(Set<EscoSkill> skills) {
        return !filterSkillsWithEntity(skills).isEmpty();
    }

    protected abstract Set<EscoSkill> filterSkillsWithEntity(Set<EscoSkill> skills);
}
