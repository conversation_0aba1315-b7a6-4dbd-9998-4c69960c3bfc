package com.erhgo.domain.classifications.erhgooccupation;

import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.referential.Behavior;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import lombok.*;

import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Entity
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OccupationBehavior extends AbstractOccupationEntity {

    @ManyToOne(optional = false)
    @Getter
    private Behavior behavior;

    @Builder
    public OccupationBehavior(Behavior behavior, ErhgoOccupation occupation, OccupationQualificationSource occupationQualificationSource) {
        super(occupationQualificationSource, occupation);
        this.behavior = behavior;
    }


    @Override
    public Set<EscoSkill> filterSkillsWithEntity(Set<EscoSkill> skills) {
        return skills.stream().filter(s -> s.getBehaviors().stream()
                .anyMatch(c -> c.getId().equals(behavior.getId())))
                .collect(Collectors.toSet());
    }

    @Override
    public UUID getEntityUuid() {
        return behavior.getId();
    }


    @Override
    public void remove() {
        var occupationToUpdate = this.getOccupation();
        super.remove();
        occupationToUpdate.updateBehaviorCategories();
    }
}
