package com.erhgo.domain.classifications.erhgooccupation;

import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
public abstract class AbstractMandatoryOccupationEntity extends AbstractOccupationEntity {

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private MandatoryState state;

    protected AbstractMandatoryOccupationEntity(OccupationQualificationSource source, ErhgoOccupation occupation) {
        super(source, occupation);
        this.state = MandatoryState.OPTIONAL;
    }

    public boolean isEssential() {
        return state == MandatoryState.ESSENTIAL;
    }
}
