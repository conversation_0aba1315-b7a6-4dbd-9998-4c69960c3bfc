package com.erhgo.domain.classifications.erhgo;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Entity
@EqualsAndHashCode
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@Builder
public class ErhgoClassification implements Comparable<ErhgoClassification> {
    @Id
    private String code;

    @NotNull
    @Column(nullable = false)
    private String title;

    @NotNull
    private int orderIndex;

    private String icon;

    private boolean highPriority;

    @Override
    public int compareTo(ErhgoClassification o) {
        return orderIndex - o.orderIndex;
    }
}
