package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractEntity;
import com.erhgo.domain.exceptions.InvalidCommandException;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;

@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@Entity
@Table(name = "Organization")
public abstract class AbstractOrganization extends AbstractEntity {
    @EqualsAndHashCode.Include
    private String siren;
    @EqualsAndHashCode.Include
    private String siret;
    @EqualsAndHashCode.Include
    private String address;
    @EqualsAndHashCode.Include
    private Double latitude;
    @EqualsAndHashCode.Include
    private Double longitude;
    @EqualsAndHashCode.Include
    private boolean privateUsers;
    @EqualsAndHashCode.Include
    private boolean privateJobs;
    @EqualsAndHashCode.Include
    private boolean internal;
    @EqualsAndHashCode.Include
    private boolean mandatoryIdentity;
    @Column(columnDefinition = "LONGTEXT")
    private String gdprMention;
    @Getter(AccessLevel.PRIVATE)
    private String externalUrl;
    @Getter(AccessLevel.PRIVATE)
    private String forcedUrl;

    @Enumerated(EnumType.STRING)
    private SiretVerificationStatus siretVerificationStatus;

    @Column(columnDefinition = "LONGTEXT")
    private String description = "";

    protected AbstractOrganization() {
        super();
    }

    AbstractOrganization(String siren, String siret, String address, Double latitude, Double longitude, String title, String description, String forcedUrl, boolean privateUsers, boolean privateJobs, boolean internal, boolean mandatoryIdentity) {
        super();
        this.siren = siren;
        this.siret = siret;
        this.address = address;
        this.latitude = latitude;
        this.longitude = longitude;
        this.privateUsers = privateUsers;
        this.privateJobs = privateJobs;
        this.internal = internal;
        this.mandatoryIdentity = mandatoryIdentity;
        this.forcedUrl = forcedUrl;
        setTitle(title);
        setDescription(description);
    }

    public boolean mayHaveConsortiums() {
        return false;
    }

    public void replaceConsortiums(Collection<Recruiter> consortiums) {
        if (consortiums != null && !consortiums.isEmpty()) {
            throw new IllegalStateException("Only Employer may have consortiums");
        }
    }

    public void setRefererRecruiter(Recruiter refererRecruiter) {
        if (refererRecruiter != null) {
            throw new InvalidCommandException("Only Employer may have referer");
        }
    }

    public void setDefaultProject(Recruiter defaultProject) {
        if (defaultProject != null) {
            throw new InvalidCommandException("Only Recruiter may have default project");
        }
    }

    public void setRefusalEmailTemplate(CustomEmailTemplate customEmailTemplate) {
        if (customEmailTemplate != null) {
            throw new InvalidCommandException("Only Recruiter may have custom email template");
        }
    }


    public enum OrganizationType {
        ENTERPRISE("E-"),
        TERRITORIAL("T-"),
        CONSORTIUM("C-"),
        PROJECT("P-"),
        SOURCING("S-");
        @Getter
        private final String prefix;

        OrganizationType(String prefix) {
            this.prefix = prefix;
        }

        public boolean mayHaveEmployer() {
            return this == TERRITORIAL || this == CONSORTIUM || this == PROJECT;
        }
    }

    public enum SiretVerificationStatus {
        SUCCESS,
        WRONG_SIRET,
        TECHNICAL_ERROR,
    }

    @Override
    public int getSuffixLeftPadCount() {
        return 4;
    }

    public boolean mayHaveProjects() {
        return false;
    }

    public String getOrganizationUrl() {
        return isForcedUrl() ? forcedUrl : externalUrl;
    }

    public boolean isForcedUrl() {
        return !StringUtils.isBlank(getForcedUrl());
    }

    public String getUrl() {
        return isForcedUrl() ? forcedUrl : externalUrl;
    }
}
