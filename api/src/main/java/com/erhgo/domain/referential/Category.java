package com.erhgo.domain.referential;

import com.erhgo.domain.AbstractEntity;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.voodoodyne.jackson.jsog.JSOGGenerator;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.SortedSet;
import java.util.TreeSet;

@Data
@Entity
@NoArgsConstructor
@JsonIdentityInfo(generator = JSOGGenerator.class)
@EqualsAndHashCode(callSuper = true)
public class Category extends AbstractEntity {

    public static final String PREFIX_CODE_CATEGORY = "CCT";
    public static final String KNOWLEDGE_CODE = "CCT-01";
    public static final String PROFESSIONAL_GESTURE_CODE = "CCT-02";

    @OneToMany(mappedBy = "category", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private SortedSet<CategoryLevel> levels = new TreeSet<>();

    @Override
    public String getPrefix() {
        return PREFIX_CODE_CATEGORY;
    }

    @Override
    public int getSuffixLeftPadCount() {
        return 2;
    }
}
