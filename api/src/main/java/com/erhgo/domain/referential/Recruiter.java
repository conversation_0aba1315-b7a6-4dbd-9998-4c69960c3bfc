package com.erhgo.domain.referential;

import jakarta.persistence.*;
import lombok.*;

import java.util.Collection;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Entity
@ToString(exclude = {"defaultProject", "refusalEmailTemplate"}, callSuper = true)
@DiscriminatorValue("recruiter")
@NoArgsConstructor
@EntityListeners(RecruiterListener.class)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class Recruiter extends AbstractOrganization {
    public static final Collection<String> PREFIXES = Stream.of(OrganizationType.values())
            .map(OrganizationType::getPrefix)
            .collect(Collectors.toSet());

    @Enumerated(EnumType.STRING)
    @Setter(AccessLevel.PRIVATE)
    @Getter
    @EqualsAndHashCode.Include
    private OrganizationType organizationType;

    @OneToOne(fetch = FetchType.LAZY)
    @Getter
    private Recruiter defaultProject;

    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Getter
    private CustomEmailTemplate refusalEmailTemplate;

    @Override
    public String getPrefix() {
        return organizationType.name().substring(0, 1);
    }

    @Override
    public void setDefaultProject(Recruiter defaultProject) {
        this.defaultProject = defaultProject;
    }

    @Override
    public void setRefusalEmailTemplate(CustomEmailTemplate customEmailTemplate) {
        this.refusalEmailTemplate = customEmailTemplate;
    }

    @Override
    public boolean mayHaveProjects() {
        return organizationType == OrganizationType.CONSORTIUM || organizationType == OrganizationType.TERRITORIAL || organizationType == OrganizationType.SOURCING;
    }

    @Builder(builderMethodName = "recruiterBuilder")
    public Recruiter(String siren,
                     String siret,
                     String address,
                     Double latitude,
                     Double longitude,
                     OrganizationType organizationType,
                     String code,
                     String title,
                     String description,
                     String forcedUrl,
                     boolean privateUsers,
                     boolean privateJobs,
                     boolean internal,
                     boolean mandatoryIdentity,
                     Recruiter defaultProject,
                     CustomEmailTemplate refusalEmailTemplate) {
        super(siren, siret, address, latitude, longitude, title, description, forcedUrl, privateUsers, privateJobs, internal, mandatoryIdentity);
        this.setCode(code);
        this.setDefaultProject(defaultProject);
        this.organizationType = organizationType == null ? OrganizationType.ENTERPRISE : organizationType;
        this.setRefusalEmailTemplate(refusalEmailTemplate);
    }

    public boolean mayHaveEmployer() {
        return organizationType.mayHaveEmployer();
    }

    public boolean isInvalidSiret() {
        return getSiretVerificationStatus() != AbstractOrganization.SiretVerificationStatus.SUCCESS;
    }
}
