package com.erhgo.domain.referential;

import com.google.common.collect.Sets;
import jakarta.persistence.*;
import lombok.*;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Entity
@ToString(callSuper = true)
@DiscriminatorValue("employer")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Employer extends AbstractOrganization {

    @ManyToOne(fetch = FetchType.LAZY)
    @Getter
    @Setter
    private Recruiter refererRecruiter;

    @ManyToMany
    private Set<Recruiter> consortiums;

    @Builder(builderMethodName = "employerBuilder")
    public Employer(String siren,
                    String siret,
                    String address,
                    Double latitude,
                    Double longitude,
                    Recruiter refererRecruiter,
                    String title,
                    String description,
                    String forcedUrl,
                    String code,
                    Collection<Recruiter> consortiums) {
        super(siren, siret, address, latitude, longitude, title, description, forcedUrl, false, false, false, false);
        if (refererRecruiter == null || !refererRecruiter.mayHaveEmployer()) {
            throw new IllegalArgumentException("Unable to create employer on illegal recruiter " + refererRecruiter);
        }
        this.setCode(code);
        this.refererRecruiter = refererRecruiter;
        this.consortiums = consortiums == null ? Sets.newHashSet() : Sets.newHashSet(consortiums);
    }

    @Override
    public String getPrefix() {
        return "M";
    }

    @Override
    public boolean mayHaveConsortiums() {
        return true;
    }

    @Override
    public void replaceConsortiums(Collection<Recruiter> consortiums) {
        if (this.consortiums == null) {
            this.consortiums = new HashSet<>();
        }
        this.consortiums.clear();
        this.consortiums.addAll(consortiums);
    }

    public Collection<Recruiter> getConsortiums() {
        return Collections.unmodifiableSet(consortiums == null ? Collections.emptySet() : consortiums);
    }
}
