package com.erhgo.security.evaluator;

import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.security.PermissionLevel;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class RecruitmentPermissionEvaluator extends AbstractEntityPermissionEvaluator<Recruitment> {

    public RecruitmentPermissionEvaluator(SecurityService securityService) {
        super(Recruitment.class, securityService);
    }

    @Override
    public boolean hasPermission(Authentication authentication, Recruitment recruitment, PermissionLevel permissionLevel) {

        return securityService.hasAnyRole(authentication, recruitment.getRecruiterCode(), recruitment.getEmployerCode()) &&
                securityService.hasAnyRole(authentication, Role.SOURCING);

    }

}
