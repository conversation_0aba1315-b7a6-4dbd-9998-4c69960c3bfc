package com.erhgo.security.evaluator;

import com.erhgo.domain.referential.Recruiter;
import com.erhgo.security.PermissionLevel;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class RecruiterPermissionEvaluator extends AbstractEntityPermissionEvaluator<Recruiter> {

    public RecruiterPermissionEvaluator(SecurityService securityService) {
        super(Recruiter.class, securityService);
    }

    @Override
    public boolean hasPermission(Authentication authentication, Recruiter recruiter, PermissionLevel permissionLevel) {
        return (securityService.hasRole(authentication, Role.SOURCING)
                && securityService.hasRole(authentication, recruiter.getCode()));
    }
}
