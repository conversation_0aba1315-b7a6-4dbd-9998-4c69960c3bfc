package com.erhgo.config;

import com.erhgo.domain.AbstractAuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Entity
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public class ConfigurableProperty extends AbstractAuditableEntity {
    @Id
    private String propertyKey;

    @NotNull
    @Setter
    @Column(nullable = false, columnDefinition = "LONGTEXT")
    private String propertyValue;
}
