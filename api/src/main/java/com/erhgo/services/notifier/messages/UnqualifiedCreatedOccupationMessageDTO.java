package com.erhgo.services.notifier.messages;

import com.erhgo.services.notifier.OccupationCreationSourceType;
import lombok.Getter;

import java.util.UUID;

@Getter
public class UnqualifiedCreatedOccupationMessageDTO extends AbstractNotifierMessageDTO {
    public UnqualifiedCreatedOccupationMessageDTO(UUID occupationId, String reworkTitle, String occupationTitle, OccupationCreationSourceType sourceType) {
        this.text = ":warning: Un nouveau métier a été créé par %s (*qualification non confirmée*) libellé source : %s, ajusté en : %s".formatted(sourceType.getLabel(), occupationTitle, getOccupationLink(reworkTitle, occupationId));
    }
}
