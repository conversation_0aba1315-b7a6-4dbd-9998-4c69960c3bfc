package com.erhgo.services.notifier.messages;

import com.erhgo.domain.recruitment.Recruitment;

import java.util.List;
import java.util.stream.Collectors;

public class SourcingDisabledAccountMessageDTO extends AbstractNotifierMessageDTO {

    public SourcingDisabledAccountMessageDTO(List<Recruitment> recruitmentsToSuspend) {
        this.text = ":lock: %d recrutements suspendus en raison d'organisations n'ayant pas activé leur compte : %s"
                .formatted(recruitmentsToSuspend.size(), recruitmentsToSuspend.stream().map(Recruitment::getRecruiter).map(r -> "%s (%s)".formatted(r.getTitle(), r.getCode())).collect(Collectors.joining(", ")));

    }

}
