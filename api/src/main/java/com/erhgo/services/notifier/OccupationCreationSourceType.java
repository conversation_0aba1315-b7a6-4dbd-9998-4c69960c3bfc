package com.erhgo.services.notifier;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum OccupationCreationSourceType {
    FROM_CV("Import de CV", true),
    FROM_ATS("ATS", false),
    FROM_CSV("Import CSV", false),
    FROM_SOURCING("Sourcing", true),
    FROM_FRONT_OFFICE("FO", true);

    private final String label;
    private final boolean requiresConfirmation;
}
