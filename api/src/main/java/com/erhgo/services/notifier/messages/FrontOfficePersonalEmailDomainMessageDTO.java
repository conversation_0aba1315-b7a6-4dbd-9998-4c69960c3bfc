package com.erhgo.services.notifier.messages;

import lombok.Builder;
import lombok.Getter;

@Getter
public class FrontOfficePersonalEmailDomainMessageDTO extends AbstractNotifierMessageDTO {

    private static final String EMOJI = ":bulb:";

    @Builder(builderMethodName = "builderForPersonalEmailDomain")
    public FrontOfficePersonalEmailDomainMessageDTO(String email, String userId) {
        this.text = "%s Compte FO créé sur un domaine non répertorié - *email : %s* (_id : %s_)".formatted(EMOJI, email, userId);
    }

}
