package com.erhgo.services.notifier;

import com.erhgo.config.SlackProperties;
import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class Notifier {
    private final SlackNotifier slackNotifier;
    private final LogNotifier logNotifier;
    private final SlackProperties slackProperties;

    private final ConcurrentMap<String, ConcurrentLinkedQueue<AbstractNotifierMessageDTO>> messagesByChannel = new ConcurrentHashMap<>();

    private static final int MAX_SLACK_SIZE = 35_000;


    @Async
    public void sendMessage(AbstractNotifierMessageDTO message) {
        var slackUrl = slackProperties.getUrl(message);
        if (Strings.isNullOrEmpty(slackUrl)) {
            logNotifier.sendMessage(message);
        } else if (message.isImmediate()) {
            slackNotifier.sendMessage(slackUrl, message);
        } else {
            messagesByChannel
                    .computeIfAbsent(slackUrl, k -> new ConcurrentLinkedQueue<>())
                    .add(message);
        }
    }

    @Scheduled(fixedRate = 10_000)
    public void sendBatchMessages() {
        messagesByChannel.forEach((channel, messagesQueue) -> {
            var aggregatedMessage = messagesQueue.poll();
            if (aggregatedMessage != null) {
                Map<String, ArrayList<String>> grouped = new HashMap<>();
                addToGroups(aggregatedMessage.getText(), grouped);
                AbstractNotifierMessageDTO nextMessage;
                while (grouped.entrySet().stream().mapToInt(kv -> kv.getKey().length() + kv.getValue().stream().mapToInt(s -> s.length() + 2).sum()).sum() < MAX_SLACK_SIZE && (nextMessage = messagesQueue.poll()) != null) {
                    addToGroups(nextMessage.getText(), grouped);
                }
                var result = grouped.entrySet().stream()
                        .map(entry -> entry.getKey() + " " + String.join(", ", entry.getValue()))
                        .collect(Collectors.joining("\n"));
                aggregatedMessage.setText(result);

                slackNotifier.sendMessage(channel, aggregatedMessage);
            }
        });
    }

    private static void addToGroups(String s, Map<String, ArrayList<String>> grouped) {
        var parts = s.split("ATS");
        var leftPart = StringUtils.trimToEmpty(parts[0]);
        var rightPart = StringUtils.trimToEmpty(parts.length > 1 ? parts[1] : null);
        grouped.putIfAbsent(leftPart, new ArrayList<>());
        grouped.get(leftPart).add(rightPart);
    }
}
