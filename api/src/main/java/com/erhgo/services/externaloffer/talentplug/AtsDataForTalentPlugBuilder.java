package com.erhgo.services.externaloffer.talentplug;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.erhgo.services.externaloffer.AtsDataBuilder;
import com.google.common.base.Joiner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class AtsDataForTalentPlugBuilder implements AtsDataBuilder<TalentPlugJob> {

    public ExtractedAtsDataDTO buildAtsData(TalentPlugJob job) {
        return job == null ? null : new ExtractedAtsDataDTO()
                .typeContractInformations(Map.of("contrat", job.getJobContract(), "type", job.getJobType()))
                .occupationTitles(Map.of("métier", job.getOfferTitle()))
                .descriptionParts(Map.of("description", job.getDescription()))
                .organizationDescriptionParts(Map.of("entreprise", job.getOrganizationDescription()))
                .localisationInformations(Map.of("location", job.getLocationIndication()))
                .criteriaRelatedData(Map.of("critères", Joiner.on(",").join(job.getCriterias())))
                .salaryRelatedData(Map.of("salary", Joiner.on(",").join(job.getSalaryValues())))
                .otherInformations(Map.of())
                ;
    }

}
