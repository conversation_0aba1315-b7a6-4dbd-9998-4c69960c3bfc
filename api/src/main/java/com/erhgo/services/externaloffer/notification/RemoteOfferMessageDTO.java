package com.erhgo.services.externaloffer.notification;

import com.erhgo.domain.externaloffer.ExternalOffer;
import com.erhgo.services.notifier.messages.AbstractNotifierMessageDTO;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

public class RemoteOfferMessageDTO extends AbstractNotifierMessageDTO {

    public RemoteOfferMessageDTO(RemoteOfferEvent remoteOfferEvent) {
        var sb = new StringBuilder();
        if (!remoteOfferEvent.hasAnyChange()) {
            sb.append("Aucune modification constatée - ATS %s".formatted(remoteOfferEvent.getAtsConfig().getAtsAndConfigCode()));
        } else {
            var plural = remoteOfferEvent.getChangesCount() > 1 ? "s" : "";
            sb.append("%nATS %s : %d modification%s constatée%s%n".formatted(remoteOfferEvent.getAtsConfig().getAtsAndConfigCode(), remoteOfferEvent.getChangesCount(), plural, plural));
            handleCreatedOffers(remoteOfferEvent.getCreatedOffers(), sb);
            handleSuspendedOffers(remoteOfferEvent.getSuspendedOffers(), sb);
            handleReactivatedCreatedOffers(remoteOfferEvent.getReactivatedOffers(), sb);
            handleModifiedOffers(remoteOfferEvent.getModifiedOffers(), sb);
        }
        this.text = sb.toString();
        setForcedSlackChannel(remoteOfferEvent.getForcedSlackChannel());
    }

    private void handleSuspendedOffers(List<ExternalOffer> suspendedOffers, StringBuilder sb) {
        appendOffersLinks("suspendue", suspendedOffers, sb, false, true);
    }

    private void appendOffersLinks(String labelIn, List<ExternalOffer> offersUnsorted, StringBuilder sb, boolean showOfferEditPage, boolean shouldExist) {
        var offers = offersUnsorted.stream().sorted(Comparator.comparing(ExternalOffer::getRemoteId)).toList();
        if (!offers.isEmpty()) {
            var pluralMark = offers.size() > 1 ? "s" : "";
            var label = labelIn + pluralMark;
            sb.append("- *%d offre%s %s* :%n".formatted(offers.size(), pluralMark, label));
            offers.forEach(o -> {
                var url = (showOfferEditPage || o.getRecruitmentId() == null) ? baseSourcingExternalOfferPath.formatted(o.getUuid()) : baseSourcingRecruitmentPath.formatted(o.getRecruitmentId());
                var link = link(url, " %s à %s".formatted(Optional.ofNullable(o.getOfferTitle()).orElse("<?>"), Optional.ofNullable(o.getOfferLocation()).orElse("<?>")));
                var noRecruitmentWarning = shouldExist && o.getRecruitmentId() == null ? " [:warning: Offre sans recrutement]" : "";
                sb.append("    ->%s %s (nom du client: %s)%n".formatted(noRecruitmentWarning, link, Optional.ofNullable(o.getOfferRecruiterCode()).orElse("<?>")));
            });
        }
    }

    private void handleReactivatedCreatedOffers(List<ExternalOffer> reactivatedOffers, StringBuilder sb) {
        appendOffersLinks("republiée", reactivatedOffers, sb, false, true);
    }

    private void handleCreatedOffers(List<ExternalOffer> createdOffers, StringBuilder sb) {
        appendOffersLinks("nouvelle", createdOffers, sb, true, false);
    }

    private void handleModifiedOffers(List<ExternalOffer> modifiedOffers, StringBuilder sb) {
        appendOffersLinks("modifiée", modifiedOffers, sb, true, true);
    }

}
