package com.erhgo.services.externaloffer.notification;

import com.erhgo.services.notifier.Notifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class RemoteOfferSlackNotifier {

    private final Notifier notifier;

    @EventListener(RemoteOfferEvent.class)
    public void publishSlackNotification(RemoteOfferEvent event) {
        notifier.sendMessage(new RemoteOfferMessageDTO(event));
    }
}
