package com.erhgo.services.externaloffer.config;


import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Data
@Accessors(chain = true)
public class AtsSendCandidaturesConfig {

    private String recruiterCode;
    private String candidatureNotificationMail;
    private int delayInMinutes;
    private int numberOfDaysToConsider;
    private String atsCode;
    private String candidatureNotificationUrl;
    private String candidatureNotificationApiKey;
    private String spontaneousCandidatureNotificationEmail;
    private String recruitersProvider;
    private String recruitersProviderConfigCode;
    /**
     * In case of a dedicated request is required to generate a token
     */
    private Authentication tokenAuthentication;
    /**
     * Id allowing ATS to recognize jenesuisPASunCV as candidature provider
     */
    private String trackingId;
    /**
     * Some custom attributes, not shared between ATS
     */
    private Map<String, String> custom;

    @Override
    public String toString() {
        return "AtsSendCandidaturesConfig{" +
                "recruiterCode='" + recruiterCode + '\'' +
                ", candidatureNotificationMail='" + candidatureNotificationMail + '\'' +
                ", delayInMinutes=" + delayInMinutes +
                ", numberOfDaysToConsider=" + numberOfDaysToConsider +
                ", atsCode='" + atsCode + '\'' +
                ", candidatureNotificationUrl='" + candidatureNotificationUrl + '\'' +
                ", candidatureNotificationApiKey='" + StringUtils.substring(candidatureNotificationApiKey, 0, 3) + "..." + '\'' +
                ", spontaneousCandidatureNotificationEmail='" + spontaneousCandidatureNotificationEmail + '\'' +
                ", recruitersProvider='" + recruitersProvider + '\'' +
                ", recruitersProviderConfigCode='" + recruitersProviderConfigCode + '\'' +
                '}';
    }

    public boolean shouldSendSpontaneousCandidatures() {
        return StringUtils.isNotBlank(spontaneousCandidatureNotificationEmail);
    }

}
