package com.erhgo.services.externaloffer.limiter;

import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;

@Service("perAuraInOutLimiter")
@RequiredArgsConstructor
@Slf4j
public class PerAuraInOutLimiter implements ExternalOfferLimiter {

    private final ConfigurablePropertyRepository configurablePropertyRepository;

    protected Integer getPropertyOrDefaults(String subProperty, String... atsCodes) {
        return Optional.ofNullable(configurablePropertyRepository.getPropertyValueOrDefaults("ats.per-aura-in-out.%s".formatted(subProperty), atsCodes))
                .map(Integer::parseInt)
                .orElse(DEFAULT_LIMIT);
    }

    @Override
    public <A extends AbstractRemoteOfferContent<A>> Map<Predicate<A>, Integer> limits(AtsGetOfferConfig config) {
        var atsCode = config.getAtsAndConfigCode();
        var auraCreationLimit = getPropertyOrDefaults("in", atsCode, config.getAtsCode());
        var outAuraCreationLimit = getPropertyOrDefaults("out", atsCode, config.getAtsCode());

        log.debug("For ATS {}: limiting to {} for AURA and {} out AURA", atsCode, auraCreationLimit, outAuraCreationLimit);

        return Map.of(
                AbstractRemoteOfferContent::isInsideAURA,
                auraCreationLimit,
                Predicate.not(AbstractRemoteOfferContent::isInsideAURA),
                outAuraCreationLimit
        );
    }
}
