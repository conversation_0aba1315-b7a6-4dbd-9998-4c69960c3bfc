package com.erhgo.services.externaloffer.config;

import com.erhgo.services.externaloffer.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExternalOfferServiceProvider {

    private final ApplicationContext applicationContext;
    private final Map<String, AbstractATSSynchronizer<?>> services = new ConcurrentHashMap<>();

    public <A extends AbstractRemoteOfferContent<A>> void initializeGenericService(AtsDataBuilder builder,
                                                                                   AtsGetOfferConfig config,
                                                                                   ExternalJobParser<A> parser) {
        log.info("Initializing service for ats - fetch offers: {} ", config);
        var service = applicationContext.getBean(GenericATSSynchronizer.class, builder, config, parser);
        services.put(config.getAtsAndConfigCode(), service);
    }

    public <A extends AbstractRemoteOfferContent<A>> void initializeCustomService(Class<? extends AbstractATSSynchronizer<A>> serviceType, Object... params) {
        log.info("Initializing custom service for ats - fetch offers: {} ", params);
        var service = applicationContext.getBean(serviceType, params);
        services.put(service.getAtsConfig().getAtsAndConfigCode(), service);
    }

    public <A extends AbstractRemoteOfferContent<A>, T extends AbstractATSSynchronizer<A>> T getService(AtsGetOfferConfig config) {
        return (T) services.get(config.getAtsAndConfigCode());
    }

    public Collection<AbstractATSSynchronizer<?>> getAllServicesScheduledHavingSecondPass() {
        return services.values().stream()
                .filter(abstractATSSynchronizer -> abstractATSSynchronizer.getAtsConfig().getIsSinglePassPerDay() != Boolean.TRUE)
                .toList();
    }

    public Collection<AbstractATSSynchronizer<?>> getAllServicesScheduled() {
        return services.values();
    }
}
