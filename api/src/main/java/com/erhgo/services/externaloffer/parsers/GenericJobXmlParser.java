package com.erhgo.services.externaloffer.parsers;

import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.ExternalJobParser;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.deser.std.StringDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

import static com.erhgo.utils.XMLUtils.extractRawXmlNodeSetAtXPath;

@Slf4j
public class GenericJobXmlParser<A extends AbstractRemoteOfferContent<A>> implements ExternalJobParser<A> {
    public class StringTrimModule extends SimpleModule {

        public StringTrimModule() {
            addDeserializer(String.class, new StringDeserializer() {
                @Override
                public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                    return Optional.ofNullable(super.deserialize(p, ctxt)).map(StringUtils::trim).orElse(null);
                }
            });
        }
    }

    protected final XmlMapper xmlMapper = (XmlMapper) new XmlMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .registerModule(new StringTrimModule())
            .registerModule(new JavaTimeModule());

    protected final Class<A> targetClass;
    protected final String rootElementName;
    protected final String atcCode;

    public GenericJobXmlParser(Class<A> targetClass, String rootElementName, String atsCode) {
        this.targetClass = targetClass;
        this.rootElementName = rootElementName;
        this.atcCode = atsCode;
    }

    @Override
    public Page<A> parseJobs(String xmlData, AtsGetOfferConfig config) {
        if (StringUtils.isBlank(xmlData)) {
            log.warn("No XML data found");
            return Page.empty();
        }
        if (config.isPaginated()) {
            throw new UnsupportedOperationException("Pagination not supported yet for XML parser");
        }
        return new PageImpl<>(extractRawXmlNodeSetAtXPath(xmlData, rootElementName).stream()
                .map(this::parseJob)
                .filter(Objects::nonNull)
                .toList());
    }

    public A parseJob(String xmlElement) {
        try {
            return xmlMapper.readValue(xmlElement, targetClass).setRawContent(xmlElement);
        } catch (JsonProcessingException e) {
            log.error("unable to parse {} job for xml {}", atcCode, xmlElement, e);
            return null;
        }
    }

}
