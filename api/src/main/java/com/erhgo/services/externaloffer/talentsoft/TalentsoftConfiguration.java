package com.erhgo.services.externaloffer.talentsoft;

import com.erhgo.services.externaloffer.GenericAtsDataBuilder;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties
public class TalentsoftConfiguration {

    private final GenericAtsDataBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.talentsoft.fetch")
    public List<AtsGetOfferConfig> talentsoftConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.talentsoft.send")
    public List<AtsSendCandidaturesConfig> talentsoftSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public GenericJobJsonParser<TalentsoftJob> talentsoftJobJSONParser(AtsGetOfferConfig config) {
        return new GenericJobJsonParser<>(TalentsoftJob.class, config.getRootPath());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeServices() throws BeansException {
        talentsoftConfigurations().forEach(config ->
                {
                    var talentsoftJobJSONParser = parserProvider.initializeBean(config.getAtsCode(), "talentsoftJobJSONParser", config);
                    serviceProvider.initializeGenericService(
                            atsDataBuilder,
                            config,
                            talentsoftJobJSONParser);
                }
        );
    }
}
