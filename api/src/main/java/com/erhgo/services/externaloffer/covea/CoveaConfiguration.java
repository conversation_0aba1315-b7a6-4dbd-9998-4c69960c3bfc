package com.erhgo.services.externaloffer.covea;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobJsonParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@EnableConfigurationProperties
@ConditionalOnProperty(name = "ats.covea.enabled", havingValue = "true")
@Configuration
public class CoveaConfiguration {
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    @ConfigurationProperties(prefix = "ats.covea.fetch")
    public List<AtsGetOfferConfig> coveaConfigurations() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.covea.send")
    public List<AtsSendCandidaturesConfig> coveaSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeServices() throws BeansException {
        coveaConfigurations().forEach(config -> {
            var coveaParser = parserProvider.initializeBean(config.getAtsCode(), "coveaJobJSONParser");
            serviceProvider.initializeCustomService(CoveaSynchronizer.class, config, coveaParser);
        });
    }

    @Bean
    @Scope("prototype")
    public GenericJobJsonParser<CoveaJob> coveaJobJSONParser() {
        return new GenericJobJsonParser<>(CoveaJob.class, null);
    }

}
