package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
<<<<<<< HEAD
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.codehaus.plexus.util.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ScrappedJob extends AbstractRemoteOfferContent<ScrappedJob> {
    private static final String DATE_PATTERN = "dd-MM-yyyy'T'HH:mm";

    @JsonProperty("url")
    private String id;

    @JsonProperty("title")
    private String offerTitle;

    @JsonProperty("contract")
    private String contractType;

    @JsonProperty("salary")
    private String salary;

    @JsonProperty("location")
    private String location;

    @JsonProperty("job_description")
    private String jobDescription;

    @JsonProperty("organization_description")
    private String organizationDescription;


    @JsonProperty("publication_date")
    @JsonDeserialize(using = FlexibleDateDeserializer.class)
    private LocalDateTime publicationDate;


    @JsonIgnore
    @Override
    public LocalDateTime getLastModificationDate() {
        return null;
    }

    @Override
    public List<String> getCriterias() {
        return List.of();
    }


    @Override
    public String getRemoteRecruiterCode() {
        return Optional.ofNullable(StringUtils.trim(id))
                .map(url -> {
                    try {
                        var uri = new URI(url);
                        return Optional.ofNullable(uri.getScheme())
                                .filter(scheme -> uri.getHost() != null)
                                .map(scheme -> scheme + "://" + uri.getHost())
                                .orElse(null);
                    } catch (URISyntaxException e) {
                        return null;
                    }
                })
                .orElse(null);
    }

    @JsonIgnore
=======
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;


@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ScrappedJob extends AbstractRemoteOfferContent<ScrappedJob> {

    private static final Set<String> FIELDS_TO_IGNORE = Set.of("lastModificationDate", "rawContent");
    private static final Pattern SALARY_PATTERN = Pattern.compile("(\\d+)[kK]");

    private String id;
    private String url;
    private String offerTitle;
    private String contract;
    private String location;
    private String salary;
    private String jobDescription;
    private String organizationDescription;

    @JsonProperty("publication_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime publicationDate;

    private LocalDateTime lastModificationDate = LocalDateTime.now();

    @Override
    protected Set<String> getFieldsToIgnore() {
        return FIELDS_TO_IGNORE;
    }

    @Override
    public String getId() {
        return url;
    }

    @Override
    public LocalDateTime getLastModificationDate() {
        return lastModificationDate;
    }

>>>>>>> chore: refacto + retour review
    @Override
    public List<Integer> getSalaryValues() {
        return Collections.emptyList();
    }

<<<<<<< HEAD
=======

>>>>>>> chore: refacto + retour review
    @Override
    public String getDescription() {
        return jobDescription;
    }

    @Override
<<<<<<< HEAD
    public TypeContractCategoryDTO getTypeContractCategory() {
        if (contractType == null) return null;
        return contractType.contains("CDI") ?
                TypeContractCategoryDTO.PERMANENT :
                TypeContractCategoryDTO.TEMPORARY;
    }

    @Override
    public String getLocationIndication() {
        return location;
    }

    @Override
    public boolean isInFrance() {
        return true;
    }

    @Override
    public String getOrganizationDescription() {
        return organizationDescription;
    }

    @Override
    public boolean shouldNotCreateNewOffer(AtsGetOfferConfig config) {
        return false;
=======
    public String getOrganizationDescription() {
        return organizationDescription;
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        if (StringUtils.isBlank(contract)) {
            return null;
        }

        String normalizedContract = contract.toUpperCase().trim();
        if (normalizedContract.contains("CDI")) {
            return TypeContractCategoryDTO.PERMANENT;
        } else if (normalizedContract.contains("CDD")) {
            return TypeContractCategoryDTO.TEMPORARY;
        }

        return null;
    }

    @Override
    public List<String> getCriterias() {
        return Collections.emptyList();
    }

    @Override
    public String getRemoteRecruiterCode() {
        return "FIRECRAWL";
    }

    @Override
    public String getLocationIndication() {
        return location;
    }

    @Override
    public boolean isInFrance() {
        return StringUtils.isNotBlank(location) && location.contains("FR");
>>>>>>> chore: refacto + retour review
    }

}
