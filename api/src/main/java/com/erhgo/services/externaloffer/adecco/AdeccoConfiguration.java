package com.erhgo.services.externaloffer.adecco;

import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.services.externaloffer.config.AtsSendCandidaturesConfig;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import com.erhgo.services.externaloffer.config.ParserProvider;
import com.erhgo.services.externaloffer.parsers.GenericJobXmlParser;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
@EnableConfigurationProperties
@RequiredArgsConstructor
public class AdeccoConfiguration {

    private final AtsDataForAdeccoBuilder atsDataBuilder;
    private final ExternalOfferServiceProvider serviceProvider;
    private final ParserProvider parserProvider;

    @Bean
    public List<AtsGetOfferConfig> adeccoConfigurations(
            @Value("${ats.adecco.fetch.remoteUrlTemplate}") String urlTemplate,
            @Value("${ats.adecco.fetch.recruiterCode}") String recruiterCode,
            @Value("${ats.adecco.fetch.rootPath}") String rootPath,
            @Value("${ats.adecco.fetch.departments}") String departments,
            @Value("${ats.adecco.forcedSlackChannel}") String forcedSlackChannel
    ) {


        return Arrays.stream(departments.split("\\|"))
                .map(String::toLowerCase)
                .map(c -> new AtsGetOfferConfig()
                        .setAtsCode("adecco")
                        .setRecruiterCode(recruiterCode)
                        .setRemoteUrl(urlTemplate.replace("<DPT>", URLEncoder.encode(c, StandardCharsets.UTF_8)))
                        .setRootPath(rootPath)
                        .setConfigCode(c)
                        .setForcedSlackChannel(forcedSlackChannel)
                ).toList();
    }

    @Bean
    @ConfigurationProperties(prefix = "ats.adecco.send")
    public List<AtsSendCandidaturesConfig> adeccoSendCandidaturesConfig() {
        return new ArrayList<>();
    }

    @Bean
    @Scope("prototype")
    public GenericJobXmlParser<AdeccoJob> adeccoXmlParser(AtsGetOfferConfig config) {
        return new GenericJobXmlParser<>(AdeccoJob.class, config.getRootPath(), config.getAtsCode());
    }


    @Component
    @RequiredArgsConstructor
    public static class AdeccoServiceInitializer {

        private final List<AtsGetOfferConfig> adeccoConfigurations;
        private final ExternalOfferServiceProvider serviceProvider;
        private final AtsDataForAdeccoBuilder atsDataBuilder;
        private final ParserProvider parserProvider;

        @EventListener(ApplicationReadyEvent.class)
        public void initializeServices() throws BeansException {
            adeccoConfigurations.forEach(config ->
                    {
                        var adeccoXmlParser = parserProvider.initializeBean(config.getAtsCode(), "adeccoXmlParser", config);
                        serviceProvider.initializeGenericService(
                                atsDataBuilder,
                                config,
                                adeccoXmlParser);
                    }
            );
        }
    }
}
