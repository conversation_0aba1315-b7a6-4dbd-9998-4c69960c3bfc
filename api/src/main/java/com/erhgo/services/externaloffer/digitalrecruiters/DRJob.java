package com.erhgo.services.externaloffer.digitalrecruiters;


import com.erhgo.openapi.dto.LocationDTO;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkContractDurationUnitDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Optional.ofNullable;


@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class DRJob extends AbstractRemoteOfferContent<DRJob> {
    private static final Set<String> FIELDS_TO_IGNORE = Set.of("publishedAt", "creationDate", "rawContent");

    public record CustomField(String name, String value) {
    }

    private String reference;
    private LocalDateTime publishedAt;
    private String catchPhrase;
    private String contractType;
    private String contractWorkPeriod;
    private String service;
    private String experienceLevel;
    private String educationLevel;
    private String title;
    private String description;
    private String profile;
    private List<String> skills;
    private LocationDTO builtInLocation;
    private List<CustomField> customFields = new ArrayList<>();
    private String applyUrl;

    @JsonIgnore
    private String entityPublicName;
    @JsonIgnore
    private String entityAround;
    @JsonIgnore
    private String entityManagerSectionTitle;
    @JsonIgnore
    private String entityManagerSectionBody;
    @JsonIgnore
    private String brandName;
    @JsonIgnore
    private String brandDescription;
    @JsonIgnore
    private Float salaryMin;
    @JsonIgnore
    private Float salaryMax;
    @JsonIgnore
    private String salaryKind;
    @JsonIgnore
    private Integer contractDurationMin;
    @JsonIgnore
    private Integer contractDurationMax;
    @JsonIgnore
    private List<String> relatedUsernames = new ArrayList<>();
    @JsonIgnore
    private boolean isInFrance;

    @JsonProperty("contract_duration")
    private void unpackContractDuration(Map<String, String> contractDuration) {
        this.contractDurationMin = Optional.ofNullable(StringUtils.trimToNull(contractDuration.get("min"))).map(Float::parseFloat).map(Float::intValue).orElse(null);
        this.contractDurationMax = Optional.ofNullable(StringUtils.trimToNull(contractDuration.get("max"))).map(Float::parseFloat).map(Float::intValue).orElse(null);
    }

    @JsonProperty("salary")
    private void unpackSalary(Map<String, String> salary) {
        this.salaryMin = Optional.ofNullable(StringUtils.trimToNull(salary.get("min"))).map(Float::parseFloat).orElse(null);
        this.salaryMax = Optional.ofNullable(StringUtils.trimToNull(salary.get("max"))).map(Float::parseFloat).orElse(null);
        this.salaryKind = salary.get("kind");
    }

    @JsonProperty("entity")
    private void unpackEntity(Map<String, Object> entity) {
        this.entityPublicName = (String) entity.get("public_name");
        this.entityAround = (String) entity.get("around");
        var manager = (Map<String, String>) entity.get("manager");
        if (manager != null) {
            this.entityManagerSectionTitle = manager.get("section_title");
            this.entityManagerSectionBody = manager.get("section_body");
            updateRelatedUser(manager, false);
        }
    }

    @JsonProperty("brand")
    private void unpackBrand(Map<String, Object> brand) {
        this.brandName = (String) brand.get("name");
        this.brandDescription = (String) brand.get("description");
    }
    private void updateRelatedUser(Map<String, String> userMap, boolean isFirst) {
        if (userMap == null) return;
        var userFullname = Stream.of(ofNullable(userMap.get("firstname")), ofNullable(userMap.get("lastname")))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.joining(" "));
        if (!StringUtils.isBlank(userFullname)) {
            if (isFirst && !relatedUsernames.isEmpty()) {
                relatedUsernames.addFirst(userFullname);
            } else {
                relatedUsernames.add(userFullname);
            }
        }
    }

    @JsonProperty("referentRecruiter")
    private void unpackReferentRecruiter(Map<String, String> referentRecruiter) {
        updateRelatedUser(referentRecruiter, true);
    }

    @JsonProperty("address")
    private void unpackAddress(Map<String, Object> address) {
        this.builtInLocation = new LocationDTO();
        var parts = (Map<String, String>) address.get("parts");
        if (parts != null) {
            this.builtInLocation.setCity(parts.get("city"));
            var postcode = parts.get("zip");
            this.builtInLocation.setPostcode(postcode);
            this.builtInLocation.setRegionName(parts.get("state"));
            // Perfectly empiric: Bangkok offer has no county and no zip; some Lyon or La Réunion offer has either no county or no zip :-/
            this.isInFrance = StringUtils.containsAnyIgnoreCase(parts.get("country"), "france", "guadeloupe", "réunion", "martinique");
        }

        var position = (Map<String, String>) address.get("position");
        if (position != null) {
            this.builtInLocation.setLongitude(ofNullable(position.get("lon")).map(Float::parseFloat).orElse(null));
            this.builtInLocation.setLatitude(ofNullable(position.get("lat")).map(Float::parseFloat).orElse(null));
        }

    }

    @Override
    public String getId() {
        return this.reference;
    }

    @Override
    public LocalDateTime getLastModificationDate() {
        return this.publishedAt;
    }

    @Override
    public Collection<String> getFieldsToIgnore() {
        return FIELDS_TO_IGNORE;
    }

    @Override
    public List<Integer> getSalaryValues() {
        // FIXME: todo ; but always empty in JSON :-/
        return Stream.of(salaryMin, salaryMax).filter(Objects::nonNull).map(Float::intValue).toList();
    }

    @Override
    public String getOrganizationDescription() {
        var organizationDescription = new StringJoiner("<p/>");

        if (!StringUtils.isBlank(this.brandDescription)) {
            organizationDescription.add(this.brandDescription);
        }


        if (!StringUtils.isBlank(this.entityManagerSectionTitle) && !StringUtils.isBlank(this.entityManagerSectionBody)) {
            organizationDescription.add("<h3>" + this.entityManagerSectionTitle + "</h3>");
            organizationDescription.add(this.entityManagerSectionBody);
        }

        if (!StringUtils.isBlank(this.entityAround)) {
            organizationDescription.add("<h3>Autour de nous</h3>");
            organizationDescription.add(this.entityAround);
        }

        return organizationDescription.toString();
    }

    @Override
    public WorkContractDurationUnitDTO getWorkContractDurationUnit() {
        return WorkContractDurationUnitDTO.MONTH;
    }

    @Override
    public Integer getWorkContractDuration() {
        var optionalDouble = Stream.of(contractDurationMin, contractDurationMax).filter(Objects::nonNull).mapToInt(Integer::intValue).average();
        if (optionalDouble.isPresent()) {
            return ((Double) optionalDouble.getAsDouble()).intValue();
        }
        return null;
    }

    @Override
    public String getDescription() {
        var jobDescription = new StringJoiner("<br>");

        if (!StringUtils.isBlank(this.catchPhrase)) {
            jobDescription.add("<h3>%s</h3>".formatted(this.catchPhrase));
        }
        if (!StringUtils.isBlank(this.description)) {
            jobDescription.add(this.description);
        }

        if (!StringUtils.isBlank(this.profile)) {
            jobDescription.add("<h3>Profil recherché</h3>");
            jobDescription.add(this.profile);
        }

        if (skills != null && !skills.isEmpty()) {
            jobDescription.add("<h3>Compétences</h3>");
            jobDescription.add(skills.stream()
                    .collect(Collectors.joining("</li><li>", "<ul><li>", "</ul></li>")));
        }

        return jobDescription.toString();
    }

    @Override
    public String getOfferTitle() {
        return title;
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        if (isFreelance()) {
            return TypeContractCategoryDTO.FREELANCE;
        }
        if (contractType != null) {
            if (contractType.toUpperCase().contains("CDD")) {
                return TypeContractCategoryDTO.TEMPORARY;
            }
            if (contractType.toUpperCase().contains("ALT")) {
                return TypeContractCategoryDTO.PRO;
            }
            if (contractType.toUpperCase().contains("CDI")) {
                return TypeContractCategoryDTO.PERMANENT;
            }
        }
        return null;
    }

    @Override
    public List<String> getCriterias() {
        return List.of();
    }

    @Override
    public boolean shouldNotCreateNewOffer(AtsGetOfferConfig config) {
        return super.shouldNotCreateNewOffer(config)
                || isStageContractType()
                || isInternalDiffusionOnly()
                || isFonciaAndNonPermanentContract(config);
    }

    /*
     * Hack: non-permanent contracts have custom questions, not handled at the moment.
     */
    private boolean isFonciaAndNonPermanentContract(AtsGetOfferConfig config) {
        return
                !isFreelance() &&
                        getTypeContractCategory() != TypeContractCategoryDTO.PERMANENT
                        && Optional.ofNullable(config)
                        .map(AtsGetOfferConfig::getConfigCode)
                        .map(String::toUpperCase)
                        .filter(c -> c.contains("FONCIA") || c.contains("EFFICITY"))
                        .isPresent();
    }

    private boolean isFreelance() {
        return Optional.ofNullable(contractType).map(String::toUpperCase).filter(c -> c.contains("FREELANCE")).isPresent();
    }

    @Override
    public String getRemoteRecruiterCode() {
        return brandName;
    }

    @Override
    public String getLocationIndication() {
        return Optional.ofNullable(builtInLocation).filter(l -> StringUtils.isNotBlank(l.getCity())).map(l -> "%s (%s)".formatted(l.getCity(), StringUtils.trimToEmpty(l.getPostcode()))).orElse("");
    }

    private boolean isInternalDiffusionOnly() {
        return StringUtils.isBlank(applyUrl);
    }

    private boolean isStageContractType() {
        return contractType != null && contractType.toLowerCase().contains("stage");
    }

}
