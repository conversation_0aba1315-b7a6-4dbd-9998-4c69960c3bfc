package com.erhgo.services.externaloffer.adecco;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.erhgo.services.externaloffer.AtsDataBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class AtsDataForAdeccoBuilder implements AtsDataBuilder<AdeccoJob> {
    private final ObjectMapper objectMapper;

    public ExtractedAtsDataDTO buildAtsData(AdeccoJob adeccoJob) {
        return adeccoJob == null ? null : new ExtractedAtsDataDTO()
                .typeContractInformations(Map.of("contrat", adeccoJob.getContractType()))
                .occupationTitles(Map.of("occupation", adeccoJob.getOfferTitle()))
                .descriptionParts(Map.of("description", adeccoJob.getDescription()))
                .organizationDescriptionParts(new HashMap<>())
                .localisationInformations(Map.of("ville", adeccoJob.getLocationIndication()))
                .criteriaRelatedData(new HashMap<>())
                .salaryRelatedData(AtsDataBuilder.writeSafely(objectMapper, salaryMap(adeccoJob)))
                ;
    }

    private static @NotNull Map<String, ? extends Serializable> salaryMap(AdeccoJob job) {
        var result = new HashMap<String, Serializable>();
        result.put("min", job.getSalaryMin());
        result.put("max", job.getSalaryMax());
        return result;
    }

}
