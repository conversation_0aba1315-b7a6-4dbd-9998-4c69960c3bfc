package com.erhgo.services.externaloffer.candidatus;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.DiplomaLevel;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkContractDurationUnitDTO;
import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;


@Slf4j
@JacksonXmlRootElement(localName = "Job")
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class CandidatusJob extends AbstractRemoteOfferContent<CandidatusJob> {
    private static final String DATE_PATTERN = "yyyyMMdd";
    private static final Set<String> FIELDS_TO_IGNORE = Set.of("JobDate");

    @JacksonXmlProperty(localName = "JobId")
    private String id;

    @JacksonXmlProperty(localName = "JobDate")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDate jobDate;

    @JacksonXmlProperty(localName = "JobName")
    private String offerTitle;

    @JacksonXmlProperty(localName = "JobItem1Id")
    private String jobItem1Id;

    @JacksonXmlProperty(localName = "JobItem1Name")
    private String jobItem1Name;

    @JacksonXmlProperty(localName = "JobItem2Id")
    private String jobItem2Id;

    @JacksonXmlProperty(localName = "JobItem2Name")
    private String jobContract;

    @JacksonXmlProperty(localName = "JobItem3Id")
    private String jobItem3Id;

    @JacksonXmlProperty(localName = "JobItem3Name")
    private String jobItem3Name;

    @JacksonXmlProperty(localName = "JobItem4Id")
    private String jobItem4Id;

    @JacksonXmlProperty(localName = "JobItem4Name")
    private String jobItem4Name;

    @JacksonXmlProperty(localName = "JobDescription")
    private String jobDescription;

    @JacksonXmlProperty(localName = "JobURL")
    private String offerUrl;

    @JacksonXmlProperty(localName = "JobEmail")
    private String candidatureEmail;

    @JacksonXmlProperty(localName = "JobReference")
    private String jobReference;

    @JacksonXmlProperty(localName = "JobLocationPostalCode")
    private String postcode;

    @JacksonXmlProperty(localName = "JobLocationCity")
    private String city;

    @JacksonXmlProperty(localName = "JobGeneric")
    private JobGeneric jobGeneric;


    @Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class JobGeneric {
        @JacksonXmlProperty(localName = "contrat")
        private String contrat;

        @JacksonXmlProperty(localName = "duree_cdd")
        private String dureeCdd;

        @JacksonXmlProperty(localName = "type_emploi")
        private String typeEmploi;

        @JacksonXmlProperty(localName = "salaire_telquel")
        private String salaireTelquel;

        @JacksonXmlProperty(localName = "salaire_min")
        private String salaireMin;

        @JacksonXmlProperty(localName = "salaire_max")
        private String salaireMax;

        @JacksonXmlProperty(localName = "salaire_periode")
        private String salairePeriode;

        @JacksonXmlProperty(localName = "salaire_devise")
        private String salaireDevise;

        @JacksonXmlProperty(localName = "salaire_cacher")
        private String salaireCacher;

        @JacksonXmlProperty(localName = "qualification")
        private String qualification;

        @JacksonXmlProperty(localName = "experience")
        private String experience;

        @JacksonXmlProperty(localName = "metier")
        private String metier;

        @JacksonXmlProperty(localName = "mobilite")
        private String mobilite;

        @JacksonXmlProperty(localName = "organisme")
        private String recruiter;
    }

    @Override
    public String getOfferTitle() {
        var trimTitle = StringUtils.trimToEmpty(offerTitle);
        var trimJobItem1Name = StringUtils.trimToEmpty(jobItem1Name);
        return trimTitle.isEmpty() && trimJobItem1Name.isEmpty() ? "" : "%s pour %s".formatted(trimTitle, trimJobItem1Name);
    }

    @Override
    public String getRemoteRecruiterCode() {
        return StringUtils.trimToEmpty(Optional.ofNullable(jobGeneric).map(JobGeneric::getRecruiter).orElse(null));
    }

    @Override
    public String getCandidatureEmail() {
        return StringUtils.trimToEmpty(candidatureEmail);
    }

    @Override
    public LocalDateTime getLastModificationDate() {
        return jobDate.atStartOfDay();
    }


    @Override
    public Set<String> getFieldsToIgnore() {
        return FIELDS_TO_IGNORE;
    }

    @Override
    public List<Integer> getSalaryValues() {
        return Collections.emptyList();
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        return Optional.ofNullable(getJobGeneric())
                .map(generic -> StringUtils.trimToEmpty(generic.getContrat()))
                .map(contrat -> switch (contrat) {
                    case "CDI" -> TypeContractCategoryDTO.PERMANENT;
                    case "CDD" -> TypeContractCategoryDTO.TEMPORARY;
                    default -> null;
                })
                .orElse(null);
    }

    @Override
    public List<String> getCriterias() {
        var criteria = new ArrayList<String>();
        Optional.ofNullable(jobGeneric).map(JobGeneric::getQualification).ifPresent(trimmedEducation -> {
            var valueCodeForDiplomaLevel = switch (trimmedEducation.toUpperCase()) {
                case "CAP", "BEP", "BEP/CAP" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.CAP_BEP);
                case "BAC", "BAC PRO" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_BAC_PRO);
                case "BAC+2" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_2);
                case "BAC+3", "BAC+4", " BAC+3/4", "LICENCE" ->
                        CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_3);
                case "BAC+5", "MASTER" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_5);
                default -> null;
            };
            if (valueCodeForDiplomaLevel != null) {
                criteria.add(valueCodeForDiplomaLevel);
            }
        });
        return criteria;
    }


    @Override
    @JsonIgnore
    public String getLocationIndication() {
        var trimCity = StringUtils.trimToEmpty(city);
        var trimPostcode = StringUtils.trimToEmpty(postcode);
        return trimCity.isEmpty() && trimPostcode.isEmpty() ? "" : "%s (%s)".formatted(trimCity, trimPostcode);
    }


    @Override
    public boolean isInFrance() {
        return true;
    }


    @Override
    public WorkingTimeDTO getWorkingTimeType() {
        return Optional.ofNullable(jobGeneric)
                .map(JobGeneric::getTypeEmploi)
                .map(a -> a.toLowerCase().contains("partiel") ? WorkingTimeDTO.PART_TIME : WorkingTimeDTO.FULL_TIME)
                .orElse(null);
    }


    @Override
    public Integer getWorkContractDuration() {
        return Optional.ofNullable(jobGeneric)
                .map(JobGeneric::getDureeCdd)
                .map(com.erhgo.utils.StringUtils::extractInt)
                .orElse(null);
    }

    @Override
    public WorkContractDurationUnitDTO getWorkContractDurationUnit() {
        if (getWorkContractDuration() == null) {
            return null;
        }
        var duration = jobGeneric.getDureeCdd();
        if (duration.toLowerCase().contains("jour")) {
            return WorkContractDurationUnitDTO.DAY;
        }
        if (duration.toLowerCase().contains("semaine")) {
            return WorkContractDurationUnitDTO.WEEK;
        }
        if (duration.toLowerCase().contains("an")) {
            return WorkContractDurationUnitDTO.YEAR;
        }
        return WorkContractDurationUnitDTO.MONTH;
    }


    @Override
    public String getDescription() {
        var descriptionItems = new ArrayList<String>();
        if (StringUtils.isNotBlank(jobDescription)) {
            descriptionItems.add(jobDescription);
        }
        Optional.ofNullable(jobGeneric).ifPresent(generic -> {
            if (StringUtils.isNotBlank(generic.getExperience())) {
                descriptionItems.add("<strong>Expérience souhaitée&nbsp;: %s</strong>".formatted(generic.getExperience()));
            }
            if (StringUtils.isNotBlank(generic.getQualification())) {
                descriptionItems.add("<strong>Niveau d'étude souhaité&nbsp;: %s</strong>".formatted(generic.getQualification()));
            }
        });
        return String.join("<br/>", descriptionItems);
    }

    @Override
    public String getOrganizationDescription() {
        return "";
    }

}
