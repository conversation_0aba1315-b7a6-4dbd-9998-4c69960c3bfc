package com.erhgo.services.externaloffer;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

public interface AtsDataBuilder<T extends AbstractRemoteOfferContent> {
    ExtractedAtsDataDTO buildAtsData(T job);


    static Map<String, String> writeSafely(ObjectMapper objectMapper, Object toWrite) {
        try {
            return objectMapper.convertValue(toWrite, new TypeReference<>() {
            });
        } catch (IllegalArgumentException e) {
            return Map.of("data", "<En erreur>");
        }
    }
}
