package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.utils.DateTimeUtils;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class FlexibleDateDeserializer extends JsonDeserializer<LocalDateTime> {

    private static final List<DateTimeFormatter> DATE_FORMATTERS = Arrays.asList(
            DateTimeFormatter.ofPattern("dd-MM-yyyy'T'HH:mm"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm"),
            DateTimeFormatter.ofPattern("dd-MM-yyyy"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd")
    );

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        var dateStr = p.getValueAsString();

        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        var trimmedDate = dateStr.trim();

        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                return LocalDateTime.parse(trimmedDate, formatter);
            } catch (DateTimeParseException e) {
                log.debug("Failed to parse date '{}' with formatter '{}': {}", trimmedDate, formatter, e.getMessage());
            }
        }

        try {
            var offsetDateTime = DateTimeUtils.parseDate(trimmedDate);
            if (offsetDateTime != null) {
                log.debug("Successfully parsed date '{}' using DateTimeUtils.parseDate", trimmedDate);
                return offsetDateTime.toLocalDateTime();
            }
        } catch (RuntimeException e) {
            log.debug("DateTimeUtils.parseDate failed for date: {}", trimmedDate, e);
        }

        if (trimmedDate.contains("T") || trimmedDate.contains(" ")) {
            var datePart = trimmedDate.split("[T ]")[0];
            try {
                var parsedDate = LocalDateTime.parse(datePart + "T00:00:00",
                        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
                log.debug("Successfully parsed date part '{}' from original '{}'", datePart, trimmedDate);
                return parsedDate;
            } catch (DateTimeParseException e) {
                try {
                    var parsedDate = LocalDateTime.parse(datePart + "T00:00:00",
                            DateTimeFormatter.ofPattern("dd-MM-yyyy'T'HH:mm:ss"));
                    log.debug("Successfully parsed European date part '{}' from original '{}'", datePart, trimmedDate);
                    return parsedDate;
                } catch (DateTimeParseException e2) {
                    log.debug("Failed to parse date part '{}' with format 'yyyy-MM-dd'T'HH:mm:ss' or 'dd-MM-yyyy'T'HH:mm:ss': {}", datePart, e2.getMessage());
                }
            }
        }

        log.warn("Unable to parse publication date: '{}'. All formats failed, returning null.", trimmedDate);
        return null;
    }
}
