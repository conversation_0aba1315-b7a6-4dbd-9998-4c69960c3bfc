package com.erhgo.services.externaloffer.eolia;

import com.erhgo.openapi.dto.ExtractedAtsDataDTO;
import com.erhgo.services.externaloffer.AtsDataBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AtsDataForEoliaBuilder implements AtsDataBuilder<EoliaJob> {

    public ExtractedAtsDataDTO buildAtsData(EoliaJob eoliaJob) {
        return eoliaJob == null ? null : new ExtractedAtsDataDTO()
                .typeContractInformations(eoliaJob.getTypeContractInformations())
                .occupationTitles(eoliaJob.getOccupationTitles())
                .descriptionParts(eoliaJob.getDescriptionParts())
                .organizationDescriptionParts(eoliaJob.getOrganizationDescriptionParts())
                .localisationInformations(eoliaJob.getLocalisationInformations())
                .criteriaRelatedData(eoliaJob.getCriteriaRelatedData())
                .salaryRelatedData(eoliaJob.getSalaryRelatedData())
                .otherInformations(eoliaJob.getOtherInformations())
                ;
    }

}
