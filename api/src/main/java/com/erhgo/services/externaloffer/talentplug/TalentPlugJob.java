package com.erhgo.services.externaloffer.talentplug;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.DiplomaLevel;
import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkContractDurationUnitDTO;
import com.erhgo.openapi.dto.WorkingTimeDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.erhgo.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;

import static org.apache.commons.lang3.StringUtils.containsIgnoreCase;
import static org.apache.commons.lang3.StringUtils.trimToEmpty;

@Slf4j
@JacksonXmlRootElement(localName = "offer")
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class TalentPlugJob extends AbstractRemoteOfferContent<TalentPlugJob> {
    private static final String DATE_PATTERN = "yyyy-MM-dd";
    private static final Set<String> FIELDS_TO_IGNORE = Set.of("updateDate");

    @JacksonXmlProperty(localName = "offer_keyid")
    private String id;

    @JacksonXmlProperty(localName = "posting_date")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDate postingDate;

    @JacksonXmlProperty(localName = "update_date")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDate updatedDate;

    @JacksonXmlProperty(localName = "expiration_date")
    @JsonFormat(pattern = DATE_PATTERN)
    private LocalDate expirationDate;

    @JacksonXmlProperty(localName = "posting_command")
    private String postingCommand;

    @JacksonXmlProperty(localName = "job_title")
    private String offerTitle;

    @JacksonXmlProperty(localName = "company_description")
    private String organizationDescription;

    @JacksonXmlProperty(localName = "job_description")
    private String jobDescription;

    @JacksonXmlProperty(localName = "applicant_profile")
    private String candidateProfile;

    @JacksonXmlProperty(localName = "applicant_experience")
    private String experience;

    @JacksonXmlProperty(localName = "salary_benefits")
    private String salaryBenefits;

    @JacksonXmlProperty(localName = "applicant_degree")
    private String education;

    @JacksonXmlProperty(localName = "company_name")
    private String remoteRecruiterCode;

    @JacksonXmlProperty(localName = "location_zipcode")
    private String postcode;

    @JacksonXmlProperty(localName = "location_town")
    private String city;

    @JacksonXmlProperty(localName = "location_country")
    private String country;

    @JacksonXmlProperty(localName = "job_type")
    private String jobType;

    @JacksonXmlProperty(localName = "job_contract")
    private String jobContract;

    @JacksonXmlProperty(localName = "job_duration")
    private String duration;

    @JacksonXmlProperty(localName = "salary_min")
    private String salaryMin;

    @JacksonXmlProperty(localName = "salary_max")
    private String salaryMax;

    @JacksonXmlProperty(localName = "salary_periode")
    private String salaryPeriod;

    @JacksonXmlProperty(localName = "teletravail")
    private String remoteWork;

    @JacksonXmlProperty(localName = "type_horaire")
    private String workTime;

    @JacksonXmlProperty(localName = "application_email")
    private String candidatureEmail;

    @Override
    public boolean shouldNotCreateNewOffer(AtsGetOfferConfig config) {
        return super.shouldNotCreateNewOffer(config) || getTypeContractCategory() == null || (postingDate != null && postingDate.isAfter(LocalDate.now()));
    }

    @Override
    public boolean doesModify(TalentPlugJob value) {
        return "edit".equals(postingCommand) && super.doesModify(value);
    }

    @Override
    public Set<String> getFieldsToIgnore() {
        return FIELDS_TO_IGNORE;
    }

    @Override
    public List<Integer> getSalaryValues() {
        return Stream.of(salaryMin, salaryMax)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .map(s -> {
                    try {
                        return Float.parseFloat(s);
                    } catch (NumberFormatException e) {
                        log.warn("Unable to parse salary for TalentPlug offer for value {}", s, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .map(s ->
                        ((Float) (switch (salaryPeriod) {
                            case "HEURE" -> hourlyToYearlySalaryCoefficient();
                            case "MOIS" -> monthlyToYearlySalaryCoefficient();
                            case "SEMAINE" -> weeklyToYearlySalaryCoefficient();
                            default -> 1;
                        } * s)
                        ).intValue()
                )
                .toList();
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        var comparator = StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE;
        return switch (org.apache.commons.lang3.StringUtils.trimToEmpty(jobContract)) {
            case String type when comparator.compare(type, "CDI") == 0 -> TypeContractCategoryDTO.PERMANENT;
            case String type when comparator.compare(type, "CDD") == 0 -> TypeContractCategoryDTO.TEMPORARY;
            case String type when comparator.compare(type, "Alternance") == 0 -> TypeContractCategoryDTO.PRO;
            default -> null;
        };
    }

    @Override
    public List<String> getCriterias() {
        var criteria = new ArrayList<String>();
        var trimmedWorkTime = trimToEmpty(workTime);
        var trimmedEducation = trimToEmpty(education);
        var trimmedRemoteWork = trimToEmpty(remoteWork);

        if (!trimmedRemoteWork.toLowerCase().contains("non") && !trimmedWorkTime.isEmpty()) {
            criteria.add(CriteriaValue.getValueCodeForPartialRemoteWork());
        }

        if (containsIgnoreCase(trimmedWorkTime, "Nuit")) {
            criteria.add(CriteriaValue.NIGHT_WORK_CRITERIA_VALUE_CODE);
        }

        if (containsIgnoreCase(trimmedWorkTime, "Samedi") || containsIgnoreCase(trimmedWorkTime, "Dimanche")) {
            criteria.add(CriteriaValue.WEEKEND_WORK_CRITERIA_VALUE_CODE);
        }
        var valueCodeForDiplomaLevel = switch (trimmedEducation.toUpperCase()) {
            case "BEP/CAP" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.CAP_BEP);
            case "BAC", "BAC PROFESSIONNEL" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_BAC_PRO);
            case "BAC+2" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_2);
            case "BAC+3", "BAC+4" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_3);
            case "BAC+5", "BAC+6" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.BAC_5);
            case "DOCTORAT", "CHERCHEUR" -> CriteriaValue.getValueCodeForDiplomaLevel(DiplomaLevel.DOCTORATE);
            default -> null;
        };
        if (valueCodeForDiplomaLevel != null) {
            criteria.add(valueCodeForDiplomaLevel);
        }
        return criteria;
    }

    @Override
    @JsonIgnore
    public String getLocationIndication() {
        var trimCity = trimToEmpty(city);
        var trimPostcode = trimToEmpty(postcode);
        return trimCity.isEmpty() && trimPostcode.isEmpty() ? "" : "%s (%s)".formatted(trimCity, trimPostcode);
    }

    @Override
    public boolean isInFrance() {
        return "France".equalsIgnoreCase(trimToEmpty(country));
    }

    @Override
    public WorkContractDurationUnitDTO getWorkContractDurationUnit() {
        return getWorkContractDuration() == null ? null : WorkContractDurationUnitDTO.WEEK;
    }

    @Override
    public WorkingTimeDTO getWorkingTimeType() {
        return Optional.ofNullable(jobType)
                .filter(a -> a.toLowerCase().contains("partiel"))
                .map(a -> WorkingTimeDTO.PART_TIME)
                .orElse(WorkingTimeDTO.FULL_TIME);
    }

    @Override
    public Integer getWorkContractDuration() {
        return Optional.ofNullable(org.apache.commons.lang3.StringUtils.trimToNull(duration)).map(a -> {
            try {
                return ((Float) Float.parseFloat(duration)).intValue();
            } catch (NumberFormatException e) {
                log.warn("unable to parse working time duration for TalentPlug: got value {}", duration, e);
                return null;
            }
        }).orElse(null);
    }

    @Override
    public String getDescription() {
        var descriptionItems = new ArrayList<String>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(jobDescription)) {
            descriptionItems.add(jobDescription);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(candidateProfile)) {
            descriptionItems.add("<br/><h3>Profile recherché</h3>");
            descriptionItems.add(candidateProfile);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(education)) {
            descriptionItems.add("<br/>Niveau d'étude souhaité&nbsp;: %s".formatted(education));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(experience)) {
            descriptionItems.add("<br/>Expérience souhaitée&nbsp;: %s".formatted(experience));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(salaryBenefits)) {
            descriptionItems.add("<br/><h3>Avantages salariaux</h3>");
            descriptionItems.add(salaryBenefits);
        }

        return String.join("<br/>", descriptionItems);
    }

    @Override
    public LocalDateTime getLastModificationDate() {
        return Optional.ofNullable(updatedDate).map(LocalDate::atStartOfDay).orElse(null);
    }

}
