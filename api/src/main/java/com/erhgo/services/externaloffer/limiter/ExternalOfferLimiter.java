package com.erhgo.services.externaloffer.limiter;

import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;

import java.util.Map;
import java.util.function.Predicate;

public interface ExternalOfferLimiter {
    int DEFAULT_LIMIT = 5;
    String DEFAULT_KEY = "default";
    
    <A extends AbstractRemoteOfferContent<A>> Map<Predicate<A>, Integer> limits(AtsGetOfferConfig config);
}
