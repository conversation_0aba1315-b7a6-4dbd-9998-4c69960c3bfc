package com.erhgo.services.externaloffer;

import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.externaloffer.config.ExternalOfferServiceProvider;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class ExternalOfferScheduler {

    private final SecurityService securityService;
    private final ExternalOfferServiceProvider externalOfferServiceProvider;

    @Scheduled(cron = "${ats.cronSecondPass}", zone = "Europe/Paris")
    @SchedulerLock(name = "analyzeDataForExternalOffersSecond")
    public void analyzeDataForExternalOffersSecondPass() {
        var services = externalOfferServiceProvider.getAllServicesScheduledHavingSecondPass();
        log.info("Handling external offers for second pass for {} services", services.size());
        services.forEach(this::processForService);

        log.info("External offer handling finished (second pass)");
    }

    @Scheduled(cron = "${ats.cronFirstPass}", zone = "Europe/Paris")
    @SchedulerLock(name = "analyzeDataForExternalOffersFirst")
    public void analyzeDataForExternalOffersFirstPass() {
        var services = externalOfferServiceProvider.getAllServicesScheduled();
        log.info("Handling external offers for first pass {} services", services.size());
        services.forEach(this::processForService);

        log.info("External offer handling finished (first pass)");
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    public void analyzeDataForATS(String atsCode, String customCode) {
        log.info("Handling external offers for {} -- {}", atsCode, customCode);
        var services = externalOfferServiceProvider.getAllServicesScheduledHavingSecondPass().stream().filter(s -> s.getAtsCode().equals(atsCode) && (StringUtils.isBlank(customCode) || StringUtils.trimToEmpty(customCode).equals(s.getAtsConfig().getConfigCode()))).toList();
        log.info("Handling custom external offers for {} services", services.size());
        services.forEach(this::processForService);

        log.info("External offer manual handling finished");

    }

    private void processForService(AbstractATSSynchronizer<?> service) {
        var configCode = service.getAtsConfig().getAtsAndConfigCode();
        log.debug("Updating external offer for {}", configCode);
        securityService.doAsAdmin(service::fetchAndUpdateOffers);
        log.debug("External offer for {} done", configCode);
    }

}
