package com.erhgo.services.externaloffer.recruiterdispatcher;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PerOfferATSConfigurationItemRepository extends JpaRepository<PerOfferATSConfigurationItem, UUID> {


    Optional<PerOfferATSConfigurationItem> findOneByAtsCodeAndRemoteRecruiterCode(String atsCode, String remoteRecruiterCode);

    @Query("""
                FROM PerOfferATSConfigurationItem
                WHERE atsCode=:atsCode
                AND (:configCode IS NULL OR configCode=:configCode)
            """)
    List<PerOfferATSConfigurationItem> findByAtsCodeAndConfigCode(String atsCode, String configCode);

    Optional<PerOfferATSConfigurationItem> findOneByAtsCodeAndConfigCodeAndLocationCode(String atsCode, String configCode, String locationCode);
}
