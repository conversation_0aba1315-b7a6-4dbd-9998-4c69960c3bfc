package com.erhgo.services.externaloffer.covea;


import com.erhgo.openapi.dto.TypeContractCategoryDTO;
import com.erhgo.openapi.dto.WorkContractDurationUnitDTO;
import com.erhgo.services.externaloffer.AbstractRemoteOfferContent;
import com.erhgo.services.recruitmentimporter.converters.FrenchFloatConverter;
import com.erhgo.services.recruitmentimporter.converters.StringQuoteTrimConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.Sets;
import com.opencsv.bean.CsvBindByPosition;
import com.opencsv.bean.CsvCustomBindByPosition;
import com.opencsv.bean.CsvDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;


@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class CoveaJob extends AbstractRemoteOfferContent<CoveaJob> {

    private static final Set<String> FIELDS_TO_IGNORE = Set.of("description", "organizationDescription");

    private static final Set<String> BRANDS = Set.of("MAAF", "MMA", "GMF");

    @CsvCustomBindByPosition(position = 0, converter = StringQuoteTrimConverter.class)
    private String id;

    @CsvBindByPosition(position = 2)
    @CsvDate("dd/MM/yyyy HH:mm")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime lastModificationDate;

    @CsvCustomBindByPosition(position = 4, converter = StringQuoteTrimConverter.class)
    private String remoteRecruiterCode;

    @CsvCustomBindByPosition(position = 5, converter = StringQuoteTrimConverter.class)
    private String status; //Ouvert

    @CsvCustomBindByPosition(position = 6, converter = StringQuoteTrimConverter.class)
    private String description;

    @CsvCustomBindByPosition(position = 7, converter = StringQuoteTrimConverter.class)
    private String contract;

    @CsvCustomBindByPosition(position = 8, converter = StringQuoteTrimConverter.class)
    private String theme;

    @CsvCustomBindByPosition(position = 10, converter = StringQuoteTrimConverter.class)
    private String rawOfferTitle;

    @CsvCustomBindByPosition(position = 11, converter = StringQuoteTrimConverter.class)
    private String postcode;

    @CsvCustomBindByPosition(position = 14, converter = StringQuoteTrimConverter.class)
    private String city;

    @CsvCustomBindByPosition(position = 16, converter = FrenchFloatConverter.class)
    private Float duration;

    private String organizationDescription;

    @Override
    public String getLocationIndication() {
        return "%s %s".formatted(StringUtils.trimToEmpty(city), StringUtils.trimToEmpty(postcode)).trim();
    }

    @Override
    public boolean isInFrance() {
        return true;
    }

    @Override
    public Integer getWorkContractDuration() {
        return Optional.ofNullable(duration).map(Float::intValue).orElse(null);
    }

    @Override
    public WorkContractDurationUnitDTO getWorkContractDurationUnit() {
        return getWorkContractDuration() == null ? null : WorkContractDurationUnitDTO.MONTH;
    }

    @Override
    public List<String> getCriterias() {
        return List.of();
    }

    @Override
    public List<Integer> getSalaryValues() {
        return List.of();
    }

    @Override
    public String getOfferTitle() {
        if (description == null) {
            return rawOfferTitle;
        }

        var descriptionLower = description.toLowerCase();
        var matchingBrands = BRANDS.stream()
                .filter(brand -> descriptionLower.contains(brand.toLowerCase()))
                .toList();

        return matchingBrands.size() == 1
                ? "%s - %s".formatted(rawOfferTitle, matchingBrands.getFirst())
                : rawOfferTitle;
    }

    @Override
    protected Collection<String> getFieldsToIgnore() {
        return Sets.union(new HashSet<>(super.getFieldsToIgnore()), FIELDS_TO_IGNORE);
    }

    @Override
    public TypeContractCategoryDTO getTypeContractCategory() {
        if (contract == null) return null;
        var normalizedContract = com.erhgo.utils.StringUtils.normalizeLowerCase(contract);
        if (normalizedContract.contains("cdd") || normalizedContract.contains(" determine"))
            return TypeContractCategoryDTO.TEMPORARY;
        if (normalizedContract.contains("altern") || contract.toLowerCase().contains("pro"))
            return TypeContractCategoryDTO.PRO;
        if (normalizedContract.contains("stage")) return null;
        return TypeContractCategoryDTO.PERMANENT;
    }
}
