package com.erhgo.services.externaloffer.config;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Authentication {
    private String clientId;
    private String clientSecret;
    private String grantType;
    private String url;
    private Boolean json;
    private String scope;

    public boolean isJson() {
        return Boolean.TRUE.equals(json);
    }


}
