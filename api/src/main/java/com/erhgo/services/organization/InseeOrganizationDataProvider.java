package com.erhgo.services.organization;

import com.erhgo.domain.exceptions.InvalidSiretException;
import com.erhgo.domain.exceptions.SiretValidationTechnicalException;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.PostConstruct;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Duration;
import java.util.Optional;
import java.util.function.Predicate;

@Slf4j
@Service
@ConditionalOnExpression("!T(org.springframework.util.StringUtils).isEmpty('${insee.apiKey:}')")
public class InseeOrganizationDataProvider implements OrganizationDataProvider {

    public static final String ETABLISSEMENT = "etablissement";
    public static final String UNITE_LEGALE = "uniteLegale";
    public static final String DENOMINATION_UNITE_LEGALE = "denominationUniteLegale";

    private static final String FIELD_KEY = "champs";
    private static final String FIELD_VALUE = DENOMINATION_UNITE_LEGALE;

    @Value("${insee.apiKey}")
    private String apiKey;

    @Value("${insee.url}")
    private String baseUrl = "";

    @Value("${insee.timeout-in-ms.connect}")
    private long connectTimeoutInMS;

    @Value("${insee.timeout-in-ms.read}")
    private long readTimeoutInMS;

    @Autowired
    private RestTemplateBuilder restTemplateBuilder;

    @VisibleForTesting
    @Setter
    private RestTemplate restTemplate;

    @PostConstruct
    public void initializeRestTemplate() {
        log.info("Initializing insee api organization data provider with connect timeout {} and read timeout {}", connectTimeoutInMS, readTimeoutInMS);
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(Duration.ofMillis(connectTimeoutInMS))
                .setReadTimeout(Duration.ofMillis(readTimeoutInMS))
                .defaultHeader("X-INSEE-Api-Key-Integration", apiKey)
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    @Override
    @Retryable(
            include = {SiretValidationTechnicalException.class},
            backoff = @Backoff(delay = 1000)
    )
    public String getNameForSiret(String siret) throws SiretValidationTechnicalException, InvalidSiretException {
        var url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .path(siret)
                .queryParam(FIELD_KEY, FIELD_VALUE)
                .encode()
                .toUriString();
        try {
            return Optional.ofNullable(restTemplate.getForObject(url, JsonNode.class))
                    .map(a -> a.get(ETABLISSEMENT))
                    .map(a -> a.get(UNITE_LEGALE))
                    .map(a -> a.get(DENOMINATION_UNITE_LEGALE))
                    .map(JsonNode::asText)
                    .filter(Predicate.not(String::isEmpty))
                    .orElseThrow(InvalidSiretException::new);
        } catch (RestClientException e) {
            if (e instanceof RestClientResponseException rcre) {
                log.warn("Siret verification fails - body: {}, status {}", rcre.getResponseBodyAsString(), rcre.getRawStatusCode(), e);
            } else {
                log.warn("Siret verification fails: {}", e.getMessage(), e);
            }
            if (isTechnicalError(e)) {
                throw new SiretValidationTechnicalException();
            }
            throw new InvalidSiretException();
        }
    }

    private boolean isTechnicalError(RestClientException e) {
        var statusCode = e instanceof RestClientResponseException rcre ? HttpStatus.valueOf(rcre.getRawStatusCode()) : HttpStatus.INTERNAL_SERVER_ERROR;
        return !statusCode.is4xxClientError() || statusCode.equals(HttpStatus.TOO_MANY_REQUESTS);
    }
}
