package com.erhgo.services;

import com.erhgo.domain.AbstractAuditableEntity;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.JobEvaluationState;
import com.erhgo.domain.enums.SourcingCriteriaStep;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidCommandException;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.JobType;
import com.erhgo.domain.recruitment.RecruitmentProfile;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Employer;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.CapacityDTOBuilder;
import com.erhgo.services.dtobuilder.JobDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.erhgo.services.dtobuilder.UserProfileDTOBuilder;
import com.erhgo.services.exporter.PdfExporterService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.userprofile.UserProfileProvider;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.erhgo.utils.DateTimeUtils.ZONE_ID;
import static java.util.Optional.ofNullable;

@Service
@RequiredArgsConstructor
public class JobService {

    private final BehaviorRepository behaviorRepository;
    private final CriteriaRepository criteriaRepository;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final CategoryRepository categoryRepository;
    private final EmployerRepository employerRepository;
    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final JobRepository jobRepository;
    private final RecruiterRepository recruiterRepository;
    private final RecruitmentProfileRepository recruitmentProfileRepository;
    private final RecruitmentRepository recruitmentRepository;
    private final ConfigurablePropertyRepository configurablePropertyRepository;

    private final KeycloakService keycloakService;
    private final SecurityService securityService;

    private final JobDTOBuilder jobDTOBuilder;
    private final UserProfileProvider userProfileProvider;
    private final UserProfileDTOBuilder userProfileDTOBuilder;

    private final PdfExporterService pdfExporterService;

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public JobPageDTO findPaginatedAndFilteredByOrganizationAndProperty(int page, int size, String rawSortProperty, String sortDirection, List<String> organizationCodes, String filter, Boolean strictOrganizationFilter) {
        var pageable = Strings.isNullOrEmpty(rawSortProperty) ? PageRequest.of(page, size) : PageRequest.of(page, size, Sort.Direction.valueOf(sortDirection), decodeSortProperty(rawSortProperty));
        var allCodes = BooleanUtils.isTrue(strictOrganizationFilter) ? new HashSet<>(organizationCodes) : organizationCodes.stream().map(keycloakService::getRolesForGroup).flatMap(Collection::stream).collect(Collectors.toSet());
        Page<Job> result;
        if (filter != null && !filter.isEmpty()) {
            result = jobRepository.findJobs(allCodes, filter, pageable);
        } else {
            result = jobRepository.findByRecruiterCodeInOrEmployerCodeIn(allCodes, pageable);
        }
        return buildJobPageDTO(result);
    }

    private String decodeSortProperty(String rawSortProperty) {
        switch (rawSortProperty) {
            case "employer":
                return "e.title";
            case "recruiter":
                return "r.title";
            default:
                return rawSortProperty;
        }
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<JobSummaryDTO> findAllPublishedJob(String organizationCode) {
        var allCodes = keycloakService.getRolesForGroup(organizationCode);
        return jobRepository.findAllPublishedJob(allCodes).stream().map(jobDTOBuilder::buildSummary).toList();
    }

    private Job findJobOrThrowException(UUID jobId) {
        return jobRepository.findById(jobId).orElseThrow(() -> new EntityNotFoundException(jobId, Job.class));
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void setRecommendation(UUID jobId, String recommendation) {
        var job = findJobOrThrowException(jobId);
        job.setRecommendation(recommendation);
        jobRepository.save(job);
    }

    @Transactional(readOnly = true)
    public JobDetailDTO getJobDetail(UUID jobId) {
        var job = jobRepository.findById(jobId).orElseThrow(() -> new EntityNotFoundException(jobId, Job.class));
        return jobDTOBuilder.buildJobDetail(job, keycloakService.getBackOfficeUserFullnameOrEmpty(job.getCreatedByUserId()), recruitmentProfileRepository.countByJobId(jobId), jobHasNoCandidature(jobId));
    }

    private void ensureJobHasNoCandidature(UUID jobId) {
        if (!recruitmentCandidatureRepository.findDistinctByRecruitmentRecruitmentProfileJobId(jobId).isEmpty()) {
            throw new InvalidCommandException(
                    "CANNOT_DELETE_JOB_WITH_CANDIDATURE",
                    "Unable to delete job " + jobId + ": at least one remaining candidature",
                    "Job deletion"
            );
        }
    }

    private boolean jobHasNoCandidature(UUID jobId) {
        return recruitmentCandidatureRepository.findDistinctByRecruitmentRecruitmentProfileJobId(jobId).isEmpty();
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void deleteJob(UUID jobId) {
        findJobOrThrowException(jobId);
        ensureJobHasNoCandidature(jobId);
        recruitmentRepository.deleteByRecruitmentProfileJobId(jobId);
        recruitmentProfileRepository.deleteByJobId(jobId);
        jobRepository.deleteById(jobId);
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<CapacityAndLevelDTO> getJobCapacities(UUID jobId) {
        var job = jobRepository.findById(jobId).orElseThrow(() -> new EntityNotFoundException(jobId, Job.class));

        var levelPerCapacity = job.getLevelForCapacities();

        return CapacityDTOBuilder.buildCapacityAndLevelDTOS(job.getAllCapacities(), levelPerCapacity);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void reorderMissions(UUID jobId, List<Long> missionsIds) {
        var job = findJobOrThrowException(jobId);

        job.reorderMissions(missionsIds);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public JobDetailDTO createFromErhgoId(UUID erhgoId, String organizationCode, JobTypeDTO jobType) {
        var allCategories = categoryRepository.findAll();

        var occupation = erhgoOccupationRepository.findById(erhgoId)
                .orElseThrow(() -> new EntityNotFoundException(erhgoId, EscoOccupation.class));
        var job = Job.createForErhgoOccupation(occupation, recruiterRepository.findOneByCode(organizationCode), allCategories, JobType.valueOf(jobType.name()));
        job = jobRepository.save(job);
        return jobDTOBuilder.buildJobDetail(job, keycloakService.getBackOfficeUserFullnameOrEmpty(job.getCreatedByUserId()), recruitmentProfileRepository.countByJobId(job.getId()), true);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void saveJob(SaveJobCommandDTO saveJobCommandDTO) {

        var recruiter = recruiterRepository.findOneByCode(saveJobCommandDTO.getRecruiterCode());

        var job = jobRepository.findById(saveJobCommandDTO.getId())
                .orElseGet(() -> Job.builder()
                        .jobType(JobType.valueOf(saveJobCommandDTO.getJobType().name()))
                        .id(saveJobCommandDTO.getId())
                        .recruiter(recruiter)
                        .build());
        jobDTOBuilder.updateJobWithCommand(job, saveJobCommandDTO);
        verifyJobEmployer(job);
        jobRepository.save(job);
    }

    private void verifyJobEmployer(Job job) {
        var recruiter = getRecruiterOrThrow(job);
        if (job.getEmployerCode() != null) {
            var employer = employerRepository.findOneByCode(job.getEmployerCode());
            if (employer == null) {
                throw new EntityNotFoundException(job.getEmployerCode(), Employer.class);
            }
            if (!employer.getRefererRecruiter().equals(recruiter)) {
                throw new InvalidCommandException("WRONG_RECRUITER_OR_EMPLOYER", "Recruiter " + recruiter.getCode() + " is not associated to employer " + employer.getCode(), "Save job");
            }
        }

    }

    private Recruiter getRecruiterOrThrow(Job job) {
        var recruiter = recruiterRepository.findOneByCode(job.getRecruiterCode());
        if (recruiter == null) {
            throw new EntityNotFoundException(job.getRecruiterCode(), Recruiter.class);
        }
        return recruiter;
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void saveBehavior(UUID jobId, List<UUID> behaviorsIds) {
        var job = findJobOrThrowException(jobId);
        var behaviors = Sets.newHashSet(behaviorRepository.findAllById(behaviorsIds));
        if (behaviors.size() != behaviorsIds.size()) {
            behaviorsIds.removeIf(id -> behaviors.stream().anyMatch(b -> b.getId().equals(id)));
            throw new EntityNotFoundException(behaviorsIds, Behavior.class);
        }
        job.setBehaviors(behaviors);
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void publish(UUID jobId, boolean shouldCreateRecruitment, PublishCommandDTO publishCommandDTO) {
        var job = findJobOrThrowException(jobId);
        job.setPublicationDate(OffsetDateTime.now(ZONE_ID));
        job.setState(JobEvaluationState.PUBLISHED);
        if (job.getJobType() == JobType.SIMPLE && recruitmentProfileRepository.countByJobId(job.getId()) == 0 && shouldCreateRecruitment) {
            recruitmentProfileRepository.save(RecruitmentProfile.buildWithAllOptional(job, publishCommandDTO.getRecruitmentProfileCustomQuestion()));
        }
    }


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<UserCandidatureDTO> getUserJobsCandidatures(String userId, String organizationCode) {
        var userProfile = userProfileProvider.getUserProfileOrCreate(userId);
        var organizationCodes = keycloakService.getRolesForGroup(organizationCode);
        var userCandidatures = recruitmentCandidatureRepository.findUserCandidatures(userProfile.userId(), organizationCodes, securityService.isAdmin());
        return userCandidatures.stream()
                .map(candidature -> new UserCandidatureDTO()
                        .organizationCode(candidature.getCodeOfRecruiter())
                        .candidatureId(candidature.getId())
                        .jobTitle(candidature.getJobTitle())
                        .refusedCandidature(candidature.isRefused())
                        .recruiterTitle(candidature.getRecruiterTitle())
                        .employerTitle(candidature.getEmployerTitle())
                        .candidatureState(CandidatureStateDTO.fromValue(candidature.getGlobalCandidatureState().name()))
                        .visibleForUser(candidature.isVisibleForUser())
                        .lastCandidatureNoteDate(candidature.getCandidatureNotes().stream()
                                .max(Comparator.comparing(AbstractAuditableEntity::getUpdatedDate))
                                .map(c -> c.getUpdatedDate().toInstant().atOffset(ZoneOffset.UTC))
                                .orElse(null)))
                .toList();
    }

    MasteryLevel computeMaxJobMasteryLevel(UserProfile userProfile, Float masteryLevelRange) {
        var masteryLevelThreshold = Float.parseFloat(configurablePropertyRepository.findOneByPropertyKey("job_mastery_level_threshold").getPropertyValue());
        // considers job with level at most one grade higher than user level OR considers masteryLevelRange
        return MasteryLevel.forLevel(userProfile.masteryLevel() + (masteryLevelRange == null ? masteryLevelThreshold : masteryLevelRange));
    }

    private JobPageDTO buildJobPageDTO(Page<Job> result) {
        return PageDTOBuilder
                .buildJobPage(result.map(job -> jobDTOBuilder
                        .buildSummaryWithRecruitmentProfile(job, recruitmentProfileRepository.findByJobIdOrderByTitleAsc(job.getId()))));
    }


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<AlreadyAppliedUserDTO> getJobCandidates(UUID jobId) {
        var job = findJobOrThrowException(jobId);

        var candidatures = recruitmentCandidatureRepository.findDistinctByRecruitmentRecruitmentProfileJobId(jobId);
        var roles = keycloakService.getRolesForGroup(job.getRecruiterCode());

        return candidatures
                .stream()
                .map(c -> userProfileDTOBuilder.buildAlreadyAppliedDTO(
                        c.getUserProfile(),
                        roles,
                        keycloakService.getFrontOfficeUserProfile(c.getUserProfile().userId()),
                        getCandidatureId(c.getUserProfile(), candidatures),
                        c.isRefused(),
                        ofNullable(c.getGlobalCandidatureState()).map(Enum::name).map(CandidatureStateDTO::fromValue).orElse(null)
                ))
                .toList();
    }

    private Long getCandidatureId(UserProfile userProfile, List<RecruitmentCandidature> allCandidatures) {
        return allCandidatures.stream()
                .filter(c -> c.getUserProfile().userId().equals(userProfile.userId()))
                .findFirst()
                .map(RecruitmentCandidature::getId)
                .orElse(null);
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.JOB.JOB_WRITE)
    public void updateCriteriaForJob(UUID jobId, List<String> criteriaValueCodes, SourcingCriteriaStepDTO restrictToSourcingStep) {
        var job = findJobOrThrowException(jobId);
        var sourcingCriteriaStep = ofNullable(restrictToSourcingStep).map(a -> SourcingCriteriaStep.valueOf(a.toString())).orElse(null);
        if (!criteriaValueCodes.isEmpty()) {
            var criteria = criteriaRepository.findCriteriaValuesByCriteriaValuesCodeIn(criteriaValueCodes);
            job.resetCriteriaValues(criteria, sourcingCriteriaStep);
        } else {
            job.clearCriteria(sourcingCriteriaStep);
        }
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.JOB.JOB_WRITE)
    public void addCriteriaValuesForJob(UUID jobId, List<WorkingTimeDTO> workingTimeDTOs) {
        var job = findJobOrThrowException(jobId);
        var criteriaValueCodes = workingTimeDTOs.stream().map(x -> CriteriaValue.getValueCodeForTypeWorkingTime(getWorkingTime(x))).collect(Collectors.toCollection(ArrayList::new));
        var addedCriterias = criteriaRepository.findCriteriaValuesByCriteriaValuesCodeIn(criteriaValueCodes);
        var finalCriterias = Stream.concat(job.getCriteriaValues().stream(), addedCriterias.stream()).toList();
        job.resetCriteriaValues(finalCriterias);
    }

    private TypeWorkingTime getWorkingTime(WorkingTimeDTO workingTimeDTO) {
        return TypeWorkingTime.valueOf(workingTimeDTO.toString());
    }
}
