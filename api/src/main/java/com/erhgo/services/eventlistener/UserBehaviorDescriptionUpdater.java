package com.erhgo.services.eventlistener;

import com.erhgo.domain.userprofile.event.UserExperienceUpdatedEvent;
import com.erhgo.services.UserBehaviorDescriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserBehaviorDescriptionUpdater {

    private final UserBehaviorDescriptionService userBehaviorDescriptionService;

    @EventListener(UserExperienceUpdatedEvent.class)
    public void onEventTriggered(UserExperienceUpdatedEvent event) {
        if (event.getUserProfile() != null) {
            var userId = event.getUserProfile().userId();
            userBehaviorDescriptionService.dirtyUserBehaviorIfNeeded(userId);
            log.debug("Event of type {} for user {} processed by UserBehaviorDescription Handler", event.getClass().getName(), userId);
        } else {
            log.warn("Event of type {} NOT processed by UserBehaviorDescription Handler due to null user profile", event.getClass().getName());
        }
    }
}
