package com.erhgo.services.trimoji;

import com.erhgo.domain.candidature.job.SpontaneousCandidature;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.SpontaneousCandidatureRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.URL;
import java.time.OffsetDateTime;

@Service
@RequiredArgsConstructor
@Slf4j
public class TrimojiService {

    private final KeycloakService keycloakService;
    private final SecurityService securityService;
    private final UserProfileRepository repository;
    private final SpontaneousCandidatureRepository candidatureRepository;
    private final TrimojiClient trimojiClient;

    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    @Transactional(readOnly = true)
    public byte[] getPdfForUser(String userId) {
        var userRepresentation = keycloakService.getFrontOfficeUserProfile(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserRepresentation.class));
        var url = repository.findByUserId(userId)
                .map(UserProfile::trimojiPdfUrl)
                .orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
        return getSoftSkillsPdfBytes(url, userRepresentation.getFirstName(), userRepresentation.getLastName());
    }

    private static byte[] getSoftSkillsPdfBytes(String url, String firstname, String lastNameOrAnonymousCode) {
        try (var inputStream = new URL(url).openStream()) {
            return TrimojiPDFAdapter.processTrimojiPDF(inputStream, firstname, lastNameOrAnonymousCode).toByteArray();
        } catch (IOException e) {
            log.error("Unable to process trimoji pdf {}", url);
            throw new GenericTechnicalException("Unable to process PDF file");
        }
    }

    @RolesAllowed(Role.CANDIDATE)
    @Transactional
    public void markEndOfTest() {
        var user = securityService.getAuthenticatedUserProfile();
        log.debug("End of Trimoji test detected for user {}", user.userId());
        repository.endSoftSkillTest(user.userId(), OffsetDateTime.now());
    }

    @PreAuthorize(AuthorizeExpression.CANDIDATURE.CANDIDATURE_READ)
    @Transactional(readOnly = true)
    public byte[] getPdfForCandidature(Long candidatureId) {
        var candidature = candidatureRepository.findById(candidatureId)
                .orElseThrow(() -> new EntityNotFoundException(candidatureId, SpontaneousCandidature.class));
        if (candidature.isAnonymous()) {
            return getSoftSkillsPdfBytes(candidature.getUserProfile().trimojiPdfUrl(), null, candidature.getAnonymousCode());
        } else {
            return this.getPdfForUser(candidature.getUserId());
        }
    }


    @RolesAllowed(Role.CANDIDATE)
    @Transactional
    public String getNewUrl() {
        securityService.getAuthenticatedUserProfile().startSoftSkillTest();
        return trimojiClient.getNewUrl();
    }
}
