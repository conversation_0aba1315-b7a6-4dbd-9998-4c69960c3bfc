package com.erhgo.services;

import com.erhgo.openapi.dto.ConfigurablePropertyDTO;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.security.Role;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ConfigurablePropertyService {
    private final ConfigurablePropertyRepository repository;

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<ConfigurablePropertyDTO> getAllConfigurableProperties() {
        var properties = repository.findAll();
        return properties.stream().map(x -> new ConfigurablePropertyDTO().propertyKey(x.getPropertyKey()).propertyValue(x.getPropertyValue())).toList();
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void updateConfigurablePropertyValue(ConfigurablePropertyDTO configurablePropertyDTO) {
        var property = repository.findOneByPropertyKey(configurablePropertyDTO.getPropertyKey());
        var oldPropertyValue = property.getPropertyValue();

        property.setPropertyValue(configurablePropertyDTO.getPropertyValue());
        log.info("Updated configurable property {} from {} to {}", property.getPropertyKey(), oldPropertyValue, configurablePropertyDTO.getPropertyValue());
    }
}
