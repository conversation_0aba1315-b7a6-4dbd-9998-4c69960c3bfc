package com.erhgo.services.dto;

import org.apache.commons.lang3.StringUtils;

public record CsvCreationResult(
        int rowNum,
        Long recruitmentId,
        String organizationCode,
        boolean newOrganization,
        String errorMessage,
        String warningMessage
) {
    public boolean isErroneous() {
        return StringUtils.isNotBlank(errorMessage);
    }

    public boolean isOk() {
        return !isErroneous() && !isWarning();
    }

    public boolean isWarning() {
        return StringUtils.isNotBlank(warningMessage);
    }
}
