package com.erhgo.services.candidature;

import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.security.Role;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.Collection;

import static com.erhgo.utils.DateTimeUtils.offsetDateToDate;

@Slf4j
@Service
@RequiredArgsConstructor
public class CandidatureArchiverService {
    private final AbstractCandidatureRepository repository;

    @Value("${application.archiving.numberOfMonthsToArchiveRefusedCandidatures}")
    private int numberOfMonthsToArchiveRefusedCandidature;
    @Value("${application.archiving.numberOfMonthsToArchiveTreatedCandidatures}")
    private int numberOfMonthsToArchiveTreatedCandidatures;
    @Value("${application.archiving.numberOfMonthsToArchiveUntreatedCandidatures}")
    private int numberOfMonthsToArchiveUntreatedCandidatures;

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void archiveCandidatures() {
        archiveCandidatures(GlobalCandidatureState.refused(), numberOfMonthsToArchiveRefusedCandidature, "refused");
        archiveCandidatures(GlobalCandidatureState.untreated(), numberOfMonthsToArchiveUntreatedCandidatures, "untreated");
        archiveCandidatures(GlobalCandidatureState.treated(), numberOfMonthsToArchiveTreatedCandidatures, "treated");
    }

    private void archiveCandidatures(Collection<GlobalCandidatureState> candidaturesStatesToArchive, int numberOfMonthsToArchiveCandidatures, String comment) {
        var thresholdDate = OffsetDateTime.now().minusMonths(numberOfMonthsToArchiveCandidatures);
        var candidaturesToArchive = repository.findByGlobalCandidatureStateInAndUpdatedDateBeforeAndArchivedIsFalse(candidaturesStatesToArchive, offsetDateToDate(thresholdDate));
        candidaturesToArchive.forEach(rC -> rC.setArchived(true));
        if (candidaturesToArchive.isEmpty()) {
            log.debug("No {} candidature found for less than {} months for archiving", comment, numberOfMonthsToArchiveCandidatures);
        } else {
            log.info("Successfully archived {} {} candidatures", comment, candidaturesToArchive.size());
        }
    }
}
