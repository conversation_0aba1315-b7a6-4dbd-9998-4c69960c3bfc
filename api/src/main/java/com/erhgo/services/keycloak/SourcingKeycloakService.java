package com.erhgo.services.keycloak;

import com.erhgo.services.dto.UserKeycloakRepresentation;

import java.util.Collection;
import java.util.Optional;

public interface SourcingKeycloakService {

    void assignUserToSourcingGroup(String userId, String organizationCode);

    UserRepresentation getSourcingUserWithGroups(String userId);

    Optional<UserRepresentation> getSourcingUser(String userId);

    Optional<UserRepresentation> getSourcingUserFromEmail(String email);

    Collection<UserRepresentation> getEnabledSourcingUsersForGroup(String groupName);

    Collection<UserRepresentation> getSourcingUsersForGroup(String groupName);

    void updateUser(String userId, String email, String phone, String fullname);

    void updateUserSiret(String userId, String siret);

    String createUserInSourcingRealm(UserKeycloakRepresentation userKeycloakRepresentation);

    void markNewAuthentication(String userId);

    void disableUser(String userId);

    void enableUser(String userId);

    void createSourcingGroupAndRole(String groupAndRole);
}
