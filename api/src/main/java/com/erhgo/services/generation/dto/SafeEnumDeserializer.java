package com.erhgo.services.generation.dto;

import com.erhgo.domain.enums.DiplomaLevel;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

public class SafeEnumDeserializer extends JsonDeserializer<DiplomaLevel> {

    @Override
    public DiplomaLevel deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        var value = p.getText();
        if (StringUtils.isBlank(value)) return null;
        try {
            return Enum.valueOf(DiplomaLevel.class, value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
