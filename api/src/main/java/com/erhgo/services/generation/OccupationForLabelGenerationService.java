package com.erhgo.services.generation;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.OccupationCreationReason;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.openapi.dto.GenerationReportItemDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.ErhgoOccupationService;
import com.erhgo.services.generation.dto.FindBestMatchingOccupationArguments;
import com.erhgo.services.generation.dto.NormalizedTitlesResponse;
import com.erhgo.services.generation.dto.OccupationForLabelGenerationResult;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import com.erhgo.services.notifier.messages.AddedAlternativeLabelMessageDTO;
import com.erhgo.services.notifier.messages.UnqualifiedCreatedOccupationMessageDTO;
import com.erhgo.utils.ReportUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RequiredArgsConstructor
@Service
@Slf4j
public class OccupationForLabelGenerationService {
    // FIXME: re-enable this feature - requires sanitization of new labels
    private static final boolean DISABLE_ALTERNATIVE_LABEL_ADD = true;
    private final Notifier notifier;
    private final FindBestMatchingOccupationService findBestMatchingOccupationService;
    private final OccupationGenerator occupationGenerator;
    private final ErhgoOccupationService erhgoOccupationService;
    private final GenerationReportLoggerService generationReportLoggerService;
    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final TitleGenerationService titleGenerationService;
    public static final String TITLE = "1 - normalisation du titre";

    public OccupationForLabelGenerationResult createOrUpdateOccupation(String jobTitle, OccupationCreationSourceType occupationCreationSourceType) {
        NormalizedTitlesResponse normalizedTitles = null;
        var error = false;
        List<GenerationReportItemDTO> reports = new ArrayList<>();

        OpenAIResponse<?> response;
        try {
            response = titleGenerationService.normalizeTitle(jobTitle);
            normalizedTitles = (NormalizedTitlesResponse) response.getResult();
            log.debug("Normalized job title from '{}' to '{}' and alternative '{}'", jobTitle, normalizedTitles.getMasculine(), normalizedTitles.getFeminine());
        } catch (FatalGenerationException ex) {
            log.warn("Failed to normalize job title '{}'", jobTitle, ex);
            error = true;
            response = ex.getResponse();
        }

        var report = new GenerationReportItemDTO();
        reports.add(ReportUtils.updateReportWithResponse(TITLE, report, response));

        if (error) {
            return new OccupationForLabelGenerationResult(null, reports, true);
        }

        var foundOccupation = findBestMatchingOccupationService.findSimilarLabel(FindBestMatchingOccupationArguments.builder()
                .newLabel(normalizedTitles.getMasculine())
                .build());

        var occupationId = foundOccupation.getId();
        if (occupationId == null) {
            occupationId = UUID.randomUUID();
            log.trace("No occupation found for job title {}, creating occupation with id {}", normalizedTitles.getMasculine(), occupationId);
            reports.addAll(occupationGenerator.qualifyOccupation(occupationId, normalizedTitles.getMasculine(), occupationCreationSourceType != null && occupationCreationSourceType.isRequiresConfirmation(), getOccupationCreationReason(occupationCreationSourceType), normalizedTitles.getFeminine()));
            error = reports.size() < 2;
            if (!error) {
                notifier.sendMessage(
                        new UnqualifiedCreatedOccupationMessageDTO(occupationId, erhgoOccupationRepository.findById(occupationId).map(ErhgoOccupation::getTitle).orElse("Inconnu"),
                                jobTitle,
                                occupationCreationSourceType));
            }
        } else {
            log.trace("Occupation found for job title {}, using occupation with id {}", jobTitle, occupationId);
            if (DISABLE_ALTERNATIVE_LABEL_ADD) {
                log.info("Libellé {} NON ajouté au métier {}", jobTitle, occupationId);
            } else {
                erhgoOccupationService.addAlternativeLabelBlockingNonAsync(occupationId, jobTitle);
            }
            notifier.sendMessage(new AddedAlternativeLabelMessageDTO(occupationId, foundOccupation.getTitle(), jobTitle, occupationCreationSourceType));
        }
        generationReportLoggerService.logForReports("occupation %s of job %s ".formatted(occupationId, jobTitle), reports);
        return new OccupationForLabelGenerationResult(occupationId, reports, error);
    }

    private OccupationCreationReason getOccupationCreationReason(OccupationCreationSourceType sourceType) {
        if (sourceType == null) {
            log.warn("OccupationCreationSourceType is null, defaulting to BO");
            return OccupationCreationReason.BO;
        }

        return switch (sourceType) {
            case FROM_CV -> OccupationCreationReason.CV;
            case FROM_ATS -> OccupationCreationReason.ATS;
            case FROM_CSV -> OccupationCreationReason.CSV;
            case FROM_SOURCING -> OccupationCreationReason.SOURCING;
            case FROM_FRONT_OFFICE -> OccupationCreationReason.XP;
        };
    }
}
