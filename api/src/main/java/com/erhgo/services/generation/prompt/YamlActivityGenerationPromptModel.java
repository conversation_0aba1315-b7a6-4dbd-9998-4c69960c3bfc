package com.erhgo.services.generation.prompt;

import lombok.Data;
import org.springframework.ai.chat.messages.Message;

import java.util.List;

@Data
public class YamlActivityGenerationPromptModel {
    private List<YamlGenericGenerationPromptModel.InnerMessage> promptMessages;
    private String computePromptAdjust;
    private String parameterizedLastPromptMessageWithRomeAndOccupationAssociated;
    private String parameterizedLastPromptMessageWithRomeAndNoOccupationAssociated;
    private String parameterizedLastPromptMessageWithoutRome;

    public List<Message> getMessages() {
        return promptMessages.stream().map(YamlGenericGenerationPromptModel.InnerMessage::getMessage).toList();
    }
}
