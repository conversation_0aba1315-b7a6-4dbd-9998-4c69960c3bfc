package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.InvalidJobTitleException;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.NormalizedTitlesResponse;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TitleGenerationService extends AbstractGenerationService<NormalizedTitlesResponse, String> {

    public TitleGenerationService(
            YamlPromptReader yamlPromptReader,
            GenerationClient generationClient,
            PromptConfig titlePromptConfig,
            ErhgoOccupationRepository erhgoOccupationRepository,
            SecurityService securityService
    ) {
        super(yamlPromptReader, generationClient, titlePromptConfig, erhgoOccupationRepository, securityService);
    }

    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE, Role.SOURCING})
    public OpenAIResponse<NormalizedTitlesResponse> normalizeTitle(String sourceTitle) throws FatalGenerationException {
        return generate(sourceTitle);
    }

    @Override
    public NormalizedTitlesResponse handleResponse(String responseContent) throws AbstractRetryableGenerationException {
        try {
            if (responseContent.contains("\"erreur\":")) {
                throw new InvalidJobTitleException("Erreur dans le titre généré: %s".formatted(responseContent));
            }
            return objectMapper.readValue(responseContent, NormalizedTitlesResponse.class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse normalized title: {}", responseContent, e);
            throw new InvalidJobTitleException("Erreur lors de la désérialisation du titre généré %s %s".formatted(responseContent, e.getMessage()));
        }
    }
}
