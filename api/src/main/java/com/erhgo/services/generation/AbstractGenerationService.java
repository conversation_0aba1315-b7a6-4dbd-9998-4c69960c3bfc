package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ChatCompletionResponse;
import com.erhgo.services.generation.dto.CustomChatCompletionRequest;
import com.erhgo.services.generation.dto.GenerationRetryState;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.content.Media;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.ResponseFormat;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Stream;

import static java.lang.System.currentTimeMillis;

@RequiredArgsConstructor
@Slf4j
public abstract class AbstractGenerationService<R, P> {
    final ObjectMapper objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    final YamlPromptReader yamlPromptReader;
    final GenerationClient generationClient;
    final PromptConfig promptConfig;
    final ErhgoOccupationRepository erhgoOccupationRepository;
    final SecurityService securityService;

    ChatCompletionResponse createChatCompletionForMessages(List<Message> prompt, boolean forceGPT4JsonMode) throws FatalGenerationException {
        var options = OpenAiChatOptions
                .builder()
                .model(promptConfig.getModel())
                .temperature(promptConfig.getTemperature())
                .maxTokens(promptConfig.getMaxTokens())
                .user(securityService.isAdmin() ? "" : securityService.getAuthenticatedUserId())
                .responseFormat(forceGPT4JsonMode ? new ResponseFormat(ResponseFormat.Type.JSON_OBJECT, null) : null)
                .build();
        var chatCompletionRequest = CustomChatCompletionRequest.customBuilder()
                .messages(prompt)
                .options(options)
                .gpt4JsonMode(forceGPT4JsonMode)
                .build();
        log.trace("Sending chat completion request {}", chatCompletionRequest);
        return sanitize(generationClient.createChatCompletion(chatCompletionRequest, promptConfig), forceGPT4JsonMode);
    }

    protected ChatCompletionResponse sanitize(ChatCompletionResponse response, boolean forceGPT4JsonMode) {
        if (forceGPT4JsonMode) {
            response.setContent(com.erhgo.utils.StringUtils.sanitizeOpenAIResponse(response.getContent()));
        }

        return response;
    }

    protected OpenAIResponse<R> generate(P args) throws FatalGenerationException {
        return generateWithRetry(args);
    }


    protected abstract R handleResponse(String content) throws AbstractRetryableGenerationException;


    protected List<Message> getChatMessages(P args) {
        var data = yamlPromptReader.readYamlDataGeneric(promptConfig.getMessageFilename());
        var prompt = new ArrayList<>(data.getMessages());

        if (args instanceof Media media) {
            prompt.add(new UserMessage(data.getParameterizedLastPromptMessage().formatted(args).trim(), media));
        } else {
            prompt.add(new UserMessage(data.getParameterizedLastPromptMessage().formatted(args).trim()));
        }

        return prompt;
    }

    ErhgoOccupation getOccupationOrThrow(UUID occupationId) {
        return erhgoOccupationRepository.findById(occupationId).orElseThrow(() -> new EntityNotFoundException(occupationId, ErhgoOccupation.class));
    }

    public List<String> extractCodesFromResponse(String response) {
        try {
            var wrapper = objectMapper.readValue(response, new TypeReference<Map<String, List<String>>>() {});
            var resultList = wrapper.entrySet().stream()
                    .findFirst()
                    .map(Map.Entry::getValue)
                    .orElse(List.of());
            return resultList.stream()
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .toList();
        } catch (JsonProcessingException e) {
            return Stream.of(response.replaceAll("[\\[\\]'\" ]", "").split(","))
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .toList();
        }
    }

    protected OpenAIResponse<R> generateWithRetry(P args) throws FatalGenerationException {
        var result = new OpenAIResponse<R>().setModel(promptConfig.getModel());
        GenerationRetryState retryState = null;
        log.info("Let's generate data with input {}, initial try", args);

        while (result.getNbTry() < promptConfig.getMaxRetry()) {
            long startTime = currentTimeMillis();
            var promptMessages = createPrompt(args, retryState, result.getNbTry());
            try {
                result.increment();
                var response = createChatCompletionForMessages(promptMessages, promptConfig.isForceJson());
                result.updateWith(startTime);
                retryState = handleResponse(args, result, retryState, promptMessages, response);
                if (result.getResult() != null) {
                    return result;
                }
            } catch (FatalGenerationException e) {
                result.updateWith(startTime);
                e.setResponse(result);
                throw e;
            } catch (RuntimeException e) {
                log.error("Unexpected error occurred during chat completion call - consider as fatal error", e);
                throw new FatalGenerationException("Runtime error while creating chat completion", e);
            }
        }
        throw new FatalGenerationException("OpenAI was unable to generate good enough data for input %s after %d tries".formatted(args, result.getNbTry()), result);
    }

    private GenerationRetryState handleResponse(P args, OpenAIResponse<R> result, GenerationRetryState retryState, List<Message> promptMessages, ChatCompletionResponse response) {
        try {
            var generated = handleResponse(response.getContent());
            result.setResult(generated);
        } catch (AbstractRetryableGenerationException e) {
            retryState = new GenerationRetryState(e.getMessage(), e.getPreviousResult(), result.getNbTry(), promptMessages);
            log.warn("Retry attempt {} for title {}: {}", result.getNbTry(), args, e.getMessage(), e);
        }
        return retryState;
    }

    private List<Message> createPrompt(P args, GenerationRetryState retryState, int nbTry) {
        if (retryState == null || retryState.getPreviousResult() == null) {
            return getChatMessages(args);
        }
        var promptMessages = new ArrayList<>(retryState.getPromptMessages());
        promptMessages.add(new AssistantMessage(retryState.getPreviousResult()));
        var promptAdjust = computePromptAdjust(retryState, nbTry, args);
        promptMessages.add(new UserMessage(promptAdjust));
        return promptMessages;
    }

    protected String computePromptAdjust(GenerationRetryState retryState, int nbTry, P args) {
        var result = new StringBuilder(retryState.getErrorMessage()).append(", ");
        var data = yamlPromptReader.readYamlDataGeneric(promptConfig.getMessageFilename());
        if (nbTry == 1) {
            result.append("recommence en respectant toutes les consignes et sans faire de commentaire : ");
        } else {
            result.append(data.getComputePromptAdjust());
        }

        result.append(data.getParameterizedLastPromptMessage().formatted(args));

        return result.toString();
    }

}
