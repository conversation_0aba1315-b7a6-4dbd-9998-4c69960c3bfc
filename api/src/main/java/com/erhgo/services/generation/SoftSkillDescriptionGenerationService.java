package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.enums.ExperienceType;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.GenericRetryableGenerationException;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ArrayWrapper;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.dto.TitleAndDescription;
import com.erhgo.services.generation.dto.UserExperienceDescription;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.utils.KeycloakUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SoftSkillDescriptionGenerationService extends AbstractGenerationService<List<TitleAndDescription>, String> {

    private static final int MAX_EXPERIENCES = 20;
    private final UserProfileRepository userProfileRepository;
    private final KeycloakService keycloakService;

    public SoftSkillDescriptionGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig softSkillsDescriptionsPromptConfig, ErhgoOccupationRepository erhgoOccupationRepository, UserProfileRepository userProfileRepository, KeycloakService keycloakService, SecurityService securityService) {
        super(yamlPromptReader, generationClient, softSkillsDescriptionsPromptConfig, erhgoOccupationRepository, securityService);
        this.userProfileRepository = userProfileRepository;
        this.keycloakService = keycloakService;
    }

    @Override
    protected List<TitleAndDescription> handleResponse(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            var wrapper = objectMapper.readValue(jsonResult, new TypeReference<ArrayWrapper<TitleAndDescription>>() {
            });
            return wrapper.getResult();
        } catch (JsonProcessingException | IllegalArgumentException e) {
            log.warn("Invalid json produced on soft skills description: {}", jsonResult, e);
            throw new GenericRetryableGenerationException(jsonResult, "bad JSON", e);
        }
    }

    @VisibleForTesting
    protected List<TitleAndDescription> generateSoftSkillsDescription(List<UserExperience> experiences, String firstName) {
        if (experiences == null || experiences.isEmpty() || experiences.size() > MAX_EXPERIENCES) {
            throw new FatalGenerationException("Experiences should not be empty or higher than %d".formatted(MAX_EXPERIENCES));
        }
        OpenAIResponse<List<TitleAndDescription>> result = null;
        try {
            var experiencesDescriptions = objectMapper.writeValueAsString(experiences.stream().map(UserExperienceDescription::forExperience).collect(Collectors.toSet()));
            result = generate("pour les expériences du candidat : %s et pour le prénom %s".formatted(experiencesDescriptions, firstName));
            log.trace("soft skills descriptions generated: {}", result.getResult());
            return result.getResult();
        } catch (FatalGenerationException e) {
            result = e.getResponse();
            throw e;
        } catch (JsonProcessingException e) {
            log.error("Unable to transform user experiences to OpenAI DTOs", e);
            throw new FatalGenerationException("Unable to use experiences", e);
        }
    }

    @Transactional
    public List<TitleAndDescription> getSoftSkillsDescriptionsOrGenerate(String userId) {
        var user = userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
        if (user.hasSoftSkills()) {
            return user.getSoftSkills();
        }
        if (user.experiences().isEmpty()) {
            log.warn("ignoring soft skills generation - empty experiences for user {}", userId);
            return new ArrayList<>();
        }
        var hasAtLeast2NonInternship = user.experiences().stream().filter(ux -> ux.getType() != ExperienceType.INTERNSHIP).count() > 2;

        var experiences = user.experiences().stream().filter(ux -> ux.getErhgoOccupation() != null)
                .collect(Collectors.groupingBy(UserExperience::getErhgoOccupation))
                .values()
                .stream()
                .flatMap(experiencesForOccupation -> experiencesForOccupation.stream().filter(ux -> !hasAtLeast2NonInternship || ux.getType() != ExperienceType.INTERNSHIP).sorted(UserExperience.RELEVANCE_COMPARATOR).limit(2))
                .sorted(UserExperience.RELEVANCE_COMPARATOR)
                .limit(MAX_EXPERIENCES)
                .toList();

        log.debug("Let's generate soft skills description for user {} with {} experiences", userId, experiences.size());
        if (experiences.isEmpty()) {
            log.warn("No experience for user {} - unable to generate soft skills descriptions", userId);
            return new ArrayList<>();
        }
        var firstname = keycloakService.getFrontOfficeUserProfile(userId).orElseGet(() -> KeycloakUtils.defaultUserRepresentation(userId)).getFirstName();
        var generatedDescriptions = generateSoftSkillsDescription(experiences, firstname);
        user.updateSoftSkillsDescriptions(generatedDescriptions);
        return generatedDescriptions;
    }

}

