package com.erhgo.services.generation;

import com.erhgo.config.PromptConfig;
import com.erhgo.domain.exceptions.openai.AbstractRetryableGenerationException;
import com.erhgo.domain.exceptions.openai.FatalGenerationException;
import com.erhgo.domain.exceptions.openai.InvalidUserExperienceException;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.services.generation.client.GenerationClient;
import com.erhgo.services.generation.dto.ArrayWrapper;
import com.erhgo.services.generation.dto.OpenAIResponse;
import com.erhgo.services.generation.dto.UserExperienceExtractionResponse;
import com.erhgo.services.generation.prompt.YamlPromptReader;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ExperiencesFromCVGenerationService extends AbstractGenerationService<List<UserExperienceExtractionResponse>, String> {
    public ExperiencesFromCVGenerationService(YamlPromptReader yamlPromptReader, GenerationClient generationClient, PromptConfig userExperiencesPromptConfig, ErhgoOccupationRepository erhgoOccupationRepository, SecurityService securityService) {
        super(yamlPromptReader, generationClient, userExperiencesPromptConfig, erhgoOccupationRepository, securityService);
    }

    public OpenAIResponse<List<UserExperienceExtractionResponse>> generateUserExperiences(String content) throws FatalGenerationException {
        return generate(content);
    }

    @Override
    protected List<UserExperienceExtractionResponse> handleResponse(String jsonResult) throws AbstractRetryableGenerationException {
        try {
            var dtoRoot = objectMapper.readValue(jsonResult, new TypeReference<ArrayWrapper<UserExperienceExtractionResponse>>() {
            });
            var dto = dtoRoot.getResult();
            if (dto.stream().anyMatch(x -> StringUtils.isBlank(x.getTitle()) || x.getType() == null)) {
                throw new InvalidUserExperienceException(jsonResult);
            }

            return dto;
        } catch (JsonProcessingException e) {
            log.warn("Invalid json produced {}", jsonResult, e);
            throw new InvalidUserExperienceException(jsonResult, e);
        }
    }
}
