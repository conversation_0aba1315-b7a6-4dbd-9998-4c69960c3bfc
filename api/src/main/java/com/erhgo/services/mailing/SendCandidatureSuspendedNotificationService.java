package com.erhgo.services.mailing;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.NotificationState;
import com.erhgo.domain.enums.NotificationType;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.notification.SuspendedRecruitmentNotification;
import com.erhgo.repositories.NotificationRepository;
import com.erhgo.repositories.RecruitmentCandidatureRepository;
import com.erhgo.security.Role;
import com.erhgo.services.keycloak.KeycloakService;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SendCandidatureSuspendedNotificationService {
    private final MailingListService mailingListService;
    private final KeycloakService keycloakService;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final NotificationRepository notificationRepository;

    @Value("${sendinblue.templates.suspend-recruitment-candidature-notification}")
    private Long suspendCandidatureSourcingTemplate;

    @RolesAllowed({Role.ODAS_ADMIN})
    public void sendSuspendedRecruitmentNotifications() {
        var candidatures = recruitmentCandidatureRepository.getRecruitmentCandidaturesFromSuspendedRecruitmentToNotify();

        log.debug("Considering {} candidatures to notify for suspended recruitments", candidatures.size());
        if (candidatures.isEmpty()) return;
        var notifications = createNotifications(candidatures);
        var notified = sendEmailsAndMarkAsNotified(candidatures);
        notifications.stream().filter(n -> notified.contains(n.getUserId())).forEach(n -> n.setType(NotificationType.EMAIL));
        notificationRepository.saveAll(notifications);
        log.debug("{} notifications for suspended recruitments generated ({} with email)", notifications.size(), notifications.stream().filter(n -> n.getType() == NotificationType.EMAIL).count());
    }

    private List<SuspendedRecruitmentNotification> createNotifications(List<RecruitmentCandidature> candidatures) {
        return candidatures
                .stream()
                .map(x -> new SuspendedRecruitmentNotification(
                        x.getUserProfile(),
                        x.getRecruitment(),
                        NotificationType.NONE,
                        NotificationState.NEW))
                .toList();
    }

    private Set<String> sendEmailsAndMarkAsNotified(List<RecruitmentCandidature> candidatures) {
        if (suspendCandidatureSourcingTemplate == null) {
            log.error("Ignoring candidature recruitment notification send, no mail template id provided for suspended recruitment");
            return new HashSet<>();
        }
        return candidatures.stream()
                .collect(Collectors.groupingBy(RecruitmentCandidature::getRecruitment))
                .entrySet()
                .stream()
                .flatMap(a -> this.sendEmailsAndMarkAsNotifiedForRecruitment(a.getKey(), a.getValue()).stream())
                .collect(Collectors.toSet());
    }

    private Set<String> sendEmailsAndMarkAsNotifiedForRecruitment(Recruitment recruitment, Collection<RecruitmentCandidature> candidatures) {
        var customDataForUser = candidatures.stream().collect(Collectors.toMap(RecruitmentCandidature::getUserId, this::buildMap));
        var location = Optional.ofNullable(recruitment.getLocation());
        var datasForRecruitment = Map.of("organization", recruitment.getOrganizationName(),
                "job_title", recruitment.getJobTitle(),
                "city", location.map(Location::getCity).orElse(""),
                "post_code", location.map(Location::getPostcode).orElse(""));
        var usersProfile = candidatures.stream().map(RecruitmentCandidature::getUserProfile).collect(Collectors.toSet());

        try {
            var notifiedUsers = mailingListService.sendMailsToProfilesForTemplate(usersProfile,
                    "Envoi de notifications aux candidats sur suspension de recrutement",
                    suspendCandidatureSourcingTemplate,
                    datasForRecruitment,
                    customDataForUser).get();
            candidatures.forEach(c -> {
                c.setNotifiedOnClosedRecruitment(true);
                recruitmentCandidatureRepository.save(c);
            });
            return notifiedUsers;
        } catch (ExecutionException | InterruptedException e) {
            log.error("Failed to send suspended recruitment notifications for recruitment {} to {} candidates", recruitment.getId(), candidatures.size(), e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }

        return new HashSet<>();
    }

    private Map<String, Object> buildMap(RecruitmentCandidature candidature) {
        var user = keycloakService.getFrontOfficeUserProfile(candidature.getUserProfile().userId()).orElseThrow();
        return Map.of("PRENOM", StringUtils.trimToEmpty(user.getFirstName()));
    }

}
