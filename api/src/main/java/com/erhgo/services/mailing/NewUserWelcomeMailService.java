package com.erhgo.services.mailing;

import com.erhgo.domain.userprofile.MailConfiguration;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.UserProfileRepository;
import com.google.api.client.util.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class NewUserWelcomeMailService {

    private final UserProfileRepository userProfileRepository;

    private final MailingListService mailingListService;
    @Value("${sendinblue.templates.confirm-account-fo-out-sector}")
    private Long confirmAccountOutSectorFoTemplate;
    @Value("${application.sectorDepartmentsCode}")
    private List<String> baseSectorDepartment;
    @Value("${application.sendFOWelcomeMailDelayInHours}")
    private Long sendFOWelcomeMailDelayInHours;

    @Value("${sendinblue.templates.second-confirm-account-fo}")
    private Long foSecondWelcomeTemplate;
    @Value("${application.sendFOSecondWelcomeMailDelayInHours}")
    private Long sendFoSecondWelcomeMailDelayInHours;

    @Transactional
    public void sendWelcomeMails() {
        var sendFirstWelcomeMailStartDate = Date.from(LocalDateTime.now().minusHours(sendFOWelcomeMailDelayInHours)
                .atZone(ZoneId.systemDefault()).toInstant());
        var allUsersFirstMail = userProfileRepository.findNewUsersRequiringWelcomeEmail(sendFirstWelcomeMailStartDate);

        var usersByInsideSector = allUsersFirstMail.stream().collect(Collectors.partitioningBy(u -> Strings.isNullOrEmpty(u.getDepartmentCode()) || baseSectorDepartment.contains(u.getDepartmentCode())));

        sendMail(usersByInsideSector.get(false), confirmAccountOutSectorFoTemplate, "Unable to send FIRST welcome mail to some users OUT OF sector", MailConfiguration.ConfirmationMailState.FIRST_SENT);

        var sendSecondWelcomeMailStartDate = Date.from(LocalDateTime.now().minusHours(sendFoSecondWelcomeMailDelayInHours)
                .atZone(ZoneId.systemDefault()).toInstant());
        var usersSecondMail = userProfileRepository.findByMailConfigurationConfirmationMailSentAndCreatedDateBefore(MailConfiguration.ConfirmationMailState.FIRST_SENT, sendSecondWelcomeMailStartDate);
        sendMail(removeUsersAlreadyNotified(usersSecondMail, allUsersFirstMail), foSecondWelcomeTemplate, "Unable to send SECOND welcome mail to some users", MailConfiguration.ConfirmationMailState.SECOND_SENT);
    }

    @NotNull
    private static List<UserProfile> removeUsersAlreadyNotified(List<UserProfile> usersSecondMail, List<UserProfile> allUsersFirstMail) {
        return usersSecondMail.stream().filter(u -> allUsersFirstMail.stream().noneMatch(f -> f.userId().equals(u.userId()))).toList();
    }

    private void sendMail(List<UserProfile> users, Long templateId, String errorMessage, MailConfiguration.ConfirmationMailState state) {
        if (users.isEmpty()) {
            log.debug("No user to notify for template  {}", templateId);
            return;
        }
        try {
            mailingListService.sendMailsToProfilesForTemplate(users, null, templateId, null, null).get();
            users.forEach(u -> u.confirmWelcomeEmailSent(state));
            log.debug("{} welcome notification sent (template id {})", users.size(), templateId);
        } catch (InterruptedException e) {
            log.error(errorMessage, e);
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error(errorMessage, e);
        }
    }
}
