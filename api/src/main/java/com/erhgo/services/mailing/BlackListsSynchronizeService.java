package com.erhgo.services.mailing;

import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.services.dto.TransactionalBlackListResult;
import com.erhgo.services.dtobuilder.MailingUserDTO;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.utils.PaginatedDataUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlackListsSynchronizeService {

    private final UserProfileRepository userProfileRepository;

    private final MailingListService mailingListService;
    private final KeycloakService keycloakService;
    @Value("${sendinblue.jobOfferSender}")
    private String jobOfferSender;


    @Async
    @Transactional
    public void updateMailingRelatedData(Long limit) {
        log.info("Starting transactional blacklist update with limit {}", limit);
        var transactionalOptOutEmails = PaginatedDataUtils.getPaginatedData(
                limit,
                (l, o) -> mailingListService.getTransactionalBlacklistedEmails(l, o, LocalDateTime.now().minusDays(1)),
                200L
        );
        var userProfilesByUserId = userProfileRepository.findAll().stream()
                .collect(Collectors.toMap(UserProfile::userId, Function.identity()));

        var allUsers = keycloakService.findAllFrontOfficeUsers()
                .stream()
                .collect(Collectors.toMap(UserRepresentation::getEmail, UserRepresentation::getId, (id1, id2) -> {
                    if (!StringUtils.equals(id1, id2)) {
                        log.error("got two different ids for same mail on SIB sync - id1={}, id2={} - using first one", id1, id2);
                    }
                    return id1;
                }));

        getAllContactsSIB(limit)
                .forEach(sibUser -> handleUserUpdate(sibUser, allUsers, userProfilesByUserId, transactionalOptOutEmails));
    }

    private void handleUserUpdate(
            MailingUserDTO sibUser,
            Map<String, String> allUsers,
            Map<String, UserProfile> userProfilesByUserId,
            List<TransactionalBlackListResult> optOutEmails
    ) {
        var email = sibUser.getEmail();
        if (email != null) {
            var keycloakUserId = allUsers.get(email);
            if (keycloakUserId != null) {
                var userProfile = userProfilesByUserId.get(keycloakUserId);
                updateWithSIB(sibUser, userProfile, optOutEmails.stream().filter(a -> a.getEmail().equals(email)).findFirst().orElse(null));
            }
        }
    }

    private void updateWithSIB(MailingUserDTO sibUser,
                               UserProfile userProfile,
                               TransactionalBlackListResult optOutEmails) {
        if (userProfile != null) {
            userProfile.updateJobOfferOptOut(optOutEmails != null && optOutEmails.containsEmail(jobOfferSender));
            userProfile.sendersOptOut(optOutEmails == null ? Collections.emptySet() : optOutEmails.getSenders());
            userProfile.updateSmsBlacklisted(sibUser.isSmsBlacklisted());
        }
    }


    protected List<MailingUserDTO> getAllContactsSIB(Long limit) {
        var contacts = PaginatedDataUtils.getPaginatedData(
                limit,
                (l, o) -> mailingListService.getContacts(l, o, LocalDateTime.now().minusDays(1)),
                200L
        );
        log.info("{} contacts retrieved", contacts.size());
        return contacts;
    }

}
