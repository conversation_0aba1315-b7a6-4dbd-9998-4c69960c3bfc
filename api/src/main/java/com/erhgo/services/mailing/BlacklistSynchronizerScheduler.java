package com.erhgo.services.mailing;

import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class BlacklistSynchronizerScheduler {

    private static final Long MAX_LIMIT = 100L;

    private final BlackListsSynchronizeService blackListsSynchronizeService;
    private final SecurityService securityService;

    @Scheduled(cron = "${application.mailing.blacklistRefreshCron}", zone = "Europe/Paris")
    @SchedulerLock(name = "launchBlacklistRefresh")
    public void refreshBlacklist() {
        log.info("Launch blacklist refresh");
        securityService.doAsAdmin(() -> blackListsSynchronizeService.updateMailingRelatedData(MAX_LIMIT));
        log.info("Blacklist launched");
    }

}
