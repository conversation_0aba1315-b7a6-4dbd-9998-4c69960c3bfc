package com.erhgo.services.mailing.check;

import com.erhgo.domain.dto.EmailVerificationResultDTO;
import com.erhgo.utils.StringUtils;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.SocketTimeoutException;
import java.time.Duration;

@Slf4j
@Service
@ConditionalOnExpression("!T(org.springframework.util.StringUtils).isEmpty('${quickemailverification.apiKey:}')")
public class QEVEmailVerificationService implements EmailVerificationService {

    @Accessors(chain = true)
    @Data
    @SuppressWarnings("java:S116")
    public static class QEVResultDTO {
        private String result;
        private String reason;
        private Boolean disposable;
        private Boolean accept_all;
        private Boolean role;
        private Boolean free;
        private String email;
        private String user;
        private String domain;
        private String mx_record;
        private String mx_domain;
        private Boolean safe_to_send;
        private String did_you_mean;
        private Boolean success;
        private String message;
    }

    private RestTemplate restTemplate;
    private static final String EMAIL_KEY = "email";
    private static final String API_KEY = "apikey";

    @Value("${quickemailverification.apiKey}")
    private String apiKey;

    @Value("${quickemailverification.url}")
    private String baseUrl = "";

    @Value("${quickemailverification.timeout-in-ms.connect}")
    private long connectTimeoutInMS;

    @Value("${quickemailverification.timeout-in-ms.read}")
    private long readTimeoutInMS;

    @Autowired
    private RestTemplateBuilder restTemplateBuilder;

    @PostConstruct
    public void initializeRestTemplate() {
        log.info("Initializing QEVEmailVerificationService with connect timeout {} and read timeout {}", connectTimeoutInMS, readTimeoutInMS);
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(Duration.ofMillis(connectTimeoutInMS))
                .setReadTimeout(Duration.ofMillis(readTimeoutInMS))
                .build();
    }

    @Override
    public EmailVerificationResultDTO verify(String email) {
        log.debug("Starting email check for {}", email);
        if (!StringUtils.isEmail(email)) {
            log.debug("Email {} is invalid - no QEV service called", email);
            return new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.INVALID_MAIL);
        }

        var url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .queryParam(EMAIL_KEY, email)
                .queryParam(API_KEY, apiKey)
                .encode()
                .toUriString();

        try {
            log.debug("Starting QEV Api call for {} email...", email);
            var result = restTemplate.getForObject(url, QEVResultDTO.class);
            log.debug("Got result {} for {} email check", result, email);
            return summarize(result);
        } catch (RestClientException e) {
            if (ExceptionUtils.indexOfType(e, SocketTimeoutException.class) >= 0) {
                log.warn("QuickEmailVerification took too long for {}", email, e);
            } else {
                log.error("Unable to check email {}", email, e);
            }
            return new EmailVerificationResultDTO().setEmailStatus(EmailVerificationResultDTO.EmailStatus.VERIFIER_ERROR);
        }
    }

    // see http://docs.quickemailverification.com/email-verification-api/verify-an-email-address
    private EmailVerificationResultDTO summarize(QEVResultDTO qevResultDTO) {
        EmailVerificationResultDTO.EmailStatus emailStatus;
        if (qevResultDTO == null || qevResultDTO.reason == null) {
            emailStatus = EmailVerificationResultDTO.EmailStatus.UNKNOWN;
        } else {
            emailStatus = switch (qevResultDTO.reason) {
                case "rejected_email", "invalid_email", "invalid_domain" -> EmailVerificationResultDTO.EmailStatus.INVALID_MAIL;
                case "no_connect", "timeout", "unavailable_smtp", "unexpected_error", "no_mx_record" -> EmailVerificationResultDTO.EmailStatus.INVALID_SERVER;
                case "accepted_email", "exceeded_storage", "temporarily_blocked" -> EmailVerificationResultDTO.EmailStatus.VALID;
                default -> EmailVerificationResultDTO.EmailStatus.UNKNOWN;
            };
        }

        return new EmailVerificationResultDTO()
                .setInvalidCauseLabel(qevResultDTO == null ? null : qevResultDTO.result)
                .setSuggestion(qevResultDTO == null ? null : qevResultDTO.did_you_mean)
                .setEmailStatus(emailStatus);
    }
}
