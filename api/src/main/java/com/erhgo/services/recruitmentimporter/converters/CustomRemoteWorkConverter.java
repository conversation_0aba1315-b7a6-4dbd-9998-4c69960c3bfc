package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.utils.StringUtils;
import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class CustomRemoteWorkConverter extends AbstractBeanField<String, String> {
    @Override
    protected String convert(String rawValue) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        var value = StringUtils.trimQuote(rawValue).orElse(null);
        if ("total".equalsIgnoreCase(value)) {
            return CriteriaValue.getValueCodeForFullRemoteWork();
        }
        if ("partiel".equalsIgnoreCase(value)) {
            return CriteriaValue.getValueCodeForPartialRemoteWork();
        }
        return null;
    }
}
