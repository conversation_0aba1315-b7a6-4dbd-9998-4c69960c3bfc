package com.erhgo.services.recruitmentimporter.converters;

import com.erhgo.utils.DateTimeUtils;
import com.erhgo.utils.StringUtils;
import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

@NoArgsConstructor
public class DateConverter extends AbstractBeanField<OffsetDateTime, String> {

    @Override
    protected OffsetDateTime convert(String value) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        return StringUtils.trimQuote(value).map(DateTimeUtils::parseDate).orElse(null);
    }

}
