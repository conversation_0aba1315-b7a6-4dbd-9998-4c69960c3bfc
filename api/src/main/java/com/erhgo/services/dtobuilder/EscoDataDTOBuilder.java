package com.erhgo.services.dtobuilder;

import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.openapi.dto.EscoOccupationDetailDTO;
import com.erhgo.openapi.dto.EscoOccupationSummaryDTO;
import com.erhgo.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;

@Service
@RequiredArgsConstructor
public class EscoDataDTOBuilder {
    private final ModelMapper modelMapper;
    private final EscoSkillDTOBuilder escoSkillDTOBuilder;

    public EscoOccupationDetailDTO buildOccupationDTO(EscoOccupation occupation) {
        var escoOccupationDetailDTO = modelMapper.map(occupation, EscoOccupationDetailDTO.class);
        escoOccupationDetailDTO.setSkills(occupation
                .getSkills()
                .stream()
                .sorted(Comparator.comparing(EscoSkill::getTitle, StringUtils.DIATRIC_AND_CASE_INSENSITIVE_COMPARATOR.INSTANCE))
                .map(escoSkillDTOBuilder::buildSkillDTO)
                .toList()
        );
        return escoOccupationDetailDTO;
    }

    public EscoOccupationSummaryDTO buildOccupationSummaryDTO(EscoOccupation escoOccupation) {
        var summary = modelMapper.map(escoOccupation, EscoOccupationSummaryDTO.class);
        summary.setAlternativeLabels(new ArrayList<>(escoOccupation.getAlternativeLabels()));
        return summary;
    }
}
