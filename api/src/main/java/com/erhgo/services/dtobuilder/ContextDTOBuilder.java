package com.erhgo.services.dtobuilder;

import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.referential.Category;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.userprofile.experience.AbstractContextMet;
import com.erhgo.domain.userprofile.experience.JobContextMet;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.openapi.dto.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ContextDTOBuilder {

    private final AuditingDTOBuilder auditingDTOBuilder;

    public ContextDTO buildContextDTO(Context context) {
        final var contextDTO = new ContextDTO();
        contextDTO.setId(context.getId());
        contextDTO.setCode(context.getCode());
        contextDTO.setTitle(context.getTitle());
        contextDTO.setDescription(context.getDescription());
        contextDTO.setOrigin(context.getOrigin() != null ? ReferentialElementOriginDTO.fromValue(context.getOrigin().name()) : null);

        final var categoryLevelDTO = new ContextCategoryLevelDTO();
        categoryLevelDTO.setId(context.getCategoryLevel().getId());
        categoryLevelDTO.setTitle(context.getCategoryLevel().getTitle());
        categoryLevelDTO.setDescription(context.getCategoryLevel().getDescription());
        categoryLevelDTO.setScore(context.getCategoryLevel().getScore());

        final var categoryDTO = new ContextCategoryDTO();
        categoryDTO.setId(context.getCategoryLevel().getCategory().getId());
        categoryDTO.setCode(context.getCategoryLevel().getCategory().getCode());
        categoryDTO.setTitle(context.getCategoryLevel().getCategory().getTitle());
        categoryDTO.setDescription(context.getCategoryLevel().getCategory().getDescription());

        categoryLevelDTO.setCategory(categoryDTO);
        contextDTO.setCategoryLevel(categoryLevelDTO);

        auditingDTOBuilder.setAuditingAttributeDTO(context, contextDTO);

        return contextDTO;
    }

    public static ContextSummaryDTO buildSummary(Context context) {
        return new ContextSummaryDTO()
                .code(context.getCode())
                .description(context.getDescription())
                .id(context.getId())
                .title(context.getTitle());
    }

    public static ContextToEvaluateReferencingExperiencesDTO buildForRequirement(Context context, RecruitmentCandidature candidature) {
        var contextMet = candidature.getUserProfile().jobContextsMet().stream()
                .filter(cm -> cm.getContext().equals(context)).findFirst();

        var frequencyString = contextMet
                .map(AbstractContextMet::getFrequency)
                .map(frequency -> FrequencyDTO.fromValue(frequency.name()))
                .orElse(null);

        var experiencesIds = contextMet.map(JobContextMet::userExperiences)
                .orElse(Collections.emptySet())
                .stream()
                .map(UserExperience::getUuid)
                .collect(Collectors.toSet());

        var result = new ContextToEvaluateReferencingExperiencesDTO();
        result.experiencesIds(new ArrayList<>(experiencesIds))
                .contextId(context.getId())
                .title(candidature.getRecruitmentProfile().getLabelForContext(context))
                .frequency(frequencyString)
                .suggestedAnswers(SuggestedAnswersDTOBuilder.buildForQuestion(candidature.getRecruitmentProfile().getQuestionForContext(context)));

        return result;
    }

    public static ContextCategoryDTO toCategoryDTO(Category category) {
        return new ContextCategoryDTO()
                .code(category.getCode())
                .description(category.getDescription())
                .id(category.getId())
                .title(category.getTitle());
    }
}
