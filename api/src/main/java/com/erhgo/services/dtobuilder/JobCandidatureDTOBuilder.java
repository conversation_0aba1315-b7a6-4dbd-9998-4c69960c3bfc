package com.erhgo.services.dtobuilder;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.CandidatureEmailRefusalState;
import com.erhgo.domain.candidature.job.CandidatureNote;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.QuestionForContexts;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.*;
import com.erhgo.services.keycloak.UserRepresentation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class JobCandidatureDTOBuilder {
    private final UserExperienceDTOBuilder userExperienceDTOBuilder;
    private final ActivityDTOBuilder activityDTOBuilder;
    private final RecruitmentDTOBuilder recruitmentDTOBuilder;

    public static CandidaturePreviewDTO buildForPreview(RecruitmentCandidature candidature) {
        return new CandidaturePreviewDTO()
                .experiences(
                        candidature.getUserProfile().experiences().stream()
                                .sorted(Comparator.reverseOrder())
                                .map(UserExperienceDTOBuilder::buildExperienceSummary)
                                .toList()
                );

    }

    public CandidatureSummaryDTO buildSummary(RecruitmentCandidature candidature, Optional<UserRepresentation> keycloakUser) {

        var candidatureRecruitmentState = candidature.getCandidatureRecruitmentState() == null ? null : CandidatureSummaryDTO.CandidatureRecruitmentStateEnum.fromValue(candidature.getCandidatureRecruitmentState().name());
        var summary = new CandidatureSummaryDTO()
                .submissionDate(candidature.getSubmissionDate())
                .id(candidature.getId())
                .userId(candidature.getUserProfile().userId())
                .effort(candidature.getEffort())
                .code(candidature.getCode())
                .notes(candidature.getCandidatureNotes() == null ? Collections.emptyList() : candidature.getCandidatureNotes().stream().map(JobCandidatureDTOBuilder::buildNote).toList())
                .customAnswer(candidature.getCustomAnswer())
                .candidatureRecruitmentState(candidatureRecruitmentState)
                .refusalDate(candidature.getRefusalDate())
                .experiences(userExperienceDTOBuilder.getExperiencesSummariesForCandidature(candidature))
                .candidatureState(Optional.ofNullable(candidature.getGlobalCandidatureState()).map(Enum::name).map(CandidatureStateDTO::fromValue).orElse(null))
                .contextsPositioning(candidature
                        .getMandatoryContexts()
                        .stream()
                        .map(context -> buildContextPositioningDTO(candidature.getRecruitmentProfile().getQuestionForContext(context), candidature.getUserProfile(), context))
                        .sorted(Comparator.comparing(ContextPositioningDTO::getContextTitle))
                        .toList())
                .recruitment(new CandidatureSummaryRecruitmentDTO()
                        .id(candidature.getRecruitment().getId())
                        .jobId(candidature.getRecruitment().getJob().getId())
                        .jobTitle(candidature.getRecruitment().getJob().getTitle())
                        .profileTitle(candidature.getRecruitment().getRecruitmentProfile().getTitle())
                        .title(candidature.getRecruitment().getTitle())
                        .customQuestion(candidature.getRecruitment().getRecruitmentProfile().getCustomQuestion())
                )
                .archived(candidature.isArchived());

        if (candidature.isSelected()) {
            addPersonalInformationToSummary(candidature, keycloakUser, summary);
        }

        return summary;
    }

    public void addPersonalInformationToSummary(RecruitmentCandidature candidature, Optional<UserRepresentation> keycloakUser, CandidatureSummaryDTO summary) {
        var generalInformation = candidature.getUserProfile().generalInformation();
        summary.firstName(keycloakUser.map(UserRepresentation::getFirstName).orElse(""))
                .lastName(keycloakUser.map(UserRepresentation::getLastName).orElse("")).phoneNumber(generalInformation.getPhoneNumber())
                .contactTime(generalInformation.getContactTime() == null ? null : ContactTimeDTO.fromValue(generalInformation.getContactTime().name()))
                .email(keycloakUser.map(UserRepresentation::getEmail).orElse(""));
    }

    public static CandidatureNoteDTO buildNote(CandidatureNote note) {
        return new CandidatureNoteDTO()
                .id(note.getUuid())
                .text(note.getText());
    }

    public static CandidatureForProfileDTO buildForProfile(RecruitmentCandidature candidature) {

        return new CandidatureForProfileDTO()
                .organization(candidature.getOrganizationName())
                .title(candidature.getJob().getTitle())
                .state(getCandidatureState(candidature))
                .recruitmentCode(candidature.getRecruitment().getCode());
    }

    static UserCandidatureStateDTO getCandidatureState(AbstractCandidature candidature) {
        if (candidature.isRefused()) {
            if (candidature.getCandidatureRefusalState().getEmailSent() == CandidatureEmailRefusalState.WAITING) {
                return UserCandidatureStateDTO.VALIDATED;
            }
            return UserCandidatureStateDTO.REFUSED;
        }
        if (candidature.isRecruitmentCandidature()) {
            var recruitmentCandidature = (RecruitmentCandidature) candidature;
            if (recruitmentCandidature.getRecruitment().isCandidatureReceivable()) {
                return recruitmentCandidature.getState() == CandidatureState.VALIDATED ? UserCandidatureStateDTO.VALIDATED : UserCandidatureStateDTO.WAITING;
            }
            return UserCandidatureStateDTO.CLOSED;
        } else {
            return UserCandidatureStateDTO.VALIDATED;
        }
    }

    public ContextPositioningDTO buildContextPositioningDTO(Optional<QuestionForContexts> contextQuestion, UserProfile userProfile, Context context) {
        var userAnswer = userProfile.getFrequencyForContext(context.getId()).map(frequency -> FrequencyDTO.valueOf(frequency.toString())).orElse(null);
        return new ContextPositioningDTO()
                .questionTitle(contextQuestion.map(QuestionForContexts::getTitle).orElse(null))
                .contextTitle(context.getTitle())
                .userAnswer(userAnswer)
                .suggestedAnswers(SuggestedAnswersDTOBuilder.buildForQuestion(contextQuestion));
    }
}
