package com.erhgo.services.dtobuilder;

import com.erhgo.domain.classifications.workenvironment.WorkEnvironment;
import com.erhgo.openapi.dto.WorkEnvironmentDTO;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WorkEnvironmentDTOBuilder {
    private final ModelMapper modelMapper;

    public WorkEnvironmentDTO buildWorkEnvironmentDTO(WorkEnvironment workEnvironment) {
        return workEnvironment == null ? null : modelMapper.map(workEnvironment, WorkEnvironmentDTO.class);
    }
}
