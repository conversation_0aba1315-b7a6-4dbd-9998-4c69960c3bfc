package com.erhgo.services.dtobuilder;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.enums.Duration;
import com.erhgo.domain.enums.DurationType;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.openapi.dto.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UserExperienceDTOBuilder {

    private final ActivityDTOBuilder activityDTOBuilder;

    public static ExperienceSummaryDTO buildExperienceSummary(UserExperience e) {
        return new ExperienceSummaryDTO()
                .id(e.getUuid())
                .jobTitle(e.getJobTitle())
                .erhgoOccupationTitle(e.getErhgoOccupationTitle())
                .organizationName(e.getOrganizationName())
                .duration(e.getDuration() == null ? null : DurationDTO.fromValue(e.getDuration().name()))
                .durationType(e.getDurationType() == null ? null : DurationTypeDTO.fromValue(e.getDurationType().name()));
    }

    public static ExperienceDetailsDTO buildExperienceDetailsDTO(UserExperience experience) {
        return buildExperienceDetailsDTOForCandidature(experience);
    }

    private static ExperienceDetailsDTO buildExperienceDetailsDTOForCandidature(UserExperience experience) {
        return new ExperienceDetailsDTO()
                .organizationName(experience.getOrganizationName())
                .jobTitle(experience.getJobTitle())
                .experienceType(experience.getType() == null ? null : ExperienceTypeDTO.fromValue(experience.getType().name()))
                .id(experience.getUuid())
                .erhgoOccupationId(experience.getOccupationId())
                .erhgoOccupationTitle(experience.getErhgoOccupationTitle())
                .isOccupationBlacklisted(experience.isOccupationBlacklisted())
                .duration(Optional.ofNullable(experience.getDuration()).map(Duration::name).map(DurationDTO::fromValue).orElse(null))
                .durationType(Optional.ofNullable(experience.getDurationType()).map(DurationType::name).map(DurationTypeDTO::fromValue).orElse(null))
                .activities(experience.getAllActivities(true).stream().map(ActivityDTOBuilder::buildSummary).toList())
                ;
    }

    List<ExperienceDetailsWithCapacitiesDTO> getExperiencesWithCapacities(UserProfile userProfile) {
        return userProfile.experiences()
                .stream()
                .sorted()
                .map(this::buildDto)
                .toList();
    }

    public List<ExperienceSummaryDTO> getExperiences(UserProfile userProfile) {
        return userProfile.experiences()
                .stream()
                .sorted()
                .map(UserExperienceDTOBuilder::buildExperienceSummary)
                .toList();
    }

    private ExperienceDetailsWithCapacitiesDTO buildDto(UserExperience e) {
        var dto = new ExperienceDetailsWithCapacitiesDTO()
                .activities(
                        e.getAllActivities(true)
                                .stream()
                                .map(activityDTOBuilder::buildActivityLabelWithCapacitiesDTO)
                                .sorted(Comparator.comparing(ActivityLabelWithCapacitiesDTO::getTitle))
                                .toList()
                )
                .duration(Optional.ofNullable(e.getDuration()).map(Duration::name).map(DurationDTO::fromValue).orElse(null))
                .durationType(Optional.ofNullable(e.getDurationType()).map(DurationType::name).map(DurationTypeDTO::fromValue).orElse(null))
                .organizationName(e.getOrganizationName())
                .jobTitle(e.getJobTitle())
                .experienceType(e.getType() == null ? null : ExperienceTypeDTO.fromValue(e.getType().name()))
                .id(e.getUuid());

        if (e.getErhgoOccupation() != null) {
            dto.erhgoOccupationId(e.getOccupationId())
                    .erhgoOccupationTotalCapacities(e.getErhgoOccupation().getNumberOfCapacities())
                    .erhgoOccupationMasteryLevel(e.getErhgoOccupation().getLevel() == null ? null : MasteryLevelDTO.fromValue(e.getErhgoOccupation().getLevel().name()))
                    .erhgoOccupationTitle(e.getErhgoOccupationTitle())
            ;
        }
        return dto;
    }

    List<ExperienceDetailsDTO> getExperiencesForCandidature(AbstractCandidature candidature) {
        return candidature.getUserProfile().experiences().stream().map(UserExperienceDTOBuilder::buildExperienceDetailsDTOForCandidature).toList();
    }

    List<ExperienceSummaryDTO> getExperiencesSummariesForCandidature(RecruitmentCandidature candidature) {
        return candidature.getUserProfile().experiences().stream().map(UserExperienceDTOBuilder::buildExperienceSummary).toList();
    }
}
