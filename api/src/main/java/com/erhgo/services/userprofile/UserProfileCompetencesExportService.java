package com.erhgo.services.userprofile;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.enums.DriverLicence;
import com.erhgo.domain.enums.TypeContractCategory;
import com.erhgo.domain.enums.TypeWorkingTime;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.services.UserBehaviorDescriptionService;
import com.erhgo.services.exporter.PdfExporterService;
import com.erhgo.services.exporter.ProfileCompetencesViewObject;
import com.erhgo.services.generation.HashtagsGenerationService;
import com.erhgo.services.generation.SoftSkillDescriptionGenerationService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.RandomAccessReadBuffer;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserProfileCompetencesExportService {

    private static final int NB_HASHTAGS_COL = 3;
    private final PdfExporterService pdfExporterService;
    private final KeycloakService keycloakService;
    private final AbstractCandidatureRepository abstractCandidatureRepository;

    private final UserBehaviorDescriptionService userBehaviorDescriptionService;
    private final HashtagsGenerationService hashtagsGenerationService;
    private final SoftSkillDescriptionGenerationService softSkillDescriptionGenerationService;
    private final TransactionTemplate transactionTemplate;
    private final UserProfileRepository userProfileRepository;

    public FilePartProvider getProfileCompetenceForBatch(Long candidatureId, String fullname, ProfileCompetencesViewObject.AnonymousMode anonymousMode) throws IOException {
        try (var outputStream = new ByteArrayOutputStream()) {
            var candidature = abstractCandidatureRepository.findById(candidatureId).orElseThrow(() -> new EntityNotFoundException(candidatureId, AbstractCandidature.class));
            var dto = buildProfileCompetencesVOForUser(candidature.getUserId(), anonymousMode, candidature.getAnonymousCode(), false);
            if (!dto.isFullfilled()) {
                log.warn("Not generating competences profile for {}", dto);
                throw new GenericTechnicalException("do NOT send empty profile in batch process");
            }
            doGenerate(outputStream, dto);
            return new FilePartProvider(outputStream.toByteArray(), "profile_competences_%s.pdf".formatted(anonymousMode == ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE ? fullname.replaceAll("\\s", "_") : candidature.getAnonymousCode()), "application/pdf");
        }
    }

    @PreAuthorize(AuthorizeExpression.CANDIDATURE.CANDIDATURE_READ)
    public void getUserProfileForCandidatureFromSourcing(Long candidatureId, OutputStream outputStream, Boolean forcedAnonymous) throws IOException {
        var candidature = abstractCandidatureRepository.findById(candidatureId).orElseThrow(() -> new EntityNotFoundException(candidatureId, AbstractCandidature.class));
        var dto = buildProfileCompetencesVOForUser(
                candidature.getUserId(),
                (candidature.isAnonymous() || BooleanUtils.toBooleanDefaultIfNull(forcedAnonymous, false)) ? ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS : ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE,
                candidature.getAnonymousCode(),
                false);
        doGenerate(outputStream, dto);
    }

    private ProfileCompetencesViewObject buildProfileCompetencesVOForUser(String userId, ProfileCompetencesViewObject.AnonymousMode anonymousMode, String anonymousCode, boolean handicap) {
        var userOptional = anonymousMode == ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS ? Optional.<UserRepresentation>empty() : keycloakService.getFrontOfficeUserProfile(userId);
        transactionTemplate.execute(unused -> {
            var u = userProfileRepository.findByUserId(userId).orElseThrow();
            if (!u.isExportable()) {
                throw new GenericTechnicalException("Unable to generate competences profile for user %s".formatted(userId));
            }
            return u;
        });
        var attitude = userBehaviorDescriptionService.getUserBehaviorDescriptionOrGenerate(userId).getDescription();
        var hashtags = hashtagsGenerationService.getHashtagsOrGenerate(userId).stream().sorted().toList();
        var hashtagsPerLine = getHashtagsPerLine(hashtags);
        var softSkillsDescription = softSkillDescriptionGenerationService.getSoftSkillsDescriptionsOrGenerate(userId);
        return transactionTemplate.execute(unused -> {
            var userProfile = userProfileRepository.findByUserId(userId).orElseThrow();
            var generalInformationOptional = Optional.ofNullable(userProfile.generalInformation());
            var contracts = userProfile.getContractsFromCriteria().stream().map(TypeContractCategory::getLabel).sorted().collect(Collectors.joining(", "));
            var workingDuration = userProfile.getTypeWorkingTimeFromCriteria().stream().map(TypeWorkingTime::getLabel).sorted().collect(Collectors.joining(", "));
            var location = generalInformationOptional.map(GeneralInformation::getLocation).map(l -> "%s (%s) %s".formatted(l.getCity(), l.getPostcode(), computeMobilityText(l))).orElse("");
            var hardSkills = userProfile.getHardSkills().entrySet().stream()
                    .filter(kv -> StringUtils.isNotBlank(kv.getValue()))
                    .map(kv -> new ProfileCompetencesViewObject.SkillViewObject$(kv.getKey().getLabel(), kv.getValue()))
                    .toList();
            return ProfileCompetencesViewObject.builder()
                    .attitude(attitude)
                    .email(userOptional.map(UserRepresentation::getEmail).map(a -> a.replaceAll("@", "<wbr/>@")).orElse(""))
                    .firstName(userOptional.map(UserRepresentation::getFirstName).map(com.erhgo.utils.StringUtils::firstNameCase).orElse(""))
                    .lastName(userOptional.map(UserRepresentation::getLastName).map(String::toUpperCase).orElse(""))
                    .phone(Strings.nullToEmpty(userProfile.getPhoneNumber()))
                    .hasDrivingLicence(userProfile.getDriverLicenceFromCriteria() == DriverLicence.LICENCE_B)
                    .location(location)
                    .contracts(org.apache.commons.lang3.StringUtils.trimToEmpty(contracts))
                    .workingDuration(org.apache.commons.lang3.StringUtils.trimToEmpty(workingDuration))
                    .hashtags(hashtagsPerLine)
                    .softSkills(softSkillsDescription.stream().limit(hardSkills.size() >= 2 ? 3 : 4).map(ProfileCompetencesViewObject.SkillViewObject$::new).toList())
                    .hardSkills(hardSkills)
                    .anonymousMode(anonymousMode)
                    .anonymousCode(Optional.ofNullable(anonymousCode).map(String::toUpperCase).orElse(null))
                    .handicap(handicap)
                    .build();
        });
    }

    private static @NotNull String computeMobilityText(Location l) {
        var mobility = "<wbr />";
        if (l.anywhere()) mobility += " Mobilité nationale";
        else if (l.getRadiusInKm() != null) mobility += "<wbr />+/- %s km".formatted(l.getRadiusInKm());
        return mobility;
    }

    private static @NotNull List<List<String>> getHashtagsPerLine(List<String> hashtags) {
        return hashtags.isEmpty() ? Collections.emptyList() : ListUtils.partition(hashtags.stream().sorted(Comparator.comparing(String::length))
                .map(com.erhgo.utils.StringUtils::addWordBreaksInHashtag)
                .toList(), NB_HASHTAGS_COL);
    }

    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void getUserProfileForUser(String userId, OutputStream outputStream, boolean anonymous, boolean isHandicap) throws IOException {
        var dto = buildProfileCompetencesVOForUser(userId, anonymous ? ProfileCompetencesViewObject.AnonymousMode.BOTH : ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE, "", isHandicap);
        doGenerate(outputStream, dto);
    }

    private void doGenerate(OutputStream outputStream, ProfileCompetencesViewObject dto) throws IOException {
        var isBoth = dto.anonymousMode() == ProfileCompetencesViewObject.AnonymousMode.BOTH;
        var anonymousOutputStream = isBoth ? new ByteArrayOutputStream() : outputStream;
        if (dto.anonymousMode() != ProfileCompetencesViewObject.AnonymousMode.NOMINATIVE) {
            generateAnonymously(anonymousOutputStream, dto);
        }
        var nominativeOutputStream = isBoth ? new ByteArrayOutputStream() : outputStream;
        if (dto.anonymousMode() != ProfileCompetencesViewObject.AnonymousMode.ANONYMOUS) {
            generateNominatively(nominativeOutputStream, dto);
        }
        if (isBoth) {
            try (var anonymousStream = new ByteArrayInputStream(((ByteArrayOutputStream) anonymousOutputStream).toByteArray());
                 var anonymousBuffer = new RandomAccessReadBuffer(anonymousStream);
                 var anonymousDoc = Loader.loadPDF(anonymousBuffer);
                 var nominativeStream = new ByteArrayInputStream(((ByteArrayOutputStream) nominativeOutputStream).toByteArray());
                 var nominativeBuffer = new RandomAccessReadBuffer(nominativeStream);
                 var nominativeDoc = Loader.loadPDF(nominativeBuffer);
            ) {
                var pdfMerger = new PDFMergerUtility();
                pdfMerger.appendDocument(anonymousDoc, nominativeDoc);
                anonymousDoc.save(outputStream);
            }

            log.trace("PDFs fusionnés avec succès");
        }
    }

    private void generateAnonymously(OutputStream outputStream, ProfileCompetencesViewObject dto) throws IOException {
        var anonymousParams = dto.clonedBuilder()
                .firstName(null)
                .lastName(null)
                .phone(null)
                .email(null)
                .build();
        pdfExporterService.generatePdf("profileCompetences", anonymousParams, outputStream);
    }

    private void generateNominatively(OutputStream outputStream, ProfileCompetencesViewObject dto) throws IOException {
        var anonymousParams = dto.clonedBuilder()
                .anonymousCode(null)
                .build();
        pdfExporterService.generatePdf("profileCompetences", anonymousParams, outputStream);
    }
}
