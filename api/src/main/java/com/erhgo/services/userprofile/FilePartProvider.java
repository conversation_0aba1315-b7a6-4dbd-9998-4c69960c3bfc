package com.erhgo.services.userprofile;

import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.InputStreamSource;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

@Getter
@AllArgsConstructor
@Accessors(fluent = true)
@NoArgsConstructor
public class FilePartProvider implements InputStreamSource {

    private byte[] content;
    private String fileName;
    private String contentType;

    public FilePartProvider(MultipartFile file) {
        try {
            this.content = file.getBytes();
            this.fileName = file.getOriginalFilename();
            this.contentType = file.getContentType();
        } catch (IOException e) {
            throw new GenericTechnicalException("Unable to get file content", e);
        }
    }

    public FilePartProvider(byte[] bytes, String fileUrl) {
        this.content = bytes;
        this.fileName = StringUtils.lastValueOfSplitString(fileUrl, '/');
        this.contentType = StringUtils.getContentType(this.fileName);
    }

    public boolean isValid() {
        return content != null && content.length > 0;
    }

    public String readAllBytesAsStringifiedBase64() throws IOException {
        return StringUtils.toStringifiedBase64(content);
    }

    public byte[] readAllBytes() throws IOException {
        return content;
    }

    @Override
    public @NotNull InputStream getInputStream() {
        return new ByteArrayInputStream(content);
    }

    public long getSize() {
        return content.length;
    }

    public boolean isEmpty() {
        return content.length == 0;
    }
}
