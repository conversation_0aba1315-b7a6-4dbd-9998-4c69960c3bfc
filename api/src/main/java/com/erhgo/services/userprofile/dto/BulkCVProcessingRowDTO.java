package com.erhgo.services.userprofile.dto;

import com.opencsv.bean.CsvBindByPosition;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Data
@Accessors(fluent = true)
@Slf4j
public class BulkCVProcessingRowDTO {

    @CsvBindByPosition(position = 0)
    private String email;

    @CsvBindByPosition(position = 1)
    private String url;

    public boolean isValid() {
        var valid = StringUtils.isNotBlank(email) && StringUtils.isNotBlank(url);
        if (!valid) {
            log.warn("Invalid row - email: {}, url: {}", email, url);
        }
        return valid;
    }
}
