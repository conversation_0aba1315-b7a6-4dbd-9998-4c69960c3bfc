package com.erhgo.services.userprofile;

import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.PersonalEmailDomainRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.FrontOfficePersonalEmailDomainMessageDTO;
import com.erhgo.services.notifier.messages.FrontofficeNotifierMessageDTO;
import com.erhgo.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserProfileProvider {

    private final UserProfileRepository userProfileRepository;
    private final PersonalEmailDomainRepository personalEmailDomainRepository;
    private final KeycloakService keycloakService;
    private final MailingListService mailingListService;
    private final SecurityService securityService;
    private final Notifier notifier;

    // FIXME: won't be needed after we control all user accounts' creations (ERHGO-75)
    private Optional<UserProfile> createCandidateProfileFromKeycloak(String userId, boolean silently) {

        var keycloakProfile = keycloakService.getFrontOfficeUserProfileWithGroups(userId);

        var optionalProfile = keycloakProfile
                .map(this::initializeProfile)
                .map(userProfileRepository::save);
        if (!silently) {
            keycloakProfile.ifPresent(userProfile -> {
                sendMessageForNewUser(userId);
                sendMessageForUserWithList(userId, userProfile.getEmail());
            });
        }
        return optionalProfile;
    }

    private void sendMessageForNewUser(String userId) {
        var message = FrontofficeNotifierMessageDTO
                .builderForAccountCreation()
                .iconEmoji(":tada:")
                .userId(userId)
                .text("Nouvel utilisateur créé -")
                .build();
        notifier.sendMessage(message);
    }

    private void sendMessageForUserWithList(String userId, String email) {
       if (personalEmailDomainRepository.findAll().stream().noneMatch(p -> p.emailHasThisDomain(email))) {
           var message = FrontOfficePersonalEmailDomainMessageDTO
                   .builderForPersonalEmailDomain()
                   .userId(userId)
                   .email(email)
                   .build();
           notifier.sendMessage(message);
       }
    }

    private UserProfile initializeProfile(UserRepresentation keycloakUser) {
        var userId = keycloakUser.getId();
        var phoneNumber = StringUtils.normalizePhone(keycloakUser.getFOPhoneNumber());
        var userProfile = new UserProfile()
                .uuid(UUID.fromString(userId))
                .userId(userId)
                .indexationRequiredDate(new Date())
                .generalInformation(GeneralInformation.builder()
                        .phoneNumber(phoneNumber)
                        .userId(userId)
                        .build());
        userProfile.generalInformation().setUserProfile(userProfile);
        var groups = Optional.ofNullable(keycloakUser.getGroups()).map(c -> c.stream().filter(Role::isOrganizationRoleOrGroup).collect(Collectors.toSet())).orElse(Collections.emptySet());
        if (!groups.isEmpty()) {
            userProfile.updatedChannels(groups, UserChannel.ChannelSourceType.UNKNOWN);
        }
        if (phoneNumber != null) {
            keycloakService.removePhoneForFOUser(userId);
        }
        return userProfile;
    }

    @Transactional
    public UserProfile getAuthenticatedUserProfileOrCreate() {
        var userId = securityService.getKeycloakUserId().orElseThrow();
        return getUserProfileOrCreate(userId);
    }

    @Transactional
    public UserProfile getUserProfileOrCreate(String userId) {
        return userProfileRepository.findByUserId(userId)
                .orElseGet(() -> this.createCandidateProfileFromKeycloak(userId, false).orElseThrow());
    }

    @Transactional
    public UserProfile getUserProfileOrCreateSilently(String userId) {
        return userProfileRepository.findByUserId(userId)
                .orElseGet(() -> this.createCandidateProfileFromKeycloak(userId, true).orElseThrow());
    }

    @Transactional
    public UserProfile getUserProfileOrCreateForHandicap(String userId) {
        return getUserProfileOrCreate(userId).isFromHandicap(true);
    }
}
