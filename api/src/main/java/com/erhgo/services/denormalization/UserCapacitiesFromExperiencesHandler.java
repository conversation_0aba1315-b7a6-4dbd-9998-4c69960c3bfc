package com.erhgo.services.denormalization;

import com.erhgo.domain.referential.Activity;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.userprofile.experience.UserExperience;
import com.erhgo.repositories.UserExperienceRepository;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserCapacitiesFromExperiencesHandler implements ActivityUpdateListener {

    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final UserExperienceRepository userExperienceRepository;
    private final UserExperienceRepository userProfileRepository;

    @Override
    public void notifyCapacitiesRemoved(Activity activity, Collection<Capacity> removedCapacities) {
        addOrRemoveActivityNewCapacitiesToUserProfile(activity);
    }

    private void addOrRemoveActivityNewCapacitiesToUserProfile(Activity activity) {
        var impactedOccupations = erhgoOccupationRepository.findByActivityIdIn(Set.of(activity.getUuid()), Pageable.unpaged());
        var impactedUsers = userExperienceRepository.findByErhgoOccupationIn(impactedOccupations).stream().map(UserExperience::getUserProfile).collect(Collectors.toSet());
        userProfileRepository.markUsersAsStaleForCapacityDenormalization(impactedUsers);
    }

    @Override
    public void notifyCapacitiesAdded(Activity activity, Collection<Capacity> addedCapacities) {
        addOrRemoveActivityNewCapacitiesToUserProfile(activity);
    }
}
