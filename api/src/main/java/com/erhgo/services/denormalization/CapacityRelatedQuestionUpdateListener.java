package com.erhgo.services.denormalization;

import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.CapacityRelatedQuestionResponse;

import java.util.Collection;

public interface CapacityRelatedQuestionUpdateListener {

    void notifyCapacitiesRemoved(CapacityRelatedQuestionResponse response, Collection<Capacity> removedCapacities);

    void notifyCapacitiesAdded(CapacityRelatedQuestionResponse response, Collection<Capacity> addedCapacities);

}
