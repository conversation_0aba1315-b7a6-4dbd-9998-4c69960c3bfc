package com.erhgo.services;

import com.erhgo.domain.classifications.erhgooccupation.*;
import com.erhgo.domain.classifications.esco.EscoOccupation;
import com.erhgo.domain.classifications.esco.EscoSkill;
import com.erhgo.domain.classifications.rome.RomeOccupation;
import com.erhgo.domain.classifications.workenvironment.WorkEnvironment;
import com.erhgo.domain.enums.BehaviorCategory;
import com.erhgo.domain.enums.ErhgoOccupationSearchOrder;
import com.erhgo.domain.exceptions.EntityAlreadyExistException;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidSortException;
import com.erhgo.domain.referential.Behavior;
import com.erhgo.domain.referential.Context;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.*;
import com.erhgo.security.Role;
import com.erhgo.services.denormalization.ErhgoOccupationUpdateListener;
import com.erhgo.services.dtobuilder.CapacityDTOBuilder;
import com.erhgo.services.dtobuilder.ErhgoOccupationDataDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.erhgo.services.exporter.PdfExporterService;
import com.erhgo.services.search.ErhgoOccupationFinder;
import com.erhgo.services.search.ErhgoOccupationIndexer;
import jakarta.annotation.security.RolesAllowed;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.stream.IntStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class ErhgoOccupationService {

    private final ErhgoOccupationFinder erhgoOccupationFinder;
    private final ErhgoOccupationIndexer erhgoOccupationIndexer;
    private final EscoOccupationRepository escoOccupationRepository;
    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final RomeOccupationRepository romeOccupationRepository;
    private final ErhgoOccupationDataDTOBuilder erhgoDataDTOBuilder;
    private final UserProfileRepository userProfileRepository;
    private final JobRepository jobRepository;
    private final EscoSkillRepository escoSkillRepository;
    private final WorkEnvironmentRepository workEnvironmentRepository;
    private final EntityManager entityManager;
    private final JobActivityLabelRepository jobActivityLabelRepository;
    private final UserExperienceRepository userExperienceRepository;
    private final Collection<ErhgoOccupationUpdateListener> erhgoOccupationUpdateListeners;
    private final PdfExporterService pdfExporterService;
    private final CriteriaRepository criteriaRepository;
    private final ErhgoClassificationRepository erhgoClassificationRepository;

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public ErhgoOccupationPageDTO getErhgoOccupationPage(Integer size, Integer page, List<ErhgoSearchOrderDTO> by, List<SortDirectionDTO> directions, String query, UUID activityId) {
        var orders = getErhgoOccupationPageOrder(by, directions);

        var pageable = PageRequest.of(page, size, Sort.by(orders));
        var occupationPage = erhgoOccupationRepository.search(query, activityId, pageable);

        return PageDTOBuilder.buildErhgoOccupationPage(occupationPage.map(erhgoDataDTOBuilder::buildOccupationSummaryDTO));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public ErhgoOccupationOTPageDTO getErhgoOccupationOTPage(Integer size, Integer page, SortDirectionDTO direction, String query) {
        var field = ErhgoOccupationSearchOrder.TITLE.getField();
        var order = direction == SortDirectionDTO.ASC ? Sort.Order.asc(field) : Sort.Order.desc(field);

        var pageable = PageRequest.of(page, size, Sort.by(order));
        var occupationPage = erhgoOccupationRepository.search(query, null, pageable);

        return PageDTOBuilder.buildErhgoOccupationOTPage(occupationPage.map(erhgoDataDTOBuilder::buildOccupationOTSummaryDTO));
    }

    private List<Sort.Order> getErhgoOccupationPageOrder(List<ErhgoSearchOrderDTO> by, List<SortDirectionDTO> directions) {
        return IntStream
                .range(0, by == null ? 0 : by.size())
                .mapToObj(i -> {
                    try {
                        @SuppressWarnings("ConstantConditions") // by cannot be null here
                        var field = ErhgoOccupationSearchOrder.valueOf(by.get(i).toString()).getField();
                        return directions.get(i) == SortDirectionDTO.ASC ? Sort.Order.asc(field) : Sort.Order.desc(field);
                    } catch (IndexOutOfBoundsException e) {
                        throw new InvalidSortException("No SortDirection found in index " + i);
                    }
                }).toList();
    }

    @Transactional(readOnly = true)
    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE})
    public ErhgoOccupationDetailDTO getByUuid(UUID uuid) {
        var occupation = getOrThrow(uuid);
        return erhgoDataDTOBuilder.buildOccupationDTO(occupation);
    }

    private ErhgoOccupation getOrThrow(UUID uuid) {
        return erhgoOccupationRepository.findById(uuid).orElseThrow(() -> new EntityNotFoundException(uuid, ErhgoOccupation.class));
    }

    private EscoOccupation getOrThrow(String uri) {
        return escoOccupationRepository.findById(uri).orElseThrow(() -> new EntityNotFoundException(uri, EscoOccupation.class));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public ErhgoOccupationState qualifyOccupation(UUID uuid) {
        var occupation = getOrThrow(uuid);
        occupation.qualifyOccupation();
        updateOccupationIndexation(occupation);
        return occupation.getQualificationState();
    }


    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public ErhgoOccupationState unqualifyOccupation(UUID uuid) {
        var occupation = getOrThrow(uuid);
        occupation.unqualifyOccupation();
        updateOccupationIndexation(occupation);
        return occupation.getQualificationState();
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public List<ErhgoOccupationsForLabelDTO> erhgoOccupationsForLabels(List<String> labels) {
        var sanitizedLabels = labels.stream()
                .map(String::toLowerCase)
                .map(String::trim)
                .toList();
        var occupations = erhgoOccupationRepository.getErhgoOccupationsWithLabels(sanitizedLabels);
        return erhgoDataDTOBuilder.buildErhgoOccupationsDTO(occupations);
    }


    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public ErhgoOccupationDetailDTO linkEscoOccupationToErhgoOccupation(EditEscoOccupationCommandDTO commandDTO) {
        var erhgo = getOrThrow(commandDTO.getId());
        var esco = getOrThrow(commandDTO.getEscoUri());
        erhgo.addEscoOccupation(esco);
        updateOccupationIndexation(erhgo);
        erhgoOccupationUpdateListeners.forEach(c -> c.notifyRomeOrEscoUpdated(erhgo));
        return erhgoDataDTOBuilder.buildOccupationDTO(erhgo);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public ErhgoOccupationDetailDTO unlinkEscoOccupationFromErhgoOccupation(EditEscoOccupationCommandDTO commandDTO) {
        var erhgo = getOrThrow(commandDTO.getId());
        var esco = getOrThrow(commandDTO.getEscoUri());
        erhgo.removeEscoOccupation(esco);
        updateOccupationIndexation(erhgo);
        erhgoOccupationUpdateListeners.forEach(c -> c.notifyRomeOrEscoUpdated(erhgo));
        return erhgoDataDTOBuilder.buildOccupationDTO(erhgo);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void linkRomeToErhgoOccupation(UUID uuid, String romeCode) {
        var erhgo = getOrThrow(uuid);
        var rome = getRomeOccupation(romeCode);
        erhgoOccupationUpdateListeners.forEach(c -> c.notifyRomeOrEscoUpdated(erhgo));
        erhgo.addRome(rome);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void unlinkRomeFromErhgoOccupation(UUID uuid, String romeCode) {
        var erhgo = getOrThrow(uuid);
        var rome = getRomeOccupation(romeCode);
        erhgoOccupationUpdateListeners.forEach(c -> c.notifyRomeOrEscoUpdated(erhgo));
        erhgo.removeRome(rome);
    }

    private RomeOccupation getRomeOccupation(String romeCode) {
        return romeOccupationRepository.findById(romeCode)
                .orElseThrow(() -> new EntityNotFoundException(romeCode, RomeOccupation.class));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void updateMasteryLevel(UpdateMasteryLevelCommandDTO updateMasteryLevelCommandDTO) {
        var occupation = getOrThrow(updateMasteryLevelCommandDTO.getId());
        var levelUpdated = occupation.updateLevel(MasteryLevel.valueOf(updateMasteryLevelCommandDTO.getLevel().toString()));
        if (levelUpdated) {
            erhgoOccupationUpdateListeners.forEach(l -> l.notifyLevelUpdated(occupation));
        }
        updateOccupationIndexation(occupation);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void updateDescription(UpdateDescriptionCommandDTO updateDescriptionCommandDTO) {
        var occupation = getOrThrow(updateDescriptionCommandDTO.getId());
        occupation.setDescription(updateDescriptionCommandDTO.getDescription());
        updateOccupationIndexation(occupation);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void updateBehaviorsDescription(UpdateBehaviorsDescriptionCommandDTO updateBehaviorsDescriptionCommandDTO) {
        var occupation = getOrThrow(updateBehaviorsDescriptionCommandDTO.getId());
        occupation.setBehaviorsDescription(updateBehaviorsDescriptionCommandDTO.getDescription());
        updateOccupationIndexation(occupation);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void addActivitiesToOccupation(OccupationReferentialEntitiesEditCommandDTO command) {
        var occupation = getOrThrow(command.getErhgoOccupationId());
        var activities = jobActivityLabelRepository.findAllById(new HashSet<>(command.getReferentialEntityIds()));
        occupation.addActivities(activities);
        updateOccupationIndexation(occupation);
        erhgoOccupationUpdateListeners.forEach(c -> c.notifyActivitiesAdded(occupation, activities.toArray(JobActivityLabel[]::new)));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void removeActivitiesFromOccupation(OccupationReferentialEntitiesEditCommandDTO command) {
        var occupation = removeEntities(command);
        updateOccupationIndexation(occupation);

        var activitiesToRemove = jobActivityLabelRepository.findAllById(command.getReferentialEntityIds())
                .toArray(JobActivityLabel[]::new);
        erhgoOccupationUpdateListeners.forEach(c -> c.notifyActivitiesRemoved(occupation, activitiesToRemove));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void setOccupationActivityMandatoryState(OccupationReferentialEntityEditWithStateCommandDTO command) {
        updateEntityMandatoryState(command);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void addContextToOccupation(OccupationReferentialEntityEditCommandDTO command) {
        var occupation = getOrThrow(command.getErhgoOccupationId());
        occupation.addContext(getOrThrow(command.getReferentialEntityId(), Context.class));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void removeContextFromOccupation(OccupationReferentialEntityEditCommandDTO command) {
        removeEntity(command);
    }

    private ErhgoOccupation removeEntity(OccupationReferentialEntityEditCommandDTO command) {
        var occupation = getOrThrow(command.getErhgoOccupationId());
        occupation.removeEntityById(command.getReferentialEntityId());
        return occupation;
    }

    private ErhgoOccupation removeEntities(OccupationReferentialEntitiesEditCommandDTO command) {
        var occupation = getOrThrow(command.getErhgoOccupationId());
        command.getReferentialEntityIds().forEach(occupation::removeEntityById);
        return occupation;
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void setOccupationContextMandatoryState(OccupationReferentialEntityEditWithStateCommandDTO command) {
        updateEntityMandatoryState(command);
    }

    private void updateEntityMandatoryState(OccupationReferentialEntityEditWithStateCommandDTO command) {
        var occupation = getOrThrow(command.getErhgoOccupationId());
        occupation.setEntityMandatoryState(command.getReferentialEntityId(), MandatoryState.valueOf(command.getState().name()));
    }

    private <A> A getOrThrow(UUID uuid, Class<A> clazz) {
        return Optional.ofNullable(entityManager.find(clazz, uuid))
                .orElseThrow(() -> new EntityNotFoundException(uuid, clazz));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public ErhgoOccupationBehaviorsCategoriesDTO addBehaviorToOccupation(OccupationReferentialEntityEditCommandDTO command) {
        var occupation = getOrThrow(command.getErhgoOccupationId());
        occupation.addBehavior(getOrThrow(command.getReferentialEntityId(), Behavior.class));
        return erhgoDataDTOBuilder.buildOccupationBehaviorsCategories(occupation);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public ErhgoOccupationBehaviorsCategoriesDTO removeBehaviorFromOccupation(OccupationReferentialEntityEditCommandDTO command) {
        var occupation = getOrThrow(command.getErhgoOccupationId());
        occupation.removeEntityById(command.getReferentialEntityId());
        return erhgoDataDTOBuilder.buildOccupationBehaviorsCategories(occupation);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void editAlternativeLabels(EditAlternativeLabelsCommandDTO command) {
        var occupation = getOrThrow(command.getId());
        occupation.setTitleAndAlternativeLabels(command.getTitle(), command.getAlternativeLabels());
        updateOccupationIndexation(occupation);
    }

    @Transactional
    public void addAlternativeLabelBlockingNonAsync(UUID occupationId, String label) {
        var occupation = getOrThrow(occupationId);
        occupation.getAlternativeLabels().add(label);

        try {
            erhgoOccupationIndexer.updateOccupationIndexation(occupation).get();
        } catch (ExecutionException | InterruptedException | CancellationException e) {
            log.error("failed to index erhgo occupation {}", occupationId, e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }
    }

    private void updateOccupationIndexation(ErhgoOccupation occupation) {
        erhgoOccupationIndexer.updateOccupationIndexation(occupation);
    }

    @Transactional(readOnly = true)
    public List<ErhgoOccupationSearchDTO> searchOccupations(String query, boolean highlights) {
        return erhgoOccupationFinder.searchOccupations(query, highlights);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public void indexAll() {
        erhgoOccupationIndexer.replaceAllOccupationsWith(erhgoOccupationRepository.findAllForIndexation());
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void unlinkSkillFromErhgoOccupation(UnlinkSkillFromErhgoOccupationCommandDTO command) {
        var occupation = getOrThrow(command.getId());
        var skill = escoSkillRepository
                .findById(command.getSkillUri())
                .orElseThrow(() -> new EntityNotFoundException(command.getSkillUri(), EscoSkill.class));
        occupation.removeSkill(skill);
        updateOccupationIndexation(occupation);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public ErhgoOccupationBehaviorsCategoriesDTO setOccupationBehaviorFamily(EditOccupationBehaviorCategoryCommandDTO commandDTO) {
        var occupation = getOrThrow(commandDTO.getId());
        var behaviorCategory = commandDTO.getBehaviorCategory() == null ? null : BehaviorCategory.valueOf(commandDTO.getBehaviorCategory().name());
        occupation.updateBehaviorCategory(behaviorCategory, commandDTO.getOccupationCategoryIndex());
        return erhgoDataDTOBuilder.buildOccupationBehaviorsCategories(occupation);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public CapacitiesResultDTO findCapacities(UUID id) {
        var occupation = getOrThrow(id);
        var occurrences = CapacityDTOBuilder.buildCapacityAndLevelDTOS(occupation);
        return new CapacitiesResultDTO().capacities(occurrences).fullyQualified(occupation.isQualificationInFinalState());
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void createOccupation(CreateErhgoOccupationCommandDTO command) {
        erhgoOccupationRepository.findById(command.getId()).ifPresent(erhgoOccupation -> {
            throw new EntityAlreadyExistException(ErhgoOccupation.class, command.getId());
        });

        var occupation = erhgoOccupationRepository.save(ErhgoOccupation
                .builder()
                .id(command.getId())
                .title(command.getTitle())
                .qualificationState(ErhgoOccupationState.NONE)
                .occupationCreationReason(OccupationCreationReason.valueOf(command.getOccupationCreationReason().name()))
                .build()
                .computeQualificationState());

        updateOccupationIndexation(occupation);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void mergeOccupations(MergeOccupationsCommandDTO command) {
        var targetOccupation = getOrThrow(command.getTargetOccupationId());
        var occupationToDelete = getOrThrow(command.getOccupationIdToDelete());
        targetOccupation.mergeOccupations(occupationToDelete, command.getIgnoreActivities());
        userExperienceRepository.findByErhgoOccupationId(occupationToDelete.getId())
                .forEach(userExperience -> userExperience.setErhgoOccupation(targetOccupation));
        userProfileRepository.findByUserRegistrationStateSelectedOccupation(occupationToDelete).forEach(u -> u.userRegistrationState().setSelectedOccupation(targetOccupation));
        jobRepository.findByErhgoOccupation(occupationToDelete).forEach(j -> j.setErhgoOccupation(targetOccupation));
        erhgoOccupationRepository.delete(occupationToDelete);
        erhgoOccupationIndexer.removeOccupation(occupationToDelete);
        updateOccupationIndexation(targetOccupation);
    }

    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE})
    @Transactional(readOnly = true)
    public ErhgoOccupationSumUpDTO getSumUp(UUID id) {
        var occupation = getOrThrow(id);
        return erhgoDataDTOBuilder.buildOccupationSumUpDTO(occupation);
    }

    @RolesAllowed({Role.ODAS_ADMIN, Role.CANDIDATE})
    @Transactional(readOnly = true)
    public void writeErhgoOccupationPDF(UUID id, ByteArrayOutputStream outputStream) throws IOException {
        var dto = getSumUp(id);
        pdfExporterService.generatePdf("erhgoOccupationDetail", dto, outputStream);
    }


    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void updateWorkEnvironments(UpdateWorkEnvironmentsCommandDTO dto) {
        var occupation = getOrThrow(dto.getOccupationId());
        var codes = dto.getWorkEnvironmentCodes();

        var environments = codes == null ? Collections.<WorkEnvironment>emptySet() :
                new HashSet<>(workEnvironmentRepository.findByCodeIn(codes));

        var missingCodes = dto.getWorkEnvironmentCodes().stream().filter(x -> environments.stream().noneMatch(y -> y.getCode().equals(x))).toList();
        if (!missingCodes.isEmpty()) {
            throw new EntityNotFoundException(missingCodes, WorkEnvironment.class);
        }

        occupation.getWorkEnvironments().clear();
        occupation.getWorkEnvironments().addAll(environments);

        log.info("Updated occupation {} with environments {}", dto.getOccupationId(), dto.getWorkEnvironmentCodes());
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void updateSpecifications(UpdateSpecificationsCommandDTO dto) {
        var occupation = getOrThrow(dto.getOccupationId());

        occupation.setTechnical(dto.getIsTechnical());

        log.info("Update occupation {} with specifications (technical: {})", dto.getOccupationId(), dto.getIsTechnical());
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void updateCriteriaValues(UpdateCriteriaValuesCommandDTO command) {
        var occupation = getOrThrow(command.getErhgoOccupationId());
        occupation.resetCriteriaValues(command.getCriteriaValueCodes().isEmpty() ? new HashSet<>() : criteriaRepository.findCriteriaValuesByCriteriaValuesCodeIn(command.getCriteriaValueCodes()));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void updateErhgoOccupationErhgoClassifications(UpdateErhgoClassificationsCommandDTO command) {
        var occupation = getOrThrow(command.getId());
        occupation.resetErhgoClassifications(erhgoClassificationRepository.findErhgoClassificationByCodeIn(command.getErhgoClassifications()));
    }
}
