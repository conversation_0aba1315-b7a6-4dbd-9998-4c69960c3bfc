package com.erhgo.services.search;

import com.algolia.search.DefaultSearchClient;
import com.algolia.search.SearchClient;
import com.algolia.search.SearchIndex;
import com.algolia.search.exceptions.AlgoliaApiException;
import com.algolia.search.models.indexing.BatchIndexingResponse;
import com.algolia.search.models.indexing.DeleteResponse;
import com.algolia.search.models.indexing.Query;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupationState;
import com.erhgo.domain.exceptions.AlgoliaFailureException;
import com.erhgo.domain.referential.JobActivityLabel;
import com.erhgo.openapi.dto.ErhgoOccupationSearchDTO;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.services.search.Algolia.AbstractAlgoliaResultItem.MatchLevel;
import com.erhgo.utils.StringSimilarityUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.erhgo.services.search.Algolia.AlgoliaSnippetResultItem.ALGOLIA_SNIPPET_RESULT_ITEM_COMPARATOR;

@Service
@Slf4j
@ConditionalOnExpression("!'${algolia.applicationId}'.isEmpty() and !'${algolia.adminApiKey}'.isEmpty()")
public class Algolia implements ErhgoOccupationFinder, ErhgoOccupationIndexer {

    private final TransactionTemplate transactionTemplate;
    private SearchIndex<AlgoliaOccupationDTO> occupationIndex;
    private ErhgoOccupationRepository erhgoOccupationRepository;


    private static final String TITLE = "title";
    private static final String DESCRIPTION = "description";
    private static final String ACTIVITIES = "activities";

    public Algolia(
            @Value("${algolia.applicationId}") String applicationId,
            @Value("${algolia.adminApiKey}") String apiKey,
            @Value("${algolia.indexPrefix}") String indexPrefix,
            TransactionTemplate transactionTemplate,
            ErhgoOccupationRepository erhgoOccupationRepository) {
        log.info("Initializing Algolia client with application ID '" + applicationId + "' and position prefix '" + indexPrefix + "'");
        var searchClient = DefaultSearchClient.create(applicationId, apiKey);

        createOccupationIndexes(indexPrefix, searchClient);
        this.transactionTemplate = transactionTemplate;
        this.erhgoOccupationRepository = erhgoOccupationRepository;
    }

    private void createOccupationIndexes(String indexPrefix, SearchClient client) {
        occupationIndex = client.initIndex(indexPrefix + "_occupation_index", AlgoliaOccupationDTO.class);
        occupationIndex.setSettings(
                AlgoliaUtils.commonIndexSettings()
                        .setSearchableAttributes(Arrays.asList("title,alternativeLabels", DESCRIPTION, ACTIVITIES))
                        .setCustomRanking(List.of("desc(qualificationState)", "asc(level)"))
        );
    }


    private void verifyQueryLength(String query) {
        var queryBytes = query.getBytes();
        if (queryBytes.length > AlgoliaUtils.ALGOLIA_SEARCH_QUERY_MAX_LENGTH) {
            throw new IllegalArgumentException("Value too big for the query (" + query + ")");
        }
    }

    private void prepareRequestForHighlightWithFields(Query request, List<String> fieldsToHighlight, boolean highlights, List<String> fieldsToSnippet) {
        if (highlights) {
            request.setAttributesToHighlight(fieldsToHighlight)
                    .setAttributesToSnippet(fieldsToSnippet)
                    .setSnippetEllipsisText("…")
                    .setHighlightPreTag(AlgoliaUtils.HIGHLIGHT_OPEN_TAG)
                    .setHighlightPostTag(AlgoliaUtils.HIGHLIGHT_CLOSE_TAG)
                    .setReplaceSynonymsInHighlight(true);
        } else {
            request.setAttributesToHighlight(List.of())
                    .setAttributesToSnippet(List.of());
        }
    }


    @Override
    public List<ErhgoOccupationSearchDTO> searchOccupations(String query, boolean highlights) {

        verifyQueryLength(query);
        try {
            var request = new Query(query);
            prepareRequestForHighlightWithFields(request, List.of(TITLE, "alternativeLabels"), highlights, List.of(DESCRIPTION, ACTIVITIES));

            var occupations = occupationIndex
                    .search(request)
                    .getHits();

            var duplicatedLabels = highlights ? getDuplicatedLabels(occupations) : Collections.<String>emptySet();

            return occupations
                    .stream()
                    .map(n -> n.buildOccupationSearchResult(query, duplicatedLabels))
                    .toList();
        } catch (AlgoliaApiException e) {
            throw new AlgoliaFailureException(e);
        }

    }

    private static Set<String> getDuplicatedLabels(List<AlgoliaOccupationDTO> occupations) {
        return occupations
                .stream()
                .flatMap(o -> Sets.union(
                        Set.of(o.getHighlightResult().getTitle().getValue()),
                        getAlternativeLabelsOfHighlightResult(o)
                ).stream())
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet()
                .stream()
                .filter(kv -> kv.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    @NotNull
    private static Set<String> getAlternativeLabelsOfHighlightResult(AlgoliaOccupationDTO o) {
        var labels = o.getHighlightResult().getAlternativeLabels();
        return labels == null ? Collections.emptySet() : labels.stream().map(AbstractAlgoliaResultItem::getValue).collect(Collectors.toSet());
    }

    @Override
    public CompletableFuture<BatchIndexingResponse> updateOccupationIndexation(ErhgoOccupation occupation) {
        if (occupation.getQualificationState() == ErhgoOccupationState.TO_CONFIRM) {
            log.debug("Removing TO_CONFIRM occupation from index {}", occupation);
            return occupationIndex.deleteObjectAsync(occupation.getId().toString())
                    .handleAsync((result, ex) -> {
                        if (ex != null) {
                            log.error("Unable to delete Occupation {}", occupation.getId(), ex);
                        }
                        return null;
                    });
        }

        try {
            var data = new AtomicReference<AlgoliaOccupationDTO>();
            transactionTemplate.execute(unused -> {
                erhgoOccupationRepository.findById(occupation.getId()).map(AlgoliaOccupationDTO::new).ifPresent(data::set);
                return null;
            });
            var dto = data.get();
            if (dto != null) {
                return occupationIndex.saveObjectAsync(dto)
                        .whenComplete(AlgoliaUtils.UNABLE_TO_INDEX_ENTITY_HANDLER);
            } else {
                log.warn("Unable to index {}", occupation);
                return CompletableFuture.completedFuture(null);
            }
        } catch (AlgoliaApiException e) {
            throw new AlgoliaFailureException(e);
        }

    }

    @Override
    public void replaceAllOccupationsWith(Iterable<ErhgoOccupation> occupations) {
        try {
            log.info("Starting reindexation of occupations...");
            occupationIndex.replaceAllObjects(StreamSupport.stream(occupations.spliterator(), false).map(AlgoliaOccupationDTO::new)::iterator, true);
            log.info("reindexation of occupations ended");
        } catch (AlgoliaApiException e) {
            log.error("Reindexation of occupations failed", e);
            throw new AlgoliaFailureException(e);
        }
    }

    @Override
    public CompletableFuture<DeleteResponse> removeOccupation(ErhgoOccupation occupationToDelete) {
        return occupationIndex.deleteObjectAsync(occupationToDelete.getId().toString()).whenComplete((result, err) -> {
            if (err != null) {
                log.error("Unable to remove occupation {}", occupationToDelete, err);
            } else {
                log.info("Occupation {} removed", occupationToDelete);
            }
        });
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    static class AlgoliaOccupationDTO {

        private String objectID;

        private String title;

        private Set<String> alternativeLabels;

        private Set<String> activities;

        private String description;

        private Integer level;

        private int qualificationState;

        @JsonSetter
        @JsonProperty("_highlightResult")
        @Setter(AccessLevel.PACKAGE)
        @VisibleForTesting
        private OccupationHighlightResultWrapper highlightResult;

        @JsonSetter
        @JsonProperty("_snippetResult")
        private OccupationSnippetResultWrapper snippetResult;

        @JsonIgnore
        public String getSnippet() {
            return snippetResult == null ? null : snippetResult.getValue();
        }

        @Data
        @Accessors(chain = true)
        public static class OccupationHighlightResultWrapper {
            private AlgoliaHighlightResultItem title;
            private List<AlgoliaHighlightResultItem> alternativeLabels;

            private List<AlgoliaHighlightResultItem> allResults() {
                var results = Lists.newArrayList(title);
                if (alternativeLabels != null) {
                    results.addAll(alternativeLabels);
                }
                return results;
            }
        }

        public AlgoliaOccupationDTO(ErhgoOccupation occupation) {
            this.title = occupation.getTitle();
            this.alternativeLabels = new HashSet<>(occupation.getAlternativeLabels());
            this.description = occupation.getDescription();
            this.objectID = occupation.getId().toString();
            this.level = occupation.getLevelAsInt();
            this.qualificationState = occupation.getQualificationState().ordinal();
            this.activities = occupation.getActivities().stream().map(JobActivityLabel::getTitle).collect(Collectors.toSet());
        }

        @Data
        public static class OccupationSnippetResultWrapper {
            private AlgoliaSnippetResultItem description;
            private List<AlgoliaSnippetResultItem> activities;

            public String getValue() {
                var allDescriptors = new ArrayList<AlgoliaSnippetResultItem>();
                allDescriptors.add(description);
                if (activities != null) {
                    allDescriptors.addAll(activities);
                }

                return allDescriptors.stream()
                        .filter(Objects::nonNull)
                        .filter(a -> a.getMatchLevel() != MatchLevel.NONE)
                        .max(ALGOLIA_SNIPPET_RESULT_ITEM_COMPARATOR)
                        .map(AbstractAlgoliaResultItem::getValue)
                        .orElse(null);
            }
        }

        public ErhgoOccupationSearchDTO buildOccupationSearchResult(String input, Set<String> duplicatedLabels) {
            var bestMatchingTitle = getBestMatchingTitle(input);
            var snippet = hasMatch(bestMatchingTitle) ? null : getSnippet();

            var finalTitle = duplicatedLabels.stream().anyMatch(n -> n.equals(bestMatchingTitle)) ? deduplicatedMatchingTitle(bestMatchingTitle) : bestMatchingTitle;
            return new ErhgoOccupationSearchDTO().code(objectID).title(finalTitle).snippet(snippet);
        }

        private String deduplicatedMatchingTitle(String bestMatchingTitle) {
            var titleToConsider = highlightResult == null ? title : highlightResult.getTitle().getValue();
            return !Objects.equals(bestMatchingTitle, titleToConsider) ? ("%s (%s)".formatted(bestMatchingTitle, title)) : titleToConsider;
        }

        private Set<String> getLabelsByMatchLevel(MatchLevel matchLevel) {
            return highlightResult
                    .allResults()
                    .stream()
                    .filter(r -> r.findByMatchLevel(matchLevel))
                    .map(AlgoliaHighlightResultItem::getValue)
                    .collect(Collectors.toSet());
        }

        private String getBestMatchingTitle(String input) {
            if (highlightResult == null) return title;

            var isAnyFullMatchLevel = highlightResult.allResults().stream().anyMatch(r -> r.findByMatchLevel(MatchLevel.FULL));
            var isAnyPartialMatchLevel = highlightResult.allResults().stream().anyMatch(r -> r.findByMatchLevel(MatchLevel.PARTIAL));
            Set<String> labels;

            if (isAnyFullMatchLevel) {
                labels = getLabelsByMatchLevel(MatchLevel.FULL);
            } else if (isAnyPartialMatchLevel) {
                labels = getLabelsByMatchLevel(MatchLevel.PARTIAL);
            } else {
                labels = getLabelsByMatchLevel(MatchLevel.NONE);
            }
            return StringSimilarityUtils.getTheMostSimilarString(input, labels);
        }
    }


    @Data
    @Accessors(chain = true)
    public abstract static class AbstractAlgoliaResultItem {

        public enum MatchLevel {
            @JsonProperty("full")
            FULL,
            @JsonProperty("partial")
            PARTIAL,
            @JsonProperty("none")
            NONE
        }

        private String value;
        private MatchLevel matchLevel;

        public boolean findByMatchLevel(MatchLevel searchedLevel) {
            return matchLevel == searchedLevel;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @Accessors(chain = true)
    public static class AlgoliaSnippetResultItem extends AbstractAlgoliaResultItem implements Comparable<AlgoliaSnippetResultItem> {

        public static final Comparator<AlgoliaSnippetResultItem> ALGOLIA_SNIPPET_RESULT_ITEM_COMPARATOR = Comparator.nullsLast(Comparator.comparing(AlgoliaSnippetResultItem::getMatchLevel)
                .thenComparing(AlgoliaSnippetResultItem::getFullyHighlighted, Comparator.nullsLast(Boolean::compareTo))
                .thenComparing(r -> StringUtils.trimToEmpty(r.getValue()).length()).reversed());
        private Boolean fullyHighlighted;

        @Override
        public int compareTo(AlgoliaSnippetResultItem other) {
            return ALGOLIA_SNIPPET_RESULT_ITEM_COMPARATOR
                    .compare(this, other);
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @Accessors(chain = true)
    public static class AlgoliaHighlightResultItem extends AbstractAlgoliaResultItem implements Comparable<AlgoliaHighlightResultItem> {

        private List<String> matchedWords;

        @Override
        public int compareTo(AlgoliaHighlightResultItem other) {
            return Comparator.nullsLast(Comparator.comparing(AlgoliaHighlightResultItem::getMatchLevel)
                            .thenComparing(r -> r.getMatchedWords().size()).reversed()
                            .thenComparing(r -> r.getMatchedWords().stream().mapToLong(String::length).max().orElse(-1)).reversed())
                    .compare(this, other);
        }
    }

    private static boolean hasMatch(String title) {
        return title != null && title.contains(AlgoliaUtils.HIGHLIGHT_OPEN_TAG);
    }


}
