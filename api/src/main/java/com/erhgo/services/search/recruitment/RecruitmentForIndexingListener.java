package com.erhgo.services.search.recruitment;

import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.recruitment.RecruitmentStateChangeEvent;
import com.erhgo.repositories.RecruitmentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class RecruitmentForIndexingListener {

    private final RecruitmentRepository recruitmentRepository;
    private final RecruitmentIndexer recruitmentIndexer;

    @EventListener(RecruitmentStateChangeEvent.class)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public void handleRecruitmentStateChange(RecruitmentStateChangeEvent event) {
        Optional.ofNullable(event.recruitmentId())
                .flatMap(recruitmentRepository::findById)
                .filter(r -> shouldBeIndexed(event, r))
                .ifPresentOrElse(
                        r -> this.handleIndexation(r, event),
                        () -> log.debug("Ignore event {}, recruitment not found or not to index", event)
                );
    }

    private static boolean shouldBeIndexed(RecruitmentStateChangeEvent event, Recruitment r) {
        return r.isSourcing();
    }

    private void handleIndexation(Recruitment recruitment, RecruitmentStateChangeEvent event) {
        log.trace("Handle indexation of recruitment with id {}", recruitment.getId());
        if (event.nextState() == RecruitmentState.PUBLISHED) {
            recruitmentIndexer.index(recruitment);
        } else if (event.previousState() == RecruitmentState.PUBLISHED) {
            recruitmentIndexer.remove(recruitment);
        }
    }

}
