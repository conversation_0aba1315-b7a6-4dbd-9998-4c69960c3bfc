package com.erhgo.services.search;

import com.algolia.search.DefaultSearchClient;
import com.algolia.search.SearchClient;
import com.algolia.search.models.apikeys.SecuredApiKeyRestriction;
import com.algolia.search.models.indexing.Query;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.exceptions.UserNotAllowedForEntity;
import com.erhgo.openapi.dto.AlgoliaConfigDTO;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.keycloak.KeycloakService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.RolesAllowed;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;

@ConditionalOnExpression("!'${algolia.applicationId}'.isEmpty() and !'${algolia.adminApiKey}'.isEmpty()")
@Service
@Slf4j
public class AlgoliaSearchConfigService {

    private static final Duration PUBLIC_KEY_LIFETIME = Duration.ofMinutes(5);

    @Value("${algolia.applicationId:''}")
    private String applicationId;
    @Value("${algolia.searchApiKey:''}")
    private String searchApiKey;
    @Value("${algolia.adminApiKey:''}")
    private String adminApiKey;
    @Value("${algolia.indexPrefix:''}")
    @Getter
    private String indexPrefix;

    @Getter
    private SearchClient searchClient;
    @Getter
    private SearchClient adminClient;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private KeycloakService keycloakService;

    @PostConstruct
    public void initialize() {
        log.info("Initializing Algolia client with application ID '" + applicationId + "' and position prefix '" + indexPrefix + "'");
        searchClient = DefaultSearchClient.create(applicationId, searchApiKey);
        adminClient = DefaultSearchClient.create(applicationId, adminApiKey);
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    public AlgoliaConfigDTO getSecuredAlgoliaConfiguration(String organizationCode) {
        return new AlgoliaConfigDTO().applicationId(applicationId)
                .apiKey(generateSecuredApiKey(organizationCode))
                .userIndexName(getUserIndexName());
    }

    private String generateSecuredApiKey(String organizationCode) {
        try {
            var restriction = new SecuredApiKeyRestriction()
                    .setRestrictIndices(List.of(getUserIndexName()))
                    .setValidUntil(PUBLIC_KEY_LIFETIME.plus(System.currentTimeMillis(), ChronoUnit.MILLIS).toSeconds());
            addAuthenticatedUserRelatedChannels(organizationCode, restriction);
            return searchClient.generateSecuredAPIKey(searchApiKey, restriction);
        } catch (Exception e) {
            log.error("Error during key generation", e);
            // Do not use root cause exception to avoid any key serialization
            throw new GenericTechnicalException("Error during Algolia key generation");
        }
    }

    private void addAuthenticatedUserRelatedChannels(String organizationCode, SecuredApiKeyRestriction restriction) {
        if (organizationCode != null) {
            var channels = keycloakService.getRolesForGroup(organizationCode)
                    .stream()
                    .map(r -> "channels:" + r)
                    .toList();
            restriction.setQuery(new Query().setFacetFilters(List.of(channels)));
        } else if (!securityService.isAdmin()) {
            throw new UserNotAllowedForEntity("Non admin user must set organization code");
        }
    }

    public String getUserIndexName() {
        return getIndexPrefix() + "_user_index";
    }
}
