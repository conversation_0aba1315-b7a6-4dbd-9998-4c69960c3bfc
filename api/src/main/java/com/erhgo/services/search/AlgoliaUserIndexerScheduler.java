package com.erhgo.services.search;

import com.erhgo.repositories.UserProfileRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;

@Service
@RequiredArgsConstructor
@Slf4j
public class AlgoliaUserIndexerScheduler {

    private final UserProfileRepository userProfileRepository;
    private final UserIndexer userIndexer;
    @Value("${application.index_user.max_number_of_indexations_per_hour}")
    private int maxNumberOfIndexationsPerHour = 1000;
    @Value("${application.index_user.user_indexation_delay_in_seconds}")
    private int userIndexationInterval = 3600;

    @Scheduled(cron = "${application.index_user.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "indexUserScheduler")
    public void index() {
        var users = userProfileRepository.findUsersToIndex(
                new Date(Instant.now().minus(userIndexationInterval, ChronoUnit.SECONDS).toEpochMilli()),
                PageRequest.of(0, maxNumberOfIndexationsPerHour)
        );
        log.info("Update indexation of {} users (over a total of {})", users.getNumberOfElements(), users.getTotalElements());
        users.forEach(userIndexer::index);
        log.info("Update indexations launched");
    }
}
