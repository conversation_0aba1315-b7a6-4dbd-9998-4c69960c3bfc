package com.erhgo.services;

import com.erhgo.domain.exceptions.EntityAlreadyExistException;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.landingpage.LandingPage;
import com.erhgo.openapi.dto.LandingPageDetailDTO;
import com.erhgo.openapi.dto.LandingPagePageDTO;
import com.erhgo.openapi.dto.LandingPageSummaryDTO;
import com.erhgo.openapi.dto.SaveLandingPageCommandDTO;
import com.erhgo.repositories.LandingPageRepository;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
public class LandingPageService {

    @Autowired
    private LandingPageRepository landingPageRepository;

    @Autowired
    private RecruiterRepository recruiterRepository;

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional
    public void saveLandingPage(SaveLandingPageCommandDTO command) {
        landingPageRepository
                .getByUrlKey(command.getUrlKey())
                .filter(landingPage -> !landingPage.getId().equals(command.getId()))
                .ifPresent(landingPage -> {
                    throw new EntityAlreadyExistException(LandingPage.class, command.getId());
                });

        var landingPage = LandingPage.builder()
                .content(command.getContent())
                .id(command.getId())
                .urlKey(command.getUrlKey())
                .build();

        var organizationCodes = command.getOrganizationCodes();

        if (organizationCodes != null && !organizationCodes.isEmpty()) {
            var organizations = recruiterRepository.findByCodeIn(organizationCodes);
            landingPage.updateTerritorialOrganizations(organizations);
        }

        landingPageRepository.save(landingPage);
    }

    @Transactional(readOnly = true)
    public String getContentForKey(String urlKey) {
        return landingPageRepository.getByUrlKey(urlKey)
                .map(LandingPage::getContent)
                .orElseThrow(() -> new EntityNotFoundException(urlKey, LandingPage.class));
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public LandingPagePageDTO getLandingPages(Integer page, Integer size) {
        return PageDTOBuilder.buildLandingPagePage(landingPageRepository.findAll(PageRequest.of(page, size, Sort.by("urlKey")))
                .map( this::buildLandingPageSummary));
    }

    private LandingPageSummaryDTO buildLandingPageSummary(LandingPage landingPage) {
        return new LandingPageSummaryDTO()
                .id(landingPage.getId())
                .organizations(landingPage.getOrganizationsTitles())
                .urlKey(landingPage.getUrlKey());
    }

    private LandingPageDetailDTO buildLandingPageDetail(LandingPage landingPage) {
        return new LandingPageDetailDTO()
                .id(landingPage.getId())
                .urlKey(landingPage.getUrlKey())
                .organizationCodes(landingPage.getOrganizationsCodes())
                .content(landingPage.getContent());
    }

    @RolesAllowed(Role.ODAS_ADMIN)
    @Transactional(readOnly = true)
    public LandingPageDetailDTO getLandingPage(UUID uuid) {
        return landingPageRepository.findById(uuid)
                .map(this::buildLandingPageDetail)
                .orElseThrow(() -> new EntityNotFoundException(uuid, LandingPage.class));
    }
}
