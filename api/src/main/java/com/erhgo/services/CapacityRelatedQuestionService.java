package com.erhgo.services;

import com.erhgo.domain.enums.QuestionType;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.InvalidCommandException;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.CapacityRelatedQuestion;
import com.erhgo.domain.referential.CapacityRelatedQuestionResponse;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.CapacityRelatedQuestionRepository;
import com.erhgo.repositories.CapacityRepository;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.security.Role;
import com.erhgo.services.denormalization.CapacityRelatedQuestionUpdateListener;
import com.erhgo.services.dtobuilder.CapacityRelatedQuestionDTOBuilder;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.google.common.collect.Sets;
import jakarta.annotation.security.RolesAllowed;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class CapacityRelatedQuestionService {

    @Autowired
    private CapacityRelatedQuestionRepository capacityRelatedQuestionRepository;

    @Autowired
    private CapacityRepository capacityRepository;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private RecruiterRepository recruiterRepository;

    @Autowired
    private Collection<CapacityRelatedQuestionUpdateListener> capacityRelatedQuestionUpdateListeners;

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void save(SaveCapacityRelatedQuestionCommandDTO command) {
        var questionType = QuestionType.valueOf(command.getQuestionType().name());
        var questionIndex = capacityRelatedQuestionRepository
                .findById(command.getId())
                .map(CapacityRelatedQuestion::getQuestionIndex)
                .orElseGet(() -> (int) capacityRelatedQuestionRepository.count());

        var previousQuestion = capacityRelatedQuestionRepository
                .findById(command.getId());

        previousQuestion.ifPresent(q -> handleResponseRemoved(command, q));

        var question = CapacityRelatedQuestion.builder()
                .id(command.getId())
                .questionIndex(questionIndex)
                .questionType(questionType)
                .title(command.getTitle())
                .build();

        IntStream.range(0, command.getResponses().size())
                .forEach(index -> this.addResponseToQuestion(command.getResponses().get(index), index, question, previousQuestion));
        capacityRelatedQuestionRepository.save(question);
    }

    private void verifyCommandAndQuestionType(QuestionTypeDTO questionType, Object recruiter) {
        if ((questionType == QuestionTypeDTO.EXTRAPROFESSIONAL) != (recruiter == null)) {
            throw new InvalidCommandException("Question type is incompatible with recruiter");
        }
    }

    private void handleResponseRemoved(
            SaveCapacityRelatedQuestionCommandDTO command,
            CapacityRelatedQuestion question
    ) {
        var nextResponsesIds = command
                .getResponses()
                .stream()
                .map(ResponseForCapacityRelatedQuestionDTO::getId)
                .collect(Collectors.toSet());

        var responsesRemoved = question.getResponses()
                .stream()
                .filter(r -> !nextResponsesIds.contains(r.getId()))
                .collect(Collectors.toSet());

        responsesRemoved
                .forEach(a -> capacityRelatedQuestionUpdateListeners.forEach(listener -> {
                            if (!a.getCapacities().isEmpty()) {
                                listener.notifyCapacitiesRemoved(a, a.getCapacities());
                            }
                        }
                ));
    }

    private void addResponseToQuestion(ResponseForCapacityRelatedQuestionDTO response,
                                       int position,
                                       CapacityRelatedQuestion question, Optional<CapacityRelatedQuestion> previousQuestion) {
        var newCapacities = capacityRepository.findAllById(response.getCapacities());

        CapacityRelatedQuestionResponse.builder()
                .id(response.getId())
                .capacities(Sets.newHashSet(newCapacities))
                .title(response.getTitle())
                .question(question)
                .position(position)
                .build();
        previousQuestion.ifPresent(q -> notifyCapacitiesIfRequired(response.getId(), q, Sets.newHashSet(newCapacities)));
    }

    private void notifyCapacitiesIfRequired(UUID responseId, CapacityRelatedQuestion question, Set<Capacity> newCapacities) {
        var previousResponse = question
                .getResponses()
                .stream()
                .filter(r -> responseId.equals(r.getId()))
                .findFirst();
        previousResponse.ifPresent(response -> doNotifyCapacities(newCapacities, response));
    }

    private void doNotifyCapacities(Set<Capacity> newCapacities, CapacityRelatedQuestionResponse response) {
        var previousCapacities = response.getCapacities();
        var removedCapacities = Sets.difference(previousCapacities, newCapacities).immutableCopy();
        var addedCapacities = Sets.difference(newCapacities, previousCapacities).immutableCopy();

        capacityRelatedQuestionUpdateListeners.forEach(listener -> {
            if (!addedCapacities.isEmpty()) {
                listener.notifyCapacitiesAdded(response, addedCapacities);
            }
            if (!removedCapacities.isEmpty()) {
                listener.notifyCapacitiesRemoved(response, removedCapacities);
            }
        });
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public CapacityRelatedQuestionDetailsDTO getQuestion(UUID questionId) {
        return capacityRelatedQuestionRepository
                .findById(questionId)
                .map(CapacityRelatedQuestionDTOBuilder::buildDetails)
                .orElseThrow(() -> new EntityNotFoundException(questionId, CapacityRelatedQuestion.class));
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public CapacityRelatedQuestionPageDTO getCapacityRelatedQuestions(QuestionTypeDTO questionTypeDTO, Integer page, Integer size, String organizationCode) {
        verifyCommandAndQuestionType(questionTypeDTO, organizationCode);
        return PageDTOBuilder.buildCapacityRelatedQuestionPage(
                capacityRelatedQuestionRepository
                        .findByQuestionTypeOrderByQuestionIndex(
                                QuestionType.valueOf(questionTypeDTO.name()),
                                PageRequest.of(page, size)
                        )
                        .map(CapacityRelatedQuestionDTOBuilder::buildSummaryDTO));
    }

    @Transactional
    @RolesAllowed(Role.ODAS_ADMIN)
    public void reorderQuestions(Map<String, Integer> questionIndexById) {
        var questionsToReorder = capacityRelatedQuestionRepository.findAllById(questionIndexById.keySet()
                .stream()
                .map(UUID::fromString)
                .toList()
        );

        for (CapacityRelatedQuestion question : questionsToReorder) {
            question.setQuestionIndex(questionIndexById.get(question.getId().toString()));
        }
        entityManager.flush();
    }

}
