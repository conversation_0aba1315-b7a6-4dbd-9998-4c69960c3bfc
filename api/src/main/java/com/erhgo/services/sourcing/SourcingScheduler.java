package com.erhgo.services.sourcing;

import com.erhgo.domain.enums.CandidatureSynchronizationState;
import com.erhgo.domain.enums.RecruitmentSendNotificationState;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.openapi.dto.InviteToRecruitmentCommandDTO;
import com.erhgo.repositories.AbstractCandidatureRepository;
import com.erhgo.repositories.RecruitmentRepository;
import com.erhgo.repositories.SourcingPreferencesRepository;
import com.erhgo.services.SecurityService;
import com.erhgo.utils.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SourcingScheduler {
    // FIXME: 3 months is notification retention time
    public static final Duration LESS_THAN_THREE_MONTHS = Duration.ofDays(3 * 28);
    private final RecruitmentRepository recruitmentRepository;
    private final AbstractCandidatureRepository abstractCandidatureRepository;
    private final SourcingService sourcingService;
    private final SourcingJobRecruitmentService sourcingJobRecruitmentService;
    private final SecurityService securityService;
    private final SourcingMailingService sourcingMailingService;
    private final SourcingPreferencesRepository sourcingPreferencesRepository;

    @Scheduled(cron = "${application.sendReminderRecruitmentMails.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "sendRecruitmentsInfoMailsScheduler")
    public void remindRecruitmentsInfo() {
        log.info("Sending remind mails for recruitments");
        try {
            log.info("Prepare sending mail for recruitments");
            sourcingMailingService.remindSourcingRecruitmentsInfo();
            log.info("Mail sent for recruitments");
        } catch (CancellationException e) {
            log.error("Unable to send mail for recruitments", e);
            Thread.currentThread().interrupt();
        }
    }


    @Scheduled(cron = "${application.sendRecruitmentNotifications.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "sendRecruitmentNotificationsScheduler")
    public void sendRecruitmentNotifications() {
        log.info("Sending sourcing recruitments notifications");
        var recruitments = recruitmentRepository.
                findBySendNotificationDateBeforeAndSendNotificationStateAndState(
                        Instant.now(),
                        RecruitmentSendNotificationState.WAITING,
                        RecruitmentState.PUBLISHED
                );
        var ids = recruitments.stream().map(Recruitment::getId).collect(Collectors.toSet());
        log.info("Prepare notifications generation for recruitments {}", ids);
        securityService.doAsAdmin(() -> recruitments.forEach(this::generateRecruitmentsNotifications));
        log.info("Notifications generated for recruitments {}", ids);
    }

    @Scheduled(cron = "${application.sendSourcingTrialMails.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "SourcingTrialMailsScheduler")
    public void sendTrialMails() {
        log.info("Sending sourcing trial start mails");
        try {
            sourcingMailingService.sendTrialWelcomeMails();
        } finally {
            log.info("Sending sourcing trial end mails");
            sourcingMailingService.sendTrialEndMails();
        }
        log.info("Sourcing trial mails sent");
    }

    @Scheduled(cron = "${application.suspendSourcingRecruitments.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "SuspendSourcingRecruitmentsScheduler")
    public void suspendSourcingRecruitments() {
        log.info("Suspending sourcing recruitments on disabled account");
        try {
            sourcingJobRecruitmentService.suspendSourcingRecruitmentsOnDisabledAccount();
            log.info("Sourcing recruitments on disabled account suspended");
        } finally {
            log.info("Suspending sourcing recruitments publication date expired");
            sourcingJobRecruitmentService.suspendEndedSourcingRecruitments();
            log.info("Recruitments with expired publication date suspended");
        }

    }

    private void generateRecruitmentsNotifications(Recruitment recruitment) {
        try {
            sourcingService
                    .generateRecruitmentNotifications(new InviteToRecruitmentCommandDTO().recruitmentId(recruitment.getId()))
                    .get();
        } catch (ExecutionException | InterruptedException | CancellationException e) {
            log.error("Unable to generate notifications for recruitment {}", recruitment.getId(), e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }
    }

    @Scheduled(cron = "${application.resendNotificationsForOpenRecruitments.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "ResendNotificationsForOpenRecruitmentsScheduler")
    public void sendRecruitmentReminderNotifications() {
        log.info("Sending recruitment reminder notifications");
        var recruitments = recruitmentRepository.findRecruitmentByStateAndCreatedDateAfter(RecruitmentState.PUBLISHED, DateTimeUtils.instantToDate(Instant.now().minus(LESS_THAN_THREE_MONTHS)));
        var ids = recruitments.stream().map(Recruitment::getId).collect(Collectors.toSet());
        log.debug("Prepare reminder notifications for {} recruitments ({})", ids.size(), ids);
        securityService.doAsAdmin(() -> recruitments.forEach(this::resendNotificationsForOpenRecruitments));
        log.info("Reminder notifications sent");
    }

    @Scheduled(cron = "${application.immediateCandidatureNotification.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "immediateCandidatureNotificationScheduler")
    public void immediateNotificationScheduler() {
        securityService.doAsAdmin(() -> {
            log.info("Sending immediate notifications");
            var allPreferences = sourcingPreferencesRepository.findAll();
            abstractCandidatureRepository.findCandidaturesToNotifyImmediately(
                            CandidatureSynchronizationState.WAITING,
                            OffsetDateTime.now().minusHours(1),
                            OffsetDateTime.now().minusDays(15)
                    )
                    .forEach(abstractCandidature -> sourcingMailingService.sendImmediateNotificationToSourcingUsers(abstractCandidature, allPreferences))
            ;
            log.info("Sending immediate notifications ended");
        });
    }


    public void resendNotificationsForOpenRecruitments(Recruitment recruitment) {
        var publicationDate = recruitment.getPublicationDate();
        if (publicationDate == null) {
            log.warn("Skipping recruitment {} due to null publication date", recruitment.getId());
            return;
        }
        var currentDate = Instant.now().atZone(DateTimeUtils.ZONE_ID).toLocalDate();
        var daysSincePublication = ChronoUnit.DAYS.between(DateTimeUtils.dateToLocalDate(publicationDate), currentDate);
        if (daysSincePublication > 1 && daysSincePublication % 7 == 0) {
            log.trace("Resending notifications for recruitment with ID: {}", recruitment.getId());
            try {
                var invitationCommand = new InviteToRecruitmentCommandDTO()
                        .recruitmentId(recruitment.getId())
                        .forceResend(false);
                sourcingService.generateRecruitmentNotifications(invitationCommand);
            } catch (CancellationException e) {
                log.error("Failed to resend notifications for recruitment with ID: {}", recruitment.getId(), e);
            }
        }
    }
}
