package com.erhgo.services.sourcing;

import com.erhgo.domain.candidature.job.AbstractCandidature;
import com.erhgo.domain.candidature.job.GlobalCandidatureState;
import com.erhgo.domain.candidature.job.RecruitmentCandidature;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.dto.EmailVerificationResultDTO;
import com.erhgo.domain.enums.CandidatureState;
import com.erhgo.domain.enums.RecruitmentSendNotificationState;
import com.erhgo.domain.exceptions.*;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.sourcing.SourcingInvitation;
import com.erhgo.domain.sourcing.SourcingPreferences;
import com.erhgo.domain.sourcing.SourcingSubscription;
import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.repositories.classifications.ErhgoOccupationRepository;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.SecurityService;
import com.erhgo.services.dto.UserKeycloakRepresentation;
import com.erhgo.services.dto.criteria.SourcingCandidatesCriteria;
import com.erhgo.services.dtobuilder.*;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.SourcingKeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.RecruitmentNotificationGenerator;
import com.erhgo.services.mailing.SendCandidatureRefusalEmailService;
import com.erhgo.services.mailing.check.EmailVerificationService;
import com.erhgo.services.matching.MatchingService;
import com.erhgo.services.notifier.Notifier;
import com.erhgo.services.notifier.messages.SourcingInvitationUsedMessageDTO;
import com.erhgo.services.notifier.messages.SourcingNewUserMessageDTO;
import com.erhgo.services.organization.OrganizationDataProvider;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.StreamSupport;

import static com.erhgo.domain.referential.AbstractOrganization.SiretVerificationStatus.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class SourcingService {

    private final EmailVerificationService emailVerificationService;
    private final SourcingKeycloakService sourcingKeycloakService;
    private final KeycloakService keycloakService;
    private final SecurityService securityService;
    private final AbstractOrganizationRepository abstractOrganizationRepository;
    private final RecruiterRepository recruiterRepository;
    private final JobRepository jobRepository;
    private final RecruitmentRepository recruitmentRepository;
    private final ErhgoOccupationRepository erhgoOccupationRepository;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final SpontaneousCandidatureRepository spontaneousCandidatureRepository;
    private final AbstractCandidatureRepository abstractCandidatureRepository;
    private final OrganizationDataProvider organizationDataProvider;
    private final CandidaturesDTOBuilder candidaturesDTOBuilder;
    private final SourcingSubscriptionDTOBuilder sourcingSubscriptionDTOBuilder;
    private final SourcingInvitationRepository sourcingInvitationRepository;
    private final SourcingSubscriptionRepository sourcingSubscriptionRepository;
    private final RecruitmentNotificationGenerator recruitmentNotificationGenerator;
    private final Notifier notifier;
    private final SourcingMailingService sourcingMailingService;
    private final SendCandidatureRefusalEmailService sendCandidatureRefusalEmailService;
    private final UserProfileRepository userProfileRepository;
    private final MatchingService matchingService;
    private final SourcingDTOBuilder sourcingDTOBuilder;
    private final SourcingPreferencesRepository sourcingPreferencesRepository;
    private final SourcingUserRepository sourcingUserRepository;
    private final SourcingUserDTOBuilder sourcingUserDTOBuilder;
    private final SourcingCandidateCriteriaBuilder sourcingCandidateCriteriaBuilder;
    private final TransactionTemplate transactionTemplate;

    @RolesAllowed(Role.SOURCING)
    @Transactional(readOnly = true)
    public CandidatesCountDTO countCandidates(SourcingCandidatesCriteria countCriteria) {
        var occupation = erhgoOccupationRepository.findById(countCriteria.occupationId())
                .orElseThrow(() -> new EntityNotFoundException(countCriteria.occupationId(), ErhgoOccupation.class));

        countCriteria.updateCriteriaWithOccupationRelatedDatasAndGlobalParameters(occupation);

        return new CandidatesCountDTO().count(sourcingUserRepository.countCandidates(countCriteria).longValue());
    }


    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_READ)
    public RepublicationCandidatesCountDTO countMatchingUsers(Long recruitmentId) {
        var recruitment = getRecruitmentOrThrow(recruitmentId);
        var countCriteria = sourcingCandidateCriteriaBuilder.forRecruitment(recruitment, false).excludingRecruitment(recruitment);
        return new RepublicationCandidatesCountDTO()
                .newUsers(sourcingUserRepository.countCandidates(countCriteria))
                .oldUsers(sourcingUserRepository.countCandidates(countCriteria.excludesNotified(false)))
                ;
    }

    @Transactional
    @RolesAllowed(Role.SOURCING)
    public InitializedAccountDTO initializeAccount() {
        var organization = Optional.ofNullable(getAuthenticatedRecruiter()).orElseGet(this::createSourcingRecruiter);
        var recruitment = getExistentRecruitment(organization);

        return new InitializedAccountDTO()
                .jobId(recruitment == null ? null : recruitment.getJob().getId())
                .hasSiretFormatError(organization.getSiretVerificationStatus() == WRONG_SIRET)
                .recruitmentId(recruitment == null ? null : recruitment.getId());
    }


    protected Recruiter getAuthenticatedRecruiter() {
        var userId = securityService.getAuthenticatedUserId();
        var kcUser = sourcingKeycloakService.getSourcingUserWithGroups(userId);
        var organizationalGroups = kcUser.getGroupsRelatedToOrganizations();
        Recruiter organization = null;
        if (organizationalGroups != null && !organizationalGroups.isEmpty()) {
            organization = organizationalGroups.stream().filter(g -> g.startsWith(AbstractOrganization.OrganizationType.SOURCING.getPrefix())).findFirst().map(recruiterRepository::findOneByCode).orElse(null);
        }
        return organization;
    }


    private Recruitment getExistentRecruitment(Recruiter organization) {
        return jobRepository
                .findByRecruiterCodeInOrEmployerCodeIn(Set.of(organization.getCode()), Pageable.unpaged()).getContent()
                .stream()
                .findFirst()
                .map(Job::getId)
                .map(recruitmentRepository::findFirstByRecruitmentProfileJobId)
                .orElse(null)
                ;
    }

    private void createNewSubscription(Recruiter recruiter) {
        var newSubscription = SourcingSubscription.builder()
                .expirationDate(null)
                .recruiter(recruiter)
                .build();
        sourcingSubscriptionRepository.save(newSubscription);
    }


    private Recruitment getRecruitmentOrThrow(Long recruitmentId) {
        return recruitmentRepository.findById(recruitmentId)
                .orElseThrow(() -> new EntityNotFoundException(recruitmentId, Recruitment.class));
    }


    @RolesAllowed({Role.ODAS_ADMIN})
    @Transactional(readOnly = true)
    public List<SourcingInvitationCodeDTO> listSourcingInvitationCode() {
        var invitationCodes = sourcingInvitationRepository.findAll();
        return StreamSupport.stream(invitationCodes.spliterator(), false)
                .map(sourcingDTOBuilder::buildInvitationCodeDTO)
                .toList();
    }

    @RolesAllowed({Role.ODAS_ADMIN})
    @Transactional
    public void saveInvitationCode(SaveSourcingInvitationCodeDTO command) {
        var organization = abstractOrganizationRepository.findOneByCode(command.getHost());
        var sourcingInvitation = SourcingInvitation
                .builder()
                .host(organization)
                .code(command.getCode())
                .duration(command.getDuration())
                .maxNumberOfGuests(command.getMaxNumberOfGuests())
                .build();
        sourcingInvitationRepository.save(sourcingInvitation);
    }

    private Recruiter createSourcingRecruiter() {
        var userId = securityService.getAuthenticatedUserId();
        var kcUser = sourcingKeycloakService.getSourcingUserWithGroups(userId);

        var organizationSiret = recruiterRepository.findBySiretAndOrganizationType(kcUser.getSiret(), AbstractOrganization.OrganizationType.SOURCING);
        if (!organizationSiret.isEmpty()) {
            throw new SiretAlreadyUsedException(kcUser.getSiret());
        }
        var organization = abstractOrganizationRepository.save(Recruiter.recruiterBuilder()
                .organizationType(AbstractOrganization.OrganizationType.SOURCING)
                .internal(false)
                .build());
        updateNameForSiret(organization, kcUser);
        organization.updateCodeOnEntityCreate();
        createNewSubscription(organization);

        sourcingKeycloakService.assignUserToSourcingGroup(userId, organization.getCode());
        keycloakService.createFrontOfficeGroupAndRole(organization.getCode());
        keycloakService.createBackOfficeGroupAndRoles(organization.getCode());
        notifySourcingOrganizationCreated(organization);
        return organization;
    }

    private void notifySourcingOrganizationCreated(Recruiter organization) {
        var isSubscriptionActivated = isRecruiterSubscriptionActivated(organization);
        notifier.sendMessage(new SourcingNewUserMessageDTO(organization, true, isSubscriptionActivated));
    }

    protected boolean isRecruiterSubscriptionActivated(Recruiter recruiter) {
        return sourcingSubscriptionRepository.findOneByRecruiter(recruiter)
                .map(SourcingSubscription::isActivated)
                .orElse(false);
    }

    private void updateNameForSiret(Recruiter organization, UserRepresentation kcUser) {
        String name;
        name = getNameForSiretAndUpdateSiretAndStatus(organization, kcUser);
        organization.setTitle(name);
    }

    private String getNameForSiretAndUpdateSiretAndStatus(Recruiter organization, UserRepresentation kcUser) {
        String name;
        AbstractOrganization.SiretVerificationStatus siretVerificationStatus;
        var siret = kcUser.getSiret();
        if (StringUtils.isBlank(siret)) {
            log.error("No siret for user with id {}", kcUser.getId());
            name = " ";
            siretVerificationStatus = WRONG_SIRET;
        } else {
            try {
                name = organizationDataProvider.getNameForSiret(siret);
                if (name == null) {
                    siretVerificationStatus = TECHNICAL_ERROR;
                    name = siret;
                } else {
                    siretVerificationStatus = SUCCESS;
                }
            } catch (InvalidSiretException e) {
                log.warn("Unable to retrieve data for siret {} - userId : {} ", siret, kcUser.getId(), e);
                name = siret;
                siretVerificationStatus = WRONG_SIRET;
            } catch (SiretValidationTechnicalException e) {
                log.warn("Technical error for siret {} - userId : {} ", siret, kcUser.getId(), e);
                name = siret;
                siretVerificationStatus = TECHNICAL_ERROR;
            }
        }
        organization.setSiretVerificationStatus(siretVerificationStatus);
        organization.setSiret(siret);
        return name;
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.CANDIDATURE.CANDIDATURE_WRITE)
    public SourcingContactInformationDTO updateCandidatureState(Long candidatureId, UpdateSourcingCandidatureStateCommandDTO command) {
        var candidature = getCandidatureOrThrow(candidatureId);
        var previousState = candidature.getGlobalCandidatureState();
        changeToNextState(command.getNextState(), candidature);
        var result = new SourcingContactInformationDTO();
        if (!candidature.isAnonymous()) {
            keycloakService.getFrontOfficeUserProfile(candidature.getUserProfile().userId())
                    .ifPresent(u -> result
                            .email(u.getEmail())
                            .firstName(u.getFirstName())
                            .lastName(u.getLastName()));
            Optional.ofNullable(candidature.getUserProfile().generalInformation())
                    .ifPresent(r ->
                            result
                                    .phone(r.getPhoneNumber())
                                    .location(r.getLocation() == null ? null : r.getLocation().buildDTO())
                    );
        }
        if (!previousState.isRefused() && candidature.isRefused() && candidature.isRecruitmentCandidature()) {
            result.numberOfGeneratedCandidatures(regenerateTopTenCandidatures(((RecruitmentCandidature) candidature).getRecruitment()).size());
        }
        return result;
    }

    private AbstractCandidature getCandidatureOrThrow(Long candidatureId) {
        return abstractCandidatureRepository.findById(candidatureId).orElseThrow(() -> new EntityNotFoundException(candidatureId, RecruitmentCandidature.class));
    }

    private void changeToNextState(SourcingCandidatureStateDTO nextState, AbstractCandidature candidature) {
        switch (nextState) {
            case DISMISS -> candidature.dismiss(securityService.getAuthenticatedUserId());
            case TO_CONTACT -> candidature.toContact();
            case CONTACTED -> candidature.contacted();
            case FAVORITE -> candidature.favorite();
            case NEW -> log.error("trying to re set candidature to new");
        }
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.CANDIDATURE.CANDIDATURE_READ)
    public SourcingCandidatureDetailDTO getCandidatureDetail(Long candidatureId) {
        var candidature = getCandidatureOrThrow(candidatureId);
        var recruiter = candidature.getRecruiter();
        var userProfile = candidature.getUserProfile();
        var spontaneousCandidature = spontaneousCandidatureRepository.findOneByRecruiterAndUserProfile(recruiter, userProfile);
        var previousCandidatures = recruitmentCandidatureRepository
                .findDistinctByRecruitmentRecruitmentProfileJobRecruiterAndGlobalCandidatureStateInAndUserProfileAndArchivedIsFalseOrderBySubmissionDateDesc(candidature.getRecruiter(), GlobalCandidatureState.finalizedValues(), userProfile);
        return candidaturesDTOBuilder.buildSourcingCandidatureDetail(candidature, previousCandidatures, spontaneousCandidature);

    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.SOURCING)
    public SourcingOrganizationDTO getAuthenticatedSourcingUserOrganization() {
        var organization = getSourcingRecruiterByAuthenticatedUser();
        return OrganizationDTOBuilder.buildForSourcing(organization);
    }

    private Recruiter getSourcingRecruiterByAuthenticatedUser() {
        var userId = securityService.getAuthenticatedUserId();
        var user = sourcingKeycloakService.getSourcingUserWithGroups(userId);
        return (Recruiter) abstractOrganizationRepository.findOneByCode(user.getSingleGroupsRelatedToOrganization());
    }

    @Transactional
    @RolesAllowed(Role.SOURCING)
    public UpdateOrganizationResultDTO updateAuthenticatedSourcingUserOrganization(UpdateSourcingOrganizationCommandDTO command) {
        var userId = securityService.getAuthenticatedUserId();
        var user = sourcingKeycloakService.getSourcingUserWithGroups(userId);
        var organization = (Recruiter) abstractOrganizationRepository.findOneByCode(user.getSingleGroupsRelatedToOrganization());
        if (!StringUtils.equals(user.getSiret(), command.getSiret())) {
            user.setSiret(command.getSiret());
            sourcingKeycloakService.updateUserSiret(userId, command.getSiret());
            getNameForSiretAndUpdateSiretAndStatus(organization, user);
        }
        var recruitment = Optional.ofNullable(command.getRecruitmentId()).flatMap(recruitmentRepository::findById);
        if (recruitment.isEmpty() || StringUtils.isBlank(organization.getDescription())) {
            organization.setDescription(command.getDescription());
        }
        if (recruitment.isEmpty() || !organization.isForcedUrl()) {
            organization.setExternalUrl(command.getExternalUrl());
        }
        recruitment.ifPresent(r -> {
            r.setExternalUrl(command.getExternalUrl());
            r.setOrganizationDescription(command.getDescription());
        });

        organization.setTitle(command.getName());
        organization.setGdprMention(command.getGdprMention());
        return new UpdateOrganizationResultDTO().hasSiretFormatError(organization.getSiretVerificationStatus() == WRONG_SIRET);
    }

    @RolesAllowed(Role.SOURCING)
    @Transactional
    public void updateAuthenticatedSourcingUser(UpdateSourcingUserCommandDTO command) {
        var userId = securityService.getAuthenticatedUserId();
        sourcingKeycloakService.updateUser(userId, command.getEmail(), command.getPhone(), command.getFullname());
        var preferencesOpt = sourcingPreferencesRepository.findByUserId(userId);
        if (command.getPreferences() != null) {
            var preferences = preferencesOpt.orElseGet(SourcingPreferences::new);
            preferences
                    .userId(userId)
                    .mailFrequency(SourcingPreferences.MailFrequency.valueOf(command.getPreferences().getMailFrequency().name()))
                    .isoWeekDay(command.getPreferences().getIsoWeekDay())
            ;
            sourcingPreferencesRepository.save(preferences);
        } else {
            preferencesOpt.ifPresent(sourcingPreferencesRepository::delete);
        }
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.RECRUITMENT.RECRUITMENT_OF_COMMAND_WRITE)
    public CompletableFuture<Void> generateRecruitmentNotifications(InviteToRecruitmentCommandDTO command) {
        var recruitment = getRecruitmentOrThrow(command.getRecruitmentId());
        var criteria = sourcingCandidateCriteriaBuilder.forRecruitment(recruitment, false)
                .excludesNotified(BooleanUtils.isNotTrue(command.getForceResend()));

        var userIdsToNotify = sourcingUserRepository.getCandidates(criteria);
        return recruitmentNotificationGenerator
                .generateNotifications(new HashSet<>(userIdsToNotify), command.getRecruitmentId())
                .handle((result, exception) -> {
                    transactionTemplate.execute(na -> {
                        recruitmentRepository.findById(recruitment.getId()).orElseThrow().changeSendNotificationsState(exception == null ? RecruitmentSendNotificationState.DONE : RecruitmentSendNotificationState.ERROR);
                        return null;
                    });
                    return null;
                });
    }

    @PreAuthorize(AuthorizeExpression.ORGANIZATION.ORGANIZATION_READ)
    public List<SourcingUserDTO> getSourcingUsersWithOrganizationCode(String organizationCode) {
        return sourcingKeycloakService.getSourcingUsersForGroup(organizationCode).stream().map(sourcingUserDTOBuilder::buildSourcingUser).toList();
    }


    @Transactional
    @PreAuthorize(AuthorizeExpression.ORGANIZATION.ORGANIZATION_OF_COMMAND_WRITE)
    public void inviteAndCreateNewUser(InviteAndCreateNewUserCommandDTO command) {
        var password = com.erhgo.utils.StringUtils.generateRandomPassword();
        var keycloakRepresentation = UserKeycloakRepresentation.createForSourcing(
                command.getFullname(),
                command.getEmail(),
                password,
                command.getOrganizationCode()
        );
        var organization = abstractOrganizationRepository.findOneByCode(command.getOrganizationCode());
        var emailVerificationStatus = processVerification(command.getEmail());

        switch (emailVerificationStatus.getEmailStatus()) {
            case INVALID_MAIL, INVALID_SERVER -> {
                if (!command.getForceEmail()) {
                    throw new UnknownEmailException(emailVerificationStatus, command.getEmail());
                }
            }
            case VERIFIER_ERROR, UNKNOWN -> log.warn("Unable to check email {}", command.getEmail());
            default -> log.debug("Email verif OK, status {}", emailVerificationStatus.getEmailStatus());
        }
        sourcingKeycloakService.createUserInSourcingRealm(keycloakRepresentation);
        var sender = geSourcingInviteSender();
        sourcingMailingService.sendEmailToInviteUserSourcing(command.getEmail(), password, organization.getTitle(), sender);

        var recruiter = recruiterRepository.findOneByCode(organization.getCode());
        var isSubscriptionActivated = isRecruiterSubscriptionActivated(recruiter);
        notifier.sendMessage(new SourcingNewUserMessageDTO(organization, false, isSubscriptionActivated));
    }

    private String geSourcingInviteSender() {
        if (securityService.isAdmin()) {
            return "jenesuisPASunCV";
        }
        var authenticatedUser = Optional.ofNullable(securityService.getAuthenticatedUserId())
                .map(sourcingKeycloakService::getSourcingUserWithGroups)
                .orElseThrow();
        var senderEmail = authenticatedUser.getEmail();
        var fullname = authenticatedUser.getFullname();
        return fullname == null ? senderEmail : fullname;
    }

    @RolesAllowed(Role.SOURCING)
    @Transactional(readOnly = true)
    public List<SourcingJobAndRecruitmentDTO> getSimilarRecruitments(UUID erhgoOccupationId, Float longitude, Float latitude) {
        var userId = securityService.getAuthenticatedUserId();
        var user = sourcingKeycloakService.getSourcingUserWithGroups(userId);
        var organization = abstractOrganizationRepository.findOneByCode(user.getSingleGroupsRelatedToOrganization());
        var recruitments = recruitmentRepository.findSimilarRecruitments(erhgoOccupationId, longitude, latitude, organization.getId());
        return recruitments.stream().map(sourcingDTOBuilder::buildSourcingJobAndRecruitmentDTO).toList();
    }

    @RolesAllowed(Role.SOURCING)
    @Transactional(readOnly = true)
    public SourcingSubscriptionDTO getSourcingSubscription() {
        var sourcingSubscription = getAuthenticatedUserSubscription();
        sourcingKeycloakService.markNewAuthentication(securityService.getAuthenticatedUserId());
        return sourcingSubscriptionDTOBuilder.buildSourcingSubscriptionDTO(sourcingSubscription);
    }

    @Transactional
    @RolesAllowed(Role.SOURCING)
    public void updateSubscription(UpdateSourcingSubscriptionCommandDTO command) {
        var sourcingSubscription = getAuthenticatedUserSubscription();
        if (sourcingSubscription == null) {
            throw new IllegalStateException("No subscription for authenticated user");
        }
        var invitation = Optional.ofNullable(command.getInvitationCode())
                .flatMap(sourcingInvitationRepository::findOneByCode)
                .orElseThrow(() -> new UnknownInvitationCodeException(command.getInvitationCode()));
        if (sourcingSubscription.acceptInvitation(invitation)) {
            notifier.sendMessage(new SourcingInvitationUsedMessageDTO(sourcingSubscription.getRecruiter(), command.getInvitationCode(), sourcingSubscription.isActivated()));
        }
    }


    protected SourcingSubscription getAuthenticatedUserSubscription() {
        var recruiter = getAuthenticatedRecruiter();
        return Optional.ofNullable(recruiter).flatMap(sourcingSubscriptionRepository::findOneByRecruiter).orElse(null);
    }


    private EmailVerificationResultDTO processVerification(String email) {
        return emailVerificationService.verify(email);
    }


    @NotNull
    private List<Long> buildCandidatureForTopTenUsers(Recruitment recruitment, List<String> matchingUserIds) {
        var candidatures = userProfileRepository.findByUserIdIn(matchingUserIds)
                .stream()
                .map(u -> RecruitmentCandidature.builder()
                        .userProfile(u)
                        .recruitment(recruitment)
                        .generated(true)
                        .state(CandidatureState.VALIDATED)
                        .build()
                )
                .toList();
        candidatures.forEach(c -> {
            c.matches(matchingService.computeMatchingResult(c));
            assignToSourcingGroup(c);
        });
        return StreamSupport.stream(recruitmentCandidatureRepository.saveAll(candidatures).spliterator(), false).map(RecruitmentCandidature::getId).toList();
    }

    private void assignToSourcingGroup(RecruitmentCandidature candidature) {
        var userProfile = candidature.getUserProfile();
        var channel = candidature.getCodeOfRecruiter();
        keycloakService.assignToFrontOfficeGroups(userProfile.userId(), Set.of(channel));
        userProfile.updatedChannels(Set.of(channel), UserChannel.ChannelSourceType.CANDIDATURE);
    }

    protected List<Long> regenerateTopTenCandidatures(Recruitment recruitment) {
        log.info("Ignore candidatures generation for recruitment {} - see https://erhgo.atlassian.net/browse/ERHGO-2009", recruitment.getId());
        return Collections.emptyList();
    }


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public SourcingSimulatedResultDTO simulateSourcingFilters(SourcingCandidatesCriteria criteria, Boolean forcedTechnical) {
        var occupation = Optional
                .ofNullable(criteria.occupationId())
                .map(e -> erhgoOccupationRepository.findById(criteria.occupationId()).orElseThrow())
                .orElse(null);

        if (occupation != null) {
            criteria.capacitiesIds(occupation.getAllCapacitiesId());
            if (Boolean.TRUE.equals(forcedTechnical)) {
                occupation.setTechnical(true);
            }
            criteria.occupationId(occupation.getId());
            criteria.addRomeAndIscoCodes(occupation);
        }

        if (criteria.withDetails()) {
            var candidatesDetail = sourcingUserRepository.getCandidatesDetail(criteria);
            return new SourcingSimulatedResultDTO()
                    .candidateDetail(buildCandidatesDetail(candidatesDetail))
                    .candidatesCount(candidatesDetail.size())
                    ;
        } else {
            return new SourcingSimulatedResultDTO()
                    .candidatesCount(sourcingUserRepository.countCandidates(criteria));
        }
    }

    private List<CandidateDetailDTO> buildCandidatesDetail(List<com.erhgo.repositories.dto.CandidateDetailDTO> details) {
        return details.stream().map(
                d -> new CandidateDetailDTO()
                        .id(d.getId())
                        .city(d.getCity())
                        .masteryLevelAsFloat(d.getMasteryLevelAsFloat())
                        .capacityScore(d.getCapacityScore())
                        .romeExperiencesCount(d.getRomeExperiencesCount())
                        .refusedClassifications(d.getRefusedClassifications() != null ?
                                Arrays.stream(d.getRefusedClassifications().split(",")).toList() :
                                List.of())
        ).toList();
    }

    @Transactional
    @RolesAllowed(Role.SOURCING)
    public SourcingUserDetailsDTO getSourcingUser() {
        var userId = securityService.getAuthenticatedUserId();
        var userOpt = sourcingKeycloakService.getSourcingUser(userId);
        if (userOpt.isEmpty()) {
            throw new EntityNotFoundException(userId, SourcingUserDetailsDTO.class);
        }
        var preference = sourcingPreferencesRepository.findByUserId(userId);
        var user = userOpt.get();
        return new SourcingUserDetailsDTO()
                .email(user.getEmail())
                .fullname(user.getFullname())
                .id(userId)
                .phone(user.getSourcingPhoneNumber())
                .preferences(preference.map(p ->
                        new SourcingPreferencesDTO()
                                .isoWeekDay(p.isoWeekDay())
                                .mailFrequency(SourcingPreferencesDTO.MailFrequencyEnum.fromValue(p.mailFrequency().name()))
                ).orElse(null))
                ;
    }


    protected Recruiter getAuthenticatedRecruiterAndEnsureCanCreateRecruitment() {
        var recruiter = getAuthenticatedRecruiter();

        if (recruiter == null) {
            throw new GenericTechnicalException("Sourcing user %s is not correctly initialized".formatted(securityService.getAuthenticatedUserId()));
        }
        if (sourcingInvitationRepository.findBySubscriptionsRecruiter(recruiter).isEmpty()
                && !Optional.ofNullable(getAuthenticatedUserSubscription()).map(SourcingSubscription::isActiveTrial).orElse(true)
        ) {

            log.warn("No invitation code for {}", recruiter);
            throw new UserNotAllowedForEntity("No invitation code for recruiter");
        }
        return recruiter;
    }

    @RolesAllowed(Role.SOURCING)
    @Transactional(readOnly = true)
    public List<SourcingRecruitmentItemDTO> getSourcingRecruitmentsForAuthenticatedUsers() {
        return recruitmentCandidatureRepository
                .findSourcingRecruitments(getGroupsRelatedToAuthenticatedUser())
                .stream()
                .map(sourcingDTOBuilder::buildSourcingRecruitmentDTO)
                .toList();
    }

    private List<String> getGroupsRelatedToAuthenticatedUser() {
        var userId = securityService.getAuthenticatedUserId();
        var kcUser = sourcingKeycloakService.getSourcingUserWithGroups(userId);
        return kcUser.getGroupsRelatedToOrganizations();
    }

}
