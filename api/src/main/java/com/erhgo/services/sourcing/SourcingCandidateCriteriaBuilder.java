package com.erhgo.services.sourcing;

import com.erhgo.domain.classifications.erhgo.ErhgoClassification;
import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.services.dto.criteria.SourcingCandidatesCriteria;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SourcingCandidateCriteriaBuilder {
    private final ObjectProvider<SourcingCandidatesCriteria> sourcingCandidatesCriteriaProvider;

    public SourcingCandidatesCriteria forRecruitment(Recruitment recruitment, boolean topTen) {
        var criteria = sourcingCandidatesCriteriaProvider.getObject()
                .topTen(topTen)
                .latitude(Optional.ofNullable(recruitment.getLocation()).map(Location::getLatitude).orElse(null))
                .longitude(Optional.ofNullable(recruitment.getLocation()).map(Location::getLongitude).orElse(null))
                .occupationId(Optional.ofNullable(recruitment.getJob().getErhgoOccupation()).map(ErhgoOccupation::getId).orElse(null))
                .typeContractCategory(recruitment.getTypeContract().getTypeContractCategory())
                .salaryMin(recruitment.getBaseSalary())
                .salaryMax(recruitment.getMaxSalary())
                .workingTimeType(recruitment.getJob().getWorkingTimes().stream().findFirst().orElse(null))
                .criteria(new ArrayList<>(recruitment.getJob().getCriteriaValuesCodes()))
                .classifications(recruitment.getErhgoClassifications().stream().sorted().map(ErhgoClassification::getCode).collect(Collectors.toCollection(ArrayList::new)))
                .lastConnectionDateByTopTen()
                .excludesRecruitment(recruitment);
        return criteria.updateCriteriaWithOccupationRelatedDatasAndGlobalParameters(recruitment.getErhgoOccupation());
    }

}
