package com.erhgo.services.notifications;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
@ConditionalOnExpression("'${firebase.apiKey}'.isEmpty()")
public class MockNotificationSender implements NotificationSenderInterface {
    public static final String IGNORE_OPERATION_WARNING = "Ignoring operation due to lack of Firebase configuration";

    @Override
    public void sendNotificationToSpecificDevices(Map<String, Long> deviceTokensAndUnreadNotificationCountMap, String subject, String content, Map<String, String> data) {
        log.warn(IGNORE_OPERATION_WARNING);
        // No op.
    }
}
