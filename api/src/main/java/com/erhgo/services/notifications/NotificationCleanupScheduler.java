package com.erhgo.services.notifications;

import com.erhgo.repositories.NotificationRepository;
import com.erhgo.services.SecurityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationCleanupScheduler {

    private final NotificationRepository notificationRepository;
    private final SecurityService securityService;

    @Value("${application.notificationCleanup.retentionInMonths}")
    private int retentionInMonths;

    @Scheduled(cron = "${application.notificationCleanup.cron}", zone = "Europe/Paris")
    @SchedulerLock(name = "cleanupOldNotifications")
    @Transactional
    public void cleanupOldNotifications() {
        log.info("Starting cleanup of old notifications");
        securityService.doAsAdmin(this::performCleanup);
        log.info("Cleanup of old notifications completed");
    }

    public void performCleanup() {
        var cutoffDate = Date.from(OffsetDateTime.now().minusMonths(retentionInMonths).toInstant());
        var deletedCount = notificationRepository.deleteByCreatedDateBefore(cutoffDate);

        if (deletedCount > 0) {
            log.info("Successfully cleaned up {} old notifications (older than {} months)", deletedCount, retentionInMonths);
        } else {
            log.debug("No old notifications to clean up (older than {} months)", retentionInMonths);
        }
    }
}
