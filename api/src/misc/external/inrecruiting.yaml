{"openapi": "3.0.0", "info": {"title": "Inrecruiting API", "version": "3.0.0"}, "paths": {"/api/v3/candidate": {"get": {"summary": "List of candidates", "description": "Retrieves the list of the candidates belonging to the company of the logged user, based on the specified filters.<br>The search results are paginated and they are returned with a maximum of 50 candidates per page.", "operationId": "Candidate.index", "parameters": [{"name": "name", "in": "query", "description": "Filter candidates by name. The term is searched for as a prefix (term *)", "required": false, "schema": {"type": "string", "example": "<PERSON>"}}, {"name": "surname", "in": "query", "description": "Filter candidates by surname. The term is searched for as a prefix (term *)", "required": false, "schema": {"type": "string", "example": "<PERSON>"}}, {"name": "email", "in": "query", "description": "Filter candidates by email address. The term is searched for as a prefix (term *)", "required": false, "schema": {"type": "string", "format": "email", "example": "<EMAIL>"}}, {"name": "employee", "in": "query", "description": "Filter candidates by type: 0 for Candidates, 1 for Employees", "required": false, "schema": {"type": "integer", "default": 0, "maximum": 1, "minimum": 0, "example": 1}}, {"name": "created_from", "in": "query", "description": "Filter candidates created from the date indicated.", "required": false, "schema": {"type": "string", "format": "date", "example": "2021-01-15"}}, {"name": "created_to", "in": "query", "description": "Filter candidates created up to the date indicated.", "required": false, "schema": {"type": "string", "format": "date", "example": "2021-06-01"}}, {"name": "currentPage", "in": "query", "description": "Pagination of results. The service returns a maximum of 50 candidates at a time. The first page is 0.", "required": false, "schema": {"type": "integer", "default": 0, "example": 0}}, {"name": "cv", "in": "query", "description": "Filter candidates who have uploaded a CV.", "required": false, "schema": {"type": "integer", "maximum": 1, "minimum": 0}}, {"name": "photo", "in": "query", "description": "Filter candidates who have uploaded a photo.", "required": false, "schema": {"type": "integer", "maximum": 1, "minimum": 0}}, {"name": "joborder", "in": "query", "description": "Filter candidates by job order ID", "required": false, "schema": {"type": "integer", "example": 1}}, {"name": "senttoclient", "in": "query", "description": "1 to retrieve candidates (registered to the specified job order) that have been sent to the client, 0 otherwise", "required": false, "schema": {"type": "boolean", "example": "false"}}], "responses": {"200": {"description": "Candidate list", "content": {"application/json": {"schema": {"properties": {"total": {"description": "Total number of retrieved candidates", "type": "string", "example": "18652"}, "items": {"description": "Number of candidates per page", "type": "integer", "example": 50}, "currentPage": {"description": "Page number. First page is 0.", "type": "integer", "example": 0}, "candidates": {"type": "array", "items": {"properties": {"id": {"description": "The Candidate's ID", "type": "string", "example": 1}, "name": {"description": "The Candidate's name", "type": "string", "example": "<PERSON>"}, "surname": {"description": "The Candidate's surname", "type": "string", "example": "<PERSON>"}, "email": {"description": "The Candidate's email address", "type": "string", "format": "email", "example": "<EMAIL>"}}, "type": "object"}}}, "type": "object"}}}}, "401": {"description": "Unauthorized resource access"}, "402": {"description": "You have reached the maximum amount of requests included in your plan"}}, "security": [{"oauth2": []}]}, "put": {"summary": "Candidate creation", "description": "Resource to create a new candidate.<br>The Inrecruiting Candidate/Employee profile is totally dynamic and can be configured within the reserved area of the company. The configurator provides the possibility to enter an arbitrary number of fields and to associate a type to them. For this reason it is not possible to define a defined schema for the Candidate except for the standard fields: therefore, the API will send the data as key-property pairs of category names and field names.<br>The Candidate will have status=active and a randomic password will be created.", "operationId": "Candidate.store", "requestBody": {"$ref": "#/components/requestBodies/candidate_request"}, "responses": {"200": {"description": "Candidate's information successfully updated", "content": {"application/json": {"schema": {"properties": {"applicantId": {"description": "The ID of the Candidate", "type": "string", "example": "362"}}, "type": "object"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"properties": {"error": {"description": "Error code", "type": "integer"}, "error_description": {"description": "Error description", "type": "string"}}, "type": "object"}, "examples": {"Malformed updateDate": {"summary": "Malformed updateDate", "value": {"error": "3", "error_description": "The format of the updateDate field is not valid"}}, "Error creating account": {"summary": "Error creating account", "value": {"error": "4", "error_description": "Error creating account"}}}}}}, "401": {"description": "Unauthorized resource access"}, "402": {"description": "You have reached the maximum amount of requests included in your plan"}, "500": {"description": "Error while saving the candidate/employee"}}, "security": [{"oauth2": []}]}}, "/api/v3/candidate/{id}": {"get": {"summary": "Retrieve candidate information", "description": "Retrieve all information about a Candidate.", "operationId": "Candidate.show", "parameters": [{"name": "id", "in": "path", "description": "Candidate ID", "required": true, "schema": {"type": "integer", "minimum": 1, "example": 297}}, {"name": "cv", "in": "query", "description": "1 to retrieve the Curriculum Vitae base64 encoded, 0 otherwise", "required": false, "schema": {"type": "integer", "maximum": 1, "minimum": 0}}, {"name": "photo", "in": "query", "description": "1 to retrieve the Candidate's profile photo base64 encoded, 0 otherwise", "required": false, "schema": {"type": "integer", "maximum": 1, "minimum": 0}}, {"name": "files", "in": "query", "description": "1 to retrieve the Candidate's attachments (e.g. Letter of introduction) base64 encoded, 0 otherwise.<br>They will appear in 'profile'.", "required": false, "schema": {"type": "integer", "maximum": 1, "minimum": 0}}, {"name": "extended_addr", "in": "query", "description": "1 - the address will be returned as an array of individual components (e.g.: street number, country, city, zipcode...)<br>0 - the address will be returned as a single string", "required": false, "schema": {"type": "integer", "example": 1}}, {"name": "labels_lang", "in": "query", "description": "Language in which the labels of the profile fields will appear.", "required": false, "schema": {"type": "string", "default": "it", "maximum": 2, "minimum": 2, "example": "it"}}, {"name": "values_lang", "in": "query", "description": "Language in which the labels of the values for select and select-multiple fields will appear.", "required": false, "schema": {"type": "string", "maximum": 2, "minimum": 2, "example": "it"}}], "responses": {"200": {"description": "Candidate information successfully retrieved", "content": {"application/json": {"schema": {"properties": {"id": {"description": "The candidate's ID", "type": "string"}, "name": {"description": "The candidate's name", "type": "string"}, "surname": {"description": "The candidate's surname", "type": "string"}, "email": {"description": "The candidate's email address", "type": "string", "format": "email"}, "source_domain": {"description": "The source domain of the candidate's first registration", "type": "string"}, "creation_date": {"description": "The creation date of the candidate's first registration", "type": "string", "format": "date"}, "last_date": {"description": "The date on which the candidate's profile was last modified", "type": "string", "format": "date"}, "cv": {"description": "The Curriculum Vitae details", "properties": {"content": {"description": "The Curriculum Vitae file content encoded in base64", "type": "string"}, "filename": {"description": "The Curriculum Vitae filename", "type": "string"}, "ext": {"description": "The Curriculum Vitae file extension", "type": "string"}}, "type": "object"}, "photo": {"description": "The Candidate's profile photo information", "properties": {"content": {"description": "The photo encoded in base64", "type": "string"}, "filename": {"description": "The photo filename", "type": "string"}, "ext": {"description": "The photo file extension", "type": "string"}}, "type": "object"}}, "type": "object"}, "example": {"id": "297", "name": "<PERSON>", "surname": "<PERSON>", "email": "<EMAIL>", "source_domain": "www.facebook.com", "creation_date": "2019-05-30", "last_update": "2021-06-02", "profile": {"Dati anagrafici": [{"Indirizzo di domicilio": {"address": "<PERSON><PERSON><PERSON>", "street_number": null, "country": "Italia", "country_ISO": "IT", "region": "Piemonte", "county": "TO", "county_code": null, "city": "Torino", "city_code": null, "locality": "Torino", "zipcode": "10121", "latitude": "45.0716", "longitude": "7.6659", "address_ext": "<PERSON><PERSON><PERSON>, Torino, TO, Italia"}, "Telefono": null, "Genere": "<PERSON><PERSON><PERSON>", "Appartenente a categorie protette": "No", "Data di nascita": "1980-01-01", "Comune di nascita": "<PERSON><PERSON>", "Stato di origine": "Italia", "Numero di cellulare": "3481234567", "Comune di domicilio": "Torino (TO)"}], "Esperienze professionali": {"1": {"Anni totali di esperienza": "2,00", "Livello di carriera": "Impiegato", "Livello di carriera precedente": null, "Professione/ Funzione": "Marketing", "Altra professione/ funzione": null, "Azienda": "Strategie & Innovazione", "Settore": "Consulenza e servizi alla Direzione"}}, "Competenze linguistiche": [{"Lingua": "<PERSON><PERSON><PERSON>", "Altra lingua": null, "Scritto": "<PERSON><PERSON><PERSON>", "Parlato": "<PERSON><PERSON><PERSON>", "Certificazione/i di lingua": null, "Anno certificazione/i": null}, {"Lingua": "Tedesco", "Altra lingua": null, "Scritto": "<PERSON><PERSON><PERSON>", "Parlato": "<PERSON><PERSON><PERSON>", "Certificazione/i di lingua": null, "Anno certificazione/i": null}], "Lettera di presentazione": [{"File lettera di presentazione": {"content": "UEs...AAA==", "filename": "lorem_ipsum.docx", "ext": "docx"}}], "disable_candidate_notification": true}}}}}, "400": {"description": "Malformed ID parameter", "content": {"application/json": {"schema": {"properties": {"error": {"description": "Error code", "type": "integer", "example": 0}, "error_description": {"description": "Error description", "type": "string", "example": "Malformed request param"}}, "type": "object"}}}}, "401": {"description": "Unauthorized resource access"}, "402": {"description": "You have reached the maximum amount of requests included in your plan"}}, "security": [{"oauth2": []}]}, "post": {"summary": "Candidate update", "description": "Resource to edit the candidate identified by the id passed.", "operationId": "Candidate.update", "parameters": [{"name": "id", "in": "path", "description": "The ID of the Candidate to be updated.", "required": true, "schema": {"type": "integer", "minimum": 1, "example": 19159}}], "requestBody": {"$ref": "#/components/requestBodies/candidate_request"}, "responses": {"200": {"description": "Candidate's information successfully updated", "content": {"application/json": {"schema": {"properties": {"applicantId": {"description": "The ID of the Candidate", "type": "string", "example": "362"}}, "type": "object"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"properties": {"error": {"description": "Error code", "type": "integer", "example": 3}, "error_description": {"description": "Error description", "type": "string", "example": "The format of the updateDate field is not valid"}}, "type": "object"}}}}, "401": {"description": "Unauthorized resource access"}, "402": {"description": "You have reached the maximum amount of requests included in your plan"}, "500": {"description": "Error while saving the Candidate information"}}, "security": [{"oauth2": []}]}}, "/api/v3/vacancy-application": {"get": {"summary": "List of vacancy applications", "description": "Retrieves the list of the vacancy applications belonging to the company of the logged user, based on the specified filters.<br>The search results are paginated and they are returned with a maximum of 50 applications per page.", "operationId": "VacancyApplication.index", "parameters": [{"name": "currentPage", "in": "query", "description": "Pagination of results. The service returns a maximum of 50 candidates at a time. The first page is 0.", "required": false, "schema": {"type": "integer", "default": 0, "example": 0}}, {"name": "candidate_id", "in": "query", "description": "Filter applications by Candidate ID.", "required": false, "schema": {"type": "integer", "example": "16845"}}, {"name": "vacancy_id", "in": "query", "description": "Filter applications by Vacancy ID.", "required": false, "schema": {"type": "integer", "example": "425"}}, {"name": "status_id", "in": "query", "description": "Filter applications by application status.", "required": false, "schema": {"type": "integer", "example": "1"}}], "responses": {"200": {"description": "Vacancy application information successfully retrieved", "content": {"application/json": {"schema": {"properties": {"total": {"description": "Total number of retrieved vacancy applications", "type": "integer", "example": 3}, "items": {"description": "Number of candidates in the current page. The service returns a maximum of 50 candidates per page.", "type": "integer", "example": 3}, "page": {"description": "Page number. First page is 0.", "type": "integer", "example": 0}, "applications": {"type": "array", "items": {"properties": {"vacancy_id": {"description": "The vacancy ID", "type": "integer"}, "candidate_id": {"description": "The Candidate's ID", "type": "integer"}, "registration_date": {"description": "The date of the candidate's registration to the vacancy", "type": "string"}, "source": {"description": "The source domain of the candidate's first registration", "type": "string"}, "status_id": {"description": "The application status ID", "type": "integer"}}, "type": "object"}}}, "type": "object"}, "example": {"total": 3, "items": 3, "page": 0, "applications": [{"vacancy_id": 425, "candidate_id": 19358, "registration_date": "2024-02-22 10:13:56", "source": "inrecruiting.intervieweb.it", "status_id": 0}, {"vacancy_id": 425, "candidate_id": 19362, "registration_date": "2024-04-24 16:04:55", "source": "Fonte da profilo candidato Registrazione ad annunci", "status_id": 0}, {"vacancy_id": 436, "candidate_id": 19379, "registration_date": "2024-05-27 10:36:04", "source": "infojobs", "status_id": 1}]}}}}, "401": {"description": "Unauthorized resource access"}, "402": {"description": "You have reached the maximum amount of requests included in your plan"}}, "security": [{"oauth2": []}]}, "put": {"summary": "New vacancy application creation", "description": "Resource to assign a candidate to a vacancy.", "operationId": "VacancyApplication.store", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["vacancy_id", "candidate_id"], "properties": {"vacancy_id": {"description": "The ID of the vacancy the candidate will be applied to", "type": "string", "example": "365"}, "candidate_id": {"description": "The Candidate's ID that will be assigned to the vacancy", "type": "string", "example": "13452"}, "self_application": {"description": "True if the candidate directly applied to the vacancy, false in case of manual addition by a company user", "type": "boolean", "example": true}, "source": {"description": "The source domain of the candidate's registration", "type": "string", "example": "inrecruiting.intervieweb.it"}, "candidate_notification": {"description": "In case of self application, set candidate_notification to 1 to disable mail notification to the candidate. Default value is 1.", "type": "integer", "example": "1"}}, "type": "object"}}}}, "responses": {"400": {"description": "'Missing candidate Id or vacancy Id' or 'Vacancy not found'", "content": {"application/json": {"schema": {"properties": {"error": {"description": "Error code", "type": "integer"}, "error_description": {"description": "Error description", "type": "string"}}, "type": "object"}, "examples": {"Missing candidate Id": {"summary": "Missing candidate Id", "value": {"error": "1", "error_description": "The candidate <PERSON><PERSON> is missing"}}, "Missing vacancy Id": {"summary": "Missing vacancy Id", "value": {"error": "1", "error_description": "The vacancy Id is missing"}}, "Vacancy not found": {"summary": "Vacancy not found", "value": {"error": "1", "error_description": "The vacancy associated to the given vacancy_id does not exist"}}}}}}, "401": {"description": "Unauthorized resource access", "content": {"application/json": {"schema": {"properties": {"error": {"description": "Error code", "type": "integer"}, "error_description": {"description": "Error description", "type": "string"}}, "type": "object"}, "examples": {"Candidate inexistent": {"summary": "Candidate inexistent", "value": {"error": "1", "error_description": "Candidate not found"}}, "Wrong company id": {"summary": "Wrong company id", "value": {"error": "1", "error_description": "Vacancy's company does not match candidate's company"}}, "Vacancy is published only for employees": {"summary": "Vacancy is published only for employees", "value": {"error": "1", "error_description": "Cannot assign a candidate to a vacancy reserved to employees"}}, "Vacancy is published only for candidates": {"summary": "Vacancy is published only for candidates", "value": {"error": "1", "error_description": "Cannot assign an employee to a vacancy reserved to candidates"}}}}}}, "402": {"description": "You have reached the maximum amount of requests included in your plan"}, "500": {"description": "Error while saving the application"}}, "security": [{"oauth2": []}]}}}, "components": {"schemas": {"Vacancy": {"properties": {"external_id": {"description": "Vacancy external ID on the calling system. This String field can be used to store any type of information. In the case of integrations, it allows to manipulate the Inrecruiting elements by associating them directly with the external ID, without the need for other mappings.", "type": "string", "maxLength": 30, "example": "testAPI", "nullable": true}, "company_id": {"description": "Unique ID of the company that owns the vacancy. If not provided, it will be valorized with the ID of the company to which the user who has logged into the API belongs.", "type": "integer", "example": 1690}, "agency_id": {"description": "ID of the business division that manages the vacancy.", "type": "integer", "example": 12, "nullable": true}, "jod_order_id": {"description": "Job order ID related to the vacancy.", "type": "integer", "example": 34}, "publish_date": {"description": "Publication date.", "type": "string", "example": "2020-01-15 00:00:00", "nullable": true}, "expire_date": {"description": "Expiration date.", "type": "string", "example": "2020-12-31 00:00:00", "nullable": true}, "job_reference": {"description": "Mnemonic identifier.", "type": "string", "maxLength": 255, "example": "SA-HRSW", "nullable": true}, "functional_area": {"description": "Functional area. In this field you can enter both the ID or the label. If there are several professions with the same label, the system will pick randomly one of them.", "type": "string", "maxLength": 255, "example": "Commerciale"}, "positions": {"description": "Number of searched positions.", "type": "integer", "example": 1, "nullable": true}, "industry": {"description": "Field of use. In this field you can enter both the ID or the label. If there are several professions with the same label, the system will pick randomly one of them.", "type": "string", "maxLength": 255, "example": "Risorse umane e sociologia", "nullable": true}, "educational_level": {"description": "Level of education required (CodeTable).", "type": "string", "maxLength": 255, "example": "Diploma", "nullable": true}, "contract": {"description": "Type of contract.", "type": "string", "maxLength": 255, "example": "Tempo indeterminato", "nullable": true}, "years_of_experience": {"type": "string", "maxLength": 255, "example": "Internship", "nullable": true}, "salary": {"description": "Salary currency (CodeTable).", "type": "integer", "example": 1500}, "currency": {"description": "Salary currency (CodeTable).", "type": "string", "maxLength": 20, "example": "Euro-EUR"}, "video_url": {"description": "URL of the video integrated into the vacancy.", "type": "string", "maxLength": 255, "example": "https://youtu.be/qV1nJbtRe3Y"}, "notes": {"description": "Internal Notes (Unpublished).", "type": "string", "example": "Sostituzione maternità"}, "status_id": {"description": "Selection progress (CodeTable).", "type": "integer", "example": 1}, "project_id": {"description": "Internal Project ID.", "type": "integer", "example": 3}, "employees_only": {"description": "1 => vacancy will be visible only to employees.", "type": "integer", "maximum": 1, "minimum": 0, "example": 0}, "approved": {"description": "Approval for publication.", "type": "integer", "maximum": 1, "minimum": 0, "example": 1}, "location": {"description": "Place of work.", "properties": {"address": {"type": "string", "maxLength": 255, "example": "Via Amedeo <PERSON>"}, "street_number": {"type": "string", "maxLength": 255, "example": "12"}, "country": {"type": "string", "maxLength": 255, "example": "Italia"}, "country_ISO": {"type": "string", "maxLength": 2, "example": "IT"}, "county": {"type": "string", "maxLength": 255, "example": "Torino"}, "county_code": {"type": "string", "maxLength": 255, "example": "TO"}, "city": {"type": "string", "maxLength": 255, "example": "Torino"}, "city_code": {"type": "string", "maxLength": 4, "example": "TO"}, "locality": {"type": "string", "maxLength": 4, "example": "Torino"}, "zipcode": {"type": "string", "maxLength": 50, "example": "10121"}, "latitude": {"description": "A valid latitude (-90 to 90).", "type": "number", "format": "float", "maxLength": 50, "example": 45.0682}, "longitude": {"description": "A valid longitude (-180 to 180).", "type": "number", "format": "float", "maxLength": 50, "example": 7.6708}, "extended_address": {"type": "string", "maxLength": 255, "example": "Via Amedeo <PERSON>, 12, Torino, TO, Italia"}}, "type": "object"}, "translations": {"description": "One property per language (en, it, ...). It is mandatory to enter at least one language.", "type": "object", "example": {"it": {"title": "Sales Account HR Software", "company": "Cliente ABC", "position": "<p>Per lo sviluppo del nostro team commerciale cerchiamo un Sales Account. La figura di Sales Account verrà inserita all’interno del team commerciale e riporterà direttamente al CEO affiancando il Direttore Commerciale.</p>", "requirements": "<ul><li>Esperienza di vendita di almeno 2-3 anni in ambito B2B, preferibilmente di servizi web/ tecnologici per l’HR o di servizi HR (costituisce titolo preferenziale la conoscenza approfondita del mercato HR e dei servizi IT per l’HR);</li><li>Forte motivazione, attitudine alla vendita e passione per il settore HR;</li></ul>", "other_information": "La sede di lavoro è a Torino. Contratto di assunzione con una RAL commisurata all'esperienza. La ricerca è rivolta a candidati ambosessi (L. 125/91)."}, "en": {"title": "Sales Account HR Software", "company": "Cliente ABC", "position": "<p>For the development of our sales team we are looking for a Sales Account. The Sales Account figure will be included within the sales team and will report directly to the CEO alongside the Sales Director.</p>", "requirements": "<ul><li>Sales experience of at least 2-3 years in the B2B field, preferably of web / technological services for HR or HR services (in-depth knowledge of the HR market and IT services for HR is preferable);</li><li>Strong motivation, sales attitude and passion for the HR sector;</li></ul>", "other_information": "The place of work is in Turin. Employment contract with a salary commensurate with experience. The research is aimed at candidates of both sexes (L. 125/91)."}}, "additionalProperties": {"description": "2-character language ISO code (en, it, ...)", "properties": {"title": {"description": "Vacancy title", "type": "string", "maxLength": 255, "nullable": true}, "company": {"description": "Company description", "type": "string", "maxLength": 4000, "nullable": true}, "position": {"description": "Description of the position searched", "type": "string", "maxLength": 4000, "nullable": true}, "requirements": {"description": "Description of the requirements", "type": "string", "maxLength": 4000, "nullable": true}, "other_information": {"description": "Other information (e.g. privacy, legal notes, etc.)", "type": "string", "maxLength": 4000}, "mail_subject": {"description": "Application confirmation notification message subject", "type": "string"}, "mail_text": {"description": "Application confirmation notification message text", "type": "string"}, "mail_subject_survey": {"description": "Survey subject", "type": "string"}, "mail_text_survey": {"description": "Survey text", "type": "string"}, "mail_screen_message": {"description": "Screen notification message text", "type": "string"}}, "type": "object"}}, "multiposting": {"description": "Enable multiposting", "type": "integer", "maximum": 1, "minimum": 0, "example": 0}, "registration_type": {"description": "Choose registration type between Form => 0, Email => 1 and External link => 2", "type": "integer", "maximum": 2, "minimum": 0, "example": 0}, "registration_link": {"description": "Link needed for external link registration type", "type": "string", "example": "https://www.inrecruiting.com"}}, "type": "object"}}, "requestBodies": {"candidate_request": {"required": true, "content": {"application/json": {"schema": {"required": ["EMail", "userType"], "properties": {"source": {"type": "string"}, "name": {"description": "The user's name", "type": "string", "example": "<PERSON>"}, "surname": {"description": "The user's surname", "type": "string", "example": "<PERSON>"}, "email": {"description": "The user's email address", "type": "string", "example": "<EMAIL>"}, "userType": {"description": "0 for Candidate, 1 for Employee", "type": "integer", "maximum": 1, "minimum": 0, "example": 0}, "updateDate": {"description": "Date and time of the Candidate's profile last modification. The current date and time will be used as the default value.", "type": "string", "format": "date", "example": "2021-06-01", "nullable": true}, "cv": {"description": "The Curriculum Vitae details", "properties": {"content": {"description": "The Curriculum Vitae file content encoded in base64", "type": "string"}, "filename": {"description": "The Curriculum Vitae filename", "type": "string"}, "ext": {"description": "The Curriculum Vitae file extension", "type": "string"}}, "type": "object"}, "photo": {"description": "The Candidate's profile photo information", "properties": {"content": {"description": "The photo encoded in base64", "type": "string"}, "filename": {"description": "The photo filename", "type": "string"}, "ext": {"description": "The photo file extension", "type": "string"}}, "type": "object"}, "profile": {"type": "array", "items": {"properties": {"id": {"description": "The field ID", "type": "integer", "example": 112134}, "group": {"description": "The field group ID", "type": "integer", "example": 0}}, "type": "object"}, "example": [{"id": 135191, "group": 0, "value": "****************"}, {"id": 112134, "group": 0, "value": "011123123132"}, {"id": 112122, "group": 0, "value": {"address": "Via Verdi", "street_number": "32", "country": "Italy", "country_ISO": "IT", "region": "Piemonte", "county": "<PERSON><PERSON>", "county_code": "AT", "city": "Portacomaro Stazione", "city_code": null, "locality": null, "zipcode": "14100", "latitude": "44.9569", "longitude": "8.2227", "address_ext": null}}]}}, "type": "object"}}}}}, "securitySchemes": {"oauth2": {"type": "oauth2", "description": "The authentication is managed through Oauth2.<br>To request the token you have to make a POST request to &lt;<i>installation_domain</i>&gt;/oauth2/token passing as parameters:<br><table><tr><td>client_id</td><td>id of the ouath2 user you want to authenticate</td></tr><tr><td>client_secret</td><td>password of the user</td></tr><tr><td>grant_type</td><td>client_credential</td></tr></table>The token that is returned must be inserted in every following call through the Authorization header.<br>Each credential is linked to a user within Inrecruiting, therefore all actions carried out through the request will be done in his name; moreover the authorizations to operate in the various resources will be controlled according to the belonging role.<br><br>This API uses OAuth 2 with the clientCredentials grant flow.", "flows": {"clientCredentials": {"tokenUrl": "/oauth2/token", "scopes": {}}}}}}}