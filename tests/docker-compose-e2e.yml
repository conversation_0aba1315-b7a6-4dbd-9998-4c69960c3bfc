version: '3.5'
services:
  selenium:
    image: selenium/standalone-chrome:4.1.2
    shm_size: 2g
    environment:
      START_XVFB: 'false'
  keycloak-db-e2e:
    image: postgres:13
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: password
    labels:
      traefik.enable: 'false'

  keycloak-e2e:
    image: erhgo/authentication:develop
    entrypoint: /bin/bash
    command: "/opt/keycloak/bin/kc.sh start-dev --bootstrap-admin-username admin --bootstrap-admin-password Pa55w0rd"
    environment:
      env: develop
      KC_DB: postgres
      KC_HOSTNAME: keycloak-e2e
      KC_HOSTNAME_PORT: 8080
      KC_DB_URL: ******************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: password
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: Pa55w0rd
      KEYCLOAK_LOGLEVEL: INFO
      ROOT_LOGLEVEL: INFO
      KC_PROXY_HEADERS: xforwarded
      KC_HTTP_ENABLED: "true"
    depends_on:
      - keycloak-db-e2e
    expose:
      - 8080
    labels:
      traefik.enable: 'false'

  mariadb-e2e: # docker stop mariadb-e2e && docker rm mariadb-e2e --volumes && docker volume rm odas_mariadb_data_e2e && docker-compose up -d mariadb-e2e
    image: mariadb:11.4.4
    environment:
      MYSQL_USER: odas
      MYSQL_DATABASE: odas
      MYSQL_PASSWORD: password
      MYSQL_ROOT_PASSWORD: password
      MARIADB_AUTO_UPGRADE: 1
    command: ['mariadbd', '--character-set-server=utf8mb4', '--collation-server=utf8mb4_unicode_ci']
    restart: on-failure

  api-e2e:
    image: "$CI_REGISTRY_IMAGE/api:$CI_COMMIT_SHA"
    environment:
      - SPRING_PROFILES_ACTIVE=e2e
      - MYSQL_URL=mariadb-e2e
      - KEYCLOAK_URL=http://keycloak-e2e:8080
      - ALGOLIA_APPLICATION_ID=3EXVCX9LB4
      - ALGOLIA_ADMIN_API_KEY=********************************
      - ALGOLIA_SEARCH_API_KEY=********************************
    expose:
      - 8080
    depends_on:
      - mariadb-e2e
      - keycloak-e2e
    restart: on-failure

  back-office-e2e:
    image: "$CI_REGISTRY_IMAGE/back-office:$CI_COMMIT_SHA"

  sourcing-e2e:
    image: "$CI_REGISTRY_IMAGE/sourcing:$CI_COMMIT_SHA"

  yarn_test:
    user: "$CURRENT_UID"
    image: "$CI_REGISTRY_IMAGE/test_e2e_launcher:$CI_COMMIT_SHA"
    command: "tail -f /dev/null"
    volumes:
      - ./conf:/conf
      - ./commands:/commands
      - ./scenarios:/scenarios
      - ./tests_output:/tests_output
      - ./vrt:/vrt
    depends_on:
      - selenium
      - back-office-e2e
      - api-e2e
