function screenshotFilePathGenerator(nightwatchClient, basePath, fileName) {
  return `./${basePath}/${fileName}`;
}

module.exports = {
  test_settings: {
    default: {
      selenium: {
        start_process: false,
        port: 4444,
        host: process.env.SELENIUM_HOST || '127.0.0.1',
      },
      desiredCapabilities: {
        browserName: 'chrome',
        javascriptEnabled: true,
        acceptSslCerts: true,
        chromeOptions: {
          w3c: false,
          args: [
            'disable-gpu',
            'no-sandbox',
            'headless',
            'window-size=1920,1080',
            'host-resolver-rules=MAP back-office-e2e.localhost back-office-e2e:8080,MAP front-office-e2e.localhost front-office-e2e:8080,MAP sourcing-e2e.localhost sourcing-e2e:8080,MAP api-e2e.localhost api-e2e:8080',
          ],
        },
      },
      screenshots : {
        enabled : true,
        path : 'tests_output/screenshots/',
        on_failure: true,
        on_error: true,
      },
    },
  },
  globals: {
    retryAssertionTimeout: 5000,
    waitForConditionTimeout: 10000,
    test_settings: {
      visual_regression_settings: {
        generate_screenshot_path: screenshotFilePathGenerator,
        latest_screenshots_path: 'vrt/latest',
        latest_suffix: '',
        baseline_screenshots_path: 'vrt/baseline',
        baseline_suffix: '',
        diff_screenshots_path: 'vrt/diff',
        diff_suffix: '',
        threshold: 0.003,
        prompt: false,
        always_save_diff_screenshot: false,
      },
    },
  },
  custom_commands_path: [
    'node_modules/@bbc/nightwatch-vrt/commands',
    'commands',
  ],
  custom_assertions_path: [
    'node_modules/@bbc/nightwatch-vrt/assertions',
  ],
};
