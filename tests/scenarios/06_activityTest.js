module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Activity creation': function(client) {
    const label1 = 'Écrire des tests E2E';
    const label2 = 'Écrire des tests Selenium';

    client
      // Quand j'ouvre la liste des activités
      .loginBOAndGoURL('/repository/activity/JOB/list')
      // Alors je vois la liste des activités
      .waitForElementPresent('#activityList')
      .waitForElementNotPresent('#activityList_loading')
      .assert.screenshotIdenticalToBaseline('body', 'page-liste-activites-avant-creation')
      // Puis je clique sur le bouton d'ajout
      .click('#createActivityButton')
      // Et je vois le formulaire de création d'activité, vierge
      .waitForElementPresent('.capacityCheckbox')
      .assert.screenshotIdenticalToBaseline('body', 'page-creation-activite')
      // Je saisi 2 libellés
      .setValue('.alternativeLabel_textInput input', label1)
      .click('#alternativeLabel_addButton')
      .setValue('.alternativeLabel_textInput:nth-of-type(2) input', label2)
      // Je sélectionne une capacité
      .click('.capacityCheckbox input + div')
      // J'attends la fin de l'animation
      .pause(400)
      // Je vérifie que le formulaire est rempli
      .assert.screenshotIdenticalToBaseline('body', 'page-creation-activite-remplie')
      // Puis je clique sur le bouton de sauvegarde
      .execute('window.scrollTo(0,document.body.scrollHeight);') // scroll to bottom of page
      .click('#saveActivityButton')
      .waitForElementNotPresent('#saveActivityButton.v-btn--loading')
      .waitForElementPresent('button.success')
      // Et je clique sur le bouton de retour à la liste
      .click('#backToActivityListButton')
      // Et je vois la liste des activités
      .waitForElementPresent('#activityList')
      .waitForElementNotPresent('#activityList_loading')
      .assert.screenshotIdenticalToBaseline('body', 'page-liste-activites-apres-creation')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
