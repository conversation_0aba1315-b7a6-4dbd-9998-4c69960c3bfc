module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'Behavior list': function(client) {

    client
      // Quand j'ouvre la liste des comportements
      .loginBOAndGoURL('/repository/behaviors')
      // Alors je vois la liste des comportements
      .waitForElementPresent('#behaviorList')
      // J'attend la fin du chargement
      .waitForElementNotPresent('#listLoading')
      // J'attends que l'input perde son style "focus"
      .pause(300)
      .assert.screenshotIdenticalToBaseline('body', 'page-liste-comportements')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function(client, done) {
    setTimeout(function() {
      done();
    }, 1000);
  },
};
