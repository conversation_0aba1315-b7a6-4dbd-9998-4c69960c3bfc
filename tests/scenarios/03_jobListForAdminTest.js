module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'jobList': function (client) {
    client
      .pause(15000)
      .loginBOAndGoURL('/setup/organization/E-0001/job/list')
      .waitForElementVisible('.jobItem')
      .assert.screenshotIdenticalToBaseline('body', 'page-list-job')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function (client, done) {
    setTimeout(function () {
      done();
    }, 1000);
  },
};
