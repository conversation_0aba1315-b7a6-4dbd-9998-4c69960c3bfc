const {sourcing_base_url} = require('../env');


module.exports = {
  '@tags': ['test', 'sourcing'],

  /**
   * @param {NightwatchAPI} client
   */

  'Filling search form in sourcing landing page': function (client) {
    client
      .url(sourcing_base_url)
      .pause(500)
      .url(function(result) {
        console.log('URL courante après redirection :', result.value);
      })
      .waitForElementVisible('.login-section__no')
      .assert.screenshotIdenticalToBaseline('main', 'sourcing-page-de-connexion')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  }

  ,

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function (client, done) {
    setTimeout(function () {
      done();
    }, 1000);
  },
};

