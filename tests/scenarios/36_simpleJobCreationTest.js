module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'jobCreation': function (client) {
    client
      // Quand j'ouvre la page de liste des job
      .loginBOAndGoURL('/setup/organization/E-0001/job/list')
      .click('#createJobBtn')
      .click('#createSimpleJobBtn')
      // Quand j'ouvre la page de création d'un job simplifié
      .waitForElementVisible('#selectorJob')
      .assert.screenshotIdenticalToBaseline('body', 'page-popin-selector-job')
      .setValue('#selectorJob', 'Opérateur ')
      .click('.selectableJob')
      .pause(200)
      .click('#searchCity')
      .keys('Lyon')
      .waitForElementVisible('#city-for-code69383', 60000)
      .keys([client.Keys.ARROW_DOWN, client.Keys.ENTER, client.Keys.NULL])
      .waitForElementNotVisible('#city-for-code69383')
      .assert.screenshotIdenticalToBaseline('body', 'page-create-simple-job-start')
      // Je passe à l'étape suivante
      .click('#nextStep')
      .pause(200)
      .waitForElementVisible('#nextStepToCriteria', 10000)
      .click('#nextStepToCriteria')
      .waitForElementVisible('#btnJobToRecruitment')
      .assert.screenshotIdenticalToBaseline('body', 'page-create-simple-job-criteria')
      // scroll to bottom of page
      .execute('window.scrollTo(0,document.body.scrollHeight);')
      .pause(200)
      .click('#btnJobToCustomQuestion')
      .pause(200)
      .waitForElementVisible('#customQuestionText', 10000)
      .setValue('#customQuestionText', 'Comment ça va ?')
      .pause(200)
      .assert.screenshotIdenticalToBaseline('body', 'page-create-simple-job-custom-question')
      .click('#customQuestion')
      .waitForElementVisible('#profileSelection', 10000)
      .assert.screenshotIdenticalToBaseline('body', 'page-create-simple-job-recruitment')
      .end();
  },
};
