module.exports = {
  '@tags': ['test', 'back-office'],

  /**
   * @param {NightwatchAPI} client
   */
  'job list for project': function (client) {
    client
      .loginBOAndGoURL('/setup/organization/E-0001/job/list')
      .waitForElementPresent('#deleteJob1')
      .click('table .mdi-account-multiple')
      .waitForElementVisible('table .candidateCompare')
      .click('#matching-users-for-job-index-view')
      .waitForElementVisible('#result-for-user-search tbody .result-item td')
      .assert.screenshotIdenticalToBaseline('body', 'page-index-user-for-job')
      .end();
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  beforeEach: function (client, done) {
    client.resizeWindow(1920, 1080, done);
  },

  /**
   * @param {NightwatchAPI} client
   * @param {function} done
   */
  afterEach: function (client, done) {
    setTimeout(function () {
      done();
    }, 1000);
  },
};
