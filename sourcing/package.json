{"name": "sourcing", "version": "0.0.1", "description": "FO de sourcing erhgo", "productName": "je recrute - jenesuisPASunCV", "author": "dev team #jenesuisPASunCV<<EMAIL>>", "private": true, "scripts": {"lint": "eslint -c eslint.config.mjs ./src/**/*.{ts,js,cjs,mjs,vue}", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "start": "quasar dev", "start-staging": "VUE_APP_API_URL=https://staging-api.jenesuispasuncv.fr VUE_APP_AUTH_URL=https://staging-auth.jenesuispasuncv.fr quasar dev"}, "dependencies": {"@fortawesome/fontawesome-pro": "^6.7.2", "@quasar/extras": "^1.16.16", "@rollup/rollup-linux-x64-gnu": "^4.34.2", "@vueup/vue-quill": "^1.2.0", "axios": "^1.7.9", "diff": "^7.0.0", "erhgo-api-client": "file:../common/generated/client", "keycloak-js": "^26.0.5", "moment": "^2.30.1", "quasar": "^2.17.7", "sass-embedded-linux-x64": "^1.83.4", "underscore": "^1.13.7", "uuid": "^11.0.5", "vue": "^3.5.13", "vue-i18n": "^11.1.0", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@intlify/vue-i18n-loader": "^4.2.0", "@quasar/app-vite": "^2.0.8", "@types/geojson": "^7946.0.10", "@types/node": "^22.13.1", "@types/underscore": "^1.11.4", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vue/eslint-config-typescript": "^14.3.0", "autoprefixer": "^10.4.14", "eslint": "9", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "globals": "^15.14.0", "prettier": "^3.4.2", "typescript": "^5.0.3", "vite-plugin-checker": "^0.8.0", "vue-tsc": "^2.2.0"}, "engines": {"node": "^18 || ^16 || ^14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}