<template>
  <div class="row justify-center" v-if="isLoading || !recruitments">
    <q-circular-progress indeterminate size="50px" color="secondary" />
  </div>
  <template v-else>
    <div class="q-px-md" id="tableHeader">
      <h6 class="d-flex justify-between row q-my-none">
        <span>Mes offres</span>
        <q-btn :to="{name: 'recruitment'}"
               v-if="subscriptionType !== SourcingSubscriptionType.TRIAL || (recruitments && !recruitments.length)"
               rounded icon="fa-light fa-add" class="bg-green text-white" label="Créer une nouvelle offre" />
        <q-btn v-else
               rounded icon="fa-light fa-phone" class="bg-green text-white"
               label="Rapprochez-vous de nos équipes pour créer vos prochaines offres"
               @click="showContactPopin"
        />
      </h6>
    </div>
    <recruitments-list :recruitments="recruitments" :isLoading="isLoading" v-if="recruitments"
                       @deleteRecruitment="(id: number) => recruitments=recruitments!.filter(r => r.recruitmentId !== id)" />
    <div v-if="!recruitments && !isError && !isLoading">
      <h4>Aucun recruitment</h4>
    </div>
    <q-banner dense class="q-ma-md text-white bg-negative" v-if="isError">
      <q-icon name="fa-light fa-warning q-mr-md" size="md" />
      Une erreur est survenue - veuillez réessayer ou contacter notre support.
    </q-banner>
  </template>

</template>

<script setup lang="ts">
import { computed, inject, onMounted, ref } from 'vue';
import { useApi } from 'src/config/api';
import { RecruitmentState, SourcingSubscriptionType } from 'erhgo-api-client';
import _ from 'underscore';
import moment from 'moment';
import accountProvider from 'src/config/AccountProvider';
import RecruitmentsList from 'components/recruitment/RecruitmentsList.vue';
import type { RecruitmentService } from 'src/hooks/useRecruitement';
import useRecruitment from 'src/hooks/useRecruitement';
import { useRouter } from 'vue-router';

const {service: {api, isError, isLoading}} = useApi();
const props = defineProps({ redirected: { type: Boolean, default: false } });
const recruitments = ref<Array<RecruitmentService>>();
const subscriptionType = computed(() => accountProvider.account.value?.subscriptionType);
const showContactPopin = inject('showContactPopin');
const router = useRouter();
onMounted(async () => {
  let fetchedRecruitments: RecruitmentService[] = (await api.getSourcingRecruitments()).data.map(r => useRecruitment(r));
  fetchedRecruitments = _.sortBy(fetchedRecruitments).reverse(a => moment(a.publicationDate.value ?? 0));
  const hasNoneOrOnlyOneDraftRecruitment = !fetchedRecruitments.length || (fetchedRecruitments.length === 1 && fetchedRecruitments[0].recruitmentState.value === RecruitmentState.DRAFT);
  if (props.redirected && hasNoneOrOnlyOneDraftRecruitment) {
    const redirect = { name: 'recruitment', params: { recruitmentId: fetchedRecruitments[0]?.recruitmentId } };
    await router.push(redirect);
  }
  recruitments.value = fetchedRecruitments;
});

</script>
