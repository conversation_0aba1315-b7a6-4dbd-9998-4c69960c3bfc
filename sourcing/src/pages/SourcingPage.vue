<template>
  <div>
    <users-notified-dialog v-model="showUserNotifyDialog" :selected-users-ids="selectedUsersIds" />

    <div class="col text-center" id="tableHeader">
      <div class="row chimney--fluid q-pa-sm items-center justify-evenly">
        <div class="text-weight-bold text-h5 q-pb-md col-12">
          Votre vivier
        </div>
        <div class="q-pb-lg d-flex center-text">
          <q-btn class="text-weight-bold green-text q-py-none q-mr-lg"
                 icon="fa fa-bell"
                 label="Configuration"
                 @click="showUserNotifyDialog=true"
                 outline
          >
            <q-tooltip content-class="bg-secondary text-white shadow-4" anchor="bottom middle" self="top middle">
              Modifier les personnes à notifier à la réception d'une candidature spontanée
            </q-tooltip>
          </q-btn>
          <q-btn class="text-weight-bold green-text  q-ml-lg"
                 v-if="organizationUrl"
                 icon="fa fa-eye"
                 label="Ma page entreprise"
                 outline
                 target="_blank"
                 :href="organizationUrl"
          />
        </div>
      </div>
    </div>
    <candidatures-list />
  </div>
</template>
<script setup lang="ts">
import CandidaturesList from 'components/candidature/CandidaturesList.vue';
import { ref } from 'vue';
import { useApi } from 'src/config/api';
import UsersNotifiedDialog from 'components/recruitment/UsersNotifiedDialog.vue';

const { service: { api } } = useApi();
const organizationUrl = ref<string | undefined>((await api.getSourcingOrganization()).data.externalUrl);
const selectedUsersIds = ref<string[]>((await api.getUsersToNotifyOnSpontaneousCandidature()).data.map(u => u.id));
const showUserNotifyDialog = ref(false);

</script>
