<template>
  <div id="q-step-recruitment" class="row justify-center">
    <div>
      <q-dialog no-backdrop-dismiss
                v-model="showSimilarRecruitmentsPopin" style="width: 800px">
        <existing-recruitment-popin
            v-if="showSimilarRecruitmentsPopin"
            :recruitments="similarRecruitments"
            @createRecruitmentAnyway="forceCreateRecruitment"
        />
      </q-dialog>
    </div>
    <div class="chimney--large" v-if="!initialized">
      <q-circular-progress indeterminate size="50px" color="secondary"/>
    </div>
    <div class="chimney--fluid" v-else>
      <candidates-result-header
        v-if="recruitment && recruitment.title && areLocationAndJobSelected"
        :nb-users="nbUsers"
        :title="recruitment.title"
      />
      <q-stepper
          v-model="step"
          ref="stepper"
          animated
          contracted
          flat
          keep-alive
          :bordered="false"
          active-color="secondary"
          inactive-color="primary"
          style="padding-bottom: 150px"
          header-nav
      >
        <q-step
            title=""
            icon="fa-light fa-info"
            active-icon="fa-light fa-info"
            done-icon="fa-light fa-info"
            :name="0"
            :header-nav="!!recruitment"
            :done="step > 0"
        >
          <div class="q-my-xl q-pa-xl" v-if="step===0">
            <h2 class="text-weight-bold custom-h1 q-mt-none">
              Créez une nouvelle offre
            </h2>
            <search-occupation
              v-model="selectedJobOccupation"
              :disabled="isLoading"
              class="q-pb-sm-xl q-pb-xs-md custom-pb-none-last"
            />
            <select-city v-model="selectedJobLocation" :disabled="isLoading" />
            <q-banner v-if="areLocationAndJobSelected && nbUsers && !waitingForCountCandidates"
                      class="user-text text-center">
              <h2
                  class="text-body1 q-my-none custom-text-md text-bold">
                <span>
                {{ $tc('foundPeople', nbUsers, {count: nbUsers >= 1000 ? 'Plus de 1000' : nbUsers}) }}
                </span>
              </h2>
            </q-banner>
            <q-banner v-if="recruitmentError" class="alert-text text-center">
              <h2 class="text-body1 q-my-none -text-md text-bold">
                {{ recruitmentError }}
              </h2>
            </q-banner>
            <q-banner class="user-text text-center"
                      v-if="selectedJobOccupation && !selectedJobOccupation.code && isLoading">
              <h2 class="text-body1 q-my-none custom-text-md text-bold">
                Vous avez précisé un nouveau métier.</h2>
              <span>
                la création de votre offre peut prendre une petite
                minute...
              </span><br />
              <span>
                Merci pour votre patience&nbsp;!
              </span>
            </q-banner>
            <q-btn class="full-width bg-secondary outline"
                   v-if="!!(selectedJobLocation && selectedJobOccupation)"
                   @click="createOrUpdateRecruitment"
                   :loading="isLoading"
            >
              Étape suivante
            </q-btn>

            <template
              v-if="!!(selectedJobLocation && selectedJobOccupation && nbUsers === 0 && !recruitment)">
              <q-banner class="user-text text-center">
                <h2 class="text-body1 q-my-none custom-text-md text-bold">
                  Aucune personne ne correspond à votre recherche.
                </h2>
              </q-banner>

            </template>

          </div>
          <div class="col-12 col-md-6" v-if="isError">
            <q-banner class="alert-text">
              Une erreur est survenue, veuillez <span class="custom-break-here">ré-essayer</span> ou contacter le
              support.
            </q-banner>
          </div>
        </q-step>

        <q-step
            title=""
            :name="1"
            caption="Optional"
            done-icon="fa-light fa-file-contract"
            active-icon="fa-light fa-file-contract"
            icon="fa-light fa-file-contract"
            :done="step > 1"
            :disable="latestStep < 1"
        >
          <contract-aspect v-if="recruitment"
                           ref="stepContractRef"
                           :recruitment="recruitment"
                           @updateUserFilter="updateUserFilter"/>
        </q-step>
        <q-step
            v-if="showClassification"
            title=""
            :name="2"
            caption="Optional"
            done-icon="fa-light fa-folder-tree"
            active-icon="fa-light fa-folder-tree"
            icon="fa-light fa-folder-tree"
            :done="step > 2"
            :disable="latestStep < 2"
        >
          <suspense>
            <work-classification v-if="recruitment"
                                 ref="stepClassificationRef"
                                 :recruitment="recruitment"/>
            <template #fallback>
              <q-circular-progress indeterminate size="50px" color="secondary"/>
            </template>
          </suspense>
        </q-step>

        <q-step
            title=""
            :name="stepWorkCondition"
            icon="fa-light fa-tree-city"
            done-icon="fa-light fa-tree-city"
            active-icon="fa-light fa-tree-city"
            :done="step > stepWorkCondition"
            :disable="latestStep < stepWorkCondition"
        >
          <suspense>
            <work-condition ref="stepWorkConditionRef"
                            :recruitment="recruitment"
                            :step="SourcingCriteriaStep.STEP1"
                            @updateCriteria="updateUserFilterCriteria"/>
            <template #fallback>
              <q-circular-progress indeterminate size="50px" color="secondary"/>
            </template>
          </suspense>
        </q-step>
        <q-step
            title=""
            :name="stepOrganization"
            icon="fa-light fa-building"
            done-icon="fa-light fa-building"
            active-icon="fa-light fa-building"
            :done="step > stepOrganization"
            :disable="latestStep < stepOrganization"
        >
          <organization-description v-if="recruitment"
                                    ref="stepOrganizationRef"
                                    :recruitment-id="effectiveRecruitmentId"
                                    :organization="recruitment.organization"
          />
        </q-step>
        <q-step
            title=""
            :name="stepQuestion"
            icon="fa-light fa-question"
            done-icon="fa-light fa-question"
            active-icon="fa-light fa-question"
            :done="step > stepQuestion"
            :disable="latestStep < stepQuestion"
        >
          <recruitment-question ref="stepQuestionRef"
                                :recruitment-id="effectiveRecruitmentId"
                                :recruitment="recruitment"/>
        </q-step>
        <q-step
            title=""
            :name="finalStep"
            icon="fa-light fa-paper-plane"
            done-icon="fa-light fa-paper-plane"
            active-icon="fa-light fa-paper-plane"
            :done="step > finalStep"
            :disable="latestStep < finalStep"
        >
          <suspense>
            <informations-control ref="stepControlRef"
                                  v-if="step===latestStep"
                                  :recruitment-id="effectiveRecruitmentId"
                                  :number-candidates="nbUsers"
                                  @updateUsersToNotifySelectionType="type => sendNotifications = UsersToNotifySelectionType.NONE !== type"
            />
            <template #fallback>
              <q-circular-progress indeterminate size="50px" color="secondary"/>
            </template>
          </suspense>
        </q-step>
        <template v-slot:navigation>
          <q-banner outline dense class="q-ma-md rounded-borders"
                    :class="{'warning-border': !isError, 'negative-border': isError, 'text-negative': isError}"
                    v-if="isError || (nbUsers === 0 && !isLoading && step>0)">
            <q-icon name="fa-light fa-warning q-mr-md" size="md"/>
            <template v-if="isError">
              Une erreur est survenue - veuillez réessayer ou contacter notre support.
            </template>
            <template v-if="nbUsers === 0 && !isLoading">
              Aucune personne ne correspond actuellement à votre recherche.
            </template>
          </q-banner>
          <div class="row justify-between items-center">
            <q-page-sticky position="bottom-left" class="custom-sticky">
              <q-btn v-if="step > 0"
                     flat
                     @click="$refs.stepper.previous()"
                     :dense="!!$q.screen.lt.sm"
                     class="bg-white q-mb-sm"
                     :size="$q.screen.lt.sm?'sm':'md'"
                     icon="fa-light fa-arrow-left"
                     label="Étape précédente"/>
            </q-page-sticky>
            <q-page-sticky position="bottom" class="custom-sticky"
                           v-if="step > 0 && step < finalStep">
              <q-btn class="custom-decompte-candidats q-mb-md q-mr-lg" ref="ripple" :loading="isLoading"
                     v-if="isLoading || nbUsers">
                <q-icon name="fa-light fa-users q-mr-sm" size="md"/>
                <span class="text-weight-bold custom-text-lg">{{ nbUsers >= 1000 ? '+ 1000' : nbUsers }}</span>
                <span class="text-caption">{{ $tc('candidate', nbUsers) }}</span>
              </q-btn>
            </q-page-sticky>
            <q-page-sticky position="bottom-right" class="custom-sticky">
              <q-btn class="bg-secondary q-mb-md"
                     :dense="!!$q.screen.lt.sm"
                     :size="$q.screen.lt.sm?'md':'lg'"
                     @click="nextStep"
                     :loading="loading"
                     v-if="recruitment && step > 0"
                     icon-right="fa-light fa-arrow-right"
                     no-caps>
                <template v-if="step === finalStep && sendNotifications">
                  Oui, je veux inviter<br>
                  {{ `${$tc('people', nbUsers, {count: nbUsers >= 1000 ? '+ 1000' : nbUsers})} à candidater` }}
                </template>
                <template v-else-if="step === finalStep">
                  Je veux publier mon offre<br />
                  Sans envoyer de notifications
                </template>
                <template v-else>
                  Étape suivante
                </template>
              </q-btn>
            </q-page-sticky>
          </div>
        </template>
      </q-stepper>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, watch, watchEffect } from 'vue';
import type { ErhgoOccupationSearch, InitializedAccount, Location, SourcingJobAndRecruitment } from 'erhgo-api-client';
import { RecruitmentState, SourcingCriteriaStep, UsersToNotifySelectionType } from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import CandidatesResultHeader from 'components/RecruitmentHeader.vue';
import ContractAspect from 'components/recruitment/ContractAspect.vue';
import WorkCondition from 'components/recruitment/WorkCondition.vue';
import OrganizationDescription from 'components/recruitment/OrganizationDescription.vue';
import RecruitmentQuestion from 'components/recruitment/RecruitmentQuestion.vue';
import type { UserFilter } from 'src/models/UserFilter';
import { useRouter } from 'vue-router';
import InformationsControl from 'components/recruitment/InformationsControl.vue';
import SelectCity from '../components/SelectCity.vue';
import SearchOccupation from '../components/SearchOccupation.vue';
import _ from 'underscore';
import WorkClassification from 'components/recruitment/WorkClassification.vue';
import ExistingRecruitmentPopin from 'components/recruitment/ExistingRecruitmentPopin.vue';
import accountProvider from 'src/config/AccountProvider';
import type { AxiosError } from 'axios';

const router = useRouter();
const step = ref<number>(0);
const latestStep = ref<number>(0);
const classificationStepIndex = 2;
const account = ref<InitializedAccount>();
const isError = ref<boolean>(false);
const createRecruitmentAnyway = ref<boolean>(false);
const ripple = ref<QBtn>();
const recruitmentError = ref('');
const props = defineProps({
  recruitmentId: {type: Number, required: false},
});
const selectedJobOccupation = ref<ErhgoOccupationSearch>();
const selectedJobLocation = ref<Location>();
const similarRecruitments = ref<SourcingJobAndRecruitment[]>([]);

const {service: {api, isLoading}} = useApi();


const initialized = ref<boolean>(false);

const userFilter = ref<UserFilter>({criteria: [], classifications: []});
const nbUsers = ref<number | null>(null);
const recruitment = ref<SourcingJobAndRecruitment>();

const stepWorkCondition = ref<number>(2);
const stepOrganization = ref<number>(3);
const stepQuestion = ref<number>(4);
const finalStep = ref<number>(5);
const showClassification = ref<boolean>(false);
const showSimilarRecruitmentsPopin = ref<boolean>(false);

const refreshCandidateCountImmediately = async () => {
  const occupationId = selectedJobOccupation.value?.code;
  if (occupationId) {
    nbUsers.value = null;
    nbUsers.value = (await api.countCandidates(
      occupationId,
      (selectedJobLocation.value)?.longitude ?? 0,
      (selectedJobLocation.value)?.latitude ?? 0,
      userFilter.value.typeContractCategory ?? undefined,
      userFilter.value.workingTimeType ?? undefined,
      userFilter.value.criteria,
      userFilter.value.salaryMin ?? undefined,
      userFilter.value.salaryMax ?? undefined,
      userFilter.value.classifications,
    )).data.count;
  }
};

const refreshCandidateCount = _.debounce(async () => refreshCandidateCountImmediately(), 500);

const updateUserFilter = async (f: Partial<UserFilter>) => {
  userFilter.value = {...userFilter.value, ...f};
  await refreshCandidateCount();
};

const updateUserFilterCriteria = async (addedCriteria: string[], removedCriteria: string[]) => {
  const criteria = [...userFilter.value.criteria, ...addedCriteria].filter(c => !removedCriteria.includes(c));
  await updateUserFilter({ criteria });
};

const updateInitialFilter = async () => {
  await updateUserFilter({
    criteria: recruitment.value?.criteriaValues || [],
    workingTimeType: recruitment.value?.workingTimeType || undefined,
    typeContractCategory: recruitment.value?.typeContractCategory || undefined,
    classifications: recruitment.value?.erhgoClassifications?.map(c => c.code) || [],
  });
};

const refreshSelectedOccupationAndLocation = () => {
  const title = recruitment.value?.title ?? '';
  const code = recruitment.value?.occupationId ?? '';
  selectedJobOccupation.value = code ? {title, code} : undefined;
  selectedJobLocation.value = recruitment.value?.location;
  step.value = recruitment.value?.step ?? 0;
  latestStep.value = step.value;
  createRecruitmentAnyway.value = false;
};

const areLocationAndJobSelected = computed(() => !!(selectedJobLocation.value && selectedJobOccupation.value));
const waitingForCountCandidates = computed(() => isLoading.value && nbUsers.value === null);

watch(() => recruitment.value?.erhgoClassifications?.length, (newValue) => {
  if (newValue === 0) {
    showClassification.value = true;
    stepWorkCondition.value = 3;
    stepOrganization.value = 4;
    stepQuestion.value = 5;
    finalStep.value = 6;
  } else {
    showClassification.value = false;
    stepWorkCondition.value = 2;
    stepOrganization.value = 3;
    stepQuestion.value = 4;
    finalStep.value = 5;
  }
});


const effectiveRecruitmentId = computed(() => recruitment.value?.recruitmentId ?? props.recruitmentId ?? account.value?.recruitmentId);

onMounted(async () => {
  account.value = await accountProvider.accountPromise.value;
  if (effectiveRecruitmentId.value) {
    recruitment.value = (await api.getSourcingJobAndRecruitment(effectiveRecruitmentId.value)).data;
    if (recruitment.value.recruitmentState !== RecruitmentState.DRAFT) {
      recruitment.value = undefined;
    }
  }
  refreshSelectedOccupationAndLocation();

  await updateInitialFilter();
  initialized.value = true;
});

interface StepContract {
  submit: (() => Promise<boolean>) | undefined;
}

const loading = ref<boolean>();
// TODO: find a way to have an array of refs named 'stepContent' instead of these ugly declarations
const stepInvitationOrOccupationRef = ref<StepContract>();
const stepContractRef = ref<StepContract>();
const stepClassificationRef = ref<StepContract>();
const stepWorkConditionRef = ref<StepContract>();
const stepOrganizationRef = ref<StepContract>();
const stepQuestionRef = ref<StepContract>();
const stepControlRef = ref<StepContract>();
const sendNotifications = ref(true);

const stepContents = computed(() => [stepInvitationOrOccupationRef.value, stepContractRef.value, stepClassificationRef.value, stepWorkConditionRef.value, stepOrganizationRef.value, stepQuestionRef.value, stepControlRef.value]
    .filter((_, i) => showClassification.value || i !== classificationStepIndex));


const goNextStep = async () => {
  if (effectiveRecruitmentId.value) {
    step.value++;
    await api.updateSourcingStep(effectiveRecruitmentId.value, {step: step.value});
  }
};

const doFinalRedirect = async () => {
  const redirect = {
    name: 'recruitment-detail',
    params: {
      recruitmentId: effectiveRecruitmentId.value,
    },
    query: {showConfirmBanner: 'true'},
  };
  await router.push(redirect);
};

const nextStep = async () => {
  loading.value = true;
  isError.value = false;
  try {
    if (await stepContents.value[step.value]?.submit?.()) {
      if (step.value === finalStep.value) {
        await doFinalRedirect();
      } else {
        await goNextStep();
      }
    }
  } catch (e) {
    console.error(e);
    isError.value = true;
  } finally {
    loading.value = false;
  }
};

const forceCreateRecruitment = async () => {
  createRecruitmentAnyway.value = true;
  await createOrUpdateRecruitment();
};
watchEffect(() => latestStep.value = Math.max(step.value, latestStep.value));
watchEffect(() => {
  if (selectedJobLocation.value && selectedJobOccupation.value) {
    void refreshCandidateCount();
  } else {
    nbUsers.value = null;
  }
});

watch(() => nbUsers.value, () => {
  ripple.value?.$el?.click?.();
});

const createOrUpdateRecruitment = async () => {
  recruitmentError.value = '';
  if (selectedJobLocation.value && selectedJobOccupation.value && selectedJobLocation.value?.longitude && selectedJobLocation.value?.latitude) {
    const erhgoOccupationId = selectedJobOccupation.value?.code;
    const title = selectedJobOccupation.value?.title;
    try {
      if (!createRecruitmentAnyway.value) {
        if (erhgoOccupationId) {
          similarRecruitments.value = (await api.getSimilarRecruitments(erhgoOccupationId, selectedJobLocation.value.longitude, selectedJobLocation.value.latitude))
            .data
            .filter(sr => sr.recruitmentId !== recruitment.value?.recruitmentId);
        } else {
          similarRecruitments.value = [];
        }
        createRecruitmentAnyway.value = !similarRecruitments.value.length;
      }
      if (createRecruitmentAnyway.value) {
        const newRecruitment = !recruitment.value;
        recruitment.value = (await api.createOrUpdateSourcingJobAndRecruitment({
          jobOccupationId: erhgoOccupationId,
          jobLocation: selectedJobLocation.value,
          recruitmentId: recruitment.value?.recruitmentId,
          title: title,
        })).data;
        createRecruitmentAnyway.value = false;
        await goNextStep();
        if (newRecruitment) {
          await router.replace({
            name: 'recruitment',
            params: {
              recruitmentId: recruitment.value?.recruitmentId,
            },
          });
        }
      } else {
        showSimilarRecruitmentsPopin.value = true;
      }
    } catch (e) {
      if ((e as AxiosError)?.response?.status === 400) {
        recruitmentError.value = 'Nous ne connaissons pas suffisamment ce métier : merci d’essayer avec un autre libellé.';
      }
    }
  }
};

</script>
