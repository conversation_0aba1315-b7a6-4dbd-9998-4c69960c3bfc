<template>
  <q-circular-progress v-if="isLoading"/>
  <div v-else>
    <div id="tableHeader">
      <recruitment-detail-header :recruitment="recruitment" :is-loading="isLoading"/>
    </div>
    <candidatures-list :recruitment-id="recruitmentId" :show-confirm-banner="showConfirmBanner"/>
    <q-banner dense class="q-ma-md text-white bg-negative" v-if="isError">
      <q-icon name="fa-light fa-warning q-mr-md" size="md"/>
      Une erreur est survenue - veuillez réessayer ou contacter notre support.
    </q-banner>
  </div>
</template>

<script setup lang="ts">
import { useApi } from 'src/config/api';
import CandidaturesList from 'components/candidature/CandidaturesList.vue';
import RecruitmentDetailHeader from 'components/RecruitmentDetailHeader.vue';

const {service: {api, isError, isLoading}} = useApi();

const props = defineProps({
  recruitmentId: {type: Number, required: true},
  showConfirmBanner: {default: false, required: false},
});

const recruitment = (await api.getSourcingJobAndRecruitment(props.recruitmentId)).data;
// In case of fav / refresh, do NOT keep message
history.replaceState({}, '', window.location.href.replaceAll('showConfirmBanner=true', ''));
</script>
