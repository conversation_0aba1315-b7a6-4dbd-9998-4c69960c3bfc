// https://stackoverflow.com/questions/62883931/how-to-merge-several-classes-in-one
@import 'quasar/src/css/variables.sass';
@import 'quasar/src/css/core/typography.sass';

.green-text {
  color: #007A66;
}

.violet-text {
  color: $erhgo-violet1;
}

.orange-text {
  background-color: #fc811a;
}

.q-field--standard:not(.q-textarea) .q-field__control:before {
  border-bottom: 3px solid #00CFAD !important;
}

.q-field--standard.q-field--highlighted.q-textarea .q-field__control:after {
  display: none !important;
}

input:placeholder-shown {
  font-style: italic;
}

.text-label {
  @extend .text-h6, .text-weight-bold, .green-text;
}

.matching-pattern-container {
  strong {
    @extend .text-weight-bold, .green-text;
  }
}

.no-emphase {
  strong {
    @extend .text-weight-regular;
  }
}

.chimney--small {
  max-width: 400px;
  margin: auto;

}

.chimney--large,
.chimney--fluid {
  margin: auto;
}

@media (min-width: $breakpoint-md-min) {
  .chimney--large {
    width: 46%;
    max-width: 800px;
  }

  .chimney--fluid {
    max-width: 1200px;
  }

  .custom-sticky {
    margin: 0 calc((100vw - 900px) / 2)
  }
}

.title-breaker {
  border-bottom: 4px solid $erhgo-green1;
}

.alert-text {
  background-color: #E5035430;
  color: #A0033D;
}

.valid-text {
  background-color: #00F0C830;
  color: #005748;
}

.user-text {
  background-color: $erhgo-violet1-light;
  color: $erhgo-violet2;
}

.bg-violet {
  background: $erhgo-violet1 !important;
}

.q-banner {
  display: block;
  padding: 0.75em;
  margin: 1em 0;
  border-radius: 4px;
  min-height: initial;
}

.q-banner--dense {
  padding: 0.25em;
  margin: 0.5em 0;
}

.custom-banner__picto {
  display: flex;
}

.q-banner__content {
  line-height: 1.5em;
}

.custom-banner-inline {
  display: inline-block;
  padding: 0.25em 0.5em;
  margin: 0.1em;
  border-radius: 4px;
}

.email-text {
  background-color: rgba(255, 224, 226, 0.96);
  color: rgba(215, 4, 49, 0.96);
}

.bordered {
  border: $secondary 2px solid !important;
  border-radius: 5px !important;
}

.bordered-right {
  border-right: $secondary 2px solid !important;
}


.q-stepper__dot {

  border: $secondary 1px solid;

  span {
    color: black !important;
  }
}

.q-stepper--horizontal .q-stepper__line:before, .q-stepper--horizontal .q-stepper__line:after {
  background-color: $secondary;
}

@media (min-width: $breakpoint-xs-max) {
  .q-stepper__dot {
    font-size: 25px;
    width: 45px;
    min-width: 45px;
    height: 45px;
  }
}

@media (max-width: $breakpoint-xs-max) {
  .q-stepper__dot {
    font-size: 20px;
    width: 30px;
    min-width: 30px;
    height: 30px;
  }
}

.q-btn-toggle .q-btn {
  border: 1px solid $secondary !important;
}

.q-textarea {
  border: 1px solid $blue-grey;
  background-color: #ffffff;
  border-radius: 1rem;
}

.q-field--standard .q-field__control:before {
  border: none !important;
}

.q-textarea .q-textarea .q-field__bottom,
.q-textarea .q-field__bottom--animated {
  transform: translateY(130%);
}

.q-textarea.q-field--focused .q-field__label {
  color: rgba(0, 0, 0, 0.6) !important;
}

.q-textarea .q-field__label {
  padding-bottom: 20px;
}

.text-erhgo-grey {
  color: $erhgo-grey2 !important;
}

.text-erhgo-grey3 {
  color: $erhgo-grey3 !important;
}

.text-erhgo-green2 {
  color: $erhgo-green2 !important;
}

.text-erhgo-violet1 {
  color: $erhgo-violet1 !important;
}

.bg-erhgo-grey {
  background: $erhgo-grey1 !important;
}

.bg-erhgo-green1-light {
  background: $erhgo-green1-light !important;
}

.text-erhgo-blue {
  color: $erhgo-blue !important;
}

.bg-erhgo-blue {
  background: $erhgo-blue !important;
}

.q-btn--rectangle::before {
  border-color: $secondary !important;
  border-width: 2px;
}

.q-item__section {
  overflow-wrap: anywhere;
}

.very-small {
  font-size: 0.7rem;
}

.q-router-link--active {
  color: #005748 !important;
  background-color: #00F0C830;
}

/* ----- Ajouts Margot ----- */

/* --- layout --- */

.custom-page-wrap {
  padding-left: 5%;
  padding-right: 5%;
}

/* menu */

.custom-toolbar {
  align-items: center;
}

.custom-toolbar__title {
  flex-direction: column;
  display: flex;
  align-items: flex-start;
}

/* page candidats */

@media (max-width: $breakpoint-md-max) {
  .q-item__section--side {
    padding-right: 7px !important;
  }

  .q-item__section--main ~ .q-item__section--side {
    padding-left: 7px !important;
  }
}

@media (max-width: $breakpoint-md-max) {
  .custom-filtre-candidates {
    justify-content: space-between;
  }
}

@media (min-width: $breakpoint-md-min) {
  .custom-list-candidates {
    width: 36%;
  }

  .custom-detail-candidates {
    width: 64%;

    .q-item {
      padding-left: 8px;
      padding-right: 8px;
    }
  }
}

@media (max-width: $breakpoint-sm-max) {
  .custom-list-candidates {
    width: 100%;
  }
}

.custom-detail-candidates__content {
  padding: 5%;
}

.custom-detail-candidates__section {
  padding-top: 1em;
  padding-bottom: 1em;
}

.custom-detail-candidates__identity {
  margin: 0;
  line-height: 1.75em;
}

@media (min-width: $breakpoint-md-min) {
  .custom-list-candidates_scroll {
    border-right: 1px solid $erhgo-grey1;
    background-color: $erhgo-grey1;
  }
}

.custom-candidate-item {
  border-top: $erhgo-grey1 1px solid;
}

.custom-candidate-item__section--avatar,
.custom-candidate-item__section--actions {
  width: 6.6em;
}


/* --- éléments de base --- */

/* titres */

.custom-h0, .custom-h1, .custom-h2, .custom-h3, .custom-h4, .custom-h5, .custom-h6 {
  line-height: 1.125em;
  font-weight: bold;
}

.custom-h0 {
  font-size: 2.5rem;
  font-weight: normal;
}

.custom-h1 {
  font-size: 2rem;
}

.custom-h2 {
  font-size: 1.5rem;
}

.custom-h3 {
  font-size: 1.25rem;
}

.custom-h4 {
  font-size: 1.125rem;
  color: $erhgo-green3;
}

.custom-h5 {
  font-size: 1rem;
}

.custom-h-rappel-metier {
  font-size: 1.5rem;
}

/* texte */

.custom-text-md, .custom-text-lg {
  line-height: 1.5em;
}

.custom-text-md {
  font-size: 1.25rem;
}

.custom-text-lg {
  font-size: 1.5rem;
}

.custom-break-here {
  display: inline-block;
}

a.custom-lien {
  color: $erhgo-green3;
  display: inline-block;
  padding: 0 0.15em;
  transition: all 0.2s ease-in-out;
}

a.custom-lien:hover, a.custom-lien:focus {
  color: #000000;
  background-color: #00f0c8;
}

/* --- composants modifiés --- */

/* boutons */

.q-btn {
  line-height: 1.25em;
  letter-spacing: 0.1em;
}

.q-btn .q-icon, .q-btn .q-spinner {
  margin: 0 0.25em 0.125em;
}

.q-btn.bg-secondary:before {
  box-shadow: 0 3px 0 $erhgo-green2 !important;
}

.q-btn.bg-purple {
  color: #ffffff;
}

.q-btn.bg-purple:before {
  box-shadow: 0 3px 0 $erhgo-violet3 !important;
}

.custom-savoir-plus {
  padding: 0.125em 0.25em;
  color: $erhgo-green3;
}

.custom-savoir-plus::before {
  box-shadow: none;
}

/* formulaires */

.q-field__control {
  color: $erhgo-green1;
  margin-top: -1px;
}

.q-field--standard .q-field__control:after {
  height: 4px;
}

/* stepper */

.q-stepper__step-content {
  border: 2px solid #eeeeee;
  border-radius: 4px;
}

/* --- petits composants ajoutés --- */

/* counter digit */

.counter-digit {
  font-size: 4em;
}

.counter-digit__wrap {
  cursor: pointer;
}

@media (min-width: $breakpoint-lg-min) {
  .counter-digit__wrap {
    margin: 0 0.5em;
  }
}

.counter-digit__wrap:first-child {
  margin-left: 0;
}

.counter-digit__wrap.large {
  padding: 0 1em;
}

.counter-digit__wrap.small {
  font-size: 1rem;
  padding: 0.1em 0.5em;
}

.counter-digit__wrap:hover {
  background-color: $erhgo-grey1;
}

.counter-digit__wrap--selected, {
  border: 3px solid $erhgo-grey2;
  border-radius: 4px;

}

.counter-digit__wrap .q-icon {
  margin-left: 0.25em;
}

.counter-digit-small {
  font-size: 1.2em;
}

.counter-digit-very-small {
  font-size: 1em;
}

.counter-digit-type {
  display: block;
  font-size: 1em;
}

/* gros picto décoratif près des titres */

.custom-h1-icon {
  position: absolute;
  font-size: 2em;
  display: block;
}

.custom-h1-icon.search {
  margin-top: -1.25em;
}

.custom-h1-icon.results {
  margin-left: -1.25em;
  margin-top: -0.2em;
}

/* tags compétences/comportements */

.custom-tag-list {
  margin: 0.25em 0;
}

.custom-tag {
  font-size: 0.875em;
  text-transform: initial;
  padding: 0 1em 0.125em;
  margin: 0.25em;
}

.custom-tag:before {
  border-width: 1px;
}

/* footer */

.custom-footer {
  border-top: solid 1px $erhgo-green1;
  color: $erhgo-grey2;
}

/* décompte candidats */


.custom-decompte-candidats {
  width: 7em;
  height: 7em;
  box-sizing: content-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1em;
  background-color: #ffffff;
  border: $erhgo-violet1 1px solid;
  border-radius: 50%;
  color: $erhgo-violet2;
  text-align: center;
  cursor: default;
}

.custom-decompte-candidats > * {
  line-height: 1em;
  margin: 0.125em;
}

/* --- helpers --- */

/* spacing */

.custom-mb-none-first:first-child {
  margin-top: 0 !important;
}

.custom-pb-none-first:first-child {
  padding-top: 0 !important;
}

.custom-mb-none-last:last-child {
  margin-bottom: 0 !important;
}

.custom-pb-none-last:last-child {
  padding-bottom: 0 !important;
}

/* couleurs */

.custom-bd-green1 {
  border: solid 1px $erhgo-green1;
}

.custom-bg-green1 {
  background-color: $erhgo-green1;
}

pre {
  font-family: inherit !important;
}

.color_0 {
  color: #E12C46;

  .q-icon {
    color: #E12C46;
  }
}

.color_1 {
  color: #e50354;

  .q-icon {
    color: #e50354;
  }
}

.color_2 {
  color: #cb037a;

  .q-icon {
    color: #cb037a;
  }
}

.color_3 {
  color: #b002a2;

  .q-icon {
    color: #b002a2;
  }
}

.color_4 {
  color: #9502ca;

  .q-icon {
    color: #9502ca;
  }
}

.color_5 {
  color: #7d03ec;

  .q-icon {
    color: #7d03ec;
  }
}

.color_6 {
  color: #662fe5;

  .q-icon {
    color: #662fe5;
  }
}

.color_7 {
  color: #5157df;

  .q-icon {
    color: #5157df;
  }
}

.color_8 {
  color: #2A74D5;

  .q-icon {
    color: #3C7FD9;
  }
}

.color_9 {
  color: #1C7B9B;

  .q-icon {
    color: #26A8D3;
  }
}

.color_10 {
  color: #0A807E;

  .q-icon {
    color: #11CFCD;
  }
}

.color_11 {
  color: #06846A;

  .q-icon {
    color: #09EFC0;
  }
}

.color_12 {
  color: #0B844B;

  .q-icon {
    color: #35EC97;
  }
}

.color_13 {
  color: #12871E;

  .q-icon {
    color: #61E96E;
  }
}

.color_14 {
  color: #458014;

  .q-icon {
    color: #8EE545;
  }
}

.color_15 {
  color: #687E11;

  .q-icon {
    color: #B9E21D;
  }
}

.color_16 {
  color: #7A7606;

  .q-icon {
    color: #D3CD0E;
  }
}

.color_17 {
  color: #906F13;

  .q-icon {
    color: #D6A51C;
  }
}

.color_18 {
  color: #AD611F;

  .q-icon {
    color: #DA7C2A;
  }
}

.color_19 {
  color: #D34022;

  .q-icon {
    color: #DE5338;
  }
}

.sticky-header-table {

  thead tr th {
    position: sticky;
    z-index: 1;
  }

  thead tr th {
    top: 0
  }

  &.q-table--cell-separator tbody tr:last-child > td {
    border-bottom-width: 1px !important;
  }
}

.outlined-banner {
  border: 1px solid $erhgo-violet1;
  border-radius: 4px;
}

.q-banner__avatar {
  align-self: center !important;
}

.text-wrap {
  text-wrap: wrap;
}

.text-erhgo-grey4 {
  color: $erhgo-grey4 !important;
}

.bg-erhgo-grey4 {
  background: $erhgo-grey4 !important;
}

.text-erhgo-title {
  color: $erhgo-title !important;
}

.text-erhgo-object {
  color: $erhgo-object !important;
}

.bg-erhgo-object {
  background: $erhgo-object !important;
}


