<template>
  <q-card style="width: 100%">
    <q-card-section class="q-pa-none q-my-md" :class="errorMessage || success ? '': 'invisible q-ma-none full-height'">
      <q-banner class="text-center full-height" :class="success ? 'valid-text':'alert-text'">
        <p v-if="success" class="text-body1">
          La personne a été invitée avec succès. Un email lui
          indiquant comment se connecter lui a été envoyé.
        </p>
        <div v-else>
          <q-icon name="fa-light fa-warning q-mr-sm" size="sm"/>
          <span class="text-body1">{{ errorMessage }}</span>
        </div>
      </q-banner>
    </q-card-section>
    <q-card-section class="q-py-none q-ma-xs">
      <div class="text-h5 q-my-md">Inviter une personne</div>
      <div class="text-caption">
        <span class="q-ma-none">La personne que vous invitez :</span>
        <ul class="q-ma-none">
          <li>voit les mêmes offres que vous</li>
          <li>peut créer des offres que vous verrez également</li>
          <li>peut inviter des personnes que vous retrouverez ici</li>
        </ul>
      </div>
    </q-card-section>
    <q-form
      @submit="inviteNewPerson"
      ref="inviteForm"
      class="q-gutter-md"
    >
      <q-card-section class="q-py-none q-my-md">
        <q-input
          v-model="email"
          label="Email"
          :model-value="email"
          @change="success = false"
          @blur="verifyDomain"
          :rules="[_v => isEmailValid || 'Email invalide']"
        />
        <q-input
          v-model="fullname"
          label="Nom complet"
          hint="Nom et prénom"
          @change="success = false"
          :rules="[ _v => isFullNameValid || 'Veuillez renseigner le nom complet']"
        />
      </q-card-section>
      <q-card-actions class="q-my-md" align="around">
        <q-btn rounded
               icon="fa-light fa-xmark"
               class="bg-red text-white"
               @click="closeInvite"
               label="Fermer">
        </q-btn>
        <q-btn rounded
               label="Inviter"
               type="submit"
               icon="fa-light fa-check"
               :loading="isLoading"
               class="bg-green text-white"
        >
          <q-tooltip anchor="bottom middle" self="top middle">
            <pre>En invitant cette personne, celle-ci aura accès aux offres de votre organisation</pre>
          </q-tooltip>
        </q-btn>
      </q-card-actions>
    </q-form>
  </q-card>
</template>
<script setup lang="ts">
import { useApi } from 'src/config/api';
import { computed, ref, watch } from 'vue';
import authentication from 'src/config/authentication';

const organizationCode = authentication.authData?.realm_access?.roles.find(e => e.includes('S-'));
const emailDomainHost = authentication.authData?.email.split('@')[1];
const email = ref<string>('');
const fullname = ref<string>('');
const success = ref<boolean>(false);
const inviteForm = ref<HTMLFormElement | null>(null);

const isEmailValid = computed(() => email.value.includes('@'));
const isFullNameValid = computed(() => fullname.value.length > 0);
const errorMessage = ref<string>();
const forceEmail = ref<boolean>(false);

const handlingApiErrors = (error) => {
  if (error?.response?.status === 409) {
    errorMessage.value = `Cet email est déjà utilisé.`;
  } else if (error?.response?.status === 400) {
    const suggestion = error?.response?.data?.suggestion ? `(Vous vouliez peut-être dire ${error?.response?.data?.suggestion})` : '';
    errorMessage.value = `Cet email semble invalide : merci de le corriger, ou de le confirmer en cliquant sur "inviter" une nouvelle fois. ${suggestion}`;
    forceEmail.value = true;
  } else {
    errorMessage.value = `Une erreur technique est survenue, veuillez vérifier l'email ou contacter le support.`;
    return true;
  }
  return false;
};
const {service: {api, isLoading}} = useApi(handlingApiErrors);

const verifyDomain = () => {
  const emailDomain = email.value.split('@')[1];
  errorMessage.value = isEmailValid.value && emailDomain !== emailDomainHost && !forceEmail.value ?
    `Le nom de domaine de l'email (${emailDomain}) est différent du nom de domaine de votre email (${emailDomainHost}).
    Vous pouvez tout de même l'inviter en cliquant sur "Inviter".` :
    ``;
};

interface Emits {
  (e: 'close'): void,

  (e: 'updateRecruiters'): void,
}

const emit = defineEmits<Emits>();

const closeInvite = () => {
  emit('close');
};

const updateRecruiters = () => {
  emit('updateRecruiters');
};

async function inviteNewPerson() {
  if (organizationCode) {
    await api.inviteAndCreateNewUser({
      email: email.value,
      fullname: fullname.value,
      organizationCode: organizationCode,
      forceEmail: forceEmail.value,
    });
    success.value = true;
    errorMessage.value = ``;
    email.value = '';
    fullname.value = '';
    inviteForm.value?.reset();
    updateRecruiters();
  }
}

watch(
  () => email.value,
  () => {
    forceEmail.value = false;
  },
);
</script>
