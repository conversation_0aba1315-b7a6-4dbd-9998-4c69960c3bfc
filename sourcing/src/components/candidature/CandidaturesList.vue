<template>
  <div id="tableForm">
    <q-banner class="valid-text custom-banner__picto flex-center" v-if="showConfirmBanner">
      <template #avatar>
        <q-icon name="fa-light fa-circle-check" size="3rem" />
      </template>
      <div>
        <h2 class="custom-h4">
          Votre offre a bien été publiée.
        </h2>
        <h2 class="custom-h4" v-if="numberOfGeneratedCandidatures">
          #jenesuisPASunCV a identifié automatiquement {{ $tc("people", numberOfGeneratedCandidatures) }} répondant au
          mieux à votre recherche&nbsp;: vous pouvez les contacter dès maintenant.
        </h2>
      </div>
    </q-banner>

    <div class="row q-mt-sm">
      <div class="col-6 col-md-4">
        <q-input
            v-model="searchQuery"
            placeholder="Rechercher une candidature"
            hint="Recherche par nom, prénom ou email"
            :clearable="!!searchQuery"
            dense
        />
      </div>
      <div class="col-6 q-pl-xl">
        <q-select
            v-model="selectedStates"
            multiple
            dense
            use-chips
            stack-label
            outlined
            :options="selectableStates"
            :clearable="clearableStates"
            @clear="clearStates"
            label="Filtrer par état des candidatures"
            hint="Par défaut, les candidatures écartées sont masquées"
        />
      </div>
    </div>
  </div>

  <q-table
      class="candidatures-table sticky-header-table"
      sticky
      flat
      bordered
      separator="cell"
      :rows="candidatures"
      :columns="columns"
      row-key="candidatureId"
      no-data-label="Aucune candidature trouvée"
      no-results-label="Aucune candidature ne correspond à ce critère de recherche"
      wrap-cells
      @request="fetchData"
      :rows-per-page-options="[ 10, 20, 50, 0 ]"
      v-model:pagination="pagination"
      ref="candidatureTable"
      :style="{ height: `${computedTableHeight}px` }"
  >
    <template v-slot:body-cell="props">
      <q-td :props="props"
            :class="{'violet-text': !props.row.email}"
            class="cursor-pointer"
            @click="goToCandidatureDetail(props.row)"
      >
        {{ props.value }}
      </q-td>
    </template>
    <template v-slot:body-cell-sectors="props">
      <q-td :props="props" v-if="!isRecruitment">
        <sector-chip v-for="sector in props.row.sectors" :key="sector.code" :sector="sector" />
      </q-td>
    </template>
    <template v-slot:body-cell-actions="props">
      <q-td :props="props">
        <q-btn flat size="sm"
               @click="goToCandidatureDetail(props.row)"
               title="Voir le Profil" icon="fa-light fa-eye"
               class="q-px-xs"
        />
        <downloader-btn-quasar
          v-if="props.row.hasSoftSkillPdf"
          flat size="sm"
          extension="pdf"
          :title="`SoftSkills_jenesuisPASunCV_${props.row.firstName??''}_${props.row.lastName??props.row.anonymousCode}`"
          :download="() => softSkillsPDF(props.row)"
          icon="fa-regular fa-heart-pulse"
          label=""
          tooltip="Les soft skills du candidat"
          class="q-px-xs"
        >
        </downloader-btn-quasar>
        <downloader-btn-quasar
          flat size="sm"
          extension="pdf"
          label=""
          :title="!props.row.email ? `competences_${props.row.anonymousCode}` : `competences_${props.row.firstName}_${props.row.lastName }`"
          :title-anonymous="!!props.row.email ? `competences_${props.row.anonymousCode}_anonyme`:undefined"
          :download="(forceAnonymous?: boolean) => userProfileFO(props.row, forceAnonymous)"
          class="q-px-xs"
        >
        </downloader-btn-quasar>
      </q-td>
    </template>
    <template v-slot:body-cell-recommendation="props">
      <q-td :props="props"
            class="cursor-pointer"
            @click="goToCandidatureDetail(props.row)">
        <div v-if="props.row.generated" class="green-text text-caption">Recommandée
          <q-tooltip anchor="top middle" self="top middle">
            #jenesuisPASunCV vous propose une sélection de candidats en pleine recherche d'opportunités
            et qui correspondent à votre offre.
          </q-tooltip>
        </div>
        <div class="green-text text-caption" v-else>
          Sur offre
        </div>
      </q-td>
    </template>
  </q-table>
</template>

<script setup lang="ts">
import type { SourcingCandidatureItem } from 'erhgo-api-client';
import { useI18n } from 'vue-i18n';
import moment from 'moment';
import { useApi } from 'src/config/api';
import { useRouter } from 'vue-router';
import type { Pagination } from 'src/hooks/useCandidatures';
import { CandidaturesService } from 'src/hooks/useCandidatures';
import type { QTableProps } from 'quasar';
import useStickyQTable from 'src/hooks/useStickyQTable';
import { computed } from 'vue';
import DownloaderBtnQuasar from 'components/candidature/DownloaderBtnQuasar.vue';
import SectorChip from 'components/candidature/SectorChip.vue';
import Departements from 'src/models/Departments';

const {t} = useI18n();
const {service: {api}} = useApi();
const isRecruitment = computed(() => !!props.recruitmentId);
const router = useRouter();

const props = defineProps({
  recruitmentId: { type: Number, required: false },
  showConfirmBanner: {default: false, required: false},
});

const columns: QTableProps['columns'] = [
  {
    name: 'submissionDate',
    label: 'Candidature',
    align: 'left',
    field: (row: SourcingCandidatureItem) => row.submissionDate ? moment(row.submissionDate).format('DD/MM/YY') : 'Inconnue',
    sortable: true,
  },
  {
    name: 'lastActionDate',
    align: 'left',
    label: 'Dernier traitement', field: (row: SourcingCandidatureItem) => moment(row.lastActionDate).format('DD/MM/YY'),
    sortable: true,
  },
  {
    name: 'candidature',
    align: 'left',
    label: 'Candidat·e',
    field: (row: SourcingCandidatureItem) => (row.firstName || row.lastName) ? `${row.firstName ?? ''} ${row.lastName ?? ''}` : row.anonymousCode,
    sortable: false,
  },
  {
    name: `${isRecruitment.value ? 'recommendation' : 'alreadyCandidated'}`,
    align: 'left',
    label: `${isRecruitment.value ? 'Type de candidature' : 'A déjà candidaté'}`,
    field: (row: SourcingCandidatureItem) => (row.numberOfOfferCandidatures ? 'Oui' : 'Non'),
    sortable: true,
  },
  {
    name: 'sectors',
    align: 'left',
    label: 'Fonctions',
    field: 'sectors',
    sortable: false,
    classes: isRecruitment.value ? 'hidden' : '',
    headerClasses: isRecruitment.value ? 'hidden' : '',
  },
  {
    name: 'locationIndication',
    align: 'left',
    label: 'Localisation',
    field: (row: SourcingCandidatureItem) => Departements(row.location?.departmentCode),
    sortable: false,
  },
  {
    name: 'lastComment',
    align: 'left',
    label: 'Dernière note',
    field: (row: SourcingCandidatureItem) => row.lastNote,
    classes: 'text-italic last-comment-cell',
    sortable: false,
  },
  {
    name: 'state',
    align: 'left',
    label: 'État',
    field: (row: SourcingCandidatureItem) => t(`candidatureState.${row.state}`),
    sortable: true,
  },
  {name: 'actions', align: 'center', label: 'Actions', field: 'actions'},
];


const {
  updatePagination,
  candidatures,
  pagination,
  searchQuery,
  selectedStates,
  selectableStates,
  clearableStates,
  clearStates,
} = new CandidaturesService(props.recruitmentId);

const userProfileFO = async (row: SourcingCandidatureItem, forceAnonymous?: boolean) => (await (api.getCandidatureUserProfileCompetences(row.candidatureId, forceAnonymous, { responseType: 'blob' }))).data;
const softSkillsPDF = async (row: SourcingCandidatureItem) => (await (api.getCandidatureSoftSkillsPdfResult(row.candidatureId, { responseType: 'blob' }))).data;

const goToCandidatureDetail = async (row: SourcingCandidatureItem) => {
  const redirect = {
    name: 'candidature-detail',
    params: {
      candidatureId: row.candidatureId,
    },
  };
  if (!window.open(router.resolve(redirect).href, 'newwindow', "width=1200,height=800,menubar=no,toolbar=no,locationbar=no,status=no,resizable=yes,scrollbars=yes'")) {
    await router.push(redirect);
  }
};

const fetchData: (props: {
  pagination: Pagination
}) => Promise<void> = async ({ pagination }) => await updatePagination(pagination);

const {tableHeight: computedTableHeight} = useStickyQTable();
const numberOfGeneratedCandidatures = computed(() => candidatures.value?.filter(c => c.generated).length);
document.addEventListener('refreshCandidature', (e: Event) => {
  const detail = (e as CustomEvent).detail;
  const candidatureToUpdateIndex = candidatures.value.findIndex(c => c.candidatureId === detail?.candidatureId);
  if (candidatureToUpdateIndex >= 0) {
    candidatures.value[candidatureToUpdateIndex] = {...detail};
  }
});
await updatePagination();

</script>
<style lang="scss">
.candidatures-table {
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th {
    background-color: $secondary;
  }
}

.last-comment-cell {
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

</style>

