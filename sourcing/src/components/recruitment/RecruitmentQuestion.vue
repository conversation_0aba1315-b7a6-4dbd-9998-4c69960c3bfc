<template>
  <div>
    <h3 class="text-weight-bold q-my-sm">
      Question personnalisée (facultative)
    </h3>
    <br>
    <div class="text-body2 text-italic q-pt-md">
      <p>Pour compléter les informations liées au profil des
        personnes qui auront candidaté, posez une question
        sur un sujet qui vous aiderait à distinguer les
        candidatures.</p>
      <p>Nous vous conseillons de poser une question ouverte, par exemple : "décrivez-nous en quelques mots ce que vous
        avez aimé lors de votre dernière expérience professionnelle" ou correspondant à un impératif du poste "avez-vous
        le permis Poids lourd" ?</p>
    </div>
    <q-input
        v-model="customQuestion"
        type="text"
        label="Ex : Qu'est-ce que vous avez aimé dans votre dernière expérience professionnelle ?"
        class="text-black text-body1"
    />
    <DiscriminatoryCriteria class="q-pt-md "/>
  </div>
</template>

<script setup lang="ts">

import type {PropType} from 'vue';
import { ref} from 'vue';
import {useApi} from 'src/config/api';
import type {SourcingJobAndRecruitment} from 'erhgo-api-client';
import DiscriminatoryCriteria from 'components/DiscriminatoryCriteria.vue';


const props = defineProps({
  recruitment: {type: Object as PropType<SourcingJobAndRecruitment>, required: true},
  recruitmentId: {type: Number, required: true},
});
const customQuestion = ref<string>(props.recruitment.customQuestion || '');
const {service: {isError, api}} = useApi();
const submitted = ref<boolean>(false);

const submit = async () => {
  submitted.value = true;
  const question = customQuestion.value;
  await api.updateSourcingRecruitmentQuestion(props.recruitmentId, {question});
  return !isError.value;
};
defineExpose({submit});
</script>

