<template>
  <div>
    <h1 class="custom-h1 q-my-sm">
      Résumé des informations présentées aux candidats
    </h1>
    <article class="q-py-lg">
    <h2 class="text-label q-mt-lg">Informations générales</h2>
    <div>
      <div class="q-pa-xs"><span class="text-weight-bold">Intitulé&nbsp;:</span> {{ recruitment.title }}</div>
      <div class="q-pa-xs"><span class="text-weight-bold">Localisation&nbsp;:</span> {{ recruitment.location.city }}
        ({{ recruitment.location.postcode }})
      </div>
    </div>
    <h2 class="text-label q-mt-lg">Aspect contractuel</h2>
    <div>
      <div class="q-pa-xs"><span class="text-weight-bold">Contrat de travail : </span>{{
          $t(`contractType.${recruitment.typeContractCategory}`)
        }}
      </div>
      <div class="q-pa-xs"><span class="text-weight-bold">Temps de travail&nbsp;:</span>
        {{ $t(`workingTimeType.${recruitment.workingTimeType}`) }}
      </div>
      <div class="q-pa-xs"><span class="text-weight-bold"> Horaire&nbsp;:</span> {{
          recruitment.workingWeeklyTime
        }}h/semaine
      </div>
      <div class="q-pa-xs"><span class="text-weight-bold">Modulable&nbsp;:</span>
        {{ recruitment.workingTimeType ? 'Oui' : 'non' }}
      </div>
      <div class="q-pa-xs"><span class="text-weight-bold">Salaire annuel brut&nbsp;:</span> Entre
        {{ formatNumber(recruitment.baseSalary) }}&nbsp;€ et
        {{ formatNumber(recruitment.maxSalary) }}&nbsp;€
      </div>
    </div>
    <p class="text-label q-pt-md" v-if="criteriaWithValues.length">Conditions de travail</p>
    <div
      v-for="(criterion, index) in criteriaWithValues"
      :key="index" class="row">
      <div class="q-pa-xs"><span class="text-weight-bold"> {{ criterion.title }}&nbsp;: </span>
        {{
          criterion.criteriaValues.filter(cv => recruitment.criteriaValues?.includes(cv.code)).map(cv => cv.titleForBO).join(', ')
        }}
      </div>
    </div>
    <h2 class="text-label q-mt-lg">Description de l'organisation</h2>
    <div>
      <div class="q-pa-xs"><span class="text-weight-bold">Description&nbsp;: </span>
        <div class="q-pl-md text-italic" v-html="recruitment.organizationDescription"/>
      </div>
      <div class="q-pa-xs"><span class="text-weight-bold">Site web&nbsp;: </span> <a
        :href="recruitment.externalUrl" target="_blank" v-if="recruitment.externalUrl">{{
          recruitment.externalUrl
        }}</a>
        <span class="text-italic" v-else>Non précisé</span>
      </div>
    </div>
    <h2 class="text-label q-mt-lg">Question</h2>
    <div>
      <div class="q-pa-xs"><span class="text-weight-bold">Question posée au candidat&nbsp;:</span>
        <pre class="q-pl-md text-italic">{{ recruitment.customQuestion }}</pre>
      </div>
    </div>
    </article>
    <q-banner class="user-text q-mt-lg">
      <p class="q-mb-xs">
        En passant à l'étape suivante, vous confirmez vouloir inviter {{ numberCandidates }}
        individus à candidater. Les notifications seront envoyées par mail et sur
        l'application mobile dans un délai de 2 jours ouvrés maximum.</p>
      <p class="q-mb-xs">Choisissez "ne pas envoyer de notifications" pour simplement publier
        cette offre, sans envoyer de notifications.</p>

      <div class="column">
        <q-radio color="black"
                 size="sm" v-model="sendNotifications" :val="UsersToNotifySelectionType.ALL">Envoyer des
          notifications
        </q-radio>
        <q-radio color="black" size="sm" v-model="sendNotifications" :val="UsersToNotifySelectionType.NONE">Ne pas
          envoyer
          de
          notifications
        </q-radio>
      </div>
    </q-banner>
  </div>
</template>


<script setup lang="ts">

import { computed, ref, watchEffect } from 'vue';
import type { SourcingJobAndRecruitment} from 'erhgo-api-client';
import { SourcingCriteriaStep, UsersToNotifySelectionType } from 'erhgo-api-client';
import { useCriteriaService } from 'src/models/CriteriaService';
import { useApi } from 'src/config/api';
import accountProvider from 'src/config/AccountProvider';


const props = defineProps({
  recruitmentId: {type: Number, required: true},
  numberCandidates: {type: Number, required: true},
});

const submit = async () => {
  if (recruitment.value.recruitmentId) {
    await api.changeSourcingRecruitmentState(recruitment.value.recruitmentId, {
      nextState: 'PUBLISHED',
      sendNotifications: sendNotifications.value ? UsersToNotifySelectionType.NEW : UsersToNotifySelectionType.NONE,
    });
    await accountProvider.refresh();
    return true;
  }
  return false;
};

const formatNumber = (value: number) => {
  return `${value}`.replace(/(\d)()(?=(\d\d\d)+(\D|$))/g, '$1\u00A0$2');
};

defineExpose({submit});
const {
  fetchCriteria, criteria,
} = useCriteriaService();
const {service: {api}} = useApi();

await fetchCriteria(SourcingCriteriaStep.STEP1);
const recruitment = ref<SourcingJobAndRecruitment>((await api.getSourcingJobAndRecruitment(props.recruitmentId)).data);
const criteriaWithValues = computed(() =>
  criteria.value.filter(c => !!c.criteriaValues.filter(cv => recruitment.value.criteriaValues?.includes(cv.code)).length),
);
const sendNotifications = ref<UsersToNotifySelectionType>(UsersToNotifySelectionType.ALL);

type Emits = (e: 'updateUsersToNotifySelectionType', usersToNotifySelectionType: UsersToNotifySelectionType) => void;

const emit = defineEmits<Emits>();

watchEffect(() => emit('updateUsersToNotifySelectionType', sendNotifications.value));
</script>

