<template>
  <q-dialog v-model="showDialog" :persistent="recruitment.isLoading.value" :maximized="$q.screen.lt.sm">
    <q-card v-if="!isRecruitmentAlreadyClosed" class="text-center q-pa-sm-lg">
      <div class="q-mt-none row items-justify q-mb-none">
        <span class="col q-mt-sm  text-weight-border text-justify text-body1 q-mb-lg">
          Clôturer le recrutement <br>
          <span class="text-blue"> {{
              recruitment.title
            }}</span> </span>
        <q-space/>
        <q-icon name="fa-light fa-close cursor-pointer q-ml-md q-mt-md" size="md" @click="closePopin"/>
      </div>
      <div class="user-text text-justify text-body2 q-mb-md q-pa-md">
        Vous ne pourrez plus re-publier ce recrutement.
        Les candidats seront informés par mail que ce recrutement est cloturé.
      </div>
      <div class="row justify-between q-pt-sm q-col-gutter-x-md">
        <div class="col-4">
          <q-btn
            :loading="recruitment.isLoading"
            outline
            class="full-width bg-secondary q-pa-xs col-6"
            no-caps
            @click="changeState"
          >
            <div class="q-mr-md-lg">
              <q-icon name="fa-light fa-check" size="xs"/>
            </div>
            Confirmer
          </q-btn>
        </div>
        <div class="col-4">
          <q-btn
            class="full-width bg-red-5 q-pa-xs col-6"
            no-caps
            @click="closePopin"
          >
            <div class="q-mr-md-lg">
              <q-icon name="fa-light fa-xmark" size="xs"/>
            </div>
            Annuler
          </q-btn>
        </div>
      </div>
    </q-card>
    <q-card v-else class="text-center q-pa-lg">
      <div class="q-mt-none row items-justify q-mb-none">
        <span class="col q-mt-sm  text-grey text-justify text-subtitle1">
          Votre recrutement pour le poste
          <span class="text-weight-bold"> {{ recruitment.title }}</span> vient de se terminer.
        </span>
        <q-space/>
        <q-icon name="fa fa-times" class="cursor-pointer q-ml-md" size="sm" @click="closePopin"/>
      </div>
      <q-separator class="q-my-sm"/>
      <div class="text-justify text-subtitle2 q-mb-sm q-pa-md">
        <div class="q-mb-md">🤔Nous aimerions savoir comment cela s'est passé et si vous êtes satisfait·e&nbsp;:
        </div>
        <div class="q-mb-md">
          👍&nbsp;Pour nous aider à nous améliorer, <strong>cliquez sur le bouton ci-dessous</strong> et répondez à
          <strong>3
            questions rapides</strong>. D'avance merci&nbsp;!
        </div>
        <q-btn color="secondary" size="sm" class="q-mb-md text-black" @click="respondToQuestion">
          <q-icon name="fa fa-edit" class="q-mr-sm"/>
          Répondre au questionnaire
        </q-btn>
        <div class="q-mb-md">
          📅&nbsp;Vous souhaitez discuter de vive voix ? Prenez rendez-vous avec nous&nbsp;:
        </div>
        <q-btn color="secondary" size="sm" class="q-mb-sm text-black" @click="scheduleAppointment">
          <q-icon name="fa fa-calendar" class="q-mr-sm"/>
          Prendre rendez-vous
        </q-btn>
      </div>
      <q-separator class="q-my-sm"/>
      <div class="flex justify-end">
        <q-btn flat size="sm" icon="fa fa-times" color="negative" class="q-mx-md" @click="closePopin">
          Fermer
        </q-btn>
      </div>
    </q-card>
  </q-dialog>
</template>
<script setup lang="ts">

import { ref, watch, watchEffect } from 'vue';
import { RecruitmentState } from 'erhgo-api-client';

const props = defineProps({
  modelValue: {type: Boolean, required: true},
  recruitment: {type: Object, required: true},
});

const showDialog = ref(props.modelValue);
const isRecruitmentAlreadyClosed = ref<boolean>(false);

const emit = defineEmits<Emits>();
type Emits = (e: 'update:modelValue', input: boolean) => void;
watchEffect(() => {
  showDialog.value = props.modelValue;
});

const changeState = async () => {
  await props.recruitment.changeState(RecruitmentState.CLOSED);
  isRecruitmentAlreadyClosed.value = true;
};
const respondToQuestion = () => {
  closePopin();
  window.open('https://jenesuispasuncv.fr/satisfaction', '_blank');
};

const scheduleAppointment = () => {
  closePopin();
  window.open('https://calendly.com/entreprisejenesuispasuncv', '_blank');
};

const closePopin = () => {
  showDialog.value = false;
  isRecruitmentAlreadyClosed.value = false;
};

watch(() => showDialog.value, newValue => {
  if (props.modelValue !== newValue) {
    emit('update:modelValue', newValue);
  }
});
</script>
