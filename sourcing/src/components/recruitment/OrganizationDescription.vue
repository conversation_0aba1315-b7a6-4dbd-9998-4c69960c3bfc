<template>
  <q-form ref="form" greedy @submit="emit('submit')">
    <h3 class="text-weight-bold q-my-sm">
      Présentation de votre entreprise (obligatoire)
    </h3>
    <div>
      <q-input
          v-model="command.name"
          label="Intitulé de votre entreprise *"
          class="q-mt-md text-black text-body1"
          :rules="[v => !!v || 'Veuillez préciser le nom de votre entreprise']"
      />
      <q-input
          v-model="command.siret"
          label="Code siret de votre entreprise *"
          class="q-mt-sm text-black text-body1"
          maxlength="14"
          :rules="[v => !!v || 'Veuillez préciser le siret de votre organisation']"
      />
      <q-banner dense class="text-negative q-mx-none q-px-none" v-if="hasSiretError">
        <q-icon name="fa-light fa-warning q-mr-xs" size="sm"/>
        Votre siret ne semble pas valide&nbsp;: merci de le corriger ou de contacter notre support.<br/>
        Vous pouvez néanmoins poursuivre.
      </q-banner>
    </div>
    <div>
      <label class="text-weight-bold" for="organizationDescription">
        Présentez votre entreprise en quelques mots&nbsp;*&nbsp;:</label>
      <quill-editor class="q-mt-md text-black text-body1"
                    content-type="html"
                    theme="snow"
                    id="organizationDescription"
                    :content="command.description"
                    ref="organizationDescriptionEditor"
                    @textChange="updateDescription"
      />
      <div class="text-caption text-negative" v-if="submitted && (!command.description || !command.description.trim())">
        Veuillez renseigner la description de votre entreprise.
      </div>
    </div>
    <div class="q-pt-md " v-if="organization.isForcedUrl">
      <label for="externalUrl" class="text-weight-bold text-body1">
        Votre page #jenesuisPASunCV&nbsp;:&nbsp;<a :href="command.externalUrl"
                                                   target="_blank"> {{ command.externalUrl }}</a>
        <q-icon @click="copy(command.externalUrl)" class="fa-light q-ma-sm cursor-pointer"
                v-bind:class="message ? 'fa-check': 'fa-copy'" size="sm">
          <q-tooltip class="bg-secondary text-black shadow-4" :offset="[10, 10]">
            Copier le lien vers votre page
          </q-tooltip>
        </q-icon>
      </label>
      <q-tooltip class="bg-white" v-bind:class="copyUrlError ? 'text-negative' : 'text-green'"
                 v-if="message">
        <q-icon name="fa-light q-mr-xs" v-bind:class="copyUrlError ? 'fa-warning' : 'fa-check'" size="sm"/>
        {{ message }}
      </q-tooltip>
    </div>
    <div class="q-pt-md" v-else>
      <label for="externalUrl" class="text-weight-bold">Adresse de votre site web&nbsp;:</label>
      <q-input v-model="command.externalUrl"
               id="externalUrl"
               placeholder="https://www.mon-entreprise.fr"
               outlined
               dense
               type="url"
      />
    </div>
    <div v-if="recruitmentId">
      <label class="user-text row text-justify q-pa-md q-mt-md ">
        <q-icon name="fa-light fa-circle-info" size="sm" color="secondary"/> &nbsp;
        <span @click="showDialog = true" class="text-weight-bold cursor-pointer">
          Cliquez ici pour modifier le responsable de l'offre ou les personnes à notifier.
        </span>&nbsp;
        Nous envoyons régulièrement une notification par mail, en cas de nouvelle candidature sur ce recrutement.

        Vous pouvez modifier la fréquence d’envoi de ces mails dans votre profil.
      </label>
      <recruiters-notified-dialog
        :recruitmentId="recruitmentId"
        v-model="showDialog"
        :is-loading="isLoading"
      />
    </div>
    <div class="q-mt-md">
      <label for="gdprMention" class="text-weight-bold">Personnaliser la mention RGPD&nbsp;:</label>
      <q-expansion-item
        expand-separator
        label="La mention RGPD"
      >
        <div class="q-mt-md">
          <p>
            En utilisant notre solution vous allez collecter et traiter des données à caractère personnel pour vos
            recrutements. Vous serez alors <strong>responsable de traitement</strong>.</p>
          <p>Renseignez ici un texte qui sera présenté à tout individu avant validation de sa candidature. Ce texte doit
            à
            minima expliquer comment cet individu peut vous contacter pour toute question relative au traitement de ses
            données personnelles et à l’exercice de ses droits sur ses données personnelles.</p>
        </div>

        <q-editor
          v-model="command.gdprMention"
          :dense="editorSettings.denseMode"
          :toolbar="editorSettings.basicToolbar"
          :fonts="editorSettings.fonts"
        />
      </q-expansion-item>
    </div>
    <q-btn type="submit" class="hidden"/>
  </q-form>
</template>

<script setup lang="ts">

import type { PropType} from 'vue';
import { ref } from 'vue';
import { useApi } from 'src/config/api';
import type { SourcingOrganization, UpdateSourcingOrganizationCommand } from 'erhgo-api-client';
import RecruitersNotifiedDialog from 'components/recruitment/UsersNotifiedDialog.vue';
import { useCustomEditorSettings } from 'src/hooks/useCustomEditorSettings';

const form = ref<{
  validate: () => Promise<boolean>
}>();

const editorSettings = useCustomEditorSettings();
const showDialog = ref(false);

type Emits = (e: 'submit') => void;
const emit = defineEmits<Emits>();

const props = defineProps({
  organization: {type: Object as PropType<SourcingOrganization>, required: true},
  recruitmentId: {type: Number, required: false},
});

const command = ref<UpdateSourcingOrganizationCommand>({
  name: props.organization.name === props.organization.siret ? '' : props.organization.name,
  siret: props.organization.siret,
  description: props.organization.description,
  externalUrl: props.organization.externalUrl,
  gdprMention: props.organization.gdprMention,
});
const {service: {isError, api, isLoading}} = useApi();
const hasSiretError = ref<boolean>(!!props.organization.hasSiretError);
const tolerateSiretError = ref<boolean>(hasSiretError.value);
const organizationDescriptionEditor = ref<{
  getHTML: () => string
}>();
const submitted = ref<boolean>(false);
const copyUrlError = ref<boolean>(false);
const message = ref<string>('');

const submit = async () => {
  submitted.value = true;
  if (await form.value?.validate() && command.value.description?.trim()) {
    hasSiretError.value = (await api.updateSourcingOrganization({
      ...command.value,
      siret: command.value.siret?.replaceAll(' ', ''),
      recruitmentId: props.recruitmentId,
    })).data.hasSiretFormatError;
    const result = !isError.value && (!hasSiretError.value || tolerateSiretError.value);
    tolerateSiretError.value = true;
    return result;
  }
};

const copy = (url: string) => {
  navigator.clipboard.writeText(url)
      .then(() => {
        message.value = `Le lien vers votre page a été copiée`;
        copyUrlError.value = false;
      })
      .catch(e => {
        console.error(e);
        message.value = `Désolé, l'url n'a pas pu être copiée`;
        copyUrlError.value = true;
      });
  setTimeout(() => {
    message.value = '';
    copyUrlError.value = false;
  }, 2000);
};

const updateDescription = () => {
  command.value.description = organizationDescriptionEditor.value?.getHTML();
};
defineExpose({submit});

</script>

