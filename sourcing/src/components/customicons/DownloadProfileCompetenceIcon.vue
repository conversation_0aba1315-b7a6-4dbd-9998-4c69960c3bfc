<template>
  <q-icon v-bind="attrs">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512">
      <!--https://fontawesome.com/kits/0c3b7a12d7/icons/light-file-user-circle-arrow-down -->
      <path
        d="M0 64C0 28.7 28.7 0 64 0L220.2 0c12.7 0 24.9 5.1 33.9 14.1L369.9 130c9 9 14.1 21.2 14.1 33.9l0 34.7c-11.2 3.2-21.9 7.4-32 12.6l0-19.2-112 0c-26.5 0-48-21.5-48-48l0-112L64 32C46.3 32 32 46.3 32 64l0 384c0 17.7 14.3 32 32 32l16 0 0-16c0-44.2 35.8-80 80-80l64 0c12 0 23.3 2.6 33.5 7.3c6.6 49.3 33.6 92.2 72.2 119.9c-3.2 .5-6.4 .7-9.8 .7L64 512c-35.3 0-64-28.7-64-64L0 64zM112 464l0 16 160 0 0-16c0-26.5-21.5-48-48-48l-64 0c-26.5 0-48 21.5-48 48zm24.6-208c11.4-19.8 32.6-32 55.4-32s44 12.2 55.4 32s11.4 44.2 0 64s-32.6 32-55.4 32s-44-12.2-55.4-32s-11.4-44.2 0-64zM160 288c0 17.7 14.3 32 32 32s32-14.3 32-32s-14.3-32-32-32s-32 14.3-32 32zM224 32.5L224 144c0 8.8 7.2 16 16 16l111.5 0c-.7-2.8-2.1-5.3-4.2-7.4L231.4 36.7c-2-2.1-4.6-3.5-7.4-4.2zM307.3 296c25.7-44.6 73.3-72 124.7-72s99 27.4 124.7 72s25.7 99.4 0 144S483.4 512 432 512s-99-27.4-124.7-72s-25.7-99.4 0-144zM335 312c-20 34.7-20 77.3 0 112s57 56 97 56s77-21.3 97-56s20-77.3 0-112s-57-56-97-56s-77 21.3-97 56zm29.7 52.7c6.2-6.2 16.4-6.2 22.6 0L416 393.4l0-89.4c0-8.8 7.2-16 16-16s16 7.2 16 16l0 89.4 28.7-28.7c6.2-6.2 16.4-6.2 22.6 0s6.2 16.4 0 22.6c-18.7 18.7-37.3 37.3-56 56c-6.2 6.3-16.4 6.3-22.6 0l-56-56c-6.2-6.2-6.2-16.4 0-22.6z" />
    </svg>
  </q-icon>
</template>
<script setup lang="ts">
import { useAttrs } from 'vue';

const attrs = useAttrs();
</script>
