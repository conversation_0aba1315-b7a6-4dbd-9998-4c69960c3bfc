import {ref} from 'vue';
import type {Criteria, SourcingCriteriaStep} from 'erhgo-api-client';
import { CriteriaQuestionType} from 'erhgo-api-client';
import {useApi} from 'src/config/api';

const criteria = ref<Array<Criteria>>([]);
const thresholdCriteria = ref<Array<Criteria>>([]);
const multipleCriteria = ref<Array<Criteria>>([]);

const {service: {isError, isLoading, api}} = useApi();

const fetchCriteria = async (step: SourcingCriteriaStep) => {
  criteria.value = (await api.getCriteria()).data.map(c => ({
    ...c,
    criteriaValues: c.criteriaValues.filter(cv => cv.sourcingCriteriaStep === step),
  })).filter(cri => cri.criteriaValues.length > 0);
  thresholdCriteria.value = criteria.value.filter(c => c.questionType === CriteriaQuestionType.THRESHOLD);
  multipleCriteria.value = criteria.value.filter(c => c.questionType === CriteriaQuestionType.MULTIPLE);
};

export function useCriteriaService() {
  return {
    isError,
    isLoading,
    fetchCriteria,
    criteria,
    thresholdCriteria,
    multipleCriteria,
  };
}
