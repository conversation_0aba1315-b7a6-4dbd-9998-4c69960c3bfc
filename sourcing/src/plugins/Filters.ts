import { baseUrl } from 'src/config/base-url';

const formatSalary = (value: number) => {
    return value > 1000 ?
      `${Math.floor(value / 1000)}\u00A0k€`.replace(/(\d)()(?=(\d\d\d)+(\D|$))/g, '$1\u00A0$2')
      : `${value}\u00A0€`;
};
export default {
    install(app) {
        app.config.globalProperties.$filters = {
            formatNumber: (value: number) => {
                return `${value}`.replace(/(\d)()(?=(\d\d\d)+(\D|$))/g, '$1\u00A0$2');
            },
            formatSalary,
            formatSalaries: (base?: number, max?: number) => {
                const baseFormatted = base ? formatSalary(base) : undefined;
                const maxFormatted = max ? formatSalary(max) : undefined;
                if (baseFormatted && maxFormatted && base !== max) {
                    return `Entre ${baseFormatted} et ${maxFormatted}`;
                }
                return baseFormatted ?? maxFormatted ?? '';
            },
            announceUrl: (value: number) => {
                return `${baseUrl('fo')}/jobs/R-${value}`;
            },
            formatPhone(value: string) {
                const phone = value?.replace('+33', '0');
                return phone?.length === 10 ? phone.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1-$2-$3-$4-$5') : phone;
            },
        };
    },
};

