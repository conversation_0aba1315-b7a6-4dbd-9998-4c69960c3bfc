import type {
  CommonSourcingRecruitmentInfos,
  ProcessingType} from 'erhgo-api-client';
import {
  RecruitmentState,
  UsersToNotifySelectionType,
} from 'erhgo-api-client';
import { useApi } from 'src/config/api';
import type { Ref, ToRefs, UnwrapNestedRefs } from 'vue';
import { reactive, ref, toRefs } from 'vue';
import moment from 'moment';

export type RecruitmentService<T extends CommonSourcingRecruitmentInfos> = ToRefs<UnwrapNestedRefs<T>> & {
  showRepublishDialog: Ref<boolean>,
  showClosedDialog: Ref<boolean>,
  isLoading: Ref<boolean>,
  newUsersToNotifyCount: Ref<number>,
  oldUsersToNotifyCount: Ref<number>,
  changeState: (nextState: RecruitmentState, sendNotifications?: UsersToNotifySelectionType, newTitle?: string) => Promise<void>,
  republish: () => Promise<void>,
}

const useRecruitment = <T extends CommonSourcingRecruitmentInfos>(recruitmentParam: T): RecruitmentService<T> => {

  const recruitment = reactive(recruitmentParam);
  const showRepublishDialog = ref(false);
  const showClosedDialog = ref(false);
  const {service: {api, isLoading}} = useApi();
  const newUsersToNotifyCount = ref(0);
  const oldUsersToNotifyCount = ref(0);

  const changeState = async (nextState: RecruitmentState, sendNotifications?: UsersToNotifySelectionType, newTitle?: string) => {
    await api.changeSourcingRecruitmentState(recruitment.recruitmentId, { nextState, sendNotifications, newTitle });
    recruitment.recruitmentState = nextState;
    showRepublishDialog.value = false;
    let processingType: ProcessingType | undefined;
    switch (nextState) {
      case 'PUBLISHED':
        processingType = 'REPUBLISH';
        break;
      case 'CLOSED':
        processingType = 'CLOSE';
        break;
      case 'UNPUBLISHED':
      case 'SELECTION':
        processingType = 'SUSPEND';
        break;
      default:
        processingType = undefined;
    }
    recruitment.lastProcessingType = processingType;
    recruitment.lastProcessingDate = new Date();
    if (newTitle) recruitment.title = newTitle;
    if (nextState === 'PUBLISHED') {
      recruitment.publicationDate = new Date();
      recruitment.publicationEndDate = moment().add(28, 'days').toDate();
    }
  };

  const republish = async () => {
    if (recruitment.recruitmentId) {
      const data = (await api.countMatchingUsers(recruitment.recruitmentId)).data;
      newUsersToNotifyCount.value = data.newUsers;
      oldUsersToNotifyCount.value = data.oldUsers;
    }
    if (newUsersToNotifyCount.value + oldUsersToNotifyCount.value > 0) {
      showRepublishDialog.value = true;
    } else {
      await changeState(RecruitmentState.PUBLISHED, UsersToNotifySelectionType.NONE);
    }

  };

  return {
    ...toRefs(recruitment),
    showRepublishDialog,
    showClosedDialog,
    isLoading,
    newUsersToNotifyCount,
    oldUsersToNotifyCount,
    changeState,
    republish,
  };

};
export default useRecruitment;
