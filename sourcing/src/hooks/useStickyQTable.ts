import { onBeforeUnmount, onMounted, ref } from 'vue';

function useStickyQTable() {

  const tableHeight = ref<number>();
  const adjustTableHeight = () => {
    const viewportHeight = window.innerHeight;
    const recruitmentHeaderHeight = document.getElementById('tableHeader')?.offsetHeight ?? 0;
    const recruitmentFormHeight = document.getElementById('tableForm')?.offsetHeight ?? 0;
    const headerHeight = document.getElementsByTagName('header')?.[0]?.offsetHeight ?? 0;
    const footerHeight = document.getElementsByTagName('footer')?.[0]?.offsetHeight ?? 0;

    tableHeight.value = viewportHeight - recruitmentHeaderHeight - recruitmentFormHeight - headerHeight - footerHeight - 20;
  };

  onMounted(() => {
    adjustTableHeight();
    window.addEventListener('resize', adjustTableHeight);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('resize', adjustTableHeight);
  });

  return { tableHeight };
}

export default useStickyQTable;
