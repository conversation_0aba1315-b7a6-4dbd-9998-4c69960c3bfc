apiVersion: v1
kind: Secret
metadata:
  name: {{ include "api.fullname" . }}
  labels:
    {{- include "api.labels" . | nindent 4 }}
type: Opaque
stringData:
  algoliaAdminApiKey: {{ .Values.algolia.adminApiKey | quote }}
  algoliaApiKey: {{ .Values.algolia.apiKey | quote }}
  algoliaApplicationId: {{ .Values.algolia.applicationId | quote }}
  algoliaSearchApiKey: {{ .Values.algolia.searchApiKey | quote }}
  firebaseApiKey: {{ .Values.firebase.apiKey | quote }}
  gmailApiKey: {{ .Values.gmail.apiKey | quote }}
  inseeApiKey: {{ .Values.insee.apiKey | quote }}
  inseeApiKey_v2: {{ .Values.insee.apiKey_v2 | quote }}
  keycloakAdminPassword: {{ .Values.keycloak.adminPassword | quote }}
  keycloakAppleClientId: {{ .Values.keycloak.appleClientId | quote }}
  keycloakAppleTeamId: {{ .Values.keycloak.appleTeamId | quote }}
  keycloakAppleKeyId: {{ .Values.keycloak.appleKeyId | quote }}
  keycloakAppleP8Key: {{ .Values.keycloak.appleP8Key | quote }}
  keycloakBackApiClientSecret: {{ .Values.keycloak.backApiClientSecret | quote }}
  keycloakBackOfficeClientSecret: {{ .Values.keycloak.backOfficeClientSecret | quote }}
  keycloakDbPassword: {{ .Values.keycloak.database.password | quote }}
  keycloakFrontApiClientSecret: {{ .Values.keycloak.frontApiClientSecret | quote }}
  keycloakFrontOfficeClientSecret: {{ .Values.keycloak.frontOfficeClientSecret | quote }}
  keycloakGoogleClientId: {{ .Values.keycloak.googleClientId | quote }}
  keycloakGoogleClientSecret: {{ .Values.keycloak.googleClientSecret | quote }}
  keycloakSecret: {{ .Values.keycloak.secret | quote }}
  openaiKey: {{ .Values.openai.apiKey | quote }}
  poleEmploiClientSecret: {{ .Values.poleEmploi.clientSecret | quote }}
  sendInBlueApiKey: {{ .Values.sendInBlue.apiKey | quote }}
  slackUserNotificationUrl: {{ .Values.slack.userNotificationUrl | quote }}
  slackToken: {{ .Values.slack.token | quote }}
  smtpPassword: {{ .Values.smtp.password | quote }}
  trimojiApiKey: {{ .Values.trimoji.apiKey | quote }}
  firecrawlApiKey: {{ .Values.firecrawl.apiKey | quote }}
