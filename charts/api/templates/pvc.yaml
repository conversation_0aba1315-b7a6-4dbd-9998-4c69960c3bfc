apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "api.fullname" . }}-mysql-dump
spec:
  {{ if .Values.backup.persistence.storageClassName }}
  storageClassName: {{ .Values.backup.persistence.storageClassName | quote }}
  {{- end }}
  accessModes:
    {{- range .Values.backup.persistence.accessModes }}
    - {{ . | quote }}
    {{- end }}
  resources:
    requests:
      storage: {{ .Values.backup.persistence.size | quote }}
