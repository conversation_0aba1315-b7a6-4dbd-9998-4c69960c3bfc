<template>
  <v-tooltip left v-if="romeCodes.length">
    <template v-slot:activator="{ on }">
      <card-btn
        v-if="isTextButton"
        primary
        btn-class="mr-3"
        @click="openPoleEmploiLink()"
      >
        Découvrir la suggestion d’offres d’emploi sur des métiers approchant
      </card-btn>
      <v-img
        v-else
        v-on="on"
        src="./pole-emploi.svg"
        max-height="24"
        max-width="24"
        class="pole-emploi-icon"
        @click="openPoleEmploiLink()"
      />
    </template>
    <span>Découvrir la suggestion d’offres d’emploi sur des métiers approchant</span>
  </v-tooltip>
</template>

<script>
import CardBtn from 'odas-plugins/CardBtn';

const cityCodeHackedFOrCityWithBoroughs = {
  '69123': '69381',
  '75056': '75d',
  '13055': '13201',
};

export default {
  name: 'PoleEmploiLink',
  components: {CardBtn},
  props: {
    romeCodes: {
      type: Array,
      required: true,
    },
    citycode: {
      type: String,
      required: false,
    },
    isTextButton: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    openPoleEmploiLink() {
      let link = `https://candidat.pole-emploi.fr/offres/recherche?offresPartenaires=true&lieux=${this.userLocationCode}&rayon=10&tri=0`;
      if (!!this.romeCodes) {
        link += `&motsCles=${this.romeCodes}`;
      }
      window.open(link, '_blank');
    },
  },
  computed: {
    userLocationCode() {
      return cityCodeHackedFOrCityWithBoroughs[this.citycode] || this.citycode || '01P';
    },
  },
};
</script>

<style scoped>
.pole-emploi-icon {
  cursor: pointer;
}
</style>
