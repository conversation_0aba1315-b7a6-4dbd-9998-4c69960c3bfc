import Vue from 'vue';
import { KeycloakLoginOptions, KeycloakProfile, KeycloakRegisterOptions } from 'keycloak-js';

class KeycloakService {

  private static instance: KeycloakService;

  private _keycloakProfile: KeycloakProfile | null = null;

  login(): void {
    Vue.$keycloak.login();
  }

  logout(url?: string): void {
    Vue.$keycloak.logout({ redirectUri: url });
  }

  get isAuthenticated(): boolean {
    return Vue.$keycloak.authenticated || false;
  }

  async initProfile(): Promise<void> {
    if (this.isAuthenticated) {
      this._keycloakProfile = await Vue.$keycloak.loadUserProfile();
    }
  }

  get email() {
    return this._keycloakProfile?.email || '';
  }

  get firstName() {
    return this._keycloakProfile?.firstName || '';
  }

  get lastName() {
    return this._keycloakProfile?.lastName || '';
  }

  get emailPrefix() {
    return this.email.split('@')[0];
  }

  get fullname() {
    return `${this.firstName} ${this.lastName}`;
  }

  get userId(): string {
    return Vue.$keycloak.subject!;
  }

  get authData() {
    return Vue.$keycloak.tokenParsed!;
  }

  createLoginUrl(options?: KeycloakLoginOptions) {
    return Vue.$keycloak.createLoginUrl(options);
  }

  createRegisterUrl(options?: KeycloakRegisterOptions) {
    return Vue.$keycloak.createRegisterUrl(options);
  }

  static getInstance(): KeycloakService {
    if (!KeycloakService.instance) {
      KeycloakService.instance = new KeycloakService();
    }
    return KeycloakService.instance;
  }
}

export default KeycloakService.getInstance();
