import SafeService from './SafeService';
import Vue from 'vue';
import { ConclusionTextDetail } from 'erhgo-api-client';

export default class ConclusionTextEditService extends SafeService {

  private _conclusionTextDetail: ConclusionTextDetail | null = null;

  constructor(private _organizationCode: string) {
    super();
  }

  async fetchConclusionText() {
    await this.safeCall(async () => {
      this._conclusionTextDetail = (await Vue.$api.getConclusionText(this._organizationCode)).data;
    });
  }

  get conclusionTextDetail(): ConclusionTextDetail | null {
    return this._conclusionTextDetail;
  }

  set conclusionTextDetail(value: ConclusionTextDetail | null) {
    this._conclusionTextDetail = value;
  }
}
