<template>
  <v-btn
    v-bind="{...$attrs}"
    v-on="$listeners"
    @click="processDownload()"
    :loading="loading"
  >
    <slot/>
  </v-btn>
</template>
<script>
export default {
  name: 'downloader-btn',
  props: {
    download: {
      type: Function,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    extension: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
    };
  },
  methods: {
    async processDownload() {
      this.loading = true;
      try {
        const result = await this.download();
        const hiddenElement = document.createElement('a');
        hiddenElement.href = window.URL.createObjectURL(new Blob([result], {type: this.mediaType}));
        hiddenElement.setAttribute('download', this.title);
        hiddenElement.download = this.title;
        document.body.appendChild(hiddenElement);
        hiddenElement.click();
      } finally {
        this.loading = false;
      }
    },
  },
  computed: {
    mediaType() {
      return this.extension === 'csv' ? 'text/csv;charset=utf-8;' : `application/${this.extension}`;
    },
  },
};
</script>
