import Keycloak, {KeycloakConfig, KeycloakOnLoad} from 'keycloak-js';
import Vue from 'vue';
import {logErrorToServer} from './error-handler';
import KeycloakService from './KeycloakService';

let refreshInterval: number;

export async function refreshToken(): Promise<void> {
  const keycloak = Vue.$keycloak;
  try {
    const refreshed = await keycloak.updateToken(70);
    if (process.env.NODE_ENV === 'development') {
      if (refreshed) {
        console.info('Token refreshed' + refreshed);
      } else {
        console.debug('Token not refreshed, valid for ' + Math.round(keycloak.tokenParsed!.exp! + keycloak.timeSkew! - new Date().getTime() / 1000) + ' seconds');
      }
    }
  } catch (e) {
    console.error('Failed to refresh token');

    // !keycloak.token <=> 400 error happened, token is invalid (ie. session has been manually disconnected)
    // Other checks: ensure refresh token is valid - otherwise, re-log
    if (!keycloak?.token || (keycloak?.isTokenExpired() && ((keycloak?.refreshTokenParsed?.exp || 0) * 1000 - Date.now() <= 0))) {
      keycloak.login();
      // Mismatch between number & Interval types
      refreshInterval && clearInterval(refreshInterval);
    }
    // otherwise: probably lost connection - will try later
  }
}

function initRefreshTokenInterval(): void {
  // @ts-ignore
  refreshInterval = setInterval(async () => {
    return refreshToken();
  }, 6000);
}

function buildInitOptions(onLoad: 'login-required' | 'check-sso', withOfflineAccess?: boolean): Keycloak.KeycloakInitOptions {
  let idToken = null, refreshToken = null, token = null;
  const initOptions = {
    onLoad,
    checkLoginIframe: false,
    scope: `openid email profile ${withOfflineAccess ? 'offline_access' : ''}`,
  };
  try {
    const cookieMap = new Map<string, string>();
    document.cookie?.split(';').forEach(c => {
      const newCookieValue = c.split('=');
      cookieMap.set(newCookieValue[0]?.trim(), newCookieValue[1]?.trim());
    });
    idToken = cookieMap.get('idToken');
    refreshToken = cookieMap.get('refreshToken');
    token = cookieMap.get('token');
  } catch (e) {
    // temporary log errors on cookie read
    logErrorToServer(e, Vue.$api, `Got ERROR with cookies:  ${document.cookie}, parsed: idToken=${idToken}, refreshToken=${refreshToken}, token=${token}`);
  }
  return (refreshToken && idToken && token) ? {
    ...initOptions,
    idToken,
    refreshToken,
    token,
  } : initOptions;
}

export function initKeycloak(keycloakConfig: KeycloakConfig, onLoad: KeycloakOnLoad, callBack: () => void, withOfflineAccess?: boolean): void {
  try {
    const initOptions = buildInitOptions(onLoad, withOfflineAccess);
    const keycloak = new Keycloak(keycloakConfig);

    Vue.$keycloak = keycloak;
    Vue.prototype.$keycloak = keycloak;
    keycloak.init(initOptions).then(async (auth) => {
      if (auth) {
        await KeycloakService.initProfile();
        initRefreshTokenInterval();
      }

      callBack();
    }).catch(e => {
      console.error('Authentication Failed');
      logErrorToServer(e, Vue.$api, 'Local error on keycloak init');
    });
  } catch (e) {
    logErrorToServer(e, Vue.$api, 'Global error on keycloak init');
  }
}

