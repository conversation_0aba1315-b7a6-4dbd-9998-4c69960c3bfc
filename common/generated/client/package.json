{"name": "erhgo-api-client", "version": "1.0.0", "description": "OpenAPI client for erhgo-api-client", "author": "OpenAPI-Generator Contributors", "keywords": ["axios", "typescript", "openapi-client", "openapi-generator", "erhgo-api-client"], "license": "Unlicense", "main": "./dist/index.js", "typings": "./dist/index.d.ts", "scripts": {"build": "tsc --outDir dist/", "prepublishOnly": "npm run build"}, "dependencies": {"axios": "^1.3.4"}, "peerDependencies": {}, "devDependencies": {"@types/node": "^18.15.11", "typescript": "~5.1.0"}}