server {
    listen 8080;
    server_name _;

    root /var/www/;
    index index.html;

    include /etc/nginx/snippets/nginx_general_security_headers.conf;

    # Enables response header of "Vary: Accept-Encoding"
    gzip_vary on;

    # Force all paths to load either itself (js files) or go through index.html.
    location / {
        try_files $uri /index.html;
        # Do not cache the index.html
        expires -1;
        add_header Pragma "no-cache";
        add_header Cache-Control "no-store, no-cache, must-revalidate";
        include /etc/nginx/snippets/nginx_general_security_headers.conf; # Repeated, see https://www.peterbe.com/plog/be-very-careful-with-your-add_header-in-nginx
    }

    location /silentSSO.html {
        include /etc/nginx/snippets/nginx_silentSSO_security_headers.conf;
    }


    location ~ \.(js|map|css|svg|woff"woff2|ttf|eot)$ {
        # Assets can be cached indefinitely, their names contains a hash of their contents
        expires 1y;
        add_header Cache-Control "public";
        include /etc/nginx/snippets/nginx_general_security_headers.conf; # Repeated, see https://www.peterbe.com/plog/be-very-careful-with-your-add_header-in-nginx
        access_log off;
    }

}
