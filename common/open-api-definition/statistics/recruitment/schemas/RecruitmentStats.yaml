type: object
properties:
  organizations:
    type: array
    items:
      type: object
      title: RecruitmentStatsItem
      properties:
        organizationCode:
          type: string
        organizationTitle:
          type: string
        lastRecruitmentPublicationDate:
          type: string
          format: date-time
        totalCandidatures:
          type: integer
        totalUsersInChannel:
          type: integer
        totalCandidaturesArchived:
          type: integer
        spontaneousCandidaturesArchived:
          type: integer
        openRecruitmentsCount:
          type: integer
        closedRecruitmentsCount:
          type: integer
        totalTransmittedCandidatures:
          type: integer
        selectedCandidaturesCount:
          type: integer
        contactedCandidaturesCount:
          type: integer
        refusedCandidaturesCount:
          type: integer
        spontaneousCandidaturesCount:
          type: integer
        connectedAts:
          type: string
        projects:
          type: string
