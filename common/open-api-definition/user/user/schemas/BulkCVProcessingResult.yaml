type: object
title: BulkCVProcessingResult
required:
  - totalRows
  - validRows
  - invalidRows
  - emails
properties:
  totalRows:
    type: integer
    description: Total number of rows in CSV
  validRows:
    type: integer
    description: Number of valid rows that will be processed
  invalidRows:
    type: integer
    description: Number of invalid rows that will be skipped
  emails:
    type: array
    items:
      type: string
    description: List of valid emails that will be processed
  invalidEmails:
    type: array
    items:
      type: string
    description: List of invalid emails that will be processed
  message:
    type: string
    description: Status message in French
