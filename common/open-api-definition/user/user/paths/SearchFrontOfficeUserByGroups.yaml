summary: search Front Office User by groups, email or id
operationId: searchFrontOfficeUserByGroups
parameters:
  - $ref: '../../../parameters/query/OrganizationCodesQuery.yaml'
  - $ref: '../../../parameters/query/Page.yaml'
  - $ref: '../../../parameters/query/Size.yaml'
  - in: query
    name: postcode
    schema:
      type: string
  - in: query
    name: search
    schema:
      type: string
  - $ref: '../../../parameters/query/StrictOrganizationFilter.yaml'
responses:
  200:
    description: The user page
    content:
      application/json:
        schema:
          $ref: '../schemas/UserByGroupPage.yaml'
