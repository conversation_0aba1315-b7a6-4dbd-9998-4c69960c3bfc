summary: Create or update user profiles from CV URLs via CSV upload
operationId: createOrUpdateProfilesForCVs
requestBody:
  required: true
  content:
    multipart/form-data:
      schema:
        type: object
        required:
          - csvFile
          - excludeExistingUsers
        properties:
          csvFile:
            type: string
            format: binary
            description: CSV file with columns email, url, and optional third column
          excludeExistingUsers:
            type: boolean
            description: Whether to exclude existing users and users with experiences
responses:
  200:
    description: CSV validation result and processing status
    content:
      application/json:
        schema:
          $ref: '../schemas/BulkCVProcessingResult.yaml'
