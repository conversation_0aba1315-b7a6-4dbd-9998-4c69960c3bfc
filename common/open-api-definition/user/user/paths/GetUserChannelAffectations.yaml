summary: List all channel affectations with prescriber info
operationId: getUserChannelAffectations
parameters:
  - $ref: '../../../parameters/path/UserId.yaml'
responses:
  200:
    description: The details of channel affectation and prescriber info
    content:
      application/json:
        schema:
          type:
            object
          title: ChannelAffectationInformations
          properties:
            prescriber:
              type: object
              title: prescriber
              properties:
                organizationName:
                  type: string
                organizationCode:
                  type: string
                channelSourceType:
                  type: string
            userChannelAffectations:
              type: array
              items:
                $ref: '../schemas/ChannelAffectationItem.yaml'
