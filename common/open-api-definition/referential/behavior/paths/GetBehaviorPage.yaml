summary: Page of behaviors
operationId: behaviorPage
parameters:
  - $ref: '../../../parameters/query/Size.yaml'
  - $ref: '../../../parameters/query/Page.yaml'
  - $ref: '../../../parameters/query/Filter.yaml'
  - $ref: '../../../parameters/query/SortBy.yaml'
  - $ref: '../../../parameters/query/SortDirection.yaml'
responses:
  200:
    description: The behaviors page
    content:
      application/json:
        schema:
          $ref: '../schemas/BehaviorPage.yaml'
