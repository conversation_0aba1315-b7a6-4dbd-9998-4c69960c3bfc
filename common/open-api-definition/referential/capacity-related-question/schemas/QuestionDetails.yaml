type: object
required:
  - id
  - title
  - responses
  - questionType
properties:
  id:
    type: string
    format: uuid
  title:
    type: string
    minLength: 1
  questionType:
    $ref: './QuestionType.yaml'
  responses:
    type: array
    items:
      title: CapacityRelatedQuestionResponse
      type: object
      required:
        - id
        - title
        - capacities
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
          minLength: 1
          maxLength: 255
        capacities:
          type: array
          items:
            $ref: '../../capacity/schemas/CapacityDetail.yaml'
