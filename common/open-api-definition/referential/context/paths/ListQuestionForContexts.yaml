summary: List question for contexts
operationId: listQuestionForContexts
parameters:
  - $ref: '../../../parameters/query/Size.yaml'
  - $ref: '../../../parameters/query/Page.yaml'
  - $ref: '../../../parameters/query/SortBy.yaml'
  - $ref: '../../../parameters/query/SortDirection.yaml'
  - $ref: '../../../parameters/query/Filter.yaml'
  - $ref: '../../../parameters/query/ContextId.yaml'
  - $ref: '../../../parameters/query/CategoryId.yaml'
responses:
  200:
    description: The list of contexts questions
    content:
      application/json:
        schema:
          $ref: '../schemas/QuestionForContextsPage.yaml'
