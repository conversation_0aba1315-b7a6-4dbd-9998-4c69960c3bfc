summary: Add a new context to the referencial
operationId: createNewContext
requestBody:
  required: true
  description: Context data
  content:
    application/json:
      schema:
        $ref: '../schemas/SaveContextCommand.yaml'
responses:
  201:
    description: Context was successfully created
    content:
      application/json:
        schema:
          $ref: '../schemas/Context.yaml'
  400:
    description: Invalid data
  500:
    description: Context creation failed
