allOf:
  - $ref: './CommonSourcingRecruitmentInfos.yaml'
  - type: object
    title: sourcingJobAndRecruitment
    required:
      - organization
    properties:
      description:
        type: string
      typeContractCategory:
        $ref: '../../job/job/schemas/TypeContractCategory.yaml'
      workingTimeType:
        $ref: '../../job/job/schemas/WorkingTime.yaml'
      hideSalary:
        type: boolean
      occupationId:
        type: string
        format: uuid
      criteriaValues:
        type: array
        items:
          type: string
      organization:
        $ref: '../schemas/SourcingOrganization.yaml'
      customQuestion:
        type: string
      step:
        type: integer
      workingWeeklyTime:
        type: integer
      modularWorkingTime:
        type: boolean
      organizationDescription:
        type: string
      externalUrl:
        type: string
      erhgoClassifications:
        type: array
        items:
          $ref: '../../classifications/erhgo/schemas/ErhgoClassification.yaml'
      isForcedUrl:
        type: boolean
      usersToNotify:
        type: array
        items:
          $ref: '../schemas/SourcingUser.yaml'

