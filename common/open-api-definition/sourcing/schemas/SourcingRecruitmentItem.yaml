allOf:
  - $ref: './CommonSourcingRecruitmentInfos.yaml'
  - type: object
    title: sourcingRecruitment
    required:
      - candidaturesCount
      - recruitmentState
      - recruitmentId
      - jobTitle
      - jobId
      - updatedDate
      - baseSalary
      - maxSalary
      - location
      - notifiedUsersCount
      - manager
    properties:
      candidaturesCount:
        title: SourcingCandidatureCountRecruitment
        type: object
        required:
          - toContactCandidatureCount
          - contactedCandidatureCount
          - newCandidatureCount
          - totalCandidatureCount
          - favoriteCandidatureCount
          - dismissCandidatureCount
        properties:
          newCandidatureCount:
            $ref: '../../recruitment/schemas/CandidatureCountItem.yaml'
          totalCandidatureCount:
            $ref: '../../recruitment/schemas/CandidatureCountItem.yaml'
          toContactCandidatureCount:
            type: integer
          contactedCandidatureCount:
            type: integer
          favoriteCandidatureCount:
            type: integer
          dismissCandidatureCount:
            type: integer
      notifiedUsersCount:
        type: integer
