summary: Update candidature state
operationId: updateSourcingCandidatureState
parameters:
  - $ref: '../../parameters/path/CandidatureId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        title: updateSourcingCandidatureStateCommand
        required:
          - nextState
        properties:
          nextState:
            $ref: '../schemas/SourcingCandidatureState.yaml'
responses:
  200:
    description: contact informations, if user is not anonymous anymore and get number of candidatures generated
    content:
      application/json:
        schema:
          title: SourcingContactInformation
          type: object
          properties:
            firstName:
              type: string
            lastName:
              type: string
            phone:
              type: string
            email:
              type: string
            numberOfGeneratedCandidatures:
              type: integer
            location:
              $ref: '../../misc/schemas/Location.yaml'
