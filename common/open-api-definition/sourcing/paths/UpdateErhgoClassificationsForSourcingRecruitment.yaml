summary: Save erhgo classifications for sourcing recruitment
operationId: updateErhgoClassificationsForSourcingRecruitment
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: setRecruitmentErhgoClassificationsCommand
        type: object
        required:
          - recruitmentId
          - erhgoClassificationCodes
        properties:
          recruitmentId:
            type: integer
            format: int64
          erhgoClassificationCodes:
            type: array
            items:
              type: string
responses:
  204:
    description: Recruitment erhgo classifications updated
