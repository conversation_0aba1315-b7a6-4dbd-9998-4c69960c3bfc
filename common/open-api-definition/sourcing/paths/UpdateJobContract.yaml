summary: Update contract aspects of job
operationId: updateSourcingJobContract
parameters:
  - $ref: '../../parameters/path/JobId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        title: updateSourcingJobContractCommand
        required:
          - typeContractCategory
          - workingTimeType
          - workingWeeklyTime
        properties:
          typeContractCategory:
            $ref: ../../job/job/schemas/TypeContractCategory.yaml
          workingTimeType:
            $ref: ../../job/job/schemas/WorkingTime.yaml
          baseSalary:
            type: integer
          maxSalary:
            type: integer
          hideSalary:
            type: boolean
          workingWeeklyTime:
            type: integer
          modularWorkingTime:
            type: boolean
responses:
  204:
    description: Contract of job has been updated
