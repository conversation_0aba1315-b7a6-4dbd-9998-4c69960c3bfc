summary: Change sourcing recruitment state and retrieve automatically generated candidatures, if some
operationId: changeSourcingRecruitmentState
parameters:
  - $ref: '../../parameters/path/RecruitmentId.yaml'
requestBody:
  required: true
  description: Recruitment next state
  content:
    application/json:
      schema:
        type: object
        title: ChangeSourcingRecruitmentStateCommand
        required:
          - nextState
        properties:
          sendNotifications:
            $ref: '../schemas/UsersToNotifySelectionType.yaml'
          nextState:
            $ref: '../../recruitment/schemas/RecruitmentState.yaml'
          newTitle:
            type: string
responses:
  200:
    description: created candidatures id
    content:
      application/json:
        schema:
          type: array
          items:
            type: integer
            format: int64


