summary: add a referential behavior to erhgo occupation
operationId: addBehaviorToOccupation
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: '../schemas/OccupationReferentialEntityEditCommand.yaml'
responses:
  200:
    description: erhgo occupation behaviors categories after update
    content:
      application/json:
        schema:
          $ref: '../schemas/ErhgoOccupationBehaviorsCategories.yaml'
  404:
    description: occupation or behavior not found
