type: object
required:
  - uri
  - title
  - descriptionEN
  - iscoOccupation
properties:
  uri:
    type: string
  title:
    type: string
  descriptionEN:
    type: string
  descriptionFR:
    type: string
  alternativeLabels:
    type: array
    items:
      type: string
  iscoOccupation:
    $ref: './../../isco/schemas/IscoSummary.yaml'
  skills:
    type: array
    items:
      $ref: './Skill.yaml'
