summary: List erhgo classifications
operationId: listErhgoClassifications
parameters:
  - in: query
    name: occupationId
    schema:
      type: string
      format: uuid
    required: false
responses:
  200:
    description: List of all erhgo classifications item
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../schemas/ErhgoClassification.yaml'
