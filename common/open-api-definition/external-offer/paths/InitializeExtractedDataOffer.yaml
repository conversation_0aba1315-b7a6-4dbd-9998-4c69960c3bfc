operationId: initializeAndExtractDataFromExternalOffer
description: Get and build data from an ATS external offer information
parameters:
  - in: query
    name: externalOfferId
    schema:
      type: string
      format: uuid
    required: false
responses:
  200:
    description: Successfully extract offer data from ATS information
    content:
      application/json:
        schema:
          title: OfferData
          type: object
          properties:
            existingRecruitment:
              $ref: '../../sourcing/schemas/SourcingJobAndRecruitment.yaml'
            latestVersion:
              $ref: '../schemas/ExtractedAtsData.yaml'
            previousVersion:
              $ref: '../schemas/ExtractedAtsData.yaml'
