summary: Get organization list of external offers
operationId: getExternalOffers
parameters:
  - $ref: '../../parameters/query/Page.yaml'
  - $ref: '../../parameters/query/Size.yaml'
  - $ref: '../../parameters/query/OrganizationCodeQuery.yaml'
responses:
  200:
    description: The list of external offers
    content:
      application/json:
        schema:
          $ref: '../schemas/ExternalOffersPage.yaml'
