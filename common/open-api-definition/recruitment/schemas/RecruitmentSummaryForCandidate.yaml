type: object
required:
  - id
  - code
  - profileTitle
  - city
  - hasApplied
properties:
  id:
    type: integer
    format: int64
  code:
    type: string
  profileTitle:
    type: string
  city:
    type: string
  hasApplied:
    type: boolean
  isRefused:
    type: boolean
  distance:
    type: number
    format: float
  radiusInKm:
    type: integer
discriminator:
  propertyName: recruitmentType
