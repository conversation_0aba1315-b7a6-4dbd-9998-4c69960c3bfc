summary: Create or update recruitment
operationId: createRecruitment
requestBody:
  required: true
  description: Recruitment data
  content:
    application/json:
      schema:
        $ref: '../schemas/SaveRecruitmentCommand.yaml'
responses:
  201:
    description: Recruitment was successfully saved
    content:
      application/json:
        schema:
          $ref: '../schemas/Recruitment.yaml'
  400:
    description: Invalid data
  500:
    description: Recruitment update failed
