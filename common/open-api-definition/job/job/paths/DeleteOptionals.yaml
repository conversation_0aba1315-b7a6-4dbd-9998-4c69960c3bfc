summary: Consider given optionals as mandatory
operationId: deleteOptionals
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
  - $ref: '../../../parameters/path/ProfileId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: deleteOptionalsCommand
        type: array
        minLength: 1
        items:
          type: object
          title: deleteOptionalsCommandItem
          required:
            - optionalType
            - optionalId
          properties:
            optionalType:
              type: string
              enum:
                - ACTIVITY
                - CONTEXT
            optionalId:
              type: string
              format: uuid
responses:
  204:
    description: Activity or context is not optional anymore
  400:
    description: One or more context or activity was not optional or is not associated to this job
