summary: Get users matching job
operationId: getUsersMatchingJob
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
  - $ref: '../../../parameters/query/CapacityThreshold.yaml'
  - in: query
    name: organizationCodes
    schema:
      type: array
      items:
        type: string
        minLength: 1
    required: false
  - $ref: '../../../parameters/query/MasteryLevelRangeQueryOptional.yaml'
  - $ref: '../../../parameters/query/StrictOrganizationFilter.yaml'
  - $ref: '../../../parameters/query/IsAffectedToNoChannel.yaml'
  - in: query
    name: postcode
    schema:
      type: string
  - in: query
    name: criteriaCodes
    schema:
      type: array
      items:
        type: string
  - $ref: '../../../parameters/query/Page.yaml'
  - $ref: '../../../parameters/query/Size.yaml'

responses:
  200:
    description: List of users
    content:
      application/json:
        schema:
          $ref: '../../../user/user/schemas/UserMatchingJobPage.yaml'
  404:
    description: Unknown jobId
