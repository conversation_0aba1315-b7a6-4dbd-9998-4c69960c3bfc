summary: Get all candidates for job
operationId: getJobCandidates
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
responses:
  200:
    description: User summary of candidates
    content:
      application/json:
        schema:
          type: array
          items:
            title: alreadyAppliedUser
            type: object
            properties:
              channels:
                type: array
                items:
                  type: string
              firstName:
                type: string
              lastName:
                type: string
              location:
                $ref: '../../../misc/schemas/Location.yaml'
              candidatureId:
                type: integer
                format: int64
              refusedCandidature:
                type: boolean
              candidatureState:
                $ref: '../../../candidature/job/schemas/GlobalCandidatureState.yaml'
  404:
    description: Unknown jobId
  500:
    description: Job retrieval failed
