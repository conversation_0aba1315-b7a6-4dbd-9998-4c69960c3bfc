summary: Mark given activities and contexts as optional for given profile
operationId: addOptionals
parameters:
  - $ref: '../../../parameters/path/JobId.yaml'
  - $ref: '../../../parameters/path/ProfileId.yaml'
requestBody:
  required: true
  content:
    application/json:
      schema:
        title: saveOptionalsCommand
        type: array
        minLength: 1
        items:
          type: object
          title: addOptionalsCommandItem
          required:
            - optionalType
            - optionalId
            - acquisitionModality
          properties:
            optionalType:
              type: string
              enum:
                - ACTIVITY
                - CONTEXT
            optionalId:
              type: string
              format: uuid
            acquisitionModality:
              $ref: '../../../referential/activity/schemas/AcquisitionModality.yaml'
responses:
  204:
    description: Activities and contexts are optional
  400:
    description: One or more context or activity is not associated to this job
