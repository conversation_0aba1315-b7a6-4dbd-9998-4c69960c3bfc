type: object
required:
  - id
properties:
  id:
    type: string
    format: uuid
  recruiterCode:
    type: string
  employerCode:
    type: string
  title:
    type: string
  description:
    type: string
    maxLength: 2000
  service:
    type: string
  location:
    $ref: '../../../misc/schemas/Location.yaml'
  createdBy:
    type: string
  observators:
    type: array
    items:
      type: string
  observationDate:
    type: string
    format: date-time
  publicationDate:
    type: string
    format: date-time
  state:
    type: string
  behaviors:
    type: array
    items:
      $ref: '../../../referential/behavior/schemas/Behavior.yaml'
  recommendation:
    type: string
  modifiable:
    type: boolean
  erhgoOccupation:
    $ref: '../../../classifications/erhgo-occupation/schemas/ErhgoOccupationOTSummary.yaml'
  level:
    $ref: '../../../classifications/erhgo-occupation/schemas/MasteryLevel.yaml'
  recruitmentProfileCount:
    type: integer
    format: int64
  jobType:
    $ref: './JobType.yaml'
  criteriaValues:
    type: array
    items:
      $ref: '../../../criteria/schemas/CriteriaValue.yaml'
  missions:
    type: array
    items:
      $ref: '../../mission/schemas/MissionDetail.yaml'
  isPrivate:
    type: boolean
