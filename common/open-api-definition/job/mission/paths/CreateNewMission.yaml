summary: Add a new mission to a job
operationId: createNewMission
requestBody:
  required: true
  description: Mission data
  content:
    application/json:
      schema:
        $ref: '../schemas/CreateMissionCommand.yaml'
responses:
  201:
    description: Mission was successfully created
    content:
      application/json:
        schema:
          $ref: '../schemas/Mission.yaml'
  400:
    description: Invalid data
  500:
    description: Mission creation failed
