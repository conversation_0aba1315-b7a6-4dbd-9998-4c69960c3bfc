summary: Update an existing mission
operationId: updateMission
parameters:
  - $ref: '../../../parameters/path/MissionId.yaml'
requestBody:
  required: true
  description: Mission data
  content:
    application/json:
      schema:
        $ref: '../schemas/UpdateMissionCommand.yaml'
responses:
  201:
    description: Mission was successfully updated
    content:
      application/json:
        schema:
          $ref: '../schemas/Mission.yaml'
  400:
    description: Invalid data, including wrong job id
  500:
    description: Mission update failed
