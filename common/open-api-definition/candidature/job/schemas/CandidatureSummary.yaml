type: object
required:
  - id
  - userId
  - code
  - recruitment
properties:
  id:
    type: integer
    format: int64
  userId:
    type: string
  code:
    type: string
  firstName:
    type: string
  lastName:
    type: string
  phoneNumber:
    type: string
  email:
    type: string
  effort:
    type: integer
  contactTime:
    $ref: '../../../user/user/schemas/ContactTime.yaml'
  submissionDate:
    type: string
    format: date-time
  candidatureRecruitmentState:
    type: string
    enum:
      - NEW
      - WAITING
      - SELECTED
  experiences:
    type: array
    items:
      $ref: '../../../user/experience/schemas/ExperienceSummary.yaml'
  recruitment:
    required:
      - id
      - jobId
      - title
      - profileTitle
      - jobTitle
    type: object
    properties:
      id:
        type: integer
        format: int64
      jobId:
        type: string
        format: uuid
      title:
        type: string
      profileTitle:
        type: string
      jobTitle:
        type: string
      customQuestion:
        type: string
  notes:
    type: array
    items:
      $ref: './CandidatureNote.yaml'
  customAnswer:
    type: string
  contextsPositioning:
    type: array
    items:
      $ref: './ContextPositioning.yaml'
  refusalDate:
    type: string
    format: date-time
  archived:
    type: boolean
  candidatureState:
    $ref: '../../../candidature/job/schemas/GlobalCandidatureState.yaml'
