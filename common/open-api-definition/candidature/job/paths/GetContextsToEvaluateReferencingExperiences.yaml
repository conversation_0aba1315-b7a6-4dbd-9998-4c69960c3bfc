summary: Get contexts candidate must evaluate
operationId: getContextsToEvaluateReferencingExperiences
parameters:
  - $ref: '../../../parameters/path/CandidatureId.yaml'
responses:
  200:
    description: A list of mandatory contexts to evaluate
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: '../schemas/ContextToEvaluateReferencingExperiences.yaml'