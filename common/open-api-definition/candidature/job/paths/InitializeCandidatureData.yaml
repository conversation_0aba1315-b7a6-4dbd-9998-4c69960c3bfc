summary: Initialize candidature data by getting all necessary recruitment and user informations
operationId: initializeCandidatureData
parameters:
  - $ref: '../../../parameters/path/RecruitmentId.yaml'
responses:
  200:
    description: Data about recruitment and user needed for candidature
    content:
      application/json:
        schema:
          $ref: '../schemas/CandidatureInitializationData.yaml'
