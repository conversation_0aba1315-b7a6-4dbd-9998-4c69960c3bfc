type: object
properties:
  id:
    type: integer
    format: int64
  submissionDate:
    type: string
    format: date-time
  refusalDate:
    type: string
    format: date-time
  state:
    $ref: '../../../user/user/schemas/UserCandidatureState.yaml'
  jobTitle:
    type: string
  typeContract:
    $ref: '../../../recruitment/schemas/TypeContract.yaml'
  city:
    type: string
  baseSalary:
    type: integer
  maxSalary:
    type: integer
  recruitmentCode:
    type: string
  organizationName:
    type: string
