module.exports = {
  root: true,
  env: {
    node: true,
  },
  parser: 'vue-eslint-parser',
  plugins: [
    '@typescript-eslint',
    'vuetify',
  ],
  extends: [
    'plugin:vue/essential',
    'plugin:@typescript-eslint/recommended',
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'eol-last': [
      'error',
      'always',
    ],
    'semi': [
      'error',
      'always',
    ],
    'comma-dangle': [
      'error',
      'always-multiline',
    ],
    'quotes': [
      'error',
      'single',
      {
        'avoidEscape': true,
        'allowTemplateLiterals': true,
      },
    ],
    'no-eval': 'error',
    'max-len': 'off',
    'no-underscore-dangle': 'off',
    'no-param-reassign': ['error', {
      props: false,
    }],
    'vue/no-mutating-props': 'off',
    'vue/multi-word-component-names': 'off',
    'vue/valid-v-slot': 'off',
    'vue/html-self-closing': ['error', {
      html: {
        void: 'always',
        normal: 'always',
        component: 'always',
      },
      svg: 'always',
      math: 'always',
    }],
    'no-unused-vars': 'off', // duplicate of @typescript-eslint/no-unused-vars
    'indent': 'off', // duplicate of @typescript-eslint/indent
    'no-undef': 'error',
    '@typescript-eslint/ban-ts-comment': 'off',
    '@typescript-eslint/indent': ['error', 2],
    '@typescript-eslint/camelcase': 'off',
    '@typescript-eslint/explicit-member-accessibility': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-non-null-assertion': 'off',
    '@typescript-eslint/no-parameter-properties': 'off',
    'vuetify/no-deprecated-classes': 'error',
    'vuetify/grid-unknown-attributes': 'error',

    // 'vuetify/no-legacy-grid': 'error', // FIXME
  },
  parserOptions: {
    parser: '@typescript-eslint/parser',
  },
  'overrides': [
    {
      'files': [
        '**/*.spec.js',
        '**/*.spec.jsx',
      ],
      'env': {
        'jest': true,
      },
    },
  ],
};
