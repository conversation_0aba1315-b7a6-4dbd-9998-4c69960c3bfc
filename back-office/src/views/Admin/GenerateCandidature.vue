<template>
  <v-col class="align justify">
    <v-row>
      <v-btn :loading="generateInProgress" @click="generate" id="generate">Générer les candidatures</v-btn>
    </v-row>
    <v-row>
      <v-alert type="error" :value="!!error">{{ error }}</v-alert>
    </v-row>
  </v-col>
</template>

<script>
export default {
  name: 'GenerateCandidature',
  data() {
    return {
      generateInProgress: false,
      error: null,
    };
  },
  methods: {
    async generate() {
      this.generateInProgress = true;
      this.error = null;

      try {
        await this.$axios.post(`/api/odas/recruitment/${this.$route.params.recruitment_id}/generate`, {});
      } catch (e) {
        this.error = e;
        this.logError(e);
      } finally {
        this.generateInProgress = false;
      }
    },
  },
};
</script>

<style scoped>

</style>
