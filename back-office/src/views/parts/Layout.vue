<template>
  <v-app>
    <!-- SIDEBAR -->
    <v-navigation-drawer persistent
                         :mini-variant="miniVariant"
                         :clipped="clipped"
                         v-model="drawer"
                         enable-resize-watcher
                         fixed
                         app>
      <v-list expand
              class="pt-3"
              three-line>
        <v-list-item>
          <v-list-item-content>
            <v-autocomplete v-model="organizationModel"
                            :items="organizationItemList"
                            :loading="isLoading"
                            :label="$t('form.organization.search')"
                            append-icon="mdi-magnify"
                            deletable-chips
                            chips
                            small-chips
                            hide-details
                            hide-no-data
                            hide-selected
                            filled
                            light
                            dense
                            single-line
                            :item-text="o => `${o.title}${o.organizationType === 'SOURCING' ? ' (sourcing)' : ''}`"
                            item-value="title"
                            return-object
                            ref="organization_autocomplete"
                            class="pa-1"/>
            <v-checkbox v-model="includesSourcing"
                        dense
                        label="Inclure orga. sourcing"
                        class="ma-0 pa-0 pl-5 very-small"
                        hide-details />
          </v-list-item-content>
        </v-list-item>
      </v-list>

      <v-list expand
              dense
              v-if="organizationModel">
        <v-list-item link :to="{
                       name: 'organizations_jobs_repository',
                       params: {organization_code: organizationModel.code},
                     }"
                     id="jobListLink"
        >
          <v-list-item-action>
            <v-icon>mdi-office-building</v-icon>
          </v-list-item-action>
          <v-list-item-title>
            {{ $t('menu.jobs') }}
          </v-list-item-title>
        </v-list-item>
        <v-list-item link :to="{
                       name: 'recruitments_list',
                       params: {organization_code: organizationModel.code},
                      }">
          <v-list-item-action>
            <v-icon>mdi-receipt</v-icon>
          </v-list-item-action>
          <v-list-item-title>
            Suivi des recrutements
          </v-list-item-title>
        </v-list-item>
        <v-list-item link :to="{
                       name: 'front_user_list_by_organization',
                       params: {organization_code: organizationModel.code},
                      }">
          <v-list-item-action>
            <v-icon>mdi-account-multiple</v-icon>
          </v-list-item-action>
          <v-list-item-title>
            {{ $t('menu.users_repository') }}
          </v-list-item-title>
        </v-list-item>
        <v-list-item
          link
          :to="{ name: 'organization_user_index',
              params: {organization_code: organizationModel.code}}">
          <v-list-item-action>
            <v-icon>recent_actors</v-icon>
          </v-list-item-action>
          <v-list-item-title>{{ $t('menu.users_index') }}</v-list-item-title>
        </v-list-item>
        <v-list-group color="secondary" :value="true" v-if="!isEnterprise">
          <template v-slot:activator>
            <v-list-item-content>
              <v-list-item-title>
                <strong>Employeurs</strong>
              </v-list-item-title>
            </v-list-item-content>
          </template>

          <v-list-item link :to="{
              name: 'employer_repository',
              params: {organization_code: organizationModel.code},
            }">
            <v-list-item-action>
              <v-icon>mdi-store</v-icon>
            </v-list-item-action>
            <v-list-item-title>Gestion des employeurs</v-list-item-title>
          </v-list-item>

        </v-list-group>

        <v-list-group color="secondary" :value="true">
          <template v-slot:activator>
            <v-list-item-content>
              <v-list-item-title>
                <strong>Référentiel</strong>
              </v-list-item-title>
            </v-list-item-content>
          </template>

          <v-list-item link :to="{
                       name: 'organizations_jobs_list',
                       params: {organization_code: organizationModel.code},
                     }">
            <v-list-item-action>
              <v-icon>mdi-web</v-icon>
            </v-list-item-action>
            <v-list-item-title>
              Nos Métiers
            </v-list-item-title>
          </v-list-item>


        </v-list-group>

        <v-list-group color="secondary" :value="true">
          <template v-slot:activator>
            <v-list-item-content>
              <v-list-item-title>
                <strong>Administration</strong>
              </v-list-item-title>
            </v-list-item-content>
          </template>

          <v-list-item link :to="{
                       name: 'organizations_employees_repository',
                       params: {organization_code: organizationModel.code},
                     }">
            <v-list-item-action>
              <v-icon>person_add</v-icon>
            </v-list-item-action>
            <v-list-item-title>
              {{ $t('menu.employee_repository') }}
            </v-list-item-title>
          </v-list-item>

        </v-list-group>

      </v-list>
      <v-list expand
              dense
              nav
              v-else>
        <v-list-item link :to="{ name: 'home' }">
          <v-list-item-action>
            <v-icon>home</v-icon>
          </v-list-item-action>

          <v-list-item-title>
            {{ $t('ref.dashboard.title') }}
          </v-list-item-title>
        </v-list-item>

        <v-list-group color="secondary" :value="true">
          <template v-slot:activator>
            <v-list-item-content>
              <v-list-item-title>
                <b>{{ $t('menu.setup') }}</b>
              </v-list-item-title>
            </v-list-item-content>
          </template>

          <v-list-item link :to="{ name: 'organizations_repository'}">
            <v-list-item-action>
              <v-icon>mdi-store</v-icon>
            </v-list-item-action>
            <v-list-item-title>{{ $t('menu.organizations') }}</v-list-item-title>
          </v-list-item>
          <v-list-item link :to="{ name: 'invitation_code_repository'}">
            <v-list-item-action>
              <v-icon>mdi-store</v-icon>
            </v-list-item-action>
            <v-list-item-title>{{ $t('menu.invitation_code') }}</v-list-item-title>
          </v-list-item>
        </v-list-group>

        <v-list-group color="secondary" :value="true">
          <template v-slot:activator>
            <v-list-item-content>
              <v-list-item-title>
                <b>{{ $t('menu.list') }}</b>
              </v-list-item-title>
            </v-list-item-content>
          </template>

          <v-list-item link
                       :to="{
                        name: 'admin_recruitments_list',
                        query: {
                          internal: false,
                          openOnly: true
                        }
          }">
            <v-list-item-action>
              <v-icon>mdi-store</v-icon>
            </v-list-item-action>
            <v-list-item-title>{{ $t('menu.recruitment_list') }}</v-list-item-title>
          </v-list-item>
        </v-list-group>

        <v-list-group color="secondary" :value="true">
          <template v-slot:activator>
            <v-list-item-content>
              <v-list-item-title>
                <b>{{ $t('menu.repositories') }}</b>
              </v-list-item-title>
            </v-list-item-content>
          </template>
          <v-list-item link v-for="(item, i) in  menu.repositories"
                       :key="i"
                       exact
                       :to="{ name: item.component, params: item.params}">
            <v-list-item-action>
              <v-icon>{{ item.icon }}</v-icon>
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>{{ item.title }}</v-list-item-title>
              <v-list-item-subtitle>{{ item.subtitle }}</v-list-item-subtitle>
            </v-list-item-content>
          </v-list-item>
        </v-list-group>

        <v-list-group color="secondary" :value="true">
          <template v-slot:activator>
            <v-list-item-content>
              <v-list-item-title>
                <b>{{ $t('menu.administrator') }}</b>
              </v-list-item-title>
            </v-list-item-content>
          </template>
          <v-list-item link :to="{ name: 'front_user_list' }">
            <v-list-item-action>
              <v-icon>recent_actors</v-icon>
            </v-list-item-action>
            <v-list-item-title>{{ $t('menu.users_repository') }}</v-list-item-title>
          </v-list-item>
          <v-list-item link :to="{ name: 'user-index' }">
            <v-list-item-action>
              <v-icon>recent_actors</v-icon>
            </v-list-item-action>
            <v-list-item-title>{{ $t('menu.users_index') }}</v-list-item-title>
          </v-list-item>
          <v-list-item link :to="{ name: 'sourcing_simulated_result' }">
            <v-list-item-action>
              <v-icon>mdi-account-search</v-icon>
            </v-list-item-action>
            <v-list-item-title>{{ $t('menu.sourcing_simulated_result') }}</v-list-item-title>
          </v-list-item>
          <v-list-item link :to="{ name: 'administrators_repository' }">
            <v-list-item-action>
              <v-icon>mdi-account-multiple</v-icon>
            </v-list-item-action>
            <v-list-item-title>{{ $t('menu.administrator_repository') }}</v-list-item-title>
          </v-list-item>

        </v-list-group>
      </v-list>
    </v-navigation-drawer>

    <!-- TOOLBAR -->
    <v-app-bar color="black"
               dark
               app
               clipped-left>
      <v-app-bar-nav-icon @click.stop="drawer = !drawer" class="black"/>
      <v-btn icon
             @click.stop="miniVariant = !miniVariant" class="black">
        <v-icon>{{ miniVariant ? 'chevron_right' : 'chevron_left' }}</v-icon>
      </v-btn>
      <v-btn icon
             @click.stop="clipped = !clipped" class="black">
        <v-icon>web</v-icon>
      </v-btn>
      <v-toolbar-title>#jenesuisPASunCV</v-toolbar-title>
      <v-spacer/>
      <v-toolbar-title v-if="organizationModel">
        {{ `${$t("form.organization.selected")} : ` }}
        <b>{{ organizationModel.title }}</b>
      </v-toolbar-title>

      <v-spacer/>

      <v-menu offset-x
              offset-y
              transition="slide-y-transition"
              :close-on-content-click="false"
              :nudge-width="200">
        <template v-slot:activator="{ on }">
          <v-btn v-on="on"
                 icon
                 large class="black">
            <v-avatar size="32px">
              <v-icon>mdi-face-profile</v-icon>
            </v-avatar>
          </v-btn>
        </template>

        <v-card>
          <v-list>
            <v-list-item link avatar>
              <v-list-item-content>
                <v-list-item-title>{{ authData.family_name }}</v-list-item-title>
                <v-list-item-subtitle>{{ authData.given_name }}</v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
          </v-list>

          <v-divider/>

          <v-list>
            <v-list-item link @click="profile">
              <v-list-item-action>
                <v-icon>mdi-face-profile</v-icon>
              </v-list-item-action>
              <v-list-item-title>
                {{ $t('menu.profile') }}
              </v-list-item-title>
            </v-list-item>
            <v-list-item link @click="logout">
              <v-list-item-action>
                <v-icon>mdi-logout</v-icon>
              </v-list-item-action>
              <v-list-item-title>
                {{ $t('menu.logout') }}
              </v-list-item-title>
            </v-list-item>
          </v-list>
        </v-card>
      </v-menu>
    </v-app-bar>

    <!-- CONTENT -->
    <v-main>
      <v-row no-gutters>
        <v-col cols="12">
          <router-view :key="$route.path"/>
        </v-col>
      </v-row>
    </v-main>

    <!-- FOOTER -->
    <v-footer :fixed="fixed"
              app>
      <span>&copy; Le travail réel 2019</span>
    </v-footer>
  </v-app>
</template>

<script>
import appStore from '@/store';
import keycloakService from 'odas-plugins/KeycloakService';
import { OrganizationType } from 'erhgo-api-client';

export default {
  name: 'appLayout',
  components: {},
  data() {
    return {
      authData: {
        email: '',
        family_name: '',
        given_name: '',
      },
      clipped: true,
      drawer: true,
      fixed: false,
      miniVariant: false,
      includesSourcing: true,
      menu: {
        repositories: [
          {
            icon: 'mdi-briefcase-outline',
            title: this.$t('menu.activity'),
            subtitle: 'Administration',
            component: 'activities_repository',
            params: {
              type: 'JOB',
            },
          }, {
            icon: 'warning',
            title: this.$t('menu.activity'),
            subtitle: 'Fusion',
            component: 'activities_merge',
          },
          {
            icon: 'mdi-briefcase-plus-outline',
            title: this.$t('menu.capacity_related_question'),
            subtitle: 'Administration',
            component: 'list_capacity_related_question',
          },
          {
            icon: 'mdi-certificate',
            title: this.$t('menu.capacity'),
            component: 'capacities_repository',
          },
          {
            icon: 'mdi-star-outline',
            title: this.$t('menu.category'),
            component: 'categories_repository',
          },
          {
            icon: 'mdi-factory',
            title: this.$t('menu.context'),
            subtitle: 'Administration',
            component: 'contexts_repository',
          },
          {
            icon: 'question_answer',
            title: this.$t('menu.context'),
            subtitle: 'Questions',
            component: 'questions_for_contexts_repository',
          },
          {
            icon: 'mdi-human-greeting',
            title: this.$t('menu.behavior'),
            subtitle: 'Administration',
            component: 'behaviors_repository',
          },
          {
            icon: 'create',
            title: this.$t('menu.criterias'),
            subtitle: 'Administration',
            component: 'criterias',
          },
          {
            icon: 'mdi-web',
            title: this.$t('menu.erhgoOccupations'),
            subtitle: 'Liste',
            component: 'erhgo_occupation_repository',
          },
          {
            icon: 'merge_type',
            title: this.$t('menu.erhgoOccupations'),
            subtitle: 'Fusion',
            component: 'merge_erhgo_occupations',
          },
          {
            icon: 'mdi-magnify',
            title: this.$t('menu.compare'),
            component: 'compare_jobs',
          },
          {
            icon: 'create',
            title: this.$t('menu.landing_page'),
            component: 'list_landing_page',
          },
        ],
      },
      isLoading: false,
      organizationsItems: {},
    };
  },
  async created() {
    await this.getOrganizationFromAPI();
    this.fetchAuthData();
    this.$root.$on('updtOrganization', (data) => {
      this.$set(this.organizationsItems, data.id, data);
    });
  },
  computed: {
    organizationModel: {
      get() {
        return appStore.getters.organizationLoaded;
      },
      set(organization) {
        if (organization) {
          this.$router.push({
            name: 'recruitments_list',
            params: {organization_code: organization.code},
          });
        } else if (this.$route.meta.visibleWhenOrganizationSelected) {
          this.$router.push(
            {name: 'dashboard'},
            () => appStore.commit('unloadOrganization'), // we need to do this *after* the route change, or it might trigger JS errors if the current page relies on the loaded organization
          );
        } else {
          appStore.commit('unloadOrganization');
        }
      },
    },
    isEnterprise() {
      return this.organizationModel.organizationType === OrganizationType.ENTERPRISE;
    },
    organizationItemList() {
      return Object.values(this.organizationsItems).filter(o => this.includesSourcing || o.organizationType !== OrganizationType.SOURCING).sort((o1, o2) => o1.title?.localeCompare(o2.title));
    },
  },
  watch: {
    organizationModel(newValue, oldValue) {
      if (newValue && newValue.id && this.organizationsItems && !this.organizationsItems[newValue.id]) {
        this.$set(this.organizationsItems, newValue.id, newValue);
      }
      if (oldValue && oldValue.id && oldValue.organizationType === OrganizationType.SOURCING) {
        const allOrga = {...this.organizationsItems};
        delete allOrga[oldValue.id];
        this.organizationsItems = {...allOrga};
      }
    },
  },
  methods: {
    fetchAuthData() {
      this.authData = keycloakService.authData;
    },
    async getOrganizationFromAPI() {
      try {
        this.isLoading = true;
        const res = await this.$api.getAllRecruiters();

        this.organizationsItems = res.data.reduce((organizationsItemsAcc, organization) => ({
          ...organizationsItemsAcc,
          [organization.id]: organization,
        }), {});
      } finally {
        this.isLoading = false;
      }
    },
    profile() {
      window.open(
        `${this.authData.iss}/account?referrer=${this.authData.aud}`,
        '_blank',
      );
    },
    logout() {
      keycloakService.logout();
    },
  },
};
</script>

<style scoped>

.menu-icon-light {
  color: rgba(0, 0, 0, 0.54);
}

</style>
