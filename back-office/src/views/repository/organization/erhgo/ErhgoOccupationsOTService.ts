import SafeService from 'odas-plugins/SafeService';
import {ErhgoOccupationOTPage, SortDirection} from 'erhgo-api-client';
import Vue from 'vue';

export default class ErhgoOccupationsOTService extends SafeService {

  private _occupationPage: ErhgoOccupationOTPage | null = null;

  async fetchOccupationPage(size: number, page: number, direction: boolean, query: string) {
    await this.safeCall(async () => {

      this._occupationPage = (await Vue.$api.erhgoOccupationOTPage(size, page, query, this.getDirection(direction))).data;
    });
  }

  get occupationPage(): ErhgoOccupationOTPage | null {
    return this._occupationPage;
  }

  getDirection(direction: boolean) {
    return !direction ? SortDirection.ASC : SortDirection.DESC;
  }
}
