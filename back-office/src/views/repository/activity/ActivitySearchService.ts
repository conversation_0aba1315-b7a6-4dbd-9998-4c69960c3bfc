import Vue from 'vue';
import asyncDebounce from 'async-debounce';
// @ts-ignore
import jsog from 'jsog';

import {
  ActivityLabelWithCapacities, ActivityType, SortDirection,
} from 'erhgo-api-client';
import SafeService from 'odas-plugins/SafeService';

interface Capacity {
  value: number;
  text: string;
}

class Pagination {

  constructor(
    private _page: number,
    private _sortBy: string[],
    private _rowsPerPage: number,
    private _descending: boolean[],
    private _service: ActivitySearchService) {
  }

  set page(page) {
    if (this._page !== page) {
      this._page = page;
      this._service.search();
    }
  }

  get page() {
    return this._page;
  }

  set sortBy(sortBy) {
    if (this._sortBy !== sortBy) {
      this._sortBy = sortBy;
      this._service.search();
    }
  }

  get sortBy() {
    return this._sortBy;
  }

  set rowsPerPage(rowsPerPage) {
    if (this._rowsPerPage !== rowsPerPage) {
      this._rowsPerPage = rowsPerPage;
      this._service.search();
    }
  }

  get rowsPerPage() {
    return this._rowsPerPage;
  }
  set descending(descending) {
    if (this._descending !== descending) {
      this._descending = descending;
      this._service.search();
    }
  }

  get descending() {
    return this._descending;
  }
}

export default class ActivitySearchService extends SafeService {

  private _capacities: Capacity[] = [];
  private _selectedCapacities: number[] = [];
  private _activities: ActivityLabelWithCapacities[] = [];
  private _type: ActivityType;
  private _totalNumberOfElements: number | null = null;
  pagination = new Pagination(
    1,
    ['title'],
    10,
    [false],
    this);

  _isCapacityRecursive = false;
  _query = '';
  _userId = '';
  
  constructor(private _questionType: ActivityType) {
    super();
    this._type = _questionType;
    this.initialize();
  }

  async initialize() {
    await this.safeCall(async () => {
      // @ts-ignore
      this._capacities = jsog.decode((await Vue.$axios.get('/api/odas/capacity/list/all')).data).map((c: {
        id: number;
        label: string;
      }) => ({value: c.id, text: c.label}));
      await this.doSearch();
    });
  }

  private async doSearch() {
    const {content, totalNumberOfElements} = (await Vue.$api.listActivityLabels(
      this._type,
      this.pagination!.page - 1,
      this.pagination!.rowsPerPage,
      this._selectedCapacities,
      this._isCapacityRecursive,
      this.query,
      this.userId,
      this.pagination!.sortBy[0],
      this.pagination!.descending[0] ? SortDirection.DESC : SortDirection.ASC)).data;
    this._activities = content;
    this._totalNumberOfElements = totalNumberOfElements;
  }

  private debouncedSearch = asyncDebounce(async () => {
    this._activities = [];
    return await this.safeCall(async () => {
      return await this.doSearch();
    });
  }, 300);

  search() {
    this.debouncedSearch.fn();
  }

  get activities() {
    return  this._activities;
  }

  set activities(value: ActivityLabelWithCapacities[]) {
    this._activities = value;
  }

  get capacities() {
    return  this._capacities;
  }

  get query() {
    return this._query;
  }

  set query(query: string) {
    this._query = query;
    this.pagination.page = 1;
    this.search();
  }

  get userId() {
    return this._userId;
  }

  set userId(userId: string) {
    this._userId = userId;
    this.pagination.page = 1;
    this.search();
  }

  set selectedCapacities(selectedCapacities) {
    this._selectedCapacities = selectedCapacities;
    this.pagination.page = 1;
    this.search();
  }

  get selectedCapacities() {
    return this._selectedCapacities;
  }

  set isCapacityRecursive(isCapacityRecursive) {
    this._isCapacityRecursive = isCapacityRecursive;
    this.pagination.page = 1;
    this.search();
  }

  get isCapacityRecursive() {
    return this._isCapacityRecursive;
  }

  get totalNumberOfElements() {
    return this._totalNumberOfElements;
  }
}
