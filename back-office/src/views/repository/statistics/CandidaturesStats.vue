<template>
  <v-container fluid>
    <v-card>
      <v-card-title primary-title>
        <h3>
          <v-icon large color="primary">mdi-chart-areaspline</v-icon>
          {{ $t('statistics.monthlyCandidatures') }}
        </h3>
      </v-card-title>

      <v-card-text>
        <v-row>
          <v-col cols="6">
            <h3 class="mb-1">
              Rhône-Alpes
              <v-icon medium color="green" @click="showInfoDialog = true">mdi-information</v-icon>
            </h3>
            <v-data-table
              :headers="monthlyCandidaturesHeaders"
              :items="rhoneAlpesItems"
              dense
              class="elevation-1 stats"
              :items-per-page="12"
              hide-default-footer
              :loading="!statisticsService.monthlyCandidaturesStats"
            />
          </v-col>
          <v-col cols="6">
            <h3 class="mb-1">
              Hors Rhône-Alpes
              <v-icon medium color="green" @click="showInfoDialog = true">mdi-information</v-icon>
            </h3>
            <v-data-table
              :headers="monthlyCandidaturesHeaders"
              :items="notRhoneAlpesItems"
              dense
              class="elevation-1 stats"
              :items-per-page="12"
              :loading="!statisticsService.monthlyCandidaturesStats"
              hide-default-footer
              item-class="text-no-wrap"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-dialog v-model="showInfoDialog" max-width="60%">
      <v-card>
        <v-card-title>
          <span class="text-h4">Informations sur les calculs</span>
        </v-card-title>
        <v-card-text>
          <p class="text-h5">Les calculs des statistiques sont effectués comme suit :</p>
          <ul class="text-h6">
            <li>
              <strong>Moyenne (excluant les recrutements sans candidature) :</strong>
              La moyenne des candidatures par recrutement, en excluant les recrutements sans candidature.
            </li>
            <li>
              <strong>Moyenne (incluant les recrutements sans candidature) :</strong>
              La moyenne des candidatures par recrutement, en incluant les recrutements sans candidature.
            </li>
            <li>
              <strong>Exclusion des candidatures pushées :</strong>
              Les candidatures pushées et les candidatures brouillons ne sont pas prises en compte dans les calculs.
            </li>
            <li>
              <strong>Exclusion des recrutements non publiés :</strong>
              Les recrutements non publiés et les recrutements brouillons ne sont pas pris en compte. Pour chaque mois, le nombre de recrutements est calculé en fonction de la date de publication et de la date de fin de publication.
            </li>
            <li>
              <strong>Totale Cand-Recruitement ayant une Notif&nbsp;:</strong> Le nombre total de candidatures sur des recrutements pour lesquels il existe une notification.
            </li>
          </ul>
        </v-card-text>
        <v-card-actions>
          <v-spacer/>
          <v-btn color="primary" @click="showInfoDialog = false">Fermer</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import StatisticsService from './StatisticsService';

export default {
  async created() {
    this.statisticsService = new StatisticsService();
    await this.statisticsService.fetchMonthlyCandidaturesStats();
  },
  data() {
    return {
      statisticsService: null,
      showInfoDialog: false,
      monthlyCandidaturesHeaders: [
        { text: 'Mois', value: 'month', width: '100px' },
        { text: 'Total Offres Créées', value: 'totalRecruitmentCount' },
        { text: 'Total Candidatures Considérées', value: 'totalCandidatureCount' },
        { text: 'Total Cand-Recruitement ayant une Notif', value: 'totalCandidatureWithNotificationCount' },
        { text: 'Moyenne', value: 'average' },
        { text: 'Moyenne Excluant les Recrutements sans Candidature', value: 'averageExcludingZero' },
      ],
    };
  },
  computed: {
    rhoneAlpesItems() {
      if (!this.statisticsService.monthlyCandidaturesStats) return [];
      return this.statisticsService.monthlyCandidaturesStats.rhoneAlpesStats.map(item => ({
        month: item.month,
        totalRecruitmentCount: this.formatNumberStatValue(item.totalRecruitmentCount),
        totalCandidatureCount: this.formatNumberStatValue(item.totalCandidatureCount),
        totalCandidatureWithNotificationCount: this.formatNumberStatValue(item.totalCandidatureWithNotificationCount),
        average: this.formatNumberStatValue(item.averageCandidatureCount),
        averageExcludingZero: this.formatNumberStatValue(item.averageCandidatureCountExcludingZero),
      }));
    },
    notRhoneAlpesItems() {
      if (!this.statisticsService.monthlyCandidaturesStats) return [];
      return this.statisticsService.monthlyCandidaturesStats.notRhoneAlpesStats.map(item => ({
        month: item.month,
        totalRecruitmentCount: this.formatNumberStatValue(item.totalRecruitmentCount),
        totalCandidatureCount: this.formatNumberStatValue(item.totalCandidatureCount),
        totalCandidatureWithNotificationCount: this.formatNumberStatValue(item.totalCandidatureWithNotificationCount),
        average: this.formatNumberStatValue(item.averageCandidatureCount),
        averageExcludingZero: this.formatNumberStatValue(item.averageCandidatureCountExcludingZero),
      }));
    },
  },
  methods: {
    formatNumberStatValue(value) {
      return Number(Math.round(value ?? 0)).toLocaleString('fr');
    },
  },
};
</script>

<style lang="scss">
@import '@/style/colors.scss';

.stats tr:nth-child(even) {
  background-color: #ECEFF1;
}
</style>
