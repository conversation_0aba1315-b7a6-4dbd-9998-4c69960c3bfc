<template>
  <v-card>
    <v-card-title>
      <h2>
        <v-icon large right>cloud_upload</v-icon>
        Traitement en lot de profils utilisateur via CV
      </h2>
    </v-card-title>
    <v-card-text>
      <v-alert type="info" outlined class="mb-4">
        <strong>Format CSV requis :</strong> Le fichier doit contenir exactement 2 colonnes : email,url du CV.
        <br />
        <strong>Exemple :</strong><EMAIL>,https://example.com/cv.pdf
      </v-alert>

      <!-- File Upload Section -->
      <v-row>
        <v-col cols="12" md="6">
          <v-file-input
            v-model="selectedFile"
            label="Sélectionner un fichier CSV"
            accept=".csv"
            prepend-icon="attach_file"
            :error-messages="fileError"
            @change="onFileChange"
          />
        </v-col>
        <v-col cols="12" md="6">
          <v-checkbox
            v-model="excludeExistingUsers"
            label="Exclure les utilisateurs inexistants ou avec des expériences"
            hint="Si coché, ignore les utilisateurs qui n'existent pas dans Keycloak ou qui ont déjà des expériences."
            persistent-hint
          />
        </v-col>
      </v-row>

      <!-- Action Buttons -->
      <v-row class="mt-4">
        <v-col cols="12">
          <v-btn
            color="primary"
            :disabled="!selectedFile || !!fileError || service.processing"
            :loading="service.processing"
            @click="processFile"
          >
            <v-icon left>play_arrow</v-icon>
            Traiter le fichier CSV
          </v-btn>
          <v-btn
            v-if="service.hasResults"
            color="secondary"
            outlined
            class="ml-3"
            @click="clearResults"
          >
            <v-icon left>clear</v-icon>
            Effacer les résultats
          </v-btn>
        </v-col>
      </v-row>

      <!-- Results Section -->
      <div v-if="service.hasResults" class="mt-6">
        <v-divider class="mb-4" />

        <!-- Processing Status -->
        <v-alert type="info" outlined class="mb-4">
          <div class="text-h6 mb-2">{{ service.message }}</div>
          Le traitement des CVs est en cours en arrière-plan. Vous pouvez consulter les logs pour suivre l'avancement.
        </v-alert>

        <!-- Emails List -->

        <div>
          <v-icon left>mdi-thumb-up-outline</v-icon>
          Emails qui seront traités ({{ service.emails.length }})

          <v-chip
            v-for="email in service.emails"
            :key="email"
            class="ma-1"
            small
            outlined
            color="success"
          >
            {{ email }}
          </v-chip>
        </div>
        <div>
          <v-icon left>mdi-thumb-down-outline</v-icon>
          Emails ignorés ({{ service.invalidEmails.length }})
          <v-chip
            v-for="email in service.invalidEmails"
            :key="email"
            class="ma-1"
            small
            outlined
            color="error"
          >
            {{ email }}
          </v-chip>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
import BulkCVProcessingService from '@/components/services/BulkCVProcessingService';

export default {
  name: 'BulkCVProcessingPage',
  data() {
    return {
      service: new BulkCVProcessingService(),
      selectedFile: null,
      excludeExistingUsers: true,
      fileError: null,

    };
  },
  methods: {
    onFileChange() {
      this.fileError = this.service.validateCsvFile(this.selectedFile);
    },
    async processFile() {
      if (!this.selectedFile || this.fileError) {
        return;
      }

      try {
        await this.service.processCsvFile(this.selectedFile, this.excludeExistingUsers);
      } catch (error) {
        this.$toast.error('Erreur lors du traitement du fichier CSV');
      }
    },
    clearResults() {
      this.service.clearResults();
    },
  },
};
</script>
