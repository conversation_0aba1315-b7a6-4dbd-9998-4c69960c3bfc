<template>
  <ais-instant-search :index-name="indexName"
                      :search-client="searchClient"
                      :middlewares="[middleware]"
                      v-if="!service.loading && searchClient">
    <v-row>
      <v-col cols="3" style="border-right: black solid 1px">
        <ais-clear-refinements>
          <template v-slot="{ canRefine, refine }">
            <v-btn outlined
                   @click.prevent="clear(canRefine, refine)"
            >
              <v-icon small color="error">cancel</v-icon>
              <span class="text-caption">Supprimer tous les filtres</span>
            </v-btn>
          </template>
        </ais-clear-refinements>
        <v-text-field
            class="pt-4"
            dense
            hide-details
            v-model="query"
            label="Chercher par activité, ville, métier ou expérience"
        />
        <popin-date-picker
            v-model="creationAfterMoment"
            label="Créé après le..."
        />
        <popin-date-picker
            v-model="lastConnectionAfterMoment"
            label="Dernière connexion après le..."
        />
        <v-divider class="my-1"/>

        <h3 class="text-center pb-2">M<PERSON><PERSON>s</h3>
        <ais-refinement-list
            class="text-body-2"
            searchable
            show-more
            :show-more-limit="50"
            searchable-placeholder="Chercher un métier"
            :transform-items="transformGeneric"
            attribute="erhgoOccupations"
        >
          <template v-slot:noResults>Aucun métier correspondant</template>
          <template v-slot:showMoreLabel="{ isShowingMore }">
            <div class="font-weight-bold text-decoration-underline">{{
                !isShowingMore ? 'Voir plus' : 'Voir moins'
              }}
            </div>
          </template>
        </ais-refinement-list>
        <v-divider class="my-3"/>
        <h3 class="text-center pb-2">À proximité de...</h3>
        <vuetify-geo-places
            label="Chercher une ville"
            @input="updateCity"
            persistent-hint
            no-data-text="Aucune correspondance"
            outlined
            :value="prefilter.location && prefilter.location.latitude && prefilter.location.longitude ? prefilter.location : null"
            class="mx-4 combobox-erhgo"
            :hint="jobRadiusInKm ? `Rayon du poste : ${jobRadiusInKm}km`:''"
            :disabled="!!prefilter.location"
            :key="geoPlacesKey"
        />
        <template v-if="latLong">
          <label>Rayon max (km)&nbsp;:</label>
          <v-slider
              class="pt-5"
              min="1"
              max="200"
              v-model="radius"
              thumb-size="20"
              thumb-label="always"
          />
        </template>
        <v-divider class="my-3"/>
        <h3 class="text-center pb-2">Uniquement utilisateur à l'écoute</h3>
        <ais-refinement-list
            class="text-body-2 d-inline"
            attribute="activeSearch"
            :transform-items="items => items.map(item => ({...item, label: item.value === 'true' ? 'Oui':'Non'}))"
        />
        <h3 class="text-center mt-5">Salaire brut annuel</h3>
        <v-row no-gutters class="justify-space-between">
          <v-col cols="5">
            <integer-field v-model="salaryMin" placeholder="min" dense hide-details />
          </v-col>
          <v-col cols="5">
            <integer-field v-model="salaryMax" placeholder="max" dense hide-details />
          </v-col>
        </v-row>

        <v-divider class="my-3"/>

        <v-expansion-panels flat class="sourcing-panels">
          <v-expansion-panel>
            <v-expansion-panel-header>
              <h3 class="text-center pb-2">
                <v-icon small>fa-plus</v-icon>
                Ajuster les critères
              </h3>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <div
                  class="text-caption font-italic text-center">Seuls les utilisateurs validant tous les critères
                sélectionnés
                sont remontés
              </div>

              <ais-refinement-list
                  class="text-body-2 pt-2"
                  attribute="criteria"
                  :transform-items="transformCriteria"
                  :limit="100"
                  operator="and"
              >
                <template v-slot:showMoreLabel="{ isShowingMore }">
                  <div class="font-weight-bold text-decoration-underline">{{
                      !isShowingMore ? 'Voir plus' : 'Voir moins'
                    }}
                  </div>
                </template>
              </ais-refinement-list>

            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
        <v-divider class="my-3"/>
        <h3 class="text-center pb-1">Degré de maîtrise</h3>
        <div
            class="text-caption font-italic pb-1 text-center" v-if="prefilter.masteryLevel">Degré de maîtrise du poste&nbsp;:
          {{ prefilter.masteryLevel }}
        </div>

        <ais-refinement-list
            class="text-body-2"
            attribute="masteryLevel"
            :transform-items="transformForLevel"
        />
        <v-divider class="my-3"/>
        <h3 class="text-center pb-2">Canaux</h3>
        <ais-refinement-list
            class="text-body-2"
            v-if="organizations.length"
            attribute="channels"
            :limit="undefined"
            :transform-items="transformChannel"
            :show-more="isAdminView"
            :show-more-limit="isAdminView?500:undefined"
        >
          <template v-slot:showMoreLabel="{ isShowingMore }">
            <div class="font-weight-bold text-decoration-underline">{{
                !isShowingMore ? 'Voir plus' : 'Voir moins'
              }}
            </div>
          </template>
        </ais-refinement-list>
        <v-divider class="my-3"/>
        <h3 class="text-center pb-2">Comportements principaux</h3>
        <ais-refinement-list
            class="text-body-2"
            attribute="mainBehaviors"
            :transform-items="transformGeneric"
        />
        <v-divider class="my-3"/>
        <h3 class="text-center pb-2">Ville</h3>
        <ais-refinement-list
            class="text-body-2"
            attribute="city"
            show-more
            :show-more-limit="50"
            :transform-items="transformGeneric"
        >
          <template v-slot:showMoreLabel="{ isShowingMore }">
            <div class="font-weight-bold text-decoration-underline">{{
                !isShowingMore ? 'Voir plus' : 'Voir moins'
              }}
            </div>
          </template>
        </ais-refinement-list>
        <v-divider class="my-3"/>
        <ais-configure
            :query="query"
            :filters.camel="numericFilter"
            :around-lat-lng.camel="latLong ? latLong : undefined"
            :around-radius.camel="latLong ? radius*1000 : undefined"
            :x-algolia-api-key="apiKey"
            :optional-filters.camel="optionalFilters"
        />
      </v-col>
      <v-col cols="9">
        <v-row justify="center">
          <v-col cols="12" class="d-flex justify-center pb-10">
            <ais-state-results>
              <template
                  v-slot="{results: { page, hitsPerPage, hits, nbHits } }"
              >
                <h4 class="text-center" v-if="hits.length">{{ page * hitsPerPage + 1 }} à
                  {{ page * hitsPerPage + hits.length }}
                  sur {{ nbHits }} utilisateurs correspondant</h4>
                <h4 v-else>Aucun utilisateur correspondant</h4>
              </template>
            </ais-state-results>
            <downloader-btn
                :disabled="!hasAnyResult"
                outlined
                class="ml-3"
                small
                :download="exportUsers"
                :title="title"
                extension="csv"
            >
              <v-icon>archive</v-icon>
              Télécharger
            </downloader-btn>
            <slot name="otherActions" v-bind="{hasAnyResult}"/>
          </v-col>
        </v-row>
        <ais-hits :transform-items="transformItems">
          <template v-slot="{ items }">
            <v-data-table
                id="result-for-user-search"
                disable-pagination
                hide-default-footer
                no-data-text="Aucun candidat ne correspond à vos filtres"
                :headers="
              [{
                text: 'Métiers',
                sortable: false,
              },{
                text: 'Canaux',
                sortable: false,
              },{
                text: 'Localisation',
                sortable: false,
              },{
                text: 'Degré de maîtrise',
                sortable: false,
              },{
                text: 'Action',
                sortable: false,
              }]"
                :items="items"
            >
              <template v-slot:item="props">
                <user-item :get-channel-label="getChannelLabel"
                           :get-level-label="getLevelLabel"
                           :result="props"
                           :organization-code="service.organization_code"
                />
              </template>
            </v-data-table>
          </template>
        </ais-hits>
        <v-divider class="py-5"/>
        <ais-pagination class="text-center"/>
        <ais-hits-per-page class="text-center"
                           :items="paginationItems"
        />
      </v-col>
    </v-row>
  </ais-instant-search>
</template>
<script>
import VuetifyGeoPlaces from 'odas-plugins/VuetifyGeoPlaces';
import UserItem from './UserItem';
import PopinDatePicker from './PopinDatePicker';
import moment from 'moment';
import DownloaderBtn from 'odas-plugins/DownloaderBtn';
import IntegerField from 'odas-plugins/IntegerField';
import { OrganizationType } from 'erhgo-api-client';

export default {
  components: {PopinDatePicker, UserItem, VuetifyGeoPlaces, DownloaderBtn, IntegerField},
  props: {
    initialService: {
      type: Object,
      required: true,
    },
    prefilter: {
      default() {
        return {
          capacities: [],
          location: null,
          criteria: [],
          masteryLevel: null,
        };
      },
    },
  },
  data() {
    return {
      query: '',
      latLong: null,
      radius: null,
      geoPlacesKey: 1,
      lastConnectionAfterMoment: null,
      creationAfterMoment: null,
      paginationItems: [
        {label: '50 lignes par page', value: 50},
        {label: '100 lignes par page', value: 100},
        {label: '150 lignes par page', value: 150},
        {label: '200 lignes par page', value: 200},
        {label: '250 lignes par page', value: 250},
        {label: '300 lignes par page', value: 300, default: true},
        {label: '1000 lignes par page', value: 1000},
      ],
      usersId: [],
      hasAnyResult: false,
      salaryMin: null,
      salaryMax: null,
      service: this.initialService,
    };
  },
  async created() {
    this.updateCity(this.prefilter.location);
  },
  methods: {
    onStateChange({uiState}) {
      this.service.uiState = uiState;
    },
    updateCity(place) {
      if (place?.latitude && place?.longitude) {
        const {latitude, longitude} = place;
        this.latLong = `${latitude},${longitude}`;
        this.radius = this.jobRadiusInKm || 50;
      } else {
        this.latLong = null;
        this.radius = null;
      }
    },
    transformForLevel(items) {
      return items.map(item => ({...item, label: this.getLevelLabel(item.value), count: `(${item.count})`}));
    },
    transformChannel(items) {
      return items.map(item => ({
        ...item,
        label: this.getChannelLabel(item.value),
        count: `(${item.count})`,
      })).filter(a => a.label).sort((a, b) => a.label.localeCompare(b.label));
    },
    transformCriteria(items) {
      const lastItemsCodes = items.map(c => c.value);
      const noValueItems = this.service.initialCriteriaFacets.filter(c => !lastItemsCodes.includes(c.value));
      return [...items, ...noValueItems].map(item => ({
        ...item,
        label: this.criteria.filter(o => o.code === item.value)[0]?.titleStandalone || `${item.value} (Inconnu)`,
        count: `(${item.count})`,
      }))
        .filter(item => !this.prefilter.criteria.length || this.prefilter.criteria.includes(item.value))
        .sort((a, b) => a.count === b.count ? a.label.toUpperCase().localeCompare(b.label.toUpperCase()) : a.count > b.count);
    },
    transformGeneric(items) {
      return items.map(item => ({
        ...item,
        count: `(${item.count})`,
      }));
    },
    getChannelLabel(item) {
      const orga = this.organizations.filter(o => o.code === item)[0];
      return (`${orga?.title}${orga.organizationType === OrganizationType.SOURCING ? ' (sourcing)' : ''}`) || (this.isAdminView ? `${item.value} (inconnu)` : '');
    },
    getLevelLabel(value) {
      switch (value) {
        case '1':
          return '1 - Professionnel';
        case '2':
          return '2 - Technique';
        case '3':
          return '3 - Complexe';
        case '4':
          return '4 - Expert';
        case '5':
          return '5 - Stratégique';
        default:
          return 'Inconnu';
      }
    },
    clear(canRefine, refine) {
      this.updateCity(this.prefilter.location);
      this.query = '';
      this.geoPlacesKey++;
      this.lastConnectionAfterMoment = null;
      this.creationAfterMoment = null;
      this.salaryMin = null;
      this.salaryMax = null;
      canRefine && refine?.();
    },
    transformItems(items) {
      this.usersId = items.map(x => x.objectID);
      // Note: hasAnyResult should be computed but this way : infinite loop on ais-hits `transformItems` call happens
      this.hasAnyResult = !!items.length;
      return items;
    },
    async exportUsers() {
      return (await this.$api.exportUsers({ deanonymizedUser: true, usersId: this.usersId })).data;
    },
    middleware() {
      return {
        onStateChange: ({uiState}) => {
          this.onStateChange({uiState});
        },
      };
    },
  },
  computed: {
    jobRadiusInKm() {
      return this.prefilter?.location?.radiusInKm;
    },
    indexName() {
      return this.service.indexName;
    },
    searchClient() {
      return this.service.searchClient;
    },
    criteria() {
      return this.service.criteria;
    },
    organizations() {
      return this.service.organizations;
    },
    optionalCapacitiesFilter() {
      return this.prefilter.capacities.map(a => `capacities:${a}`);
    },
    optionalCriteriaFilter() {
      return this.prefilter.criteria.map(a => `criteria:${a}`);
    },
    optionalFilters() {
      return [...this.optionalCapacitiesFilter, ...this.optionalCriteriaFilter];
    },
    apiKey() {
      return this.service.apiKey;
    },
    isAdminView() {
      return !this.service.organization_code;
    },
    lastConnectionFilter() {
      return this.lastConnectionAfterMoment ? `lastConnectionTimestamp >= ${this.lastConnectionAfterMoment.unix()}` : '';
    },
    creationFilter() {
      return this.creationAfterMoment ? `creationTimestamp >= ${this.creationAfterMoment.unix()}` : '';
    },
    salaryFilters() {
      return [this.salaryMin ? `salary >= ${this.salaryMin}` : '', this.salaryMax ? `salary <= ${this.salaryMax}` : ''];
    },
    numericFilter() {
      return [this.lastConnectionFilter, this.creationFilter, ...this.salaryFilters].filter(v => !!v).join(' AND ');
    },
    title() {
      return `utilisateurs_${moment().format('MM-DD-YYYY_HH-mm-ss')}.csv`;
    },

  },
};
</script>
<style>
.ais-Pagination-item {
  display: inline-block;
  padding-right: 5px;
}

.ais-RefinementList-showMore--disabled {
  visibility: hidden;
}

.ais-SearchBox-input {
  border: black 1px solid;
  padding: 5px;
  margin-bottom: 15px;
}

.ais-RefinementList-item {
  list-style-type: none;
}

.ais-HitsPerPage-select {
  -moz-appearance: auto !important;
  -webkit-appearance: auto !important;
}

.sourcing-panels .v-expansion-panel-content__wrap {
  padding: 0;
}

.d-inline .ais-RefinementList-item {
  display: inline;
}

.d-inline .ais-RefinementList-item:nth-of-type(1) {
  margin-right: 15px;
}
</style>
