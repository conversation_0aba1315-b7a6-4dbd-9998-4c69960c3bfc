import SafeService from 'odas-plugins/SafeService';
import {UserRegistrationState} from 'erhgo-api-client';

export default class RegistrationStateService extends SafeService {
  private _userRegistrationState: UserRegistrationState | null = null;

  constructor(private userRegistrationState: UserRegistrationState) {
    super();
    this._userRegistrationState = userRegistrationState;
  }

  get salary() {
    return this._userRegistrationState?.salary;
  }

  get situation() {
    return this._userRegistrationState?.situation;
  }

  get city() {
    return this._userRegistrationState?.city;
  }

  get occupation() {
    return this._userRegistrationState?.selectedOccupation;
  }

  get occupationId() {
    return this._userRegistrationState?.selectedOccupationId;
  }

  get jobTitle() {
    return this._userRegistrationState?.jobTitle;
  }
}
