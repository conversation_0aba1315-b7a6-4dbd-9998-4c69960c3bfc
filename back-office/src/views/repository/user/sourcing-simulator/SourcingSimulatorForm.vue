<template>
  <v-card flat class="simulator-form">
    <v-row no-gutters>
      <v-col cols="12" class="py-0">
        <v-expansion-panels flat>
          <v-expansion-panel>
            <v-expansion-panel-header class="px-1 py-0">
              <h4>
                Aspect métier
              </h4>
            </v-expansion-panel-header>

            <v-expansion-panel-content>
              <v-row no-gutters>
                <v-col cols="12">
                  <p class="py-1 font-weight-bold">
                    M<PERSON>tier
                  </p>

                  <occupation-combobox @input="newVal => service.selectOccupation(newVal)"/>
                  <v-btn text
                         class="mt-3 px-0 text-none primary--text"
                         v-if="sourcingSimulatorQuery.occupation"
                         target="_blank"
                         :to="{
                        name: 'erhgo_occupation_detail',
                        params: {
                          id: sourcingSimulatorQuery.occupation.id,
                        }
                     }">
                    <u>Détail métier choisi</u>
                  </v-btn>

                </v-col>
              </v-row>

              <v-row class="pt-8" no-gutters>
                <v-col cols="12" class="px-0">
                  <p class="py-1 font-weight-bold">
                    Couverture capacitaire (85% par défaut)
                  </p>

                  <v-switch @click="enablingCapacityTolerance"
                            :disabled="!sourcingSimulatorQuery.occupation"
                            :label="capacityToleranceSwitchText"
                            :value="sourcingSimulatorQuery.enableCapacityTolerance"
                  />
                  <v-text-field
                    v-model="sourcingSimulatorQuery.capacityTolerance"
                    label="Couverture capacitaire"
                    type="number"
                    hide-details
                    dense
                    class="mr-1"
                    :rules="[
                        v => !!v || 'La couverture capacitaire est obligatoire',
                        v => v >= 0 || 'La couverture capacitaire doit être supérieure ou égale à 0',
                        v => v <= 100 || 'La couverture capacitaire doit être inférieure ou égale à 100',
                      ]"
                    :disabled="!sourcingSimulatorQuery.enableCapacityTolerance"
                  />
                </v-col>
              </v-row>
              <v-row class="pt-8" no-gutters>
                <v-col cols="12" class="px-0">
                  <p class="py-1 font-weight-bold">Niveau de maîtrise
                  </p>
                  <v-switch v-model="sourcingSimulatorQuery.enableMasteryLevel">
                    <template v-slot:label>
                      {{ sourcingSimulatorQuery.enableMasteryLevel ? 'Désa' : 'A' }}ctiver le
                      choix du niveau de
                      maîtrise
                    </template>
                  </v-switch>
                  <v-text-field
                    v-model="sourcingSimulatorQuery.masteryLevel"
                    type="number"
                    label="Niveau de maîtrise"
                    hide-details
                    dense
                    class="mr-1"
                    :rules="[
                      v => !!v || 'Le niveau de maîtrise est obligatoire',
                      v => v >= 1 || 'Le niveau de maîtrise doit être supérieur ou égal à 1',
                      v => v <= 5 || 'Le niveau de maîtrise doit être inférieur ou égal à 5',
                    ]"
                    :disabled="!sourcingSimulatorQuery.enableMasteryLevel"
                  />

                </v-col>
              </v-row>
              <v-row no-gutters>
                <v-col cols="12" class="px-0">

                  <p class="py-1 font-weight-bold">
                    Souhaits
                  </p>

                  <v-switch v-model="sourcingSimulatorQuery.enableClassifications">
                    <template v-slot:label>
                      {{ sourcingSimulatorQuery.enableClassifications ? 'Désa' : 'A' }}ctiver le
                      choix des
                      souhaits
                    </template>
                  </v-switch>
                  <v-checkbox
                    v-for="item in service.erhgoClassifications"
                    :key="`key-${item.code}`"
                    v-model="sourcingSimulatorQuery.selectedErhgoClassificationsCodes"
                    hide-details
                    dense
                    :label="item.title"
                    class="mt-0"
                    :value="item.code"
                    :disabled="!sourcingSimulatorQuery.enableClassifications"
                  />
                </v-col>
              </v-row>
              <v-row no-gutters>
                <v-col cols="12" class="px-0">
                  <p class="py-1  font-weight-bold">
                    Aspect technique du métier
                  </p>

                  <v-checkbox v-model="sourcingSimulatorQuery.forcedTechnical">
                    <template v-slot:label>
                      Métier technique
                      {{ sourcingSimulatorQuery.forcedTechnical ? 'forcé' : 'non forcé' }}
                    </template>
                  </v-checkbox>
                  <p class="error--text" v-if="sourcingSimulatorQuery.isOccupationTechnical">
                    <v-icon color="error" small>warning</v-icon>
                    Le métier sélectionné est technique
                  </p>

                </v-col>
              </v-row>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col cols="12" class="py-0">
        <v-expansion-panels flat>
          <v-expansion-panel>
            <v-expansion-panel-header class="px-1 py-0">
              <h4>
                Localisation
              </h4>
            </v-expansion-panel-header>

            <v-expansion-panel-content>
              <v-switch
                @click="enablingRadius"
                :label="radiusSwitchText"
                :disabled="!sourcingSimulatorQuery.locationCity"
                :value="sourcingSimulatorQuery.enableRadius"
              />
              <vuetify-geo-places
                id="searchCity"
                :value="{city: sourcingSimulatorQuery.locationCity}"
                @input="updateCity"
                persistent-hint
                no-data-text="Aucune correspondance"
                outlined
                class="combobox-erhgo"
              />
              <div class="my-5 error--text">
                <v-slider
                  class="mt-8"
                  v-model="sourcingSimulatorQuery.radius"
                  :thumb-size="44"
                  :max="200"
                  :disabled="!sourcingSimulatorQuery.enableRadius"
                >
                  <template v-slot:thumb-label>
                    {{ sourcingSimulatorQuery.radius }} km
                  </template>
                </v-slider>
                <h5 class="black--text" v-if="sourcingSimulatorQuery.enableRadius">
                  Rayon : {{ sourcingSimulatorQuery.radius ? sourcingSimulatorQuery.radius : 0 }} km
                </h5>
                <h5>Valeur par défaut : 50km autour d'une ville choisie</h5>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>

      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col cols="12" class="py-0">
        <v-expansion-panels flat>
          <v-expansion-panel>
            <v-expansion-panel-header class="px-1 py-0">
              <h4>
                Critère contrat de travail
              </h4>
            </v-expansion-panel-header>

            <v-expansion-panel-content>
              <v-radio-group class="my-0" v-model="sourcingSimulatorQuery.typeContractCategory"
                             label="Type de contrat de travail">
                <v-radio class="my-1" label="CDI" :value="TypeContractCategory.PERMANENT"/>
                <v-radio class="my-1" label="Autre qu'un CDI" :value="TypeContractCategory.TEMPORARY"/>
                <v-radio class="my-1" label="Alternance" :value="TypeContractCategory.PRO"/>
              </v-radio-group>
              <v-radio-group class="my-0" v-model="sourcingSimulatorQuery.workingTimeType"
                             label="Temps de travail">
                <v-radio class="my-1" label="Temps plein" :value="WorkingTime.FULL_TIME"/>
                <v-radio class="my-1" label="Temps partiel" :value="WorkingTime.PART_TIME"/>
              </v-radio-group>
              <v-btn dense
                     outlined
                     color="error"
                     @click="() => service.resetContractCriteria()"
              >Réinitialiser
              </v-btn>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col cols="12">
        <v-expansion-panels flat>
          <v-expansion-panel>
            <v-expansion-panel-header class="px-1 py-0">
              <h4>Salaire</h4>
            </v-expansion-panel-header>

            <v-expansion-panel-content>

              <v-switch v-model="sourcingSimulatorQuery.enableSalaries">
                <template v-slot:label>
                  {{ sourcingSimulatorQuery.enableSalaries ? 'Désa' : 'A' }}ctiver le choix des
                  salaires
                </template>
              </v-switch>

              <v-range-slider
                v-model="sourcingSimulatorQuery.salaryRange"
                :thumb-size="55"
                :max="100000"
                :min="0"
                :disabled="!sourcingSimulatorQuery.enableSalaries"
              >
                <template v-slot:thumb-label="props">
                  {{ props.value.toLocaleString() }}&nbsp;€
                </template>
              </v-range-slider>

              <div class="my-2 d-flex">
                <v-text-field
                  v-model="sourcingSimulatorQuery.salaryRange[0]"
                  type="number"
                  hide-details
                  dense
                  class="mr-1"
                  label="Salaire minimum"
                  suffix="€"
                  :rules="salariesRules"
                  :disabled="!sourcingSimulatorQuery.enableSalaries"
                />
                <v-text-field
                  v-model="sourcingSimulatorQuery.salaryRange[1]"
                  type="number"
                  hide-details
                  dense
                  class="ml-1"
                  label="Salaire maximum"
                  suffix="€"
                  :rules="salariesRules"
                  :disabled="!sourcingSimulatorQuery.enableSalaries"
                />
              </div>
              <v-alert
                outlined
                v-if="sourcingSimulatorQuery.enableSalaries"
                dense
                color="error"
                class="text-caption"
              >
                Des tolérances sont appliqués sur les salaires&nbsp;:
                <ul>
                  <li>Salaire min - 30%</li>
                  <li>Salaire max + 15%</li>
                </ul>
                Ces tolérances ne sont pas <strong>modifiables</strong>
              </v-alert>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col cols="12" class="py-0">
        <v-expansion-panels flat>
          <v-expansion-panel>
            <v-expansion-panel-header class="px-1 py-0">
              <h4>
                Situation
              </h4>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-checkbox v-model="sourcingSimulatorQuery.activeSearch"
                          label="Ne considérer que les candidats en veille ou en recherche"
                          class="my-5"
              />

              <v-menu
                close-on-content-click
                transition="scale-transition"
                offset-y
                max-width="290px"
                min-width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    :value="lastConnectionFormattedDate"
                    label="Date de dernière connexion"
                    prepend-icon="event"
                    v-on="on"
                    v-bind="attrs"
                    hide-details
                    dense
                    clearable
                    @click:clear="sourcingSimulatorQuery.lastConnectionDatetime = null"
                  />
                </template>
                <v-date-picker v-model="sourcingSimulatorQuery.lastConnectionDatetime"
                               no-title
                               scrollable
                               :max="new Date().toISOString()"
                               min="2018-01-01"
                               first-day-of-week="1"
                               locale="fr"/>
              </v-menu>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col cols="12" class="py-0">
        <v-expansion-panels flat>
          <v-expansion-panel>
            <v-expansion-panel-header class="px-1 py-0">
              <h4>
                Critères
              </h4>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-btn dense
                     outlined
                     color="error"
                     @click="() => service.resetCriteria()"
              >Réinitialiser
              </v-btn>
              <criteria-selector
                :key="`key-${service.criteriaRerenderKey}`"
                v-model="sourcingSimulatorQuery.selectedCriteria"
                :column="true"/>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-col>
    </v-row>
  </v-card>
</template>
<script>
import OccupationCombobox from '@/components/common/OccupationCombobox';
import VuetifyGeoPlaces from 'odas-plugins/VuetifyGeoPlaces';
import moment from 'moment';
import CriteriaSelector from '@/components/criteria/CriteriaSelector';
import { TypeContractCategory, WorkingTime } from 'erhgo-api-client';

export default {
  components: {CriteriaSelector, OccupationCombobox, VuetifyGeoPlaces},
  props: {
    service: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      TypeContractCategory, WorkingTime,
      key: 1,
    };
  },
  methods: {
    updateCity(place) {
      if (!!place) {
        const {city, latitude, longitude} = place;
        this.sourcingSimulatorQuery.location = {city, latitude, longitude};
        this.sourcingSimulatorQuery.enableRadius = true;
        this.sourcingSimulatorQuery.radius = 50;

      } else {
        this.sourcingSimulatorQuery.location = null;
        this.sourcingSimulatorQuery.enableRadius = false;
        this.sourcingSimulatorQuery.radius = 50;
      }
    },
    enablingCapacityTolerance() {
      this.sourcingSimulatorQuery.enableCapacityTolerance = !this.sourcingSimulatorQuery.enableCapacityTolerance;
      this.sourcingSimulatorQuery.capacityTolerance = this.sourcingSimulatorQuery.enableCapacityTolerance ? 90 : 0;
    },
    enablingRadius() {
      this.sourcingSimulatorQuery.enableRadius = !this.sourcingSimulatorQuery.enableRadius;
      this.sourcingSimulatorQuery.radius = 50;
    },
  },
  computed: {
    sourcingSimulatorQuery() {
      return this.service.sourcingSimulatorQuery;
    },
    capacityToleranceSwitchText() {
      if (!this.sourcingSimulatorQuery.occupation) {
        return 'Choisissez un métier pour activer la couverture capacitaire';
      } else {
        return `${this.sourcingSimulatorQuery.enableCapacityTolerance ? 'Désa' : 'A'}ctiver la couverture capacitaire`;
      }
    },
    radius() {
      return this.service.sourcingSimulatorQuery.radius;
    },
    radiusSwitchText() {
      if (!this.sourcingSimulatorQuery.locationCity) {
        return 'Choisissez une ville pour activer le choix du rayon';
      } else {
        return `${this.sourcingSimulatorQuery.enableRadius ? 'Désa' : 'A'}ctiver le choix du rayon`;
      }
    },
    lastConnectionFormattedDate() {
      return moment(this.sourcingSimulatorQuery.lastConnectionDatetime).locale('fr').format('LL');
    },
    salariesRules() {
      return [
        v => (v <= 100000) || 'La valeur est supérieure à 100 000€',
        v => (v >= 0) || 'La valeur est  inférieur à 0€',
      ];
    },
  },
  watch: {
    sourcingSimulatorQuery: {
      async handler() {
        await (this.service.debouncedSimulateSourcingFilters.fn(this.sourcingSimulatorQuery.withDetails));
      },
      deep: true,
    },
    radius(curr) {
      if (curr === 0) {
        this.sourcingSimulatorQuery.radius = null;
      }
    },
  },
};
</script>
<style>
.simulator-form .v-expansion-panel-content__wrap {
  padding: 0 !important;
}
</style>
