import SafeService from 'odas-plugins/SafeService';
import {ErhgoClassification, ErhgoOccupationSearch, SourcingSimulatedResult} from 'erhgo-api-client';
import Vue from 'vue';
import moment from 'moment';
import SourcingSimulatorQuery from '@/views/repository/user/sourcing-simulator/SourcingSimulatorQuery';
import asyncDebounce from 'async-debounce';

enum MasteryLevel {
  PROFESSIONAL = 1,
  TECHNICAL = 2,
  COMPLEX = 3,
  EXPERT = 4,
  STRATEGIC = 5,
}

export default class SourcingSimulatorService extends SafeService {
  criteriaRerenderKey = 1;
  sourcingSimulatorQuery = new SourcingSimulatorQuery();
  candidatesResult: SourcingSimulatedResult | null = null;
  _allErhgoClassifications: ErhgoClassification[] = [];

  get erhgoClassifications() {
    return this._allErhgoClassifications;
  }

  resetContractCriteria() {
    this.sourcingSimulatorQuery.typeContractCategory = null;
    this.sourcingSimulatorQuery.workingTimeType = null;
  }

  resetCriteria() {
    this.sourcingSimulatorQuery.selectedCriteria = [];
    this.criteriaRerenderKey++;
  }

  async simulateSourcingFilters(withDetails: boolean) {
    this.sourcingSimulatorQuery.withDetails = withDetails;
    await this.safeCall(async () => {
      this.candidatesResult = (await Vue.$api.simulateSourcingFilters(
        this.sourcingSimulatorQuery.activeSearch,
        this.sourcingSimulatorQuery.withDetails,
        this.sourcingSimulatorQuery.forcedTechnical,
        this.sourcingSimulatorQuery.showTopTen,
        this.sourcingSimulatorQuery.occupation?.id,
        this.sourcingSimulatorQuery.enableClassifications ? this.sourcingSimulatorQuery.selectedErhgoClassificationsCodes : undefined,
        this.sourcingSimulatorQuery.selectedCriteria ? this.sourcingSimulatorQuery.selectedCriteria.map(c => c.code) : undefined,
        this.sourcingSimulatorQuery.typeContractCategory ? this.sourcingSimulatorQuery.typeContractCategory : undefined,
        this.sourcingSimulatorQuery.workingTimeType ? this.sourcingSimulatorQuery.workingTimeType : undefined,
        this.sourcingSimulatorQuery.enableMasteryLevel && this.sourcingSimulatorQuery.masteryLevel ? this.sourcingSimulatorQuery.masteryLevel : undefined,
        this.sourcingSimulatorQuery.location?.longitude,
        this.sourcingSimulatorQuery.location?.latitude,
        this.sourcingSimulatorQuery.enableRadius ? this.sourcingSimulatorQuery.radius : undefined,
        this.sourcingSimulatorQuery.enableSalaries ? this.sourcingSimulatorQuery.salaryRange[0] : undefined,
        this.sourcingSimulatorQuery.enableSalaries ? this.sourcingSimulatorQuery.salaryRange[1] : undefined,
        this.sourcingSimulatorQuery.capacityTolerance / 100,
        this.sourcingSimulatorQuery.lastConnectionDatetime ? moment(this.sourcingSimulatorQuery.lastConnectionDatetime).unix() : undefined,
      )).data;
    });
  }

  public debouncedSimulateSourcingFilters = asyncDebounce(async (withDetails: boolean) => {
    await this.simulateSourcingFilters(withDetails);
  }, 300);

  async selectOccupation(newValue: ErhgoOccupationSearch) {
    await this.safeCall(async () => {
      if (newValue) {
        this.sourcingSimulatorQuery.occupation = (await Vue.$api.getErhgoOccupation(newValue.code)).data;
        this.sourcingSimulatorQuery.masteryLevel =
          this.sourcingSimulatorQuery.occupation?.level ?
            MasteryLevel[this.sourcingSimulatorQuery.occupation.level] :
            null;
        this.sourcingSimulatorQuery.forcedTechnical = !!this.sourcingSimulatorQuery.occupation?.isTechnical;
        this.sourcingSimulatorQuery.enableCapacityTolerance = true;
        this.sourcingSimulatorQuery.capacityTolerance = 85;
        this.sourcingSimulatorQuery.selectedErhgoClassificationsCodes = this.sourcingSimulatorQuery.occupation?.erhgoClassifications.map(c => c.code) || [];
        this.sourcingSimulatorQuery.selectedCriteria = this.sourcingSimulatorQuery.occupation?.criteriaValues || [];
        this.sourcingSimulatorQuery.enableClassifications = true;
        this.sourcingSimulatorQuery.enableMasteryLevel = true;
        this.criteriaRerenderKey++;
      } else {
        this.sourcingSimulatorQuery.occupation = null;
        this.sourcingSimulatorQuery.enableCapacityTolerance = false;
        this.sourcingSimulatorQuery.capacityTolerance = 0;
        this.sourcingSimulatorQuery.masteryLevel = null;
        this.sourcingSimulatorQuery.forcedTechnical = false;
        this.sourcingSimulatorQuery.selectedErhgoClassificationsCodes = [];
        this.sourcingSimulatorQuery.selectedCriteria = [];
        this.sourcingSimulatorQuery.enableClassifications = false;
        this.sourcingSimulatorQuery.enableMasteryLevel = false;
        this.criteriaRerenderKey++;
      }
    });
  }

  async fetchAllErhgoClassifications() {
    await this.safeCall(async () => {
      this._allErhgoClassifications = (await Vue.$api.listErhgoClassifications()).data.sort((a, b) => a.title.localeCompare(b.title));
    });
  }
}
