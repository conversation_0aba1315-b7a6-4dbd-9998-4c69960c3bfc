<template>
  <div v-if="service">
    <experiences-generator :service="service"/>
    <create-experience :service="service"/>
    <experiences-list :service="service"/>
  </div>
</template>

<script>
import CreateExperience from './CreateExperience';
import ExperiencesList from './ExperiencesList';
import UserExperiencesService from './UserExperiencesService';
import ExperiencesGenerator from '@/views/repository/user/edit/experiences/ExperiencesGenerator.vue';

export default {
  name: 'user-criteria',
  components: {ExperiencesGenerator, CreateExperience, ExperiencesList},
  props: {
    userId: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      service: null,
    };
  },
  async created() {
    this.service = new UserExperiencesService(this.userId);
    await this.service.fetchExperiences();
  },
};
</script>

