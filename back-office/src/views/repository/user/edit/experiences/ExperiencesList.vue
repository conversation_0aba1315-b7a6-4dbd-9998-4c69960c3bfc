<template>
  <v-list class="d-inline-block" two-line subheader dense v-if="service.experiences.length">
    <v-list-item v-for="experience in service.experiences" :key="experience.id">
      <v-list-item-content>
        <v-list-item-title>
          {{ experience.jobTitle }}
        </v-list-item-title>
        <v-list-item-subtitle>
          {{ experience.experienceType ? $t(`experience.${experience.experienceType}`) : '' }},
          {{ experience.durationType ? $t(`durationType.${experience.durationType}`) : '' }}
          {{ experience.duration ? $t(`duration.${experience.duration}`) : '' }}
          {{
            experience.organizationName ? ` chez
                ${experience.organizationName}` : ''
          }}
        </v-list-item-subtitle>
      </v-list-item-content>
      <v-list-item-action class="ma-0 pa-0">
        <v-btn icon @click="removeExperience(experience.id)">
          <v-icon small color="red">fa-trash-alt</v-icon>
        </v-btn>
      </v-list-item-action>
    </v-list-item>
  </v-list>
  <v-alert type="info" outlined dense v-else>Aucune expérience pour ce candidat.</v-alert>
</template>

<script>
import UserExperiencesService from './UserExperiencesService';

export default {
  name: 'user-experience',
  props: {
    service: {
      required: true,
      type: UserExperiencesService,
    },
  },
  methods: {
    removeExperience(id) {
      this.service.removeExperience(id);
    },
  },
};
</script>

