<template>
  <v-col>
    <v-alert :value="hasLabel"
             outlined
             :color="isCompleted ? 'success':'error'"
             :icon="isCompleted ? 'check':'error' ">
      {{ userFileImportStateType[service.fileImportState] }}
      {{ isCompleted ? "" : "(rafraichir la page pour suivre les mises à jour)" }}
    </v-alert>
    <v-row>
      <v-file-input v-model="file" label="Choisissez un fichier... (Types supportés : .pdf)" :disabled="isProcessing" />
      <v-btn
        color="primary"
        class="mt-3"
        @click="submitFile"
        :loading="loading || isProcessing"
        :disabled="!file"
      >Générer les expériences</v-btn>
    </v-row>
  </v-col>
</template>

<script>
import UserExperiencesService from '@/views/repository/user/edit/experiences/UserExperiencesService';
import { UserFileImportState } from 'erhgo-api-client';

export default {
  name: 'experiences-generator',
  data() {
    return {
      file: null,
      loading: false,
      UserFileImportState,
      userFileImportStateType: {
        WAITING: "CV en file d'attente...",
        ONGOING: "CV en cours d'extraction...",
        COMPLETED: 'Extraction réalisée avec succès.',
        ERROR: 'Une erreur est survenue, veuillez réessayer.',
      },
    };
  },
  props: {
    service: {
      required: true,
      type: UserExperiencesService,
    },
  },
  async created() {
    await this.service.fetchFileImportState();
  },
  computed: {
    isProcessing() {
      return (this.service.fileImportState === UserFileImportState.WAITING || this.service.fileImportState === UserFileImportState.ONGOING);
    },
    hasLabel() {
      return (this.service.fileImportState !== UserFileImportState.NONE);
    },
    isCompleted() {
      return this.service.fileImportState === UserFileImportState.COMPLETED;
    },
  },
  methods: {
    submitFile() {
      this.loading = true;
      this.service.generateExperiencesFromFile(this.file);
      this.loading = false;
      this.service.fileImportState = UserFileImportState.WAITING;
    },
  },
};
</script>
