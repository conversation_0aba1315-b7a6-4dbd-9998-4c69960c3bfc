<template>
    <v-row class="align-baseline hoverable" no-gutters>
        <v-col cols>
          <strong>{{ item.title }}&nbsp;</strong>
        </v-col>
        <v-col cols>
          <v-select v-model="item.selected" row dense hide-details
                    class="text-caption"
                    :items="[
                        {text: 'Attiré par ce domaine', value: true},
                        {text: 'Éviter ce domaine', value: false},
                        {text: 'Sans opinion', value: null},
                        ]" />
        </v-col>
    </v-row>
</template>

<script>
import {UserErhgoClassificationItem} from './UserErhgoClassificationService';

export default {
  name: 'user-erhgo-classification-selector',
  props: {
    item: {
      required: true,
      type: UserErhgoClassificationItem,
    },
  },
};
</script>

<style>
.hoverable:hover {
  background-color: #dadada !important;
}
</style>
