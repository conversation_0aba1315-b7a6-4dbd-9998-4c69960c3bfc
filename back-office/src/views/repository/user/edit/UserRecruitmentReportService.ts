import SafeService from 'odas-plugins/SafeService';
import {UserRecruitmentReportItem} from 'erhgo-api-client';
import Vue from 'vue';

export default class UserRecruitmentReportService extends SafeService {
  private _recruitmentReportsByUser: UserRecruitmentReportItem[] = [];

  constructor(private _userId: string) {
    super();
  }

  async fetchRecruitmentReportsByUser() {
    await this.safeCall(async () => {
      this._recruitmentReportsByUser = (await Vue.$api.listUserRecruitmentReports(this._userId)).data;
    });
  }

  get recruitmentReportsByUser() {
    return this._recruitmentReportsByUser;
  }
}
