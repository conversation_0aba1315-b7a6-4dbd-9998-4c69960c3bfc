<template>
  <v-row no-gutters>
    <v-col cols="12" md="6">
      <ul class="text-body-2" style="list-style: none;">
        <li class="my-2">
          <user-email-wrapper :transactional-blacklisted="transactionalBlacklisted">
            <v-icon small color="primary">arrow_forward_ios</v-icon>
            Email&nbsp;:
            <span><strong>{{
                candidateDetails.email
              }}</strong></span>
          </user-email-wrapper>
        </li>
        <user-sms-wrapper :is-sms-blacklisted="!!isSmsBlacklisted">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Téléphone&nbsp;:
          <span><strong>{{ candidateDetails.phoneNumber }}</strong></span>
        </user-sms-wrapper>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Accepte de recevoir les offres d'emplois&nbsp;:
          <template v-if="wannaReceiveJobOffers"><strong>{{
              receiveJobOffers
            }}</strong></template>
          <em v-else v-html="unknownLabel"/>
        </li>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Date de naissance&nbsp;:
          <template v-if="candidateDetails.birthDate"><strong>{{ candidateDetails.birthDate | formatDate }}</strong>
          </template>
          <em v-else v-html="unknownLabel"/>
        </li>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Organisation(s) associée(s)&nbsp;:
          <strong>{{
              (channels || []).join(', ') || 'Aucune'
            }}
            <v-row>
              <v-col>
                <v-btn color="primary"
                       x-small
                       outlined
                       :to="{
                          name: 'user-edit',
                          params: {
                            userId,
                          },
                        }"
                >
                  <v-icon x-small class="mr-2">fa-pencil</v-icon>
                  Modifier cet utilisateur
                </v-btn>
              </v-col>
            </v-row>
          </strong><br/>
          <span class="font-italic pl-5" v-if="isPrivate">
          <v-icon small class="pb-1">warning</v-icon>
          Cet utilisateur est associé à une organisation "utilisateurs privés", il dispose de droits limités</span>
        </li>
      </ul>
    </v-col>
    <v-col cols="12" md="6">
      <ul class="text-body-2" style="list-style: none;">
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Nombre de capacités&nbsp;: <strong>{{ progress.capacitiesCount || 'Aucune' }}</strong>
        </li>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Niveau de maîtrise&nbsp;:
          <template v-if="progress.masteryLevel"><strong>{{ progress.masteryLevel.toFixed(1) }}</strong></template>
          <em v-else v-html="unknownLabel"/>
        </li>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Nombre d'expériences professionnelles renseignées&nbsp;:
          <strong>{{ progress.experiencesCount || 'Aucune' }}</strong>
        </li>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Nombre de questions extra-professionnelles répondues&nbsp;:
          <strong>{{ extraProfessional.userAnsweredQuestionsCount }}/{{
              extraProfessional.totalQuestionsCount
            }}</strong>
        </li>
        <li class="my-2">
          <v-icon small color="primary">arrow_forward_ios</v-icon>
          Comportements précisés&nbsp;:
          <strong>{{ progress.hasBehaviors ? 'Oui' : 'Non' }}</strong>
        </li>
      </ul>
    </v-col>

  </v-row>
</template>

<script>
import { GeneralInformation, QuestionsSumupForUser, UserProfileProgress } from 'erhgo-api-client';
import UserEmailWrapper from '@/components/user/UserEmailWrapper';
import UserManagementService from '@/components/services/UserManagementService';
import UserSmsWrapper from '@/components/user/UserSmsWrapper';


export default {
  name: 'ProgressDetails',
  components: {UserEmailWrapper, UserSmsWrapper},
  props: {
    candidateDetails: {
      required: true,
      type: GeneralInformation,
    },
    progress: {
      required: true,
      type: UserProfileProgress,
    },
    extraProfessional: {
      required: true,
      type: QuestionsSumupForUser,
    },
    transactionalBlacklisted: {
      required: true,
      type: Boolean,
    },
    channels: {
      required: true,
      type: Array,
    },
    isPrivate: {
      required: true,
      type: Boolean,
    },
    organizationCode: {
      default: null,
      type: String,
    },
    userId: {
      required: true,
      type: String,
    },
    wannaReceiveJobOffers: {
      required: true,
      type: Boolean,
    },
  },
  data() {
    return {
      userManagementService: new UserManagementService(),
      unknownLabel: 'Non renseigné',
    };
  },
  computed: {
    isTransactionalBlacklisted() {
      return this.transactionalBlacklisted ? 'Oui' : 'Non';
    },
    receiveJobOffers() {
      return this.wannaReceiveJobOffers ? 'Oui' : 'Non';
    },
    isSmsBlacklisted() {
      return this.candidateDetails?.smsBlacklisted;
    },
    phoneNumber() {
      if (this.candidateDetails.smsBlacklisted) {
        return 'Téléphone : ' + this.candidateDetails?.phoneNumber + ' (Blacklisté)';
      } else {
        return 'Téléphone : ' + this.candidateDetails?.phoneNumber;
      }
    },
  },
};
</script>
