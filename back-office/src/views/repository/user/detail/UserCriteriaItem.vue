<template>
  <v-col class="text-body-2 my-2" cols="12" md="6" >
    <v-icon small color="primary">arrow_forward_ios</v-icon>
    {{ criterion.title }}
    <span v-if="noValueForUser">&nbsp;:&nbsp;Inconnu</span>
    <ul class="ml-4" style="list-style: none;" v-else>
      <template v-for="criteriaValue in criterion.criteriaValues">
        <user-criteria-value-item
            :criteria-value="criteriaValue"
            :user-criteria="userCriteria"
            :question-type="criterion.questionType"
            :key="criteriaValue.code"/>
      </template>
    </ul>
  </v-col>
</template>
<script>
import UserCriteriaValueItem from './UserCriteriaValueItem';

export default {
  name: 'user-criteria-item',
  components: {UserCriteriaValueItem},
  props: {
    criterion: {
      type: Object,
      required: true,
    },
    userCriteria: {
      type: Array,
      required: true,
    },
  },
  computed: {
    criterionValuesCode() {
      return this.criterion.criteriaValues.map(cv => cv.code);
    },
    noValueForUser() {
      return !this.userCriteria.some(c => this.criterionValuesCode.includes(c.code));
    },

  },
};
</script>
