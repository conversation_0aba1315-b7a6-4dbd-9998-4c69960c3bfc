<template>
  <v-dialog v-model="showDialog" persistent width="50%">
    <template v-slot:activator="{ on, attrs }">
      <v-btn class="mr-4"
             color="primary"
             v-bind="attrs"
             v-on="on"
             @click="showDialog = true"
      >
        <v-icon left>
          fa-asterisk
        </v-icon>
        Modifier le mot de passe
      </v-btn>
    </template>
    <v-card>
      <v-card-title
        class="text-h5 grey lighten-2"
        primary-title
      >
        Modification de mot de passe
        <v-spacer/>
      </v-card-title>
      <v-card-text>
        <div class="my-4">
          <v-icon color="error">error</v-icon>
          Attention, vous êtes sur le point de modifier le mot de passe de l'utilisateur
        </div>
        <password-field
          :passwordService="passwordService"
          @passwordFieldChanged="passwordFieldChanged"
          outlined
        />
        <v-row>
          <v-alert
            outlined
            type="success"
            class="ma-auto"
            :value="passwordHasBeenReset"
          >
            Le mot de passe a été correctement modifié
          </v-alert>
        </v-row>
      </v-card-text>
      <v-divider/>
      <v-card-actions>
        <card-btn
          :disabled="passwordService.loading || mailSent"
          dark
          class='float-left'
          :loading="passwordService.loading"
          @click="resendMail()"
        >
          {{ mailSent ? 'Mail renvoyé' : 'Renvoyer le mail initial' }}
        </card-btn>
        <v-spacer/>
        <v-btn
          color="primary"
          class='float-left'
          :disabled="!isValidPassword(newPassword)"
          :loading="passwordService.loading"
          @click="resetPassword()"
        >
          Valider
        </v-btn>
        <v-spacer/>
        <v-btn
          color="error"
          class='float-right'
          @click="showDialog = false"
        >
          {{ closeAction }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>

import PasswordField from '@/components/common/PasswordField';
import PasswordService from '@/components/services/PasswordService';
import CardBtn from 'odas-plugins/CardBtn';

export default {
  name: 'ResetPasswordDialog',
  components: {PasswordField, CardBtn},
  props: {
    userId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      newPassword: '',
      passwordHasBeenReset: false,
      showDialog: false,
      isValidPassword: PasswordService.isValidPassword,
      passwordService: new PasswordService(),
      mailSent: false,
    };
  },
  methods: {
    passwordFieldChanged(value) {
      this.newPassword = value;
      this.passwordHasBeenReset = false;
    },
    async resetPassword() {
      await this.passwordService.resetPassword(this.userId, this.newPassword);
      this.passwordHasBeenReset = true;
    },
    async resendMail() {
      await this.passwordService.resendInitialMail(this.userId);
      this.mailSent = true;
    },
  },
  computed: {
    closeAction() {
      return this.passwordHasBeenReset ? 'Fermer' : 'Annuler';
    },
  },
};
</script>

<style scoped>

</style>
