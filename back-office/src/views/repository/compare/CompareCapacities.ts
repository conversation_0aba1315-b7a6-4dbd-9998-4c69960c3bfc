import Vue from 'vue';
import { CapacityAndLevel, ErhgoOccupationSearch, EscoOccupationSummary, UserSummary } from 'erhgo-api-client';
import JobPicker, { Autocompleteable } from '@/components/organization/JobPicker';
import asyncDebounce from 'async-debounce';
import SafeService from 'odas-plugins/SafeService';

export default class CompareCapacities extends SafeService {

  private _erhgoJob: ErhgoOccupationSearch | null = null;
  private _escoJob: EscoOccupationSummary | null = null;
  private _job: Autocompleteable | null = null;
  private _profile: Autocompleteable | null = null;
  private _user: UserSummary | null = null;
  private _userMasteryLevel = 0;

  private _capacities: CapacityAndLevel[] = [];
  private _fullyQualified = false;

  private _escoItems: { uri: string; title: string }[] = [];
  private _erhgoItems: { code: string; title: string }[] = [];
  private _userItems: { id: string; firstName?: string; lastName?: string }[] = [];

  other: CompareCapacities | null = null;
  onUpdate?: () => unknown;
  onRefresh?: () => unknown;

  private _pagination = {
    page: 1,
    sortBy: 'code',
    descending: false,
    rowsPerPage: 50,
  };

  private _erhgoQuery = '';
  private _escoQuery = '';
  private _userQuery = '';

  constructor() {
    super();
  }

  private async doSearch() {
    let res;
    if (this._erhgoJob) {
      res = await Vue.$api.getErhgoOccupationCapacities(this._erhgoJob.code);
    } else if (this._escoJob) {
      res = await Vue.$api.getEscoOccupationCapacities(this._escoJob.uri);
    } else if (this._user) {
      res = await Vue.$api.getUserCapacities(this._user.id);
      this._userMasteryLevel = (await Vue.$api.getUserLevel(this._user.id)).data;
    } else if (this._profile && this._job) {
      res = await Vue.$api.getProfileCapacities(this._profile.id as string, this._job.id as string);
    } else if (this._job) {
      res = await Vue.$api.getJobCapacities(this._job.id as string);
    }
    return res;
  }

  async search() {
    await this.safeCall(async () => {
      if (this.hasCapacitiesHolder()) {
        const result = (await this.doSearch())!.data;
        this._fullyQualified = result.fullyQualified !== false;
        result.capacities.sort((a, b) => (a.capacity.code || '').localeCompare(b.capacity.code || ''));
        this._capacities = result.capacities;
      }
    });
    Vue.nextTick(() => this.onUpdate && this.onUpdate());
  }

  private debouncedSearchEsco = asyncDebounce(async () => {
    return await this.safeCall(async () => {
      if (this.escoQuery) {
        this._escoItems = (await Vue.$api.searchEscoOccupation(this.escoQuery)).data;
      }
    });
  }, 500);

  private debouncedSearchErhgo = asyncDebounce(async () => {
    return await this.safeCall(async () => {
      if (this.erhgoQuery) {
        this._erhgoItems = (await Vue.$api.searchOccupations(this.erhgoQuery, false)).data;
      }
    });
  }, 500);

  private debouncedSearchUser = asyncDebounce(async () => {
    return await this.safeCall(async () => {
      if (this.userQuery) {
        this._userItems = (await Vue.$api.searchFrontOfficeUser(20, 0, this.userQuery)).data.content;
      }
    });
  }, 500);

  private hasCapacitiesHolder() {
    return this._erhgoJob || this._escoJob || this._job || this._profile || this._user;
  }

  get erhgoJob() {
    return this._erhgoJob;
  }

  set erhgoJob(value: ErhgoOccupationSearch | null) {
    if (value) {
      this._erhgoJob = value;
      this.escoJob = null;
      this.user = null;
      this.capacitiesHolderChooser = null;
      this.search();
    } else {
      this._erhgoJob = null;
    }
  }

  get escoJob(): EscoOccupationSummary | null {
    return this._escoJob;
  }

  set escoJob(value: EscoOccupationSummary | null) {
    if (value) {
      this._escoJob = value;
      this.erhgoJob = null;
      this.user = null;
      this.capacitiesHolderChooser = null;
      this.search();
    } else {
      this._escoJob = null;
    }
  }

  get user(): UserSummary | null {
    return this._user;
  }

  set user(value: UserSummary | null) {
    if (value) {
      this._user = value;
      this.erhgoJob = null;
      this.escoJob = null;
      this.capacitiesHolderChooser = null;
      this.search();
    } else {
      this._user = null;
    }
  }

  get userMasteryLevel(): string {
    return this._userMasteryLevel?.toFixed(2);
  }

  set capacitiesHolderChooser(jobPicker: JobPicker | null) {
    if (jobPicker) {
      this._job = jobPicker.job;
      this._profile = jobPicker.profile;
      this.erhgoJob = null;
      this.escoJob = null;
      this.user = null;
      jobPicker.showDialog = false;
      this.search();
    } else {
      this._job = null;
      this._profile = null;
    }
  }

  get job() {
    return this._job;
  }

  get profile() {
    return this._profile;
  }

  get capacities() {
    return this._capacities;
  }

  get fullyQualified() {
    return this._fullyQualified;
  }

  get erhgoQuery() {
    return this._erhgoQuery;
  }

  set erhgoQuery(query) {
    this._erhgoQuery = query;
    this.debouncedSearchErhgo.fn();
  }

  get escoQuery() {
    return this._escoQuery;
  }

  set escoQuery(query) {
    this._escoQuery = query;
    this.debouncedSearchEsco.fn();
  }

  get userQuery(): string {
    return this._userQuery;
  }

  set userQuery(query: string) {
    this._userQuery = query;
    this.debouncedSearchUser.fn();
  }

  get escoItems(): { uri: string; title: string }[] {
    return this._escoItems;
  }

  get erhgoItems(): { code: string; title: string }[] {
    return this._erhgoItems;
  }

  get userItems(): { id: string; firstName?: string; lastName?: string }[] {
    return this._userItems;
  }

  get holderDescriptor() {
    let title = '';
    if (this._erhgoJob) {
      title = `Métier ERHGO : ${this._erhgoJob.title}`;
    } else if (this._escoJob) {
      title = `Métier ESCO : ${this._escoJob.title}`;
    } else if (this._user) {
      title = `Utilisateur : ${this._user.firstName || ''} ${this._user.lastName || ''}`;
    } else if (this._profile) {
      title = `Profil : ${this._profile.title}, `;
    }
    if (this._job) {
      title += `Poste : ${this._job.title}`;
    }
    return title;
  }

  get capacitiesData() {
    return this._capacities ? this._capacities
      .map(c => {
        const capacity = c.capacity;
        let commonCapacity = null;
        let lowerLevel = null;
        if (this.other && this.other.capacities) {
          const otherCapacity = this.other.capacities.filter(o => o.capacity.code === capacity.code)[0];
          commonCapacity = !!otherCapacity;
          lowerLevel = otherCapacity?.masteryLevel && c.masteryLevel && otherCapacity.masteryLevel > c.masteryLevel;
        }
        return {
          code: capacity.code,
          title: capacity.title,
          commonCapacity,
          lowerLevel,
          masteryLevel: c.masteryLevel,
        };
      }) : [];
  }

  get pagination() {
    return this._pagination;
  }

  set pagination(pagination) {
    this._pagination = pagination;
  }

  get headers() {
    return [
      {
        text: 'Nom',
        align: 'center',
        sortable: true,
        value: 'title',
      },
      {
        text: 'Code',
        align: 'center',
        sortable: true,
        value: 'code',
      },
      {
        text: 'Niveau de maîtrise',
        align: 'center',
        sortable: true,
        value: 'level',
      },
    ];
  }

}

