import Vue from 'vue';
import SafeService from 'odas-plugins/SafeService';

import {ErhgoClassification} from 'erhgo-api-client';

export default class OccupationErhgoClassificationService extends SafeService {
  private _erhgoClassifications: ErhgoClassification[] = [];

  constructor(private _selectedClassifications: string[], private _occupationId: string) {
    super();
  }

  async fetchAllErhgoClassifications() {
    await this.safeCall(async () => {
      this._erhgoClassifications = (await Vue.$api.listErhgoClassifications()).data.sort((a, b) => a.title.localeCompare(b.title));
    });
    this.success = false;
  }

  async generateErhgoClassifications() {
    await this.safeCall(async () => {
      if (this._occupationId) {
        this._selectedClassifications = (await Vue.$api.generateOccupationClassification(this._occupationId)).data.map(c => c.code);
      }
    });
    this.success = false;
  }

  get erhgoClassifications() {
    return this._erhgoClassifications;
  }

  get selectedClassifications() {
    return this._selectedClassifications;
  }

  set selectedClassifications(selectedClassifications) {
    this.safeCall(async () => (await Vue.$api.updateErhgoOccupationErhgoClassifications({
      id: this._occupationId,
      erhgoClassifications: selectedClassifications,
    }))).then(() => this._selectedClassifications = selectedClassifications);
  }
}
