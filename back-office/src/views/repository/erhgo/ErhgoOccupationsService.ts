import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import {ErhgoOccupationPage, ErhgoSearchOrder, SortDirection} from 'erhgo-api-client';

export default class ErhgoOccupationsService extends SafeService {

  private _occupationPage: ErhgoOccupationPage | null = null;


  async fetchOccupationPage(size: number, page: number, by: ErhgoSearchOrder[], direction: boolean[], query: string, activityId: string) {
    await this.safeCall(async () => {
      this._occupationPage = (await Vue.$api.erhgoOccupationPage(size, page, query, activityId, by, this.getDirectionList(direction))).data;
    });
  }


  get occupationPage(): ErhgoOccupationPage | null {
    return this._occupationPage;
  }


  getDirectionList(directions: boolean[]) {
    return directions.map(value => !value ? SortDirection.ASC : SortDirection.DESC);
  }
}
