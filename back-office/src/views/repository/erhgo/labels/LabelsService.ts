import SafeService from 'odas-plugins/SafeService';
import {ErhgoOccupationDetail, ErhgoOccupationsForLabel} from 'erhgo-api-client';
import {v4 as uuid} from 'uuid';
import Vue from 'vue';
import asyncDebounce from 'async-debounce';
import _ from 'lodash';

class LabelItem {

  _id: string;
  constructor(private _title: string, private _service: LabelsService) {
    this._id = uuid();
  }

  set title(title: string) {
    this._title = title;
    if (title && title.replace(/\s/g, '').length) {
      this._service.update();
    }
  }

  get title() {
    return this._title;
  }

  get id() {
    return this._id;
  }
}

export default class LabelsService extends SafeService {

  private _title: string;
  private _labels: LabelItem[] = [];

  constructor(private _initialOccupation: ErhgoOccupationDetail) {
    super();
    this._title = _initialOccupation.title;
    this._labels = _initialOccupation.alternativeLabels.map(title => new LabelItem(title, this));
  }

  fetchDuplicatedOccupationsForLabel = async (label: string): Promise<ErhgoOccupationsForLabel[]> => {
    if (label.length) {
      return this.safeCall(async () => {
        return (await Vue.$api.getErhgoOccupationsWithLabels([label])).data.filter(o => o.id !== this._initialOccupation.id);
      });
    } else {
      return [];
    }
  };

  get title() {
    return this._title;
  }

  set title(title) {
    this._title = title;
    this.update();
  }

  get alternative() {
    return this._labels;
  }

  update() {
    this.debouncedUpdate.fn();
  }

  removeAlternativeLabel(label: LabelItem) {
    this._labels = this._labels.filter(l => label.id !== l.id);
    this.update();
  }

  setAsDefault(label: LabelItem) {
    const labelForTitle = new LabelItem(this.title, this);
    this._labels = [labelForTitle, ...this._labels.filter(l => label.id !== l.id)];
    this.title = label.title;
  }

  addAlternativeLabel() {
    this._labels = [...this._labels, new LabelItem('', this)];
  }

  private debouncedUpdate = asyncDebounce(async () => {
    return this.safeCall(async () => {
      const title = this._title;
      if (title && title.replace(/\s/g, '').length && !this.isLabelAlreadyUsedInTitle()) {
        const id = this._initialOccupation.id;
        const alternativeLabels = _.uniq(this._labels.map(l => l.title).filter(l => !!l));
        await Vue.$api.editAlternativeLabels({id, alternativeLabels, title});
        this._initialOccupation.title = title;
      }
    });
  }, 500);


  isLabelAlreadyUsedInTitle() {
    return this._labels.filter(value => value.title && (value.title.trim() === this._title?.trim())).length > 0;
  }
}
