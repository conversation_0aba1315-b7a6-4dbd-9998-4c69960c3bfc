import SafeService from 'odas-plugins/SafeService';
import { BehaviorPage, ErhgoSearchOrder, SortDirection } from 'erhgo-api-client';
import Vue from 'vue';

export default class BehaviorsService extends SafeService {
  
  private _behaviorPage: BehaviorPage | null = null;

  async fetchBehaviorPage(size: number, page: number, by: ErhgoSearchOrder, direction: SortDirection, query: string) {
    await this.safeCall(async () => {
      this._behaviorPage = (await Vue.$api.behaviorPage(size, page, query, by, direction)).data;
    });
  }

  get behaviorPage(): BehaviorPage | null {
    return this._behaviorPage;
  }

  set behaviorPage(value: BehaviorPage | null) {
    this._behaviorPage = value;
  }
}
