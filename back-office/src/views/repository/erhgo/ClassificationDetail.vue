<template>
  <v-row>
    <v-col cols="12">
      <v-expansion-panels>
        <v-expansion-panel>
          <v-expansion-panel-header>
            <h2 id="classificationTitle" class="d-flex align-center">
              <v-icon class="mr-2"
                      color="primary">
                account_balance
              </v-icon>
              Classifications
            </h2>
          </v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-row no-gutters id="classification" v-if="erhgoOccupationService">
              <v-col cols="12">
                <p class="font-weight-bold mb-0">Spécifications</p>
              </v-col>
              <classification-selector :occupation-service="erhgoOccupationService"/>
            </v-row>
            <v-divider class="mt-5"/>
            <v-row id="technical" v-if="erhgoOccupationService">
              <v-col cols="12">
                <v-checkbox
                    class="mt-0"
                    hide-details
                    label="Il s'agit d'un métier technique"
                    v-model="isTechnical"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <p class="font-weight-bold">ESCO / ISCO</p>
                <v-tooltip top>
                  <template v-slot:activator="{ on, attrs }">
                    <v-autocomplete empty
                                    v-bind="attrs"
                                    v-on="on"
                                    v-model="dummyModel"
                                    :items="escoItems"
                                    :search-input.sync="escoSearch"
                                    :loading="escoOccupationsService.loading"
                                    label="Saisir un libellé de métier ESCO pour l'associer à ce métier erhgo"
                                    @change="onAddESCOOcupation"
                                    prepend-icon="add_box"
                                    item-text="title"
                                    close-on-select
                                    no-filter
                                    hide-no-data
                                    return-object/>
                  </template>
                  <span>Les aptitudes d'un métier ESCO associé sont ajoutées au métier ERHGO.
                    <span v-if="!isQualified">
                      <br/>Les activités, comportements et contextes issus de la qualification actuelle des aptitudes seront également associés au métier ERHGO.
                      <br/>Vous pourrez les supprimer manuellement.
                    </span>
                  </span>
                </v-tooltip>
                <v-list id="escoOccupations" v-if="escoOccupations && escoOccupations.length > 0" class="py-0">
                  <v-list-item v-for="esco in escoOccupations" :key="esco.uri" class="itemBorder my-2">
                    <v-list-item-content>
                      <span>URI ESCO&nbsp;: <strong>{{ esco.uri }}</strong></span>
                      <span>Titre ESCO&nbsp;: <strong>{{ esco.title }}</strong></span>
                      <span class="mt-2">Groupe ISCO&nbsp;: <strong>{{ esco.isco.iscoGroup }}</strong></span>
                      <span>Titre ISCO&nbsp;: <strong>{{ esco.isco.title }}</strong></span>
                    </v-list-item-content>
                    <v-list-item-action>
                      <div>
                        <v-btn icon
                               color="primary"
                               class="mx-1"
                               :to="{name: 'occupation_detail', params: {'uri': esco.uri}}">
                          <v-icon>visibility</v-icon>
                        </v-btn>
                        <v-btn icon
                               class="mx-1"
                               color="error"
                               :loading="erhgoOccupationService.loading"
                               @click="onRemoveESCOOcupation(esco)">
                          <v-icon>delete</v-icon>
                        </v-btn>
                      </div>
                    </v-list-item-action>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
            <v-row id="romes" v-if="romesService">
              <v-col cols="12">
                <p class="font-weight-bold mb-0">ROME
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on }">
                      <v-btn icon
                             v-on="on"
                             small
                             color="primary"
                             :loading="romesService.loading"
                             @click="generateRomeOccupations"
                             class="ml-auto">
                        <v-icon small
                                color="primary">
                          fa-recycle
                        </v-icon>
                      </v-btn>
                    </template>
                    <span class="text-center">les ou le code rome sera généré automatiquement par l'IA </span>
                  </v-tooltip>
                </p>

                <v-autocomplete empty
                                v-model="dummyModel"
                                :items="romeItems"
                                :search-input.sync="romeSearch"
                                :loading="romesService.loading"
                                label="Saisir un libellé de métier ou un code ROME pour l'associer à ce métier erhgo"
                                @change="onAddROMEOcupation"
                                prepend-icon="add_box"
                                item-text="code"
                                close-on-select
                                no-filter
                                hide-no-data
                                return-object>
                  <template v-slot:item="data">
                    <v-list-item-content>
                      <v-list-item-title>{{ data.item.code }}</v-list-item-title>
                      <v-list-item-subtitle>
                        {{ data.item.title }}
                      </v-list-item-subtitle>
                    </v-list-item-content>
                  </template>
                </v-autocomplete>
                <v-list v-if="romes && romes.length > 0">
                  <v-list-item v-for="rome in romes" :key="rome.code">
                    <v-list-item-content>
                      <v-list-item-title>
                        <v-chip>{{ rome.code }}</v-chip>
                        <span class="ml-2">{{ rome.title }}</span>
                      </v-list-item-title>
                    </v-list-item-content>
                    <v-list-item-action>
                      <v-btn icon
                             color="error"
                             :loading="erhgoOccupationService.loading"
                             @click="onRemoveROMEOcupation(rome)"
                      >
                        <v-icon>delete</v-icon>
                      </v-btn>
                    </v-list-item-action>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col cols="12">
                <span class="font-weight-bold mb-0">
                  Codes ROME accessibles <span class="text-caption">(Les métiers ayant au moins un de ces codes ROME sont accessibles depuis ce métier)&nbsp;:</span><br />
                </span>
                <span v-for="rome in accessibleRomeOccupations" :key="rome.code">{{ rome.title }} ({{ rome.code
                  }}), </span>
              </v-col>
              <v-col cols="12">
                <span class="font-weight-bold mb-0">
                  Accessible depuis les codes ROME
                  <span class="text-caption">
                    (Les métiers ayant au moins un de ces codes ROME donnent accès à ce métier)&nbsp;:
                  </span><br />
                </span>
                <span v-for="rome in accessibleFromRomeOccupations" :key="rome.code">{{ rome.title }} ({{ rome.code
                  }}), </span>
              </v-col>
            </v-row>
            <v-row id="work-environment" v-if="workEnvironmentService">
              <v-col cols="12">
                <p class="font-weight-bold mb-0">Environments de travail</p>
                <v-select multiple
                          :loading="workEnvironmentService.loading"
                          :items="workEnvironments"
                          v-model="workEnvironmentsItems"
                          label="Choisissez des environments de travail"
                          item-text="title"
                          prepend-icon="add_box"
                          clearable
                          return-object/>
              </v-col>
            </v-row>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-col>
  </v-row>
</template>

<script>
import RomesService from './RomesService';
import _ from 'lodash';
import ErhgoOccupationService from './ErhgoOccupationService';
import EscoOccupationsService from '../esco/EscoOccupationsService';
import WorkEnvironmentService from './WorkEnvironmentService';
import ClassificationSelector from './erhgoClassification/ClassificationSelector';

export default {
  name: 'ClassificationDetail',
  components: {ClassificationSelector},
  props: {
    erhgoOccupationService: ErhgoOccupationService,
    isQualified: Boolean,
  },
  data() {
    return {
      romesService: null,
      escoOccupationsService: null,
      workEnvironmentService: null,
      romeSearch: null,
      escoSearch: null,
      dummyModel: null,
      romeItems: [],
      escoItems: [],
      workEnvironmentsItems: [],
      isTechnical: false,
    };
  },
  async created() {
    this.romesService = new RomesService();
    this.escoOccupationsService = new EscoOccupationsService();
    this.workEnvironmentService = await new WorkEnvironmentService().fetchEnvironments();
    this.workEnvironmentsItems = this.occupation.workEnvironments;
    this.isTechnical = this.occupation.isTechnical;
    this.result = this.romes;
  },
  computed: {
    occupation() {
      return this.erhgoOccupationService?.occupation;
    },
    romes() {
      return this.occupation.romeOccupations;
    },
    accessibleRomeOccupations() {
      return this.occupation.accessibleRomeOccupations;
    },
    accessibleFromRomeOccupations() {
      return this.occupation.accessibleFromRomeOccupations;
    },
    escoOccupations() {
      return this.occupation.escoOccupations;
    },
    workEnvironments() {
      return this.workEnvironmentService?.workEnvironments || [];
    },
  },
  methods: {
    async onAddROMEOcupation(rome) {
      if (rome) {
        await this.erhgoOccupationService.linkRomeToErhgoOccupation(this.occupation.id, rome);
      }
      this.dummyModel = null;
      this.romeSearch = null;
      this.romeItems = [];
    },
    async generateRomeOccupations() {
      await this.erhgoOccupationService.generateRomeOccupations(this.occupation.id);
    },
    async onRemoveROMEOcupation(rome) {
      await this.erhgoOccupationService.deleteLinkRomeToErhgoOccupation(this.occupation.id, rome.code);
      this.romeItems = [];
    },
    async onAddESCOOcupation(esco) {
      if (esco) {
        await this.erhgoOccupationService.linkEscoOccupationToErhgoOccupation(this.occupation.id, esco.uri);
      }
      this.dummyModel = null;
      this.escoSearch = null;
      this.escoItems = [];
    },
    onRemoveESCOOcupation(esco) {
      this.$emit('onRemoveESCO', esco);
    },
    async confirmRemoveESCOOcupation(esco) {
      await this.erhgoOccupationService.unlinkEscoOccupationFromErhgoOccupation(this.occupation.id, esco.uri);
      this.escoItems = [];
    },
  },
  watch: {
    romeSearch: _.debounce(async function doSearch(search) {
      if (search && search.length > 0) {
        await this.romesService.fetchRomePage(100, 0, this.romeSearch);
        this.romeItems = this.romesService.romePage.content.filter(o => !this.romes.map(ca => ca.code).includes(o.code));
      }
    }, 200),
    escoSearch: _.debounce(async function doSearch(search) {
      if (search && search.length > 0) {
        await this.escoOccupationsService.fetchOccupationPage(100, 0, this.escoSearch);
        this.escoItems = this.escoOccupationsService.occupationPage.content.filter(o => !this.escoOccupations.map(esco => esco.uri).includes(o.uri));
      }
    }, 200),
    workEnvironmentsItems: _.debounce(async function updateWorkEnvironments(newValues) {
      await this.erhgoOccupationService.updateWorkEnvironments({
        occupationId: this.occupation.id,
        workEnvironmentCodes: newValues?.map(x => x.code) || [],
      });
    }, 200),
    isTechnical: _.debounce(async function updateSpecification() {
      await this.erhgoOccupationService.updateSpecifications({
        occupationId: this.occupation.id,
        isTechnical: this.isTechnical,
      });
    }, 200),
  },
};
</script>

<style>
.itemBorder {
  border: thin solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}
</style>
