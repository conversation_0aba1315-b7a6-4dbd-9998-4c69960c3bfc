<template>
  <v-list two-line>
    <v-list-item v-if="withCheckbox && displayItems.length">
      <v-list-item-action class="mb-0">
        <v-alert :value="true"
                 v-if="displayError"
                 outlined
                 color="error"
                 icon="warning">
          Vous ne pouvez préciser que trois activités comme essentielles.
        </v-alert>
        <span class="text-caption">Essentielle ? ({{ essentialActivities }}/3 maximum) - {{
            displayItems.length
          }} activités associées au poste</span>
      </v-list-item-action>
    </v-list-item>
    <template v-for="(item, index) in displayItems">
      <v-list-item :key="item.id">
        <v-list-item-action v-if="withCheckbox">
          <v-checkbox
            v-model="item.selected"
            @change="selected => onChangeState(item.id, selected)"
            :disabled="!item.selected && hasReachedActivitiesLimit"
            color="primary"
          />
        </v-list-item-action>
        <entity-referential-list-item :item-text="itemText"
                                      :is-red="idsToRemove.includes(item.id)"
                                      :sub-title-text="subTitleText"
                                      :subtitle-resolver="subtitleResolver"
                                      :item="item"/>
        <v-list-item-action>
          <v-icon @click="onDeleteItem(item.id)"
                  :disabled="idsToRemove.includes(item.id)">
            cancel
          </v-icon>
        </v-list-item-action>
        <v-list-item-action v-if="updateItem">
          <v-icon @click="onUpdateItem(item.id)">edit</v-icon>
        </v-list-item-action>
      </v-list-item>
      <v-divider :key="`${item.id}-div`" v-if="index !== displayItems.length - 1"/>
    </template>
  </v-list>
</template>

<script>
import EntityReferentialListItem from './EntityReferentialListItem';

export default {
  name: 'EntityReferentialList',
  components: {EntityReferentialListItem},
  props: {
    subtitleResolver: Function,
    displayItems: Array,
    withCheckbox: Boolean,
    itemText: String,
    subTitleText: String,
    updateItem: Boolean,
  },
  data() {
    return {
      idsToRemove: [],
      displayError: false,
      essentialActivitiesCount: 0,
    };
  },
  created() {
    this.updateEssentialActivitiesCount();
  },
  computed: {
    essentialActivities() {
      return this.displayItems.filter(a => a.selected === true).length;
    },
    hasReachedActivitiesLimit() {
      return this.essentialActivitiesCount >= 3;
    },
  },
  methods: {
    onDeleteItem(id) {
      this.idsToRemove.push(id);
      this.$emit('removeItem', id);
    },
    onChangeState(id, selected) {
      this.updateEssentialActivitiesCount();
      this.displayError = selected && this.hasReachedActivitiesLimit;
      this.$emit('changeState', {id, selected});
    },
    onUpdateItem(id) {
      this.$emit('updateItem', id);
    },
    updateEssentialActivitiesCount() {
      this.essentialActivitiesCount = this.displayItems.filter(x => x.selected).length;
    },
  },
};
</script>
