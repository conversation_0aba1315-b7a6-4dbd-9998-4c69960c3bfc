<template>
  <v-card flat>
    <v-card-text>
      <v-autocomplete empty
                      hint="Maximum : 16 par métier"
                      persistent-hint
                      v-model="dummyModel"
                      :items="suggestedItems"
                      :search-input.sync="search"
                      :loading="loading"
                      :label="label"
                      @change="onAddItem"
                      prepend-icon="search"
                      :item-text="itemText"
                      close-on-select
                      no-filter
                      hide-no-data
                      :append-outer-icon="(createItem && search) ? 'add_circle' : null"
                      @click:append-outer="onCreateItem"
                      :disabled="disabledAdd"
                      return-object>
        <template v-slot:item="data">
          <v-list-item-content>
            <v-list-item-title>{{ data.item[itemText] }}</v-list-item-title>
            <v-list-item-subtitle v-if="subtitleResolver">
              {{subtitleResolver(data.item) | truncate(200)}}
            </v-list-item-subtitle>
          </v-list-item-content>
        </template>
      </v-autocomplete>
    </v-card-text>
    <entity-referential-list :display-items="selectedItems"
                             @changeState="onChangeState"
                             @removeItem="onDeleteItem"
                             @updateItem="onUpdateItem"
                             :item-text="itemText"
                             :update-item="updateItem"
                             :subtitle-resolver="subtitleResolver"
                             :with-checkbox="withCheckbox"
    />
  </v-card>
</template>

<script>
import EntityReferentialList from './EntityReferentialList';
import _ from 'lodash';

export default {
  name: 'ErhgoOccupationActivities',
  components: { EntityReferentialList },
  props: {
    maxSelected: Boolean,
    subtitleResolver: Function,
    suggestedItems: Array,
    selectedItems: Array,
    loading: Boolean,
    label: String,
    itemText: String,
    withCheckbox: Boolean,
    createItem: Boolean,
    updateItem: Boolean,
  },
  data() {
    return {
      search: null,
      dummyModel: null,
    };
  },
  methods: {
    onAddItem(item) {
      if (item) {
        this.$emit('addItem', item);
      }
      this.$nextTick(() => {
        this.dummyModel = null;
        this.search = null;
      });
    },
    onDeleteItem(id) {
      this.$emit('removeItem', id);
    },
    onChangeState({id, selected}) {
      this.$emit('changeState', {id, selected});
    },
    onCreateItem() {
      this.$emit('createItem', this.search);
    },
    onUpdateItem(id) {
      this.$emit('updateItem', id);
    },
  },
  watch: {
    search: _.debounce(async function doSearch(search) {
      if (search && search.length > 0) {
        this.$emit('fetchEntity', search);
      }
    }, 200),
  },
  computed: {
    disabledAdd() {
      return this.loading || this.maxSelected;
    },
  },
};
</script>
