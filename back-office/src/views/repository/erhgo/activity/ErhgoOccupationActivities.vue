<template>
  <div id="activities">
    <v-col cols="12">
      <span class="text--secondary text-caption mb-0"> Ajouter des activités&nbsp;:</span>
      <v-tooltip bottom>
        <template v-slot:activator="{ on, attrs }">
          <v-btn icon
                 v-on="on"
                 small
                 color="primary"
                 :loading="erhgoOccupationActivityService.loading"
                 @click="qualifyOccupation"
                 v-bind="attrs"
                 class="ml-auto">
            <v-icon small
                    color="primary">
              fa-recycle
            </v-icon>
          </v-btn>
        </template>
        <span class="text-center">les activités seront générées automatiquement par l'IA </span>
      </v-tooltip>

      <v-dialog v-model="showDialog" persistent width="35%">
        <v-card>
          <v-card-title
            class="text-h5 grey lighten-2"
            primary-title
          >
            Qualification du métier erhgo
            <v-spacer/>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-alert
                outlined
                type="error"
                class="ma-auto mt-4"
              >
                Attention, vous êtes sur le point de qualifier un métier n'ayant  pas de CODE ROME.
                Cela peut impacter la qualité de la qualification des activités.
              </v-alert>
            </v-row>
          </v-card-text>
          <v-divider/>
          <v-card-actions>
            <v-btn
              color="primary"
              class='float-right'
              :loading="erhgoOccupationActivityService.loading"
              @click="generateOccupationActivities"
            >
              Continuer la qualification
            </v-btn>
            <v-spacer/>
            <v-btn
              color="error"
              class='float-right'
              @click="showDialog = false"
            >
              Ajouer un code Rome
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-col>

    <edit-referential-entity :selected-items="occupationActivities"
                             :suggested-items="activities"
                             :maxSelected="maxSelected"
                             with-checkbox
                             :loading="activitySearchService.loading"
                             label="Ajouter des activités au métier erhgo"
                             :subtitle-resolver="subtitleResolver"
                             item-text="title"
                             create-item
                             update-item
                             @addItem="addActivity"
                             @removeItem="removeActivity"
                             @changeState="changeActivityState"
                             @fetchEntity="fetchEntity"
                             @updateItem="editActivity"
                             @createItem="createActivity"
                             @updateActivities="updateActivities"
                             :key="`${refreshId}`"
    />
    <errorDialog v-model="erhgoOccupationActivityService.showAPIError"/>
    <activity-edit-modal v-model="showCreateActivityModal"
                         @closed="closeActivityModal"
                         @submit="onActivityUpdated"
                         :title="newActivityTitle"
                         :edit-activity-id="editActivityId"
                         :key="editActivityId"
                         :activity-type="ActivityType.JOB"/>
    <ul>
      <v-row>
        <v-col>
          <div v-if="activitiesToAdd.length >0">
            <span class="font-weight-bold">Activités à ajouter&nbsp;:</span>
            <li v-for="activity in activitiesToAdd" :key="activity.id"> {{ activity.title }}</li>
          </div>
        </v-col>
        <v-col>
          <div v-if="activitiesIdsToRemove.length >0">
            <span class="font-weight-bold red--text">Activités à supprimer&nbsp;:</span>
            <li v-for="activity in activitiesToRemove" class="red--text" :key="activity.id">{{ activity.title }}</li>
          </div>
        </v-col>
      </v-row>

    </ul>
    <v-row>
      <v-col>
        <v-btn @click="updateActivities"
               v-if="activitiesIdsToRemove.length > 0 || activitiesToAdd.length > 0"
               color="primary"
               :loading="erhgoOccupationActivityService.loading"
        >
          Enregistrer les modifications
        </v-btn>
      </v-col>
      <v-col cols="2">
        <v-btn
            @click="cancelActivities"
            v-if="activitiesIdsToRemove.length >0 || activitiesToAdd.length >0"
            color="red"
            :loading="erhgoOccupationActivityService.loading"
        >
          Annuler
        </v-btn>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import ErhgoOccupationDetail from '../ErhgoOccupationDetail';
import ErhgoOccupationActivityService from './ErhgoOccupationActivityService';
import EditReferentialEntity from '../common/EditReferentialEntity';
import ErrorDialog from '@/components/common/ErrorDialog.vue';
import ActivitySearchService from '../../activity/ActivitySearchService';
import {ActivityType, MandatoryState} from 'erhgo-api-client';
import ActivityEditModal from '@/views/repository/activity/ActivityEditModal.vue';

export default {
  name: 'ErhgoOccupationActivities',
  components: {EditReferentialEntity, ErrorDialog, ActivityEditModal},
  props: {
    occupation: ErhgoOccupationDetail,
  },
  data() {
    return {
      erhgoOccupationActivityService: ErhgoOccupationActivityService,
      activitySearchService: ActivitySearchService,
      ActivityType,
      showCreateActivityModal: false,
      newActivityTitle: null,
      editActivityId: null,
      activitiesToAdd: [],
      activitiesIdsToRemove: [],
      activitiesToRemove: [],
      errorOnAddOrRemove: false,
      refreshId: 1,
      showDialog: false,
    };
  },
  computed: {
    maxSelected() {
      return this.occupationActivities.length + this.activitiesToAdd.length >= 16;
    },
    occupationActivities() {
      return this.erhgoOccupationActivityService.activities;
    },
    activities: {
      set(newValue) {
        this.activitySearchService.activities = newValue;
      },
      get() {
        if (this.activitySearchService.activities) {
          return this.activitySearchService.activities.filter(a => !this.occupationActivities
            .map(a2 => a2.id).includes(a.id)).filter(a => !this.activitiesToAdd.map(a2 => a2.id).includes(a.id));
        } else {
          return [];
        }
      },
    },
  },
  methods: {
    subtitleResolver(activity) {
      return activity.inducedCapacities.map((o) => `${o.code} - ${o.title}`).join(', ');
    },
    async fetchEntity(query) {
      this.activitySearchService.query = query;
      await this.activitySearchService.search();
    },

    async updateActivities() {
      try {
        if (this.activitiesToAdd.length) {
          await this.erhgoOccupationActivityService.addActivitiesToOccupation(this.activitiesToAdd);
          this.activitiesToAdd = [];
        }
        if (this.activitiesIdsToRemove) {
          await this.erhgoOccupationActivityService.removeActivitiesFromOccupation(this.activitiesIdsToRemove);
          this.activitiesToRemove = [];
          this.activitiesIdsToRemove = [];
        }
        this.refreshId++;
      } catch (e) {
        this.errorOnAddOrRemove = true;
      }
    },

    async cancelActivities() {
      this.activitiesToAdd = [];
      this.activitiesIdsToRemove = [];
      this.activitiesToRemove = [];
      this.refreshId++;
    },

    addActivity(activity) {
      this.activitiesToAdd.push(activity);
    },
    removeActivity(activityId) {
      this.activitiesIdsToRemove.push(activityId);
      this.activitiesToRemove = this.occupationActivities.filter(a => this.activitiesIdsToRemove.includes(a.id));
    },

    async changeActivityState({id, selected}) {
      await this.erhgoOccupationActivityService.updateMandatoryState(id, selected);
      this.occupation.activities.reduce((activities, el) => {
        if (el.activity.id === id) {
          el.state = selected ? MandatoryState.ESSENTIAL : MandatoryState.OPTIONAL;
        }
        activities.push(el);
        return activities;
      }, []);
    },
    createActivity(title) {
      this.newActivityTitle = title;
      this.showCreateActivityModal = true;
    },
    editActivity(activityId) {
      this.editActivityId = activityId;
      this.showCreateActivityModal = true;
    },
    closeActivityModal() {
      this.editActivityId = null;
      this.showCreateActivityModal = false;
    },
    onActivityUpdated(activity) {
      this.editActivityId = null;
      this.erhgoOccupationActivityService.updateActivities(activity);
      this.closeActivityModal();
    },
    async generateOccupationActivities() {
      await this.erhgoOccupationActivityService.generateOccupationActivities();
      this.showDialog = false;
      this.refreshId++;
    },
    async qualifyOccupation() {
      if (this.occupation.romeOccupations === null || this.occupation.romeOccupations.length === 0) {
        this.showDialog = true;
      } else {
        await this.generateOccupationActivities();
      }
    },

  },
  created() {
    this.erhgoOccupationActivityService = new ErhgoOccupationActivityService(this.occupation);
    this.activitySearchService = new ActivitySearchService(ActivityType.JOB);
  },
};
</script>
