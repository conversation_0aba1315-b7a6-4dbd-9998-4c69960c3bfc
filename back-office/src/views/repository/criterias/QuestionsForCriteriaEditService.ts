import SafeService from 'odas-plugins/SafeService';
import {Criteria} from 'erhgo-api-client';
import Vue from 'vue';

export default class QuestionsForCriteriaEditService extends SafeService {
  constructor() {
    super();
    this.fetchCriterias();
  }

  private _criterias: Criteria[] = [];

  async fetchCriterias() {
    await this.safeCall(async () => {
      this._criterias = (await Vue.$api.getCriteria()).data;
    });
  }

  getCriteria() {
    return this._criterias;
  }
}
