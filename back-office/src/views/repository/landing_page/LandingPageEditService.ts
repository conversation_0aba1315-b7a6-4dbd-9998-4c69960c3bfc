import Vue from 'vue';
import {v4 as uuid} from 'uuid';
import SafeService from 'odas-plugins/SafeService';
import {baseUrl} from 'odas-plugins/base-url';
import asyncDebounce from 'async-debounce';
import {LandingPageDetail} from 'erhgo-api-client';
import {AxiosError} from 'axios';

export default class LandingPageEditService extends SafeService {

  private readonly _id: string = '';
  private _urlKey = '';
  private _content = '';
  private _organizationCodes: string[] = [];
  private _isConflict = false;

  constructor(landingPageDetail: LandingPageDetail) {
    super();
    if (landingPageDetail) {
      this._id = landingPageDetail.id;
      this._urlKey = landingPageDetail.urlKey;
      this._content = landingPageDetail.content;
      this._organizationCodes = landingPageDetail.organizationCodes || [];
    } else {
      this._id = uuid();
    }
  }

  async saveLandingPage() {
    this._isConflict = false;
    if (this.isValid) {
      await this.safeCall(async () => {
        try {
          const command = {
            content: this._content,
            id: this._id,
            urlKey: this._urlKey,
            organizationCodes: this._organizationCodes || undefined,
          };
          await Vue.$api.saveLandingPage(command);
        } catch (e) {
          if ((e as AxiosError)?.response?.status == 409) {
            this._isConflict = true;
          }
          throw e;
        }
      });
    }
  }

  private debouncedSave = asyncDebounce(async () => {
    await this.saveLandingPage();
  }, 300);

  get isConflict(): boolean {
    return this._isConflict;
  }

  get isValid() {
    return this._urlKey && this._content && !LandingPageEditService.isUrlKeyInvalid(this._urlKey);
  }

  get url() {
    return new URL(`${baseUrl('fo')}/pages/${this._urlKey}`).href;
  }

  get urlKey(): string {
    return this._urlKey;
  }

  set urlKey(value: string) {
    if (value !== this._urlKey) {
      this._urlKey = value.trim();
      if (value.length > 0) {
        this.debouncedSave.fn();
      }
    }
  }

  static isUrlKeyInvalid(key: string) {
    return /[`!@#$%^&*()_+=\[\]{};:"\\|,.<>\/?~]/.test(key);
  }

  get content(): string {
    return this._content;
  }

  set content(value: string) {
    if (value !== this._content) {
      this._content = value;
      if (value.length > 0) {
        this.debouncedSave.fn();
      }
    }
  }

  get organizationCodes() {
    return this._organizationCodes;
  }

  set organizationCodes(value) {
    this._organizationCodes = value;
    this.debouncedSave.fn();
  }
}
