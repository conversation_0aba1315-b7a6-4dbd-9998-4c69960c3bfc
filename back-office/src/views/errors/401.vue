<template>
  <v-app>
    <v-row align="center" justify="center">
      <v-card>
        <v-card-title class="justify-center">
          <v-icon large color="red">mdi-do-not-disturb</v-icon>
        </v-card-title>
        <v-card-text class="text-center">
          <p>Erreur 401: Unauthorized to access to this page</p>
          <v-btn :to="{ name: 'home' }">Go back to the homepage</v-btn>
          <v-btn @click="loginAsOtherUser" id="loginAsOtherUserButton">Login with another account</v-btn>
        </v-card-text>
      </v-card>
    </v-row>
  </v-app>
</template>

<script>
import keycloakService from 'odas-plugins/KeycloakService';

export default {
  methods: {
    loginAsOtherUser() {
      keycloakService.logout();
    },
  },
};
</script>

<style>

</style>
