<template>
  <v-col class="my-5">
    <v-progress-circular v-if="!error" indeterminate class="ma-auto"/>
    <v-alert v-else type="error" :value="error" class="ma-auto">{{ error }}</v-alert>
  </v-col>
</template>

<script>
export default {
  name: 'ChangeRecruitmentState',
  props: {
    recruitmentCode: {
      type: String,
      required: true,
    },
    targetState: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      error: false,
    };
  },
  async created() {
    try {
      const nextState = this.targetState;
      await this.$api.changeRecruitmentState(this.recruitmentCode, { nextState });

      this.$router.back();
    } catch (e) {
      this.error = e;
      this.logError(e);
    }
  },
};
</script>

<style scoped>

</style>
