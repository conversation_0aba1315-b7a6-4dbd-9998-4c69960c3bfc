import Vue from 'vue';
import asyncDebounce from 'async-debounce';
import { RecruitmentService } from '@/views/setup/recruitment/list/RecruitmentService';
import { RecruitmentPage, RecruitmentSort, SortDirection } from 'erhgo-api-client';
import { AxiosError } from 'axios';

export class RecruitmentListService extends RecruitmentService {
  private _showImportFormatError = false;
  private _page: RecruitmentPage | null = null;
  private _query = '';
  private _refreshId = 0;
  pagination = {
    page: 1,
    rowsPerPage: 10,
    by: RecruitmentSort.JOB,
    descending: false,
  };

  constructor(
    organizationCode: string | undefined,
    _selectedOrganizationCodes: string[],
    _withNewCandidaturesOnly: boolean,
    _withOpenRecruitmentOnly: boolean,
    _internal: boolean,
    _organizationTypeFilter: string,
  ) {
    super(organizationCode, _selectedOrganizationCodes, _withNewCandidaturesOnly, _withOpenRecruitmentOnly, _internal, null, null, _organizationTypeFilter);
    this.loading = true;
  }

  get page() {
    return this._page;
  }

  set query(value) {
    this._query = value;
    this.debouncedRefresh.fn();
  }

  get query() {
    return this._query;
  }

  public debouncedRefresh = asyncDebounce(async () => {
    await this.safeCall(async () => {
      this._page = (await Vue.$api.listRecruitments(
        this.pagination.page - 1,
        this.pagination.rowsPerPage,
        this.pagination.by ? this.pagination.by : RecruitmentSort.ID,
        this.organizationCode ? this.organizationCode : undefined,
        this._selectedOrganizationCodes,
        this.withNewCandidaturesOnly,
        this.withOpenRecruitmentOnly,
        this.organizationTypeFilter ? this.organizationTypeFilter : undefined,
        this.internal,
        this.pagination.descending ? SortDirection.DESC : SortDirection.ASC,
        this.query,
      )).data;
    });
    this._refreshId++;
  }, 300);

  get refreshId() {
    return this._refreshId;
  }

  get showImportFormatError() {
    return this._showImportFormatError;
  }

  async importOffers(file: File) {
    this._showImportFormatError = false;

    await this.safeCall(async () => {
      try {
        await Vue.$api.importCsvRecruitments(file);
      } catch (e) {
        if ((e as AxiosError)?.response?.status === 400) {
          this._showImportFormatError = true;
        } else {
          throw e;
        }
      }
    });
  }
}
