import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import { RecruitmentPageForCandidate } from 'erhgo-api-client';

export default class CandidateRecruitmentListService extends SafeService {

  private _recruitments: RecruitmentPageForCandidate | null = null;

  constructor(private _jobId: string, private _userId: string) {
    super();
    this.initialize();
  }

  async initialize() {
    await this.safeCall(async () => {
      this._recruitments = (await Vue.$api.listRecruitmentsForCandidate(this._jobId, this._userId, 0, -1)).data;
    });
  }

  get recruitments() {
    return this._recruitments?.content;
  }

}
