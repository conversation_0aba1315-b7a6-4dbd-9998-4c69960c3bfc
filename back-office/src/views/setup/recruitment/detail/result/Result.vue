<template>
  <v-row>
    <v-col cols v-if="resultService && !resultService.loading">
      <h1 class="d-flex align-center mb-2">
        <v-icon large
                class="mr-2"
                :color="candidatureState.iconColor">{{ candidatureState.iconText }}
        </v-icon>
        {{ $t(candidatureState.titleText) }}
      </h1>
      <v-alert dense
               :class="{invisible:!resultService.showResult}"
               :type="resultService.actionResult && resultService.actionResult.type ? resultService.actionResult.type : undefined"
               outlined>
        {{ (resultService.actionResult && resultService.actionResult.message) }}
      </v-alert>
      <v-data-table
        :sort-by.sync="pagination.sortBy"
        :page.sync="pagination.page"
        :sort-desc.sync="pagination.descending"
        :items-per-page.sync="pagination.rowsPerPage"
        :server-items-length="resultService.result.totalNumberOfElements"
        :footer-props="{
        'items-per-page-options': rowsPerPage,
        'items-per-page-text': 'Nb lignes par page',
        }"
        :headers="headers"
        :items="resultService.result.content"
        class="elevation-15"
        disable-sort
      >
        <template slot="no-data">
          <v-alert :value="true"
                   outlined
                   color="error"
                   icon="warning">
            Aucune candidature.
          </v-alert>
        </template>
        <template v-slot:item="props">
          <tr>
            <td><a :href="`mailto:${props.item.email}`">{{ props.item.email }}</a></td>
            <td>
              <a :href="`tel:${props.item.phoneNumber}`" class="hidden-md-and-up">{{ props.item.phoneNumber }}</a>
              <span class="hidden-sm-and-down">{{ props.item.phoneNumber }}</span>
            </td>
            <td>{{ $t(`contactTimes.${props.item.contactTime}`) }}</td>
            <td>{{ props.item.firstName }}</td>
            <td>{{ props.item.lastName }}</td>
            <td>
              <v-icon :color="props.item.candidatureIsArchived ? 'success': 'error'">
                {{ props.item.candidatureIsArchived ? 'fa-check-square' : 'fa-times-circle' }}
              </v-icon>
            </td>
            <td class="d-flex align-center justify-center">
              <v-tooltip left v-if="matching">
                <template v-slot:activator="{ on }">
                  <v-btn id="seeCandidature"
                         color="primary"
                         icon
                         v-on="on"
                         @click="goToCandidature(props.item.candidatureId)">
                    <v-icon>visibility</v-icon>
                  </v-btn>
                </template>
                <span>{{ $t('candidatureStates.show-detail') }}</span>
              </v-tooltip>
              <v-tooltip left v-if="allowsConfirmCandidature">
                <template v-slot:activator="{ on }">
                  <v-btn text
                         :id="`confirmCandidature${props.index}`"
                         icon
                         color="primary"
                         v-on="on"
                         :loading="isConfirmationLoading(props.item.candidatureId)"
                         @click="confirmCandidature(props.item.candidatureId)"
                  >
                    <v-icon>mdi-account-check</v-icon>
                  </v-btn>
                </template>
                <span>Forcer la finalisation de la candidature</span>
              </v-tooltip>
              <v-tooltip left>
                <template v-slot:activator="{ on }">
                  <div v-on="on">
                    <v-btn v-if="props.item.refusalDate === null"
                           :id="`refuseCandidature${props.index}`"
                           icon
                           color="primary"
                           @click="popinCandidature = props.item">
                      <v-icon>mdi-cancel</v-icon>
                    </v-btn>
                    <v-icon v-else color="red">mdi-cancel</v-icon>
                  </div>
                </template>
                <span>{{
                    props.item.refusalDate === null ? 'Refuser la candidature' : `Candidature refusée le ${formatRefusalDate(props.item.refusalDate)}`
                  }}</span>
              </v-tooltip>
            </td>
          </tr>
        </template>
        <template v-slot:footer.page-text="props">
          Lignes de {{ props.pageStart }} à {{ props.pageStop }} sur {{ props.itemsLength }}
        </template>
      </v-data-table>
    </v-col>
    <v-col v-else>
      <div class="text-center">
        <v-progress-circular indeterminate/>
      </div>
    </v-col>
    <refuse-candidature-popin :candidature-contact-info="popinCandidature"
                              :result-service="resultService"
                              :job-title="recruitment.jobTitle"
                              :recruitment-title="recruitment.title"
                              :user-id="popinCandidature.userId"
                              :candidature-id="popinCandidature.candidatureId"
                              @onClose="popinCandidature = null"
                              v-if="popinCandidature"
    />
  </v-row>
</template>

<script>
import ResultService from './ResultService';
import _ from 'lodash';
import moment from 'moment';
import RefuseCandidaturePopin from './RefuseCandidaturePopin';
import {RecruitmentForMatching} from 'erhgo-api-client';
import appStore from '@/store';

export default {
  components: {RefuseCandidaturePopin},
  props: {
    matching: {
      type: Boolean,
      default: undefined,
    },
    recruitment: {
      type: RecruitmentForMatching,
      required: true,
    },
    allowsConfirmCandidature: {
      type: Boolean,
      required: false,
    },
  },
  data() {
    return {
      resultService: ResultService,
      pagination: {
        page: 1,
        sortBy: [],
        descending: [false],
        rowsPerPage: 10,
      },
      rowsPerPage: [10, 25, 50, 100, 200],
      popinCandidature: null,
    };
  },
  async created() {
    this.resultService = new ResultService();
    await this.fetchCandidatures();
  },
  methods: {
    async fetchCandidatures() {
      await this.resultService.fetchCandidatureContactInfoPage(this.pagination.rowsPerPage,
        this.pagination.page - 1,
        this.recruitment.id,
        this.matching);
    },
    async confirmCandidature(candidatureId) {
      await this.resultService.confirmCandidature(candidatureId);
      this.$emit('confirmed');
    },
    isConfirmationLoading(candidatureId) {
      return this.resultService.isLoading(candidatureId);
    },
    formatRefusalDate(refusalDate) {
      return moment(refusalDate).format('DD/MM/YYYY HH:mm');
    },
    goToCandidature(candidatureId) {
      this.$router.push({
        name: 'candidature_details',
        params: {
          organization_code: this.organization.code,
          candidature_id: candidatureId,
        },
      });
    },
  },
  watch: {
    pagination: {
      handler: _.debounce(function () {
        this.fetchCandidatures();
      }, 200),
      deep: true,
    },
  },
  computed: {
    organization() {
      return appStore.getters.organizationLoaded;
    },
    headers() {
      return [
        {text: 'Email', value: 'email'},
        {text: 'Téléphone', value: 'phoneNumber'},
        {text: 'Période de contact préférée', value: 'contactTime'},
        {text: 'Prénom', value: 'firstName'},
        {text: 'Nom', value: 'lastName'},
        {text: 'Archivée', value: 'candidatureIsArchived'},
        {text: 'Actions', sortable: false, align: 'center'},
      ];
    },
    candidatureState() {
      let iconColor, iconText, titleText = 'candidatureStates.result.';
      if (this.matching === true) {
        iconColor = 'green';
        iconText = 'work';
        titleText += 'matched';
      } else if (this.matching === false) {
        iconColor = 'orange';
        iconText = 'work_off';
        titleText += 'unmatched';
      } else {
        iconColor = 'indigo';
        iconText = 'work_outline';
        titleText += 'invalid';
      }
      return {iconColor, iconText, titleText};
    },
  },
};
</script>
<style>
.v-icon {
  padding: 5px;
}
</style>
