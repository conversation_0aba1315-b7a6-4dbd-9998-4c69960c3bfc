<template>
  <v-row v-if="loading" style="background: white" align="end" justify="center">
    <v-progress-circular
      :size="50"
      color="primary"
      indeterminate
    />
  </v-row>
  <v-row v-else style="background: white" align="end" justify="center">
    <div id="missions">
      <h1 class="text-h5 pl-6">
        Profil de recrutement pour le poste&nbsp;: {{job.title}}
      </h1>
      <v-alert :value="true"
               outlined
               v-if="!modifiable"
               type="info">
        Au moins un recrutement existe pour ce profil de recrutement&nbsp;: seuls son titre et la question spécifique au
        candidat sont modifiables
        <v-dialog v-if="!modifiable" v-model="showEditDialog">
          <template v-slot:activator="{ on }">
            <v-btn v-on="on">Forcer la requalification des missions</v-btn>
          </template>
          <v-card>
            <v-card-title>Modification d'un profil de recrutement publié</v-card-title>
            <v-card-text>
              Modifier un profil de recrutement publié comporte des risques.<br/>
              En particulier, une fois les modifications terminées, il vous faut raffraichir le matching pour les
              recrutements déjà ouverts. L'ensemble des candidats repassera à 'nouveau'.
            </v-card-text>
            <v-card-actions>
              <v-spacer/>
              <v-btn outlined color="primary" @click="showEditDialog = false">Annuler</v-btn>
              <v-btn color="primary" @click="forceEdit = true">Modifier ce profil</v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-alert>


      <div class="pt-3 pl-3">
        <h2>
          <v-text-field
            @click.self.stop
            class="h2"
            v-model="profile.title"
            :rules="[v => !!v || $t('form.global.required'),
                        v => (v && v.length <= 50) || $t('form.global.maxLength', {maxLength: 50})]"
            :label="$t('form.recruitment_profile.title.label')"
            @input="saveProfile.fn"
            autofocus
            :loading="saveProfile.pending"
            required>
            <template v-slot:prepend>
              <v-icon color="green" :class="{'hidden': !titleSaved || saveProfile.pending}">check</v-icon>
            </template>
          </v-text-field>
        </h2>
      </div>
      <v-list v-if="profile.title" class="pl-5">
        <v-list-item
          v-for="(mission, index) in job.missions"
          :key="mission.id"
          class="py-1"
          :class="{
            'active': selectedMission && mission.id === selectedMission.id,
            'v-label theme--light disabled': isDisabled(index, mission.id)
          }"
          @click="!isDisabled(index, mission.id) && updateIndex(index)"
        >
          <v-list-item-icon class="my-0 elevated-text">•</v-list-item-icon>
          <v-list-item-content class="py-0">
            <v-list-item-title>
              Mission&nbsp;: {{mission.title}}
            </v-list-item-title>
            <v-list-item-subtitle>
              {{missionStateLabel(mission, index)}}<label
              v-if="showError && selectedMission && mission.id === selectedMission.id" class="error--text"> - Veuillez
              donner une modalité d'acquisition pour chaque contexte et chaque activité optionnelle.</label>
            </v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-list-item
          :class="{
            'active': customQuestionSelected,
            'v-label theme--light disabled': !isCustomQuestionClickable()
          }"
          @click="clickCustomQuestion()"
        >
          <v-list-item-icon class="my-0 elevated-text">•</v-list-item-icon>
          <v-list-item-content class="py-0">
            <v-list-item-title>
              Question spécifique
            </v-list-item-title>
            <v-list-item-subtitle>
              {{currentCustomQuestion || 'Aucune question spécifique posée au candidat'}}
            </v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
      </v-list>
      <v-col class="shrink mission" v-if="selectedMission">
        <v-divider/>
        <h1 class="text-h5 pa-3">
          {{selectedMission.title}}
        </h1>
        <v-btn v-if="modifiable" :loading="this.loadingToggle" @click="toggleAll(true)">Tout rendre obligatoire</v-btn>
        <v-btn v-if="modifiable" :loading="this.loadingToggle" @click="toggleAll(false)">Tout rendre optionnel</v-btn>
        <v-col class="mission--grey">
          <h2 class="pl-3">Qualification des activités de la mission</h2>
          <ul>
            <li
              class="optional-handler"
              v-for="activity in selectedMission.activities"
              :key="activity.id">
              <optional-handler
                optional-type="activity"
                :optionalModality="getOptionalModalityForActivity(activity)"
                :initial="activity"
                :profile-id="profile.id || profile_id"
                :job-id="job_id"
                :disabled="!modifiable"
                ref="activity"
                @touch="showError && validate()"
                @optional-deleted="optionalActivityDeleted"
                @optional-added="optionalActivityAdded"
              />
              <v-divider/>
            </li>
          </ul>
        </v-col>
        <h2 class="pl-3">Qualification des contextes de la mission
          <v-alert :value="true"
                   outlined
                   type="info">
            Pour chaque contexte obligatoire, vous pouvez préciser une question qui sera présentée aux candidats.
            A défaut, l'intitulé du contexte lui sera présenté.
          </v-alert>
        </h2>
        <v-col>
          <ul>
            <li
              class="my-4 optional-handler"
              v-for="context in selectedMission.contextsForCategory ? selectedMission.contextsForCategory.flatMap(c => c.contexts):[]"
              :key="context.id">
              <optional-handler
                optional-type="context"
                :optional-modality="getOptionalModalityForContext(context)"
                :initial="context"
                :question-prop="getQuestionForContext(context)"
                :profile-id="profile.id || profile_id"
                :job-id="job_id"
                :disabled="!modifiable"
                ref="context"
                @touch="showError && validate()"
                @optional-deleted="optionalContextDeleted"
                @optional-added="optionalContextAdded"
              />
              <v-divider/>
            </li>
          </ul>
        </v-col>
        <v-alert :value="showError"
                 outlined
                 color="error"
                 icon="warning">
          Veuillez donner une modalité d'acquisition pour chaque contexte et chaque activité optionnelle.
        </v-alert>
        <v-btn
          class="ml-3"
          color="primary"
          @click="submit(selectedMissionIndex+1)"
          :loading="loadingSubmit"
        >
          {{ isLastMission ? 'Question spécifique' : 'Mission suivante'}}
          <v-icon>chevron_right</v-icon>
        </v-btn>
      </v-col>
      <v-col class="shrink mission" v-if="customQuestionSelected">
        <v-divider/>
        <h1 class="text-h5 pa-3">
          Poser une question spécifique au candidat
        </h1>
        <div class="pt-3 pl-3">
          <h2>
            <v-text-field
              class="h2"
              v-model="profile.customQuestion"
              :counter="255"
              maxlength="255"
              :label="$t('form.recruitment_profile.custom_question.label')"
              @input="saveQuestion.fn"
              clearable
              ref="customQuestionField"
              :loading="saveQuestion.pending">
              <template v-slot:prepend>
                <v-icon color="green" :class="{'hidden': !questionSaved || saveQuestion.pending}">check</v-icon>
              </template>
            </v-text-field>
          </h2>
        </div>
        <v-btn class="ml-3" color="primary" @click="submit()" :loading="loadingSubmit">
          <template v-if="modifiable">Terminer la qualification</template>
          <template v-else>Retour aux postes</template>
        </v-btn>
      </v-col>
    </div>
    <v-col>
      <v-btn v-if="showBackButton"
             outlined
             class="ml-3"
             color="primary"
             @click="submit()"
             :loading="loadingSubmit">
        {{ modifiable ? 'Sauvegarder le profil' : 'Retour aux postes' }}
      </v-btn>
    </v-col>
  </v-row>
</template>

<script>
import asyncDebounce from 'async-debounce';
import { v4 as uuid } from 'uuid';
import _ from 'lodash';

import OptionalHandler from './OptionalHandler.vue';
import JobSearchService from '../job/JobSearchService';

export default {
  components: {
    OptionalHandler,
  },
  props: {
    organization_code: {
      type: String,
      required: true,
    },
    job_id: {
      type: String,
      required: true,
    },
    profile_id: {
      type: String,
      required: false,
      default: null,
    },
  },
  computed: {
    title() {
      return this.profile.title;
    },
    selectedMission() {
      return !this.title || this.selectedMissionIndex < 0 ? null : this.job.missions[this.selectedMissionIndex];
    },
    modifiable() {
      return this.forceEdit || this.profile.modifiable !== false;
    },
    isLastMission() {
      return this.selectedMissionIndex === this.job.missions.length - 1;
    },
    showBackButton() {
      return this.profile && this.profile.title && !(this.customQuestionSelected && !this.modifiable);
    },
    job() {
      return this.jobSearchService.job;
    },
  },
  data() {
    return {
      jobSearchService: JobSearchService,
      loadingToggle: false,
      loadingSubmit: false,
      loading: false,
      titleSaved: false,
      questionSaved: false,
      customQuestionSelected: false,
      selectedMissionIndex: -1,
      forceEdit: false,
      showEditDialog: false,

      showError: false,
      profile: {
        id: null,
        title: '',
        jobId: null,
        optionalActivities: [],
        optionalContexts: [],
        qualifiedMissionIds: [],
        contextQuestions: [],
        customQuestion: '',
      },

      currentCustomQuestion: null,

      saveProfile: asyncDebounce(async () => {
        if (this.profile.title) {
          await this.$api.saveRecruitmentProfile(this.job.id, this.profile);
          this.titleSaved = true;
          setTimeout(() => {
            this.titleSaved = false;
          }, 5000);
        }
      }, 1000),
      saveQuestion: asyncDebounce(async () => {
        if (this.$refs.customQuestionField.validate()) {
          await this.$api.saveRecruitmentProfile(this.job.id, this.profile);
          this.currentCustomQuestion = this.profile.customQuestion;
          this.questionSaved = true;
          setTimeout(() => {
            this.questionSaved = false;
          }, 5000);
        }
      }, 1000),

    };
  },
  async created() {
    this.jobSearchService = new JobSearchService();
    this.loading = true;
    try {
      await this.jobSearchService.fetchJob(this.job_id);
      if (!this.profile_id) {
        this.profile.id = uuid();
      } else {
        this.profile = (await this.$api.getRecruitmentProfile(this.job.id, this.profile_id)).data;
        this.currentCustomQuestion = this.profile.customQuestion;
      }
    } catch (e) {
      this.showAPIErrorDialog = true;
      this.logError(e);
    } finally {
      this.loading = false;
    }

    this.selectedMissionIndex = 0;
  },
  methods: {
    getOptionalModalityForActivity(activity) {
      const modalities = this.profile.optionalActivities.filter(o => activity.id === o.activity.id).map(o => o.acquisitionModality);
      return modalities.length ? modalities[0] : null;
    },
    getOptionalModalityForContext(context) {
      const modalities = this.profile.optionalContexts.filter(o => o.context.id === context.id).map(o => o.acquisitionModality);
      return modalities.length ? modalities[0] : null;
    },
    getQuestionForContext(context) {
      return this.profile.contextQuestions.filter(o => o.contextId === context.id).map(o => o.question)[0];
    },
    missionStateLabel(mission, index) {
      if (this.profile.qualifiedMissionIds.includes(mission.id)) {
        return 'Qualification terminée';
      }
      return (this.selectedMission && (mission.id === this.selectedMission.id || this.isNextMissionToQualify(index))) ? 'Qualification en cours' : 'Qualification à faire';
    },
    async submit(nextIndex) {
      if (this.validate()) {
        if (!this.customQuestionSelected && !this.profile.qualifiedMissionIds.includes(this.selectedMission.id)) {
          this.loadingSubmit = true;
          await this.$api.endQualification(this.job_id, this.profile.id, this.selectedMission.id);
          this.profile.qualifiedMissionIds.push(this.selectedMission.id);
          this.loadingSubmit = false;
        }
        this.updateIndex(nextIndex);
      }
    },
    updateIndex(nextIndex) {
      this.customQuestionSelected = false;
      if (nextIndex !== undefined) {
        this.customQuestionSelected = nextIndex === this.job.missions.length;
        this.selectedMissionIndex = this.customQuestionSelected ? -1 : nextIndex;
      } else {
        this.$router.push({
          name: 'organizations_jobs_repository',
          params: {selected_job_id: this.job.id, new_profile_title: this.modifiable ? this.profile.title : ''},
        });
      }
    },
    validate() {
      this.showError = false;
      if (!_.every(this.$refs.activity, a => a.validate()) || !_.every(this.$refs.context || [], a => a.validate())) {
        this.showError = true;
        return false;
      }
      return true;
    },
    isDisabled(index, missionId) {
      return index !== this.selectedMissionIndex
        && !this.profile.qualifiedMissionIds.includes(missionId)
        && !this.isNextMissionToQualify(index);
    },
    isNextMissionToQualify(index) {
      return index > 0 &&
        this.profile.qualifiedMissionIds.includes(this.job.missions[index - 1].id);
    },
    async toggleAll(mandatory) {
      this.loadingToggle = true;
      try {
        this.$refs.activity.forEach(a => a.toggle(mandatory));
        this.$refs.context?.forEach(c => c.toggle(mandatory));
      } finally {
        this.loadingToggle = false;
      }
    },
    optionalActivityAdded(addedOptional) {
      this.profile.optionalActivities.push(addedOptional);
    },
    optionalActivityDeleted(deletedOptionalId) {
      this.profile.optionalActivities = this.profile.optionalActivities.filter(o => deletedOptionalId !== o.optionalId);
    },
    optionalContextAdded(addedOptional) {
      this.profile.optionalContexts.push(addedOptional);
    },
    optionalContextDeleted(deletedOptionalId) {
      this.profile.optionalContexts = this.profile.optionalContexts.filter(o => deletedOptionalId !== o.optionalId);
    },
    isCustomQuestionClickable() {
      return this.profile.qualifiedMissionIds.length === this.job.missions.length;
    },
    clickCustomQuestion() {
      if (this.isCustomQuestionClickable()) {
        this.customQuestionSelected = true;
        this.selectedMissionIndex = -1;
      }
    },
  },

};
</script>

<style lang="scss">
.disabled {
  cursor: default;
}
</style>

<style scoped lang="scss">
@import '../../../style/colors.scss';

#missions {
  width: 100%;
}

.clickable {
  cursor: pointer;

  span:hover {
    text-decoration: underline;
    text-decoration-color: $primary;
  }
}

.elevated-text {
  color: $primary;
  text-shadow: 0 0 0.1em black;
}

.v-list {
  .v-list-item__icon {
    font-size: 2em;
    text-align: center;
    color: $primary;
    justify-content: center;
  }

  .active {

    background-color: $bg-light-grey;

    .v-list-item__content {
      font-weight: bold;
    }

    .error--text {
      font-weight: normal;
    }
  }
}

li.optional-handler {
  list-style-type: none;
  border-left: $primary solid;
}

.mission--grey {
  background-color: $bg-light-grey;
}

.hidden {
  visibility: hidden;
}
</style>
