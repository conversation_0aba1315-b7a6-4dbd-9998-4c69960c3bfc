import Vue from 'vue';
import SafeService from 'odas-plugins/SafeService';
import {UserCandidature} from 'erhgo-api-client';

export default class CandidaturesService extends SafeService {

  private _candidatures: UserCandidature[] = [];

  constructor(private _userId: string, private _enterpriseCode: string | undefined) {
    super();
  }

  async fetchCandidatures() {
    return this.safeCall(async () => {
      this._candidatures = (await Vue.$api.getUserJobsCandidatures(
        this._userId,
        this._enterpriseCode,
      )).data;
    });
  }

  get candidatures(): UserCandidature[] {
    return this._candidatures;
  }


}
