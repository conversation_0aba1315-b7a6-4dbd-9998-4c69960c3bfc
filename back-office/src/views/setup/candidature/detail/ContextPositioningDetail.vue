<template>
  <v-row>
    <v-col cols="12" class="pa-0 ma-0">
      <div class="text-caption">
        Voici la liste des prérequis incluant la question soumise, les réponses proposées et, en surbrillance, celle choisie par le candidat.
      </div>
    </v-col>
    <v-col v-for="(positioning, i) in contextPositioning" :key="i">
      <v-card elevation="12">
        <v-card-title class="pt-2">
          {{positioning.contextTitle}}
        </v-card-title>
        <v-card-subtitle class="black--text py-0">
          <em :class="{invisible: !positioning.questionTitle}">{{ positioning.questionTitle }}</em>
        </v-card-subtitle>
        <v-card-text :id="`contextPositioning${i}`" class="pb-2">
          <ol>
            <li v-for="(_, key) in positioning.suggestedAnswers" :key="key"
                :class="isSelectedAnswer(positioning.userAnswer, key) ? 'font-weight-bold' : 'grey--text text--darken-1 font-italic'">
              <v-icon small v-if="isSelectedAnswer(positioning.userAnswer, key)">chevron_right</v-icon>
              {{ positioning.suggestedAnswers[key] }}
              <v-icon small v-if="isSelectedAnswer(positioning.userAnswer, key)">chevron_left</v-icon>
            </li>
          </ol>
        </v-card-text>
      </v-card>
    </v-col>
  </v-row>
</template>

<script>
export default {
  name: 'ContextPositioningDetail',
  props: {
    contextPositioning: {
      type: Array,
      required: true,
    },
  },
  methods: {
    isSelectedAnswer(userAnswer, suggestedAnswersKey) {
      return this.getUserAnswer(userAnswer) === suggestedAnswersKey || this.isAnswerNone(userAnswer, suggestedAnswersKey);
    },
    getUserAnswer(frequency) {
      return frequency?.toLowerCase();
    },
    isAnswerNone(userAnswer, suggestedAnswersKey) {
      return userAnswer === null && suggestedAnswersKey === 'none';
    },
  },
};
</script>
