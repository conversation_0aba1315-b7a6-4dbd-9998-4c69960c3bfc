import Vue from 'vue';
import { CandidatureSummary, ContactForCandidature, SaveCandidatureNoteCommand } from 'erhgo-api-client';
import { logErrorToServer } from 'odas-plugins/error-handler';
import { v4 as uuid } from 'uuid';

export class Candidature {

  private _loading = false;
  private _contact: ContactForCandidature | null = null;
  private _savingCandidatureNote = false;
  private _candidatureNote: SaveCandidatureNoteCommand;
  showAPIErrorDialog = false;

  constructor(
    private _candidature: CandidatureSummary,
  ) {
    const isCandidatureSelected = _candidature.candidatureRecruitmentState === 'SELECTED';
    this._contact = isCandidatureSelected ? _candidature : null;
    this._candidatureNote = (_candidature.notes && _candidature.notes.length) ? _candidature.notes[0] : {
      id: uuid(),
      text: '',
    };
  }

  static async initialize(candidatureId: number): Promise<Candidature> {
    const candidature = (await Vue.$api.getMatchingCandidature(candidatureId)).data;
    return new Candidature(candidature);
  }

  async meetCandidate() {
    this.showAPIErrorDialog = false;
    try {
      this._loading = true;
      this._contact = (await Vue.$api.meetCandidature(this._candidature.id)).data;
    } catch (e) {
      this.showAPIErrorDialog = true;
      logErrorToServer(e, Vue.$api);
    } finally {
      this._loading = false;
    }
  }

  async saveCandidatureNote() {
    this.showAPIErrorDialog = false;
    try {
      this._savingCandidatureNote = true;
      const savedCandidatureNote = (await Vue.$api.saveCandidatureNote(
        this._candidature.id,
        this._candidatureNote,
      )).data;
      this._candidatureNote = savedCandidatureNote;
    } catch (error) {
      this.showAPIErrorDialog = true;
      logErrorToServer(error, Vue.$api);
    } finally {
      this._savingCandidatureNote = false;
    }
  }

  get submissionDate() {
    return this._candidature.submissionDate;
  }

  get firstName() {
    return this._contact!.firstName;
  }

  get lastName() {
    return this._contact!.lastName;
  }

  get recruitment() {
    return this._candidature.recruitment;
  }

  get notes() {
    return this._candidature.notes;
  }

  get userId() {
    return this._candidature.userId;
  }

  get email() {
    return this._contact!.email;
  }

  get customAnswer() {
    return this._candidature.customAnswer;
  }

  get experiences() {
    return this._candidature.experiences;
  }

  get contextsPositioning() {
    return this._candidature.contextsPositioning;
  }

  get candidatureRecruitmentState() {
    return this._candidature.candidatureRecruitmentState;
  }

  get phoneNumber() {
    return this._contact!.phoneNumber;
  }

  get contactTime() {
    return this._contact!.contactTime;
  }

  get isCandidatureSelected() {
    return !!this._contact;
  }

  get refusalDate() {
    return this._candidature.refusalDate;
  }

  set refusalDate(date) {
    this._candidature.refusalDate = date;
  }

  get loading() {
    return this._loading;
  }

  get candidatureName() {
    return this.isCandidatureSelected ? `${this._contact!.firstName || ''} ${this._contact!.lastName || ''}` : null;
  }

  get candidatureNote() {
    return this._candidatureNote;
  }

  get saveNoteButtonActive() {
    return this.candidatureNote.text && this.candidatureNote.text.length > 0;
  }

  get savingCandidatureNote() {
    return this._savingCandidatureNote;
  }

  get candidatureState() {
    return this._candidature.candidatureState;
  }

  get isArchived() {
    return this._candidature.archived;
  }
}
