<template>
  <v-container fluid class="job-edit">
    <v-row v-if="job">
      <v-col cols="12">
        <v-card flat>
          <v-card-title>
            <v-col cols="12">
              <v-breadcrumbs :items="breadcrumbs">
                <v-icon slot="divider">chevron_right</v-icon>
              </v-breadcrumbs>
            </v-col>
            <v-col cols="12">
              <h1>
                <v-icon large
                        color="primary">collections_bookmark
                </v-icon>
                {{ $t('ref.job.edit') }}&nbsp;: {{ job.title }}
              </h1>
            </v-col>
          </v-card-title>
          <v-card-text>
            <v-col cols="12">
              <v-stepper non-linear
                         v-model="state">
                <v-stepper-header>
                  <template v-for="(step, index) in steps">
                    <v-stepper-step
                      :key="index"
                      :editable="state > index + 1"
                      :complete="state > index + 1"
                      edit-icon="mdi-check"
                      :step="index + 1">
                      {{ $t(`ref.job.steps.${step}.title`) }}
                      <small> {{ $t(`ref.job.steps.${step}.subtitle`) }}</small>
                    </v-stepper-step>
                    <v-divider :key="`div-${index}`"/>
                  </template>
                </v-stepper-header>
                <v-stepper-items>
                  <v-stepper-content :step="1">
                    <definition :job="job"
                                :recruitment-profile-count="recruitmentProfileCount"
                                :organization="organization"
                                :disabled="!isModifiable"
                                @back="back"
                                @validate="next"
                                @unvalidate="unvalidate"
                                :loadingJob="loadingJob"/>
                  </v-stepper-content>

                  <v-stepper-content :step="2">
                    <missions @input="m => missions = m"
                              :job="job"
                              :disabled="!isModifiable"
                              @back="back"
                              @validate="next"
                              @error="showAPIErrorDialog = true"/>
                  </v-stepper-content>

                  <v-stepper-content :step="3">
                    <criteria :job="job"
                              :organization="organization"
                              v-if="state >= 3"
                              @back="back"
                              @error="showAPIErrorDialog = true"
                              @validate="submitCriteria"/>
                  </v-stepper-content>

                  <v-stepper-content :step="4">
                    <behaviors :job="job"
                               :behaviors="behaviors"
                               @back="back"
                               @validate="submitBehavior"/>
                  </v-stepper-content>

                  <v-stepper-content :step="5">
                    <recommendation :job="job"
                                    @input="r => recommendation = r"
                                    @back="back"
                                    @next="next"
                                    @error="showAPIErrorDialog = true"/>
                  </v-stepper-content>

                  <v-stepper-content :step="6">
                    <publishing :job="job"
                                :behaviors="job.behaviors"
                                :organization="organization"
                                :missions="missions"
                                :recommendation="recommendation"
                                @back="back"
                                @validate="publish"/>
                  </v-stepper-content>

                  <v-stepper-content :step="7">
                    <confirmation :job="job"
                                  :organization="organization"
                                  @validate="publish"/>
                  </v-stepper-content>
                </v-stepper-items>
              </v-stepper>
            </v-col>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-dialog v-model="showAPIErrorDialog" persistent>
      <v-card>
        <v-card-title
          class="text-h5 grey lighten-2"
          primary-title
        >
          Erreur
          <v-spacer/>
          <v-icon color="error">error</v-icon>
        </v-card-title>
        <v-card-text>
          Une erreur technique s'est produite lors de la sauvegarde des données.
          Merci de réessayer, ou de contacter le support si l'erreur persiste.
        </v-card-text>
        <v-divider/>
        <v-card-actions>
          <v-spacer/>
          <v-btn
            color="primary"
            @click="jobEditService.showAPIError = false"
          >
            Continuer
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <template-selector
      v-if="isFromErhgo && isNewJob"
      @input="selectJob"
      :job-type="jobType"
      :recruiterCode="defaultRecruiterCode"/>
  </v-container>
</template>

<script>
import appStore from '@/store';
import keycloakService from 'odas-plugins/KeycloakService';
import { JobType } from 'erhgo-api-client';
import TemplateSelector from './template/TemplateSelector.vue';
import Definition from './step/Definition.vue';
import Missions from './step/Missions.vue';
import Criteria from './step/Criteria.vue';
import Behaviors from './step/Behaviors.vue';
import Recommendation from './step/Recommendation.vue';
import Publishing from './step/Publishing.vue';
import Confirmation from './step/Confirmation.vue';
import JobSearchService from './JobSearchService';
import JobEditService from './JobEditService';

export default {
  components: {
    Definition,
    Missions,
    Criteria,
    Behaviors,
    Recommendation,
    Publishing,
    Confirmation,
    TemplateSelector,
  },
  props: {
    mode: String,
  },
  data() {
    return {
      keycloakService,
      jobSearchService: JobSearchService,
      jobEditService: JobEditService,
      paramJobId: this.$route.params.job_id,
      steps: [],
      organization: {
        title: '',
        code: '',
      },
      job: null,

      activities: [],
      contexts: [],
      evaluations: [],
      behaviors: [],
      state: 1,
      missions: [],
      recommendation: '',
      recruitmentProfileCount: 0,
      haveToCreateRecruitment: false,
    };
  },
  async created() {
    this.organization = appStore.state.organizationLoaded;
    this.jobSearchService = new JobSearchService();
    this.jobEditService = new JobEditService();
    if (typeof this.paramJobId !== 'undefined') {
      await this.jobSearchService.fetchJob(this.paramJobId);
      this.job = this.jobSearchService.job;
      if (this.isFromJobTemplate) {
        this.job.id = null;
        this.job.modifiable = true;
        this.job.publicationDate = null;
        this.job.state = 'LISTED';
        this.job.missions.forEach(mission => {
          mission.id = null;
          mission.jobId = null;
        });
        this.job.recruiterCode = this.defaultRecruiterCode;
        this.job.employerCode = null;
      }
    } else if (!this.isFromErhgo) {
      this.job = {
        state: 'LISTED',
        contexts: [],
        criteriaValues: [],
        missions: [],
        evaluations: [],
        behaviors: [],
        recommendation: '',
        recruiterCode: this.defaultRecruiterCode,
        jobType: this.jobType,
      };
    }
    this.steps = this.defineSteps;
    this.recruitmentProfileCount = this.jobSearchService.job?.recruitmentProfileCount || 0;
  },
  computed: {
    defineSteps() {
      return this.jobType === 'SIMPLE' ? ['definition', 'missions', 'criteria'] : ['definition', 'missions', 'criteria', 'behaviors', 'recommendation', 'publishing'];
    },
    loadingJob() {
      return this.jobSearchService.loading;
    },
    isFromJobTemplate() {
      return this.mode === 'createFromJob';
    },
    isFromErhgo() {
      return this.mode === 'createFromErhgo' || this.mode === 'createSimpleFromErhgo';
    },
    jobType() {
      if (this.mode !== 'edit') {
        return this.mode === 'createSimpleFromErhgo' ? JobType.SIMPLE : JobType.OBSERVED;
      }
      return this.job.jobType;
    },
    isNewJob() {
      return (this.paramJobId === undefined || this.isFromJobTemplate) && (!this.job?.id || this.job.id < 0);
    },
    isModifiable() {
      return this.job.modifiable !== false;
    },
    defaultRecruiterCode() {
      return this.organization.defaultProject?.code || this.organization.code;
    },
    showAPIErrorDialog: {
      get() {
        return this.jobEditService.showAPIError;
      },
      set(error) {
        this.jobEditService.showAPIError = error;
      },
    },
    breadcrumbs() {
      return (!this.organization || !this.organization.code) ? [] : [
        {text: this.$t('ref.job.breadcrumb.setup')},
        {
          text: this.$t('ref.job.breadcrumb.organizationSelected', {name: this.organization.title}),
          to: {
            name: 'organizations_jobs_repository',
            params: {organization_code: this.organization.code},
          },
        },
        {text: this.$t('ref.job.breadcrumb.editJob')},
        {text: `${this.job.title || ''}`},
      ];

    },
  },
  methods: {
    selectJob(job) {
      this.job = job;
    },
    back() {
      this.state = this.state > 1 ? this.state - 1 : 1;
    },
    async unvalidate(job) {
      if (job) {
        await this.submit(job);
        this.job.modifiable = true;
      }
    },
    async next(redirect, job, callback) {
      if (job) {
        await this.submit(job);
      }
      if (this.job.jobType === 'SIMPLE') {
        await this.publish(redirect);
      } else if (this.job.jobType !== 'SIMPLE') {
        return this.redirectOrContinue(redirect, callback);
      }
    },
    async submitBehavior(redirect, callback) {
      await this.jobEditService.saveBehavior(this.job.id, this.job.behaviors);
      await this.redirectOrContinue(redirect, callback);
    },
    async submitCriteria(redirectToJobsList, callback, recruitmentProfileCustomQuestion) {
      const criteriaSavingSuccess = await this.jobEditService.saveCriteria(this.job.id, this.job.criteriaValues);
      if (criteriaSavingSuccess) {
        if (this.job.jobType === 'SIMPLE') {
          await this.publish(redirectToJobsList, undefined, recruitmentProfileCustomQuestion, !redirectToJobsList);
        } else {
          return this.redirectOrContinue(redirectToJobsList, callback);
        }
      }
      callback();
    },
    async publish(redirectToJobsList, callback, recruitmentProfileCustomQuestion, haveToCreateRecruitment) {
      let endOfCreatingJob = haveToCreateRecruitment;
      if (endOfCreatingJob === undefined || endOfCreatingJob === null) {
        endOfCreatingJob = this.haveToCreateRecruitment;
      }
      await this.jobEditService.publish(this.job.id, endOfCreatingJob, recruitmentProfileCustomQuestion);
      await this.redirectOrContinue(redirectToJobsList, callback);
    },
    async redirectOrContinue(redirect, callback) {
      if (redirect) {
        await this.$router.push({
          name: 'organizations_jobs_repository',
          params: {organization_code: this.organization.code},
        });
      } else {
        this.state++;
        callback && callback();
      }
    },
    dispatch(job) {
      this.behaviors.splice(0, this.behaviors.length);
      Array.from(job.behaviors).forEach((behavior) => {
        this.behaviors.push(behavior);
      });
    },
    async submit(postValueFromStep) {
      /* eslint-disable @typescript-eslint/no-unused-vars */
      const {
        missions,
        recommendation,
        createdBy,
        ...job
      } = postValueFromStep;

      const command = this.jobEditService.getJobCommand(job);
      if (this.isNewJob) {
        await this.jobEditService.saveJob(command);
        this.$set(this.job, 'id', command.id);
        this.job.createdBy = keycloakService.fullname;
      } else {
        await this.jobEditService.saveJob(command);
      }
      this.dispatch(this.job);
    },
  },
};
</script>
<style lang="scss">
.job-edit {
  .v-card__text {
    padding: 0;
  }

  .v-stepper__content {
    padding-left: 0;
    padding-right: 0;

  }
}
</style>
