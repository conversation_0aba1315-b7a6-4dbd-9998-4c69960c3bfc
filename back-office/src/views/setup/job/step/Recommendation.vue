<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card flat>
          <v-card-title>
            <h1>{{$t('form.job.create.recommendation')}}</h1>
          </v-card-title>
          <v-card-text>
            <vue-editor class="mt-2"
                        v-model="recommendation"/>
          </v-card-text>
          <v-card-actions class="d-flex justify-space-between">
            <v-btn color="grey lighten-5"
                   small
                   @click.stop="back">
              <v-icon small
                      left>arrow_back_ios
              </v-icon>
              Étape précédente&nbsp;: Donner vos critères
            </v-btn>
            <v-btn
              small
              outlined
              :loading="loading"
              @click="submit(true)">
              <v-icon class="mr-2" small>
                save
              </v-icon>
              Enregistrer et revenir aux postes
            </v-btn>
            <v-btn color="primary"
                   small
                   :loading="loading"
                   @click.stop="submit(false)">
              Étape suivante&nbsp;: Vérifier le poste
              <v-icon right>arrow_forward_ios</v-icon>
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  props: {
    job: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      recommendation: '',
      loading: false,
      submitColor: 'primary',
      submitIcon: 'save',
    };
  },
  computed: {
    jobId() {
      return this.job.id;
    },
  },
  watch: {
    jobId() {
      this.updateRecommendation();
    },
  },
  created() {
    this.updateRecommendation();
  },
  methods: {
    updateRecommendation() {
      this.recommendation = this.job.recommendation;
    },
    back() {
      this.$emit('back');
    },
    async submit(redirect) {
      this.loading = true;
      try {
        // Quickfix: openApi replace falsy body by an empty object => when no recommendation, use 'not falsy' blank recommendation
        await this.$api.setRecommendationForJob(this.job.id, !this.recommendation ? ' ' : this.recommendation);
        this.$emit('next', redirect);
        this.$emit('input', this.recommendation);
      } catch (e) {
        this.logError(e);
        this.$emit('error');
      } finally {
        this.loading = false;
      }
    },
    clear() {
      this.submitColor = 'primary';
      this.submitIcon = 'save';
    },
  },
};
</script>

<style>
</style>
