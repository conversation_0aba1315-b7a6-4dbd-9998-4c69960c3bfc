<template>
  <v-container fluid>
    <v-card-title primary-title>
      <div>
        <h3 class="text-h5 mb-0">Observation du poste terminée</h3>
      </div>
    </v-card-title>
    <v-card-text>
      Le poste a été finalisé avec succès. Vous pouvez désormais retourner à la liste des postes.
    </v-card-text>
    <v-card-actions>
      <v-btn color="primary"
             @click.stop="backToJobs">
        Retourner à la liste des postes
      </v-btn>
      <v-btn color="primary"
             outlined
             v-if="job.jobType === 'SIMPLE'"
             :to="{
               name: 'recruitment_create',
               params: {
                 'organization_code': organization.code,
                 'job': job
               }
             }">
        C<PERSON>er un recrutement
      </v-btn>
    </v-card-actions>
  </v-container>
</template>

<script>
export default {
  props: {
    organization: {
      type: Object,
      required: true,
    },
    job: {
      type: Object,
      required: true,
    },
  },
  methods: {
    backToJobs() {
      this.$router.push({
        name: 'organizations_jobs_repository',
        params: {organization_code: this.organization.code},
      });
    },
  },
};
</script>

<style>
</style>
