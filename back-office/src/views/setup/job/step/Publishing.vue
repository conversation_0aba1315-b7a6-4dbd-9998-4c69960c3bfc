<template>
  <v-container fluid>
    <v-card flat>
      <v-card-text>
        <v-row
        >
          <v-col cols="6">
            <v-card flat
                    color="secondary"
                    class="white--text">
              <v-card-title>
                <div>
                  <h3 class="text-h5 mb-0">{{ $t('form.job.create.title') }}</h3>
                </div>
              </v-card-title>
              <v-card-text class="white black--text">
                <v-row>
                  <v-col cols="6">
                    <v-label>{{ $t('form.job.create.organization.title') }}</v-label>&nbsp;: {{ job.title }}
                  </v-col>
                  <v-col cols="6">
                    <v-label xs6>{{ $t('form.job.create.organization.service') }}</v-label>&nbsp;: {{ job.service }}
                  </v-col>
                  <v-col cols="6">
                    <v-label>{{ $t('form.job.create.organization.contexts') }}</v-label>&nbsp;: {{ jobLocation }}
                  </v-col>
                  <v-col cols="12">
                    <v-label>{{ $t('form.job.create.organization.description') }}</v-label>&nbsp;: {{ job.description }}
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="6">
            <v-card flat
                    color="primary">
              <v-card-title>
                <div>
                  <h3 class="text-h5 mb-0">Espace erhgo</h3>
                </div>
              </v-card-title>
              <v-card-text class="white black--text">
                <v-row>
                  <v-col cols="6">
                    <v-label>{{ $t('form.job.create.odas.owner') }}</v-label>&nbsp;: {{ job.createdBy }}
                  </v-col>
                  <v-col cols="6">
                    <v-label>{{ $t('form.job.create.odas.observators') }}</v-label>&nbsp;: {{
                      job.observators ?
                        job.observators.join(', ') : ''
                    }}
                  </v-col>
                  <v-col cols="6">
                    <v-label>Date d'observation</v-label>&nbsp;: {{ job.observationDate | formatDate }}
                  </v-col>
                  <v-col cols="6" v-if="job.erhgoOccupation">
                    <v-label>{{ $t('form.job.create.erhgoOccupation.title') }}</v-label>&nbsp;: {{ job.erhgoOccupation.title }}
                  </v-col>
                  <v-col cols="6" v-if="job.level">
                    <v-label>Niveaux de maîtrise du poste</v-label>&nbsp;: {{ levelLabelAndDescription(job.level) }}
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12">
            <v-divider/>
            <v-card flat>
              <v-card-title>
                <h2>Missions</h2>
              </v-card-title>
              <v-card-text>
                <v-expansion-panels accordion>
                  <v-expansion-panel v-for="(mission, index) in validMissions" :key="mission.id">
                    <v-expansion-panel-header>
                      <v-row justify="start">
                        <v-col>
                          <v-btn small text @click.stop="moveMissionUp(index)" :disabled="index === 0">
                            <v-icon>arrow_upward</v-icon>
                          </v-btn>
                        </v-col>
                        <v-col cols="9">
                          <h3>{{ mission.title }}</h3>
                        </v-col>
                        <v-col class="text-right">
                          Mission&nbsp;<span
                          class="text-h6">{{
                            $t(`masteryLevels.${getLevelFromScore(getScore(getContexts(mission)))}.label`)
                          }}</span>
                        </v-col>
                      </v-row>
                    </v-expansion-panel-header>
                    <v-expansion-panel-content>
                      <v-col cols="12">
                        <h3>Activités&nbsp;:</h3>
                      </v-col>
                      <v-col cols="11" offset="1" v-for="(activity) in mission.activitiesIds.map(id => getActivity(id))"
                             :key="activity.id">
                        <h4>{{ activity.title }}</h4>
                        <ul>
                          <li
                            v-for="(capacity, index) in activity.inducedCapacities"
                            :key="index">
                            {{ capacity.code }} - {{ capacity.title }}
                          </li>
                        </ul>
                      </v-col>

                      <v-col cols="12">
                        <h3>Contextes par catégorie&nbsp;:</h3>
                      </v-col>
                      <v-col cols="11" offset="1" v-for="category in getCategories()" :key="category.id">
                        <h4>{{ category.title }}</h4>
                        <ul>
                          <li
                            v-for="(contextTitle, index) in getContextsTitlesForCategoryId(mission, category.id)"
                            :key="index">
                            {{ contextTitle }}
                          </li>
                        </ul>
                      </v-col>
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12">
            <v-divider/>
            <v-card flat>
              <v-card-title>
                <h2>Comportements</h2>
              </v-card-title>
              <v-card-text>
                <ul>
                  <li v-for="behavior in behaviors" :key="behavior.id">{{ behavior.title }}</li>
                </ul>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12">
            <v-divider/>
            <v-card flat>
              <v-card-title>
                <h2>Critères</h2>
              </v-card-title>
              <v-card-text>
                <ul>
                  <li v-for="criterion in job.criteriaValues" :key="criterion.code">{{ criterion.titleForBO }}</li>
                </ul>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12">
            <v-divider/>
            <v-card flat>
              <v-card-title>
                <h2>Préconisations</h2>
              </v-card-title>
              <v-card-text><div v-html="recommendation"/></v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions class="d-flex justify-space-between">
        <v-btn color="grey lighten-5"
               class="mb-2"
               small
               @click.stop="back">
          <v-icon small
                  left>arrow_back_ios
          </v-icon>
          Étape précédente&nbsp;: Préconisations
        </v-btn>
        <v-btn
          small
          outlined
          :loading="loadingNext"
          @click="validate(true)">
          <v-icon class="mr-2" small>
            save
          </v-icon>
          Enregistrer et revenir aux postes
        </v-btn>
        <v-btn
          color="primary"
          class="mb-2"
          small
          :loading="loadingNext"
          title="Un poste finalisé ne peut plus être modifié"
          @click.stop="validate(false)">
          <v-icon class="mr-2" small>
            next_week
          </v-icon>
          Finaliser le poste
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-container>
</template>
<script>
import _ from 'lodash';
import appStore from '@/store';
import ProcessReview from '@/components/mixins/ProcessReview';
import MasteryLevelUtils from '@/components/mixins/MasteryLevelUtils';

export default {
  mixins: [ProcessReview, MasteryLevelUtils],
  props: {
    job: {
      type: Object,
      required: true,
    },
    organization: {
      type: Object,
      required: true,
    },
    missions: {
      type: Array,
      required: true,
    },
    behaviors: {
      type: Array,
      required: true,
    },
    recommendation: {
      type: String,
      default: '',
    },
  },
  data: () => ({
    loadingNext: false,
  }),
  computed: {
    validMissions() {
      return this.missions.filter(mission => mission.id);
    },
    jobLocation() {
      return this.job.location ? `${this.job.location.city || ''}, ${this.job.location.postcode || ''}` : '';
    },
  },
  methods: {
    back() {
      this.$emit('back');
    },
    getActivity(activityId) {
      return appStore.state.observedJobActivities[activityId];
    },
    getCategories() {
      return appStore.state.categories;
    },
    getContextsTitlesForCategoryId(mission, categoryId) {
      const contexts = mission.contextsForCategory
        .filter(c => c.category.id === categoryId)
        .flatMap(c => c.contexts)
        .map(c => c.title);

      if (!contexts.length) {
        contexts.push('Aucun contexte pour cette catégorie.');
      }
      return contexts;
    },
    getContexts(mission) {
      return mission.contextsForCategory
        .flatMap(c => c.contexts);
    },
    async moveMissionUp(missionIndex) {
      const previousMissionForIndex = this.missions[missionIndex];

      this.$set(this.missions, missionIndex, this.missions[missionIndex - 1]);
      this.$set(this.missions, missionIndex - 1, previousMissionForIndex);

      await this.saveMissionsOrder();
    },
    saveMissionsOrder: _.debounce(function doSave() {
      return this.$api.reorderMissions(this.job.id, this.validMissions.map(mission => mission.id));
    }, 300),
    validate(redirect) {
      this.loadingNext = true;
      this.job.state = 'PUBLISHED';
      this.$emit('validate', redirect, () => {
        this.loadingNext = false;
      });
    },
  },
};
</script>

<style>
</style>
