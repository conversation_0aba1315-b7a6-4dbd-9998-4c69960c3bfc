import SafeService from 'odas-plugins/SafeService';
import Vue from 'vue';
import { BulkCVProcessingResult } from 'erhgo-api-client';

export default class BulkCVProcessingService extends SafeService {

  private _validationResult: BulkCVProcessingResult | null = null;
  private _processing = false;

  async processCsvFile(file: File, excludeExistingUsers: boolean): Promise<void> {
    this._processing = true;
    this._validationResult = null;

    await this.safeCall(async () => {
      this._validationResult = (await Vue.$api.createOrUpdateProfilesForCVs(file, excludeExistingUsers)).data;
    });

    this._processing = false;
  }

  get validationResult(): BulkCVProcessingResult | null {
    return this._validationResult;
  }

  get processing(): boolean {
    return this._processing;
  }

  get hasResults(): boolean {
    return this._validationResult !== null;
  }

  get totalRows(): number {
    return this._validationResult?.totalRows || 0;
  }

  get validRows(): number {
    return this._validationResult?.validRows || 0;
  }

  get invalidRows(): number {
    return this._validationResult?.invalidRows || 0;
  }

  get emails(): string[] {
    return this._validationResult?.emails || [];
  }

  get invalidEmails(): string[] {
    return this._validationResult?.invalidEmails || [];
  }

  get message(): string {
    return this._validationResult?.message || '';
  }

  clearResults(): void {
    this._validationResult = null;
  }

  validateCsvFile(file: File): string | null {
    if (!file) {
      return 'Veuillez sélectionner un fichier CSV';
    }

    if (!file.name.toLowerCase().endsWith('.csv')) {
      return 'Le fichier doit être au format CSV';
    }

    if (file.size === 0) {
      return 'Le fichier CSV est vide';
    }

    return null;
  }
}
