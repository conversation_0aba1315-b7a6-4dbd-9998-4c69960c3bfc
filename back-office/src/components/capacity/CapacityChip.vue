<template>
  <v-tooltip bottom :disabled="!small">
    <template v-slot:activator="{ on }">
      <v-chip v-on="on"
              @mouseover="$emit('mouseover')"
              @mouseout="$emit('mouseout')"
              :outlined="outline"
              class="ma-1"
              small
              :color="!disabled && color != null ? color : ''"
              :class="{'max-width-chip': small}"
      >
        {{ capacity.code }} - {{ capacity.title }}
      </v-chip>
    </template>
    {{ capacity.code }} - {{ capacity.title }}
  </v-tooltip>
</template>

<script>
export default {
  props: {
    capacity: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    outline: {
      type: Boolean,
      default: false,
    },
    small: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    color() {
      return this.getSkillColor(this.capacity.code);
    },
    outlineColor() {
      return `${this.color} darken-2`;
    },
  },
  methods: {
    getSkillColor(code) {
      const colorMatch = new Map([
        ['CA1', '#ABEBC6'],
        ['CA2', '#F7DC6F'],
        ['CA3', '#F5B7B1'],
        ['ACT', '#13CCFF'],
      ]);
      let nodeColor = '';
      Object(colorMatch).forEach((color, prefix) => {
        if (code.indexOf(prefix) === 0) {
          nodeColor = color;
        }
      });
      return nodeColor;
    },
  },
};
</script>


<style>
.max-width-chip.v-chip {
  max-width: 100%;
}

.max-width-chip .v-chip__content {
  padding-right: 5px !important;
  padding-left: 5px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 20px;
  display: inline-block !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;

}
</style>
