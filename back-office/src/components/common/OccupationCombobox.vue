<template>
  <v-combobox
    dense
    hide-details
    id="selectorJob"
    class="pt-3"
    v-model="service.occupation"
    @change="selectedOccupation"
    :items="service.occupations"
    :loading="service.loading"
    :search-input.sync="service.query"
    hide-selected
    placeholder="Rechercher un métier"
    no-filter
    label="Sélectionner le métier erhgo correspondant"
    no-data-text="Aucun métier trouvé"
    item-text="title"
    clearable
    :error="service.showAPIError"
    :error-messages="service.showAPIError ? 'Une erreur est survenue' : null"
  >
    <template v-slot:selection="data">
      <span v-html="data.item.title"/>
    </template>
    <template v-slot:item="data">
      <span v-html="data.item.title"/>
      <template v-if="data.item.snippet">
        <span class="pl-3 text-caption font-italic" v-html="data.item.snippet"/>
      </template>
    </template>
  </v-combobox>
</template>
<script>
import OccupationService from '@/components/common/OccupationService';

export default {
  props: {
    value: {
      required: false,
      default: null,
    },
  },
  data() {
    return {
      service: new OccupationService(),
    };
  },
  methods: {
    selectedOccupation(occupation) {
      this.$emit('input', occupation);
    },
  },
};
</script>
