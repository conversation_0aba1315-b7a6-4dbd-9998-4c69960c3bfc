<template>
  <v-speed-dial v-if="renderSpeedDial()"
                fixed
                bottom
                right
                open-on-hover
                direction="top"
                transition="slide-x-reverse-transition">
    <template v-for="action in actions" v-slot:activator>
      <v-tooltip v-if="action.primary"
                 :key="action.label"
                 color="red darken-1"
                 left>
        <template v-slot:activator="{ on }">
          <v-btn fab
                 dark
                 color="red darken-1"
                 class="text-center"
                 v-on="on"
                 :to="action.to ? action.to : { name: action.component}">
            <v-icon>{{ action.icon }}</v-icon>
          </v-btn>
        </template>
        <span class="font-weight-bold">{{$t(action.label)}}</span>
      </v-tooltip>
      <v-tooltip v-else
                 :key="action.label"
                 :color="action.color"
                 left>
        <template v-slot:activator="{ on }">
          <v-btn fab
                 dark
                 :color="action.color"
                 v-on="on"
                 class="text-center"
                 :to="action.to ? action.to : { name: action.component}">
            <v-icon>{{ action.icon }}</v-icon>
          </v-btn>
        </template>
        <span class="font-weight-bold">{{$t(action.label)}}</span>
      </v-tooltip>
    </template>
  </v-speed-dial>
</template>

<script>
export default {
  props: {
    actions: {
      type: Array,
    },
    enable: {
      type: Boolean,
      default: true,
    },
  },
  data: () => ({}),
  methods: {
    renderSpeedDial() {
      return (
        this.enable &&
        typeof this.actions !== 'undefined' &&
        this.actions !== null &&
        this.actions.length > 0
      );
    },
  },
};
</script>

<style>
</style>
