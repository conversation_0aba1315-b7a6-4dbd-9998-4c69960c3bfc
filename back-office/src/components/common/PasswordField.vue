<template>
  <v-text-field
    label="Saisir un mot de passe"
    v-model="value"
    :outlined="outlined"
    :rules="rules"
    @input="$emit('passwordFieldChanged', value)"
    :type="showPassword ? 'text' : 'password'"
    :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
    @click:append="showPassword = !showPassword"
  />
</template>

<script>
import PasswordService from '@/components/services/PasswordService';

export default {
  name: 'PasswordField',
  props: {
    outlined: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      value: '',
      rules: [
        value => !!value || 'Champ obligatoire',
        value => value?.length >= 8 || '8 caractères minimum',
        value => PasswordService.containsLowerCase(value) || 'Au moins une lettre minuscule',
        value => PasswordService.containsUpperCase(value) || 'Au moins une lettre majuscule',
        value => PasswordService.containsDigit(value) || 'Au moins un chiffre',
      ],
      showPassword: false,
    };
  },
};
</script>

<style scoped>

</style>
