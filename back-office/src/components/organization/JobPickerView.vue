<template>
  <v-card>
    <v-card-title>
      <v-col cols="12">
        <h3>Sélectionnez un poste {{forJobCreate ? '' : 'ou un profil de recrutement'}}</h3>
      </v-col>
    </v-card-title>
    <v-card-text>
      <v-col cols="12">
        <v-stepper v-model="data.jobSelectorStep" vertical>
          <v-stepper-step editable :complete="data.jobSelectorStep > 1" step="1">
            <template v-if="!data.organization">
              Sélection de l'organisation
            </template>
            <template v-else>
              Organisation sélectionnée&nbsp;: {{ data.organization.title }}
            </template>
          </v-stepper-step>
          <v-stepper-content step="1">
            <v-autocomplete v-model="data.organization"
                            :items="data.organizationsList"
                            :loading="data.loading"
                            deletable-chips
                            chips
                            small-chips
                            hide-no-data
                            filled
                            light
                            dense
                            :item-text="o => `${o.title}${o.organizationType === 'SOURCING' ? ' (sourcing)' : ''}`"
                            item-value="title"
                            return-object
                            class="pa-1"/>
          </v-stepper-content>
          <v-stepper-step :editable="!!data.organization" :complete="data.jobSelectorStep > 2" step="2">
            <template v-if="!data.job">
              Sélection du poste
            </template>
            <template v-else>
              Poste sélectionné&nbsp;: {{ data.job.title }}
            </template>
          </v-stepper-step>
          <v-stepper-content step="2">
            <v-autocomplete v-model="data.job"
                            :items="data.jobsList"
                            :loading="data.loading"
                            deletable-chips
                            chips
                            small-chips
                            hide-no-data
                            filled
                            light
                            dense
                            item-text="title"
                            item-value="title"
                            return-object
                            class="pa-1"/>
            <template v-if="data.job">
              <v-btn color="primary" @click="$emit('submit', data)" class="mr-5">
                Utiliser ce poste
              </v-btn>
              <span v-if="!forJobCreate">
                <v-btn color="primary" outlined @click="data.jobSelectorStep = 3" v-if="data.profilesList.length">
                  Choisir un profil
                </v-btn>
                <span v-else>
                Aucun profil pour ce poste.
              </span>
              </span>
            </template>
          </v-stepper-content>
          <div v-if="!forJobCreate">
            <v-stepper-step :editable="!!data.job" :complete="data.jobSelectorStep > 3"
                            step="3">
              Sélection du profil de recrutement
            </v-stepper-step>

            <v-stepper-content step="3">
              <v-autocomplete v-model="data.profile"
                              :items="data.profilesList"
                              :loading="data.loading"
                              deletable-chips
                              chips
                              small-chips
                              hide-no-data
                              filled
                              light
                              dense
                              item-text="title"
                              item-value="title"
                              return-object
                              class="pa-1"/>
              <v-btn color="primary" @click="$emit('submit', data)" class="mr-5">
                Valider
              </v-btn>
            </v-stepper-content>
          </div>
        </v-stepper>
      </v-col>
    </v-card-text>
  </v-card>
</template>

<script>
import JobPicker from './JobPicker';

export default {
  props: {
    data: {
      type: JobPicker,
      required: true,
    },
    forJobCreate: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
<style lang="scss" scoped>

</style>
