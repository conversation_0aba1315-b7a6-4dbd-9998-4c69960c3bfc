export default {
  methods: {
    isSkillToQualify(skill) {
      return (!skill.activities.length && !skill.noActivity) ||
        (!skill.contexts.length && !skill.noContext) ||
        (!skill.behaviors.length && !skill.noBehavior);
    },
    isOccupationToQualify(occupation) {
      return occupation.skills.some(skill => this.isSkillToQualify(skill));
    },
    nextSkillToQualify(skill, occupation) {
      const skillsToQualify = occupation.skills.filter(s => this.isSkillToQualify(s));
      const nextIndex = skillsToQualify.findIndex(s => s.uri === skill.uri) + 1;
      return occupation && skillsToQualify.length > 1 && (skillsToQualify[nextIndex] || skillsToQualify[0]);
    },
  },
};
