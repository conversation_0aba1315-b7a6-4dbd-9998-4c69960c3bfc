import { MasteryLevel } from 'erhgo-api-client';

export default {
  data() {
    return {
      MasteryLevel,
    };
  },
  computed: {
    jobsMasteryLevels() {
      return Object.values(this.MasteryLevel);
    },
  },
  methods: {
    computeMasteryLevel(jobMasteryLevel) {
      return this.jobsMasteryLevels.indexOf(jobMasteryLevel) + 1;
    },
    levelDescription(jobMasteryLevel) {
      const masteryLevel = this.computeMasteryLevel(jobMasteryLevel);
      const masteredLabels = Object.values(this.$t('masteryLevels'));
      return masteryLevel + (masteryLevel && masteryLevel <= masteredLabels.length ? ` (${masteredLabels[masteryLevel - 1].label})` : '');
    },
    levelLabelAndDescription(jobMasteryLevel) {
      const masteryLevel = this.computeMasteryLevel(jobMasteryLevel);
      const masteredLabels = Object.values(this.$t('masteryLevels'));
      const levelLabelAndDescription = masteredLabels[masteryLevel - 1];
      return `${levelLabelAndDescription.label} (${levelLabelAndDescription.description})`;
    },
  },
};
