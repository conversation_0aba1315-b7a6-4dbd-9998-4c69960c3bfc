import Vue from 'vue';
import {AlgoliaQuery} from 'erhgo-api-client';
import SafeService from 'odas-plugins/SafeService';

export default class NotificationService extends SafeService {

  async countNotifiableUsers(filter: AlgoliaQuery) {
    return this.safeCall(async () => (await Vue.$api.countNotifiableUsers(filter.query, filter.filters, filter.aroundLatLng, filter.aroundRadius)).data);
  }

  async sendNotification(usersSelectionQuery: AlgoliaQuery, subject: string, content: string, link: string) {
    await this.safeCall(async () => Vue.$api.sendNotificationsToUsersSelection({
      usersSelectionQuery,
      subject,
      content,
      link,
    }));
  }

}
