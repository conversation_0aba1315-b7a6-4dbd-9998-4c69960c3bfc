FROM openapitools/openapi-generator-cli:v6.6.0 as client-builder
# Generate the JS client from OpenAPI spec
COPY ./common/generated /local/common/generated
RUN /usr/local/bin/docker-entrypoint.sh generate -i /local/common/generated/api.yaml -g typescript-axios -o /local/common/generated/client --additional-properties=npmName=erhgo-api-client,supportsES6=true,enumPropertyNaming=UPPERCASE,sortModelPropertiesByRequiredFlag=false --type-mappings=set=Array  --type-mappings=DateTime=Date --type-mappings=Date=Date

FROM node:18 as build
WORKDIR /usr/src/app
# Copy all package.json files and install dependencies. This is a separate step for caching purposes.
COPY ./back-office/package.json ./back-office/package.json
COPY ./.eslintrc.js ./
COPY ./common/plugins/package.json ./common/plugins/package.json
COPY ./common/libs/async-debounce/package.json ./common/libs/async-debounce/package.json
COPY ./package.json ./yarn.lock ./.npmrc ./
COPY --from=client-builder /local/common/generated/client ./common/generated/client
# https://gitlab.com/gitlab-org/gitlab-runner/-/issues/3161
RUN yarn install --frozen-lockfile --no-progress --non-interactive --network-timeout 1000000
# Build the generated client. This is a separate step for caching purposes.
RUN cd ./common/generated/client && yarn build && (../../../node_modules/.bin/eslint --fix ./ || true)
# Copy the rest of the code.
COPY ./common/plugins ./common/plugins
COPY ./common/libs ./common/libs
ARG APP
COPY ./${APP} ./${APP}
# Run the linter and unit tests
RUN cd ./${APP} && \
    yarn lint --no-fix
# Build the application.
ARG VERSION=dev
RUN cd ./${APP} && echo "${VERSION}" > ./public/version && NODE_ENV=production yarn build

# Final image
FROM nginx:1.26.0
ARG APP
COPY --from=build /usr/src/app/${APP}/dist/ /var/www
COPY ./common/*_security_headers.conf /etc/nginx/snippets/
COPY ./common/static_nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 8080
