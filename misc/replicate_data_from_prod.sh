#!/bin/bash
set -euo pipefail

die() {
    echo -e >&2 "$@"
    exit 1
}

if [[ ($# != 1) ]]
  then
    die "Incorrect target environment. Accepted values are: erhgo-preprod, erhgo-staging\nSyntax: replicate_data_from_prod.sh <target-environment>"
fi

targetEnv="${1}"

if ! [[ "$targetEnv" =~ ^(erhgo-preprod|erhgo-staging)$ ]];
  then
    die "Incorrect target environment. Accepted values are: erhgo-preprod, erhgo-staging"
fi

# update keycloak database with new environment values
echo "Please enter keycloak postgresql password:"
read -r keycloakDatabasePassword;

echo "Please enter credential.secret_data value for admin user (keycloak - tips: you can find it with: select * from credential where user_id='4c5356c6-1ef7-4bf6-8433-dd6e23f729b6'):"
read -r credentialValue;

echo "Please enter credential.salt value for admin user (keycloak):"
read -r credentialSalt;

# dirty fix for keycloak deployment typo for preprod namespace
[[ $targetEnv == "erhgo-staging" ]] \
    && targetKeycloak="keycloak" \
    || targetKeycloak="keycloack"

prodApiPod=$(kubectl get pods -n erhgo-prod | grep production-api | awk '{print $1}' | head -n 1)
targetApiDatabase=$(kubectl get pods -n "$targetEnv" | grep mariadb | awk '{print $1}')
targetKeycloakDatabase=$(kubectl get pods -n "$targetEnv" | grep "$targetKeycloak"-postgresql | awk '{print $1}')

echo "Copying from ${prodApiPod} to local..."

kubectl cp erhgo-prod/"$prodApiPod":backup/master/. /tmp/"$targetEnv"
kubectl cp erhgo-prod/"$prodApiPod":backup/master-psql/. /tmp/"$targetEnv"

echo "Local copy finished"

targetApiDump=$(find /tmp/"$targetEnv"/*erhgo* )
targetKeycloakDump=$(find /tmp/"$targetEnv"/*keycloak* )

echo "Copying from local ${targetApiDump} to ${targetEnv}/${targetApiDatabase}"
kubectl cp "$targetApiDump" "$targetEnv"/"$targetApiDatabase":/tmp/mysql.sql.gz

echo "Copying from local ${targetKeycloakDump} to ${targetEnv}/${targetKeycloakDatabase}"
kubectl cp "$targetKeycloakDump" "$targetEnv"/"$targetKeycloakDatabase":/tmp/psql.sql.gz

echo "Cleaning temporary files"
rm -rf /tmp/"$targetEnv"

echo "Importing api database..."

# stop api pod just in case
targetApiDeployment="${targetEnv//erhgo-/}"
kubectl -n "$targetEnv" scale deployment "$targetApiDeployment-api" --replicas=0

kubectl exec -it "$targetApiDatabase" -n "$targetEnv" -- sh -c "zcat /tmp/mysql.sql.gz | mysql -u root -p erhgo"
kubectl exec -it "$targetApiDatabase" -n "$targetEnv" -- rm -f /tmp/mysql.sql.gz

# reset tokens to avoid sending mobile notifications to real users
kubectl exec -it "$targetApiDatabase" -n "$targetEnv" -- sh -c "mysql -u root -p erhgo -e 'TRUNCATE TABLE UserMobileToken'"

echo "Importing keycloak database..."

kubectl exec -it "$targetKeycloakDatabase" -n "$targetEnv" -- sh -c "zcat /tmp/psql.sql.gz | psql -U keycloak -W -d keycloak"
kubectl exec -it "$targetKeycloakDatabase" -n "$targetEnv" -- rm -f /tmp/psql.sql.gz

[[ $targetEnv == "erhgo-staging" ]] \
    && currentEnv="staging" \
    || currentEnv="testing"

{
  echo "CREATE TEMPORARY VIEW credentialValue(val) AS VALUES ('${credentialValue}');"
  echo "CREATE TEMPORARY VIEW credentialSalt(val) AS VALUES ('${credentialSalt}');"
  echo "CREATE TEMPORARY VIEW currentEnv(val) AS VALUES ('${currentEnv}');"
  cat replication_keycloak_update.sql;
} | kubectl exec -i "$targetKeycloakDatabase" -n "$targetEnv" -- sh -c "PGPASSWORD=${keycloakDatabasePassword} psql -U keycloak -d keycloak"

# restart keycloak pod
targetKeycloakPod=$(kubectl get pods -n "$targetEnv" | grep "$targetKeycloak" | awk '{print $1}' | head -n 1)
kubectl delete pod "$targetKeycloakPod" -n "$targetEnv"

# restart api pod
kubectl -n "$targetEnv" scale deployment "$targetApiDeployment-api" --replicas=1

echo "Successfully replicated all data, please go to /admin/indexing to re-index all users!"

if [[ "$targetEnv" == "erhgo-preprod" ]];
  then
    echo "Also please anonymize users thanks to the same dashboard."
fi
